package com.ps.aggregation.controller.merchant;

import cn.hutool.core.collection.CollUtil;
import com.ps.exception.BusinessException;
import com.ps.ps.feign.MerchantPlatformPermissionSetMealFeign;
import com.ps.ps.feign.order.OrderAmountFeign;
import com.ps.ps.feign.user.MerchantEuLabelRuleFeign;
import com.sdsdiy.userapi.dto.OldDataMerchantDto;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/25 11:35
 **/
@RestController
@RequestMapping("/merchantOldData")
@RequiredArgsConstructor
@Api("商户旧数据处理")
public class MerchantOldDataController {
    private final MerchantPlatformPermissionSetMealFeign merchantPlatformPermissionSetMealFeign;
    private final MerchantEuLabelRuleFeign merchantEuLabelRuleFeign;
    private final OrderAmountFeign orderAmountFeign;

    @PostMapping("batchDesign")
    public void batchDesign(@RequestBody List<Long> setMealIds) {
        if(CollUtil.isEmpty(setMealIds)){
            return;
        }
        merchantPlatformPermissionSetMealFeign.oldBatchDesign(setMealIds);

    }
    /**算力权限初始化-转换已使用*/
    @PostMapping("oldDataCalculatePointsUsedCount")
    public void oldDataCalculatePointsUsedCount(@RequestBody OldDataMerchantDto dto) {
        merchantPlatformPermissionSetMealFeign.oldDataCalculatePointsUsedCount(dto);
    }
    /**算力权限初始化-创建算力*/
    @PostMapping("oldDataCalculatePointsCreate")
    public void oldDataCalculatePointsCreate(@RequestBody OldDataMerchantDto dto) {
        merchantPlatformPermissionSetMealFeign.oldDataCalculatePointsCreate(dto);
    }
    /**算力权限初始化-删除旧权限*/
    @PostMapping("oldDataCalculatePointsDelete")
    public void oldDataCalculatePointsDelete(@RequestBody OldDataMerchantDto dto) {
        merchantPlatformPermissionSetMealFeign.oldDataCalculatePointsDelete(dto);
    }
    /**增值服务算力权限初始化*/
    @PostMapping("oldDataCalculatePointsAddServiceInit")
    public void oldDataCalculatePointsAddServiceInit(@RequestBody OldDataMerchantDto dto) {
        merchantPlatformPermissionSetMealFeign.oldDataHashrateAddServiceInit(dto);
    }

    @GetMapping("initMerchantFreeSetMeal")
    public void initMerchantFreeSetMeal(@RequestParam Long merchantId){
        merchantPlatformPermissionSetMealFeign.initMerchantFreeSetMeal(merchantId);
    }

    @PostMapping("syncRepresentativeToNewTable")
    public void syncRepresentativeToNewTable(@RequestParam("key") String key) {
        if (!"pn8CMVqBZdpXa2dOwWHO".equals(key)) {
            throw new BusinessException("reject");
        }
        merchantEuLabelRuleFeign.syncRepresentativeToNewTable();
    }

    /**orderAmount paymentType旧数据处理*/
    @PostMapping("orderAmountPaymentType")
    public void updateOrderAmountPaymentType(@RequestParam(value = "orderId",required = false) String orderId,
                                             @RequestParam(value = "lastIdStr",required = false) String lastIdStr,
                                             @RequestParam(value = "stopIdStr",required = false) String stopIdStr) {
        orderAmountFeign.oldDataUpdateOrderAmountPaymentType(orderId,lastIdStr,stopIdStr);
    }
}
