package com.ps.aggregation.controller.payment;

import com.ps.aggregation.feign.payment.MerchantTotalBillFeign;
import com.ps.ps.feign.MerchantSysUserFeign;
import com.ps.ps.feign.payment.MerchantBillFeign;
import com.ps.support.ISecurityUtils;
import com.ps.tool.EasyExcelUtil;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.paymentapi.constant.BillExcelTemplate;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import com.sdsdiy.paymentapi.dto.BillMonthlyAmountOfMerchantVo;
import com.sdsdiy.paymentapi.dto.MerchantBillVo;
import com.sdsdiy.paymentapi.param.bill.BillQueryParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/merchant/merchantBill")
@Slf4j
@RequiredArgsConstructor
@Api("商户端-账单")
public class MerchantBillController {
    private final MerchantTotalBillFeign merchantTotalBillFeign;
    private final MerchantSysUserFeign merchantSysUserFeign;
    private final MerchantBillFeign merchantBillFeign;

    @PostMapping("/queryMonthlyAmount")
    @ApiOperation(value = "商户账单-本月金额数据", notes = "relatedMerchantId必传=当前商户ID，部门：merchantDepartmentId，操作人账号有选择的话：operateUserId传ID，operateRole传MERCHANT")
    public BillMonthlyAmountOfMerchantVo merchantBillQueryMonthlyAmount(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.MERCHANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getMerchantId());
        return merchantBillFeign.monthlyAmountStatForMerchant(queryParam);
    }

    @PostMapping("/query")
    @ApiOperation(value = "商户账单-分页", notes = "relatedMerchantId必传=当前商户ID，部门：merchantDepartmentId，操作人账号有选择的话：operateUserId传ID，operateRole传MERCHANT")
    public PageResultDto<MerchantBillVo> merchantBillQuery(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.MERCHANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getMerchantId());

        return merchantBillFeign.billQueryPage(queryParam);
    }

    @GetMapping("/export")
    @ApiOperation(value = "商户账单-导出", notes = "relatedMerchantId必传=当前商户ID")
    public void merchantBillExport(
        BillQueryParam queryParam,
        HttpServletResponse response
    ) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.MERCHANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getMerchantId());

        // 1. 查询月度汇总金额数据
        BillMonthlyAmountOfMerchantVo monthlyAmount = merchantBillFeign.monthlyAmountStatForMerchant(queryParam);
        
        List<MerchantBillVo> vos = merchantBillFeign.billQueryList(queryParam);

        // 3. 选择导出模板
        BillExcelTemplate template = BillExcelTemplate.MERCHANT_BILL_FOR_TENANT_OR_SAAS;
        String fileName = template.getFileName();
        String templatePath = template.getTemplate();

        // 4. 执行导出
        try {
            EasyExcelUtil.exportExcel(fileName, templatePath, monthlyAmount, vos, response);
        } catch (Exception e) {
            log.error("POD商户账单导出异常", e);
            throw new RuntimeException("导出失败");
        }
    }
}
