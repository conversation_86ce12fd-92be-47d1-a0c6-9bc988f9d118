package com.ziguang.sdsfactory.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.ps.amazon.s3.S3ServiceV2;
import com.ps.base.entity.SearchBean;
import com.ps.exception.BusinessException;
import com.ps.ps.feign.FactoryOrderAfterServiceFeign;
import com.ps.ps.feign.FactoryOrderCompensationHistoryFeign;
import com.ps.ps.feign.FactoryOrderFeign;
import com.ps.ps.feign.FactoryOrderRejectHistoryFeign;
import com.ps.ps.feign.factory.FactoryTaskFeign;
import com.ps.ps.feign.statistics.FactoryOrderSqlStatFeign;
import com.ps.ps.service.FactoryOrderService;
import com.ps.ps.service.FileUploadService;
import com.ps.ps.service.OrderRemarkService;
import com.ps.ps.service.linstener.OrderEventService;
import com.ps.ps.service.order.OrderFormatService;
import com.ps.support.Assert;
import com.ps.support.FileRespon;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.helper.ZipUtils;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.constant.event.message.factory.FactoryDownloadOrderMessage;
import com.sdsdiy.orderapi.dto.FactoryOrderQueryParameters;
import com.sdsdiy.orderapi.dto.factoryorder.*;
import com.sdsdiy.orderapi.dto.productionLine.FactoryOrderByProductGroupingDto;
import com.sdsdiy.orderapi.dto.productionLine.FactoryOrderByProductionLineGroupingDto;
import com.sdsdiy.orderapi.enums.DownTypeEnum;
import com.sdsdiy.orderapi.enums.FactoryOrderStatusEnum;
import com.sdsdiy.orderapi.enums.PrintTypeEnum;
import com.sdsdiy.orderapi.param.FactoryOrderExportExcelParam;
import com.sdsdiy.orderdata.dto.FactoryOrderStatRespDTO;
import com.sdsdiy.orderdata.dto.factory.order.FactoryOrderRespDto;
import com.sdsdiy.orderdata.dto.factory.order.*;
import com.sdsdiy.orderdata.dto.factory.task.FactoryOrderTaskOptionReqDto;
import com.sdsdiy.orderdata.dto.factory.task.FactoryTaskRespDto;
import com.sdsdiy.orderdata.dto.factoryOrder.FactoryOrderCheckRespDTO;
import com.sdsdiy.orderdata.dto.factoryOrder.FactoryOrderDeliveryLabelDto;
import com.sdsdiy.orderdata.dto.product.flow.ProductFlowStepCheckRespDTO;
import com.ziguang.base.dto.FactoryOrderDto;
import com.ziguang.base.dto.FactoryOrderListDto;
import com.ziguang.base.dto.FactoryOrderRevenueStatisticsRespDto;
import com.ziguang.base.dto.FactoryOrderUrgentTimeStatisticsRespDto;
import com.ziguang.base.model.FactoryOrder;
import com.ziguang.base.model.OrderRemark;
import com.ziguang.base.support.Response;
import com.ziguang.base.support.ResponseCode;
import com.ziguang.base.support.StringUtils;
import com.ziguang.base.support.contant.OrderOriginType;
import com.ziguang.base.support.contant.Platform;
import com.ziguang.base.vo.FactoryOrderStatusVo;
import com.ziguang.base.vo.ImgUrlVo;
import com.ziguang.sdsfactory.feign.FactoryUserInterfaceWhiteFeign;
import com.ziguang.sdsfactory.service.FactoryOrderCustomDownloadService;
import com.ziguang.sdsfactory.service.ImgWriteCellHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import support.ISecurityUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * 2019/3/12 0012
 */
@Api("工厂订单表")
@Slf4j
@RequestMapping("factory_orders")
@RestController
public class FactoryOrderController {

    @Autowired
    private FactoryOrderService factoryOrderService;

    @Autowired
    private OrderRemarkService orderRemarkService;

    @Resource
    private FactoryUserInterfaceWhiteFeign factoryUserInterfaceWhiteFeign;

    @Resource
    private FactoryTaskFeign factoryTaskFeign;
    @Resource
    private OrderEventService orderEventService;

    @Resource
    private FactoryOrderFeign factoryOrderFeign;
    @Resource
    private S3ServiceV2 s3ServiceV2;
    @Resource
    private OrderFormatService orderFormatService;

    @ApiOperation("产品归类")
    @RequestMapping(value = "/product/grouping", method = RequestMethod.GET)
    public List<FactoryOrderByProductGroupingDto> productGrouping(@SpringQueryMap FactoryOrderQueryParameters factoryOrderQueryParameters) {
        factoryOrderQueryParameters.setFactoryId(ISecurityUtils.getFactoryId());
        return this.factoryOrderFeign.productGrouping(factoryOrderQueryParameters);
    }
    @ApiOperation("产品归类走es")
    @RequestMapping(value = "/product/groupingByEs", method = RequestMethod.GET)
    public List<FactoryOrderByProductGroupingDto> productGroupingByEs(@SpringQueryMap FactoryOrderQueryParameters factoryOrderQueryParameters) {
        factoryOrderQueryParameters.setFactoryId(ISecurityUtils.getFactoryId());
        return this.factoryOrderFeign.productGroupingByEs(factoryOrderQueryParameters);
    }

    @ApiOperation("生产线归类")
    @RequestMapping(value = "/production_line/grouping", method = RequestMethod.GET)
    public List<FactoryOrderByProductionLineGroupingDto> productionLineGrouping(@SpringQueryMap FactoryOrderQueryParameters factoryOrderQueryParameters) {
        factoryOrderQueryParameters.setFactoryId(ISecurityUtils.getFactoryId());
        return this.factoryOrderFeign.productionLineGrouping(factoryOrderQueryParameters);

    }

    @ApiOperation("生产线归类走es")
    @RequestMapping(value = "/production_line/groupingByEs", method = RequestMethod.GET)
    public List<FactoryOrderByProductionLineGroupingDto> productionLineGroupingByEs(@SpringQueryMap FactoryOrderQueryParameters factoryOrderQueryParameters) {
        factoryOrderQueryParameters.setFactoryId(ISecurityUtils.getFactoryId());
        return this.factoryOrderFeign.productionLineGroupingByEs(factoryOrderQueryParameters);
    }

    @ApiOperation("订单列表")
    @GetMapping("/v2/order/all")
    @RequiresPermissions(value = {"order:all:list", "order:status:tocomplete", "order:status:tosend"}, logical = Logical.OR)
    public com.sdsdiy.orderdata.dto.factory.order.FactoryOrderListDto<FactoryOrderRespDto> all(FactoryOrderQueryParameters params) {
        params.setFactoryId(ISecurityUtils.getFactoryId());
        return this.factoryOrderFeign.page(params);
    }
    @ApiOperation("订单列表走es")
    @GetMapping("/v2/order/allByEs")
    @RequiresPermissions(value = {"order:all:list", "order:status:tocomplete", "order:status:tosend"}, logical = Logical.OR)
    public com.sdsdiy.orderdata.dto.factory.order.FactoryOrderListDto<FactoryOrderRespDto> allByEs(FactoryOrderQueryParameters params) {
        params.setFactoryId(ISecurityUtils.getFactoryId());
        params.setTenantId(ISecurityUtils.getCurrUser().getTenantId());
        return this.factoryOrderFeign.esPage(params);
    }

    @ApiOperation("待排产订单列表")
    @GetMapping("/v2/ping/list")
    @RequiresPermissions("order:ping:list")
    public com.sdsdiy.orderdata.dto.factory.order.FactoryOrderListDto<FactoryOrderRespDto> page(FactoryOrderQueryParameters params) {
        params.setFactoryId(ISecurityUtils.getFactoryId());
        return this.factoryOrderFeign.page(params);
    }

    @ApiOperation("待排产订单列表走es")
    @GetMapping("/v2/ping/listByEs")
    @RequiresPermissions("order:ping:list")
    public com.sdsdiy.orderdata.dto.factory.order.FactoryOrderListDto<FactoryOrderRespDto> pageByEs(FactoryOrderQueryParameters params) {
        params.setFactoryId(ISecurityUtils.getFactoryId());
        params.setTenantId(ISecurityUtils.getCurrUser().getTenantId());
        return this.factoryOrderFeign.esPage(params);
    }

    @ApiOperation("待排产加入生产计划检查加急订单")
    @PostMapping("unconfirmed/check/warning")
    public FactoryOrderCheckRespDTO checkWarning(@RequestBody FactoryOrderQueryParameters params) {
        params.setFactoryId(ISecurityUtils.getFactoryId());
        params.setStatus("1");
        params.setIsJoinTask(0);
        if (StrUtil.isNotBlank(params.getEarlyWarningIdStr())) {
            // 有指定预警时，与其他条件是且的关系
            params.setAndEarlyWarningIds(StringUtils.stringToLongList(params.getEarlyWarningIdStr()));
        }
        // 或 强制预警 加急生产单（加急（加急，售后，驳回，漏件），首单，速卖通）
        params.setEarlyWarningIdStr("9,27,35");
        if (StrUtil.isBlank(params.getAfterServiceRefuseType())) {
            // 没有指定类型时，预警要加上售后类型
            params.setEarlyWarnIncludeAfterSale(Boolean.TRUE);
        }
        return this.factoryOrderFeign.unconfirmedCheckWarning(params);
    }


    @ApiOperation("v7.16.0 根据订单编号工厂订单详情")
    @RequestMapping(value = "/v2/no/{no}", method = RequestMethod.GET)
    public FactoryOrderRespDto findInfoByNo(@PathVariable(value = "no") String no) {
        FactoryOrderRespDto detail = this.factoryOrderService.detailByFactoryOrderNo(ISecurityUtils.getFactoryId(), no);
        Assert.validateNull(detail, "订单不存在");
        return detail;
    }

    @ApiOperation("工厂订单详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public List<FactoryOrderDto> detail(@PathVariable(value = "id") String ids
            , @RequestParam(value = "is_print", defaultValue = "0") int isPrint
            , @RequestParam(value = "isIncomingIdSort", defaultValue = "false") Boolean isIncomingIdSort) {
        List<Long> factoryIds = StringUtils.stringToLongList(ids);
        List<FactoryOrderDto> details = this.factoryOrderService.detail(ISecurityUtils.getFactoryId(), factoryIds, isPrint, isIncomingIdSort);
        Long tenantId = ISecurityUtils.getCurrUser().getTenantId();
        if (BasePoConstant.yes(isPrint) && !TenantCommonConstant.isSdsdiy(tenantId)) {
            orderFormatService.formatDistributorTenantName(tenantId, details);
        }
        return details;
    }


    @RequestMapping(method = RequestMethod.GET)
    public FactoryOrderListDto<FactoryOrderDto> list(@RequestParam(value = "complete_start_time", required = false) String completeStartTime,
                                                     @RequestParam(value = "complete_end_time", required = false) String completeEndTime,
                                                     @RequestParam(value = "start_time", required = false) String startTime,
                                                     @RequestParam(value = "end_time", required = false) String endTime,
                                                     @RequestParam(value = "keyword", required = false) String keyword,
                                                     @RequestParam(value = "sort", required = false) String sort,
                                                     @RequestParam(value = "status", required = false) String status,
                                                     @RequestParam(value = "page", required = false) Integer page,
                                                     @RequestParam(value = "size", defaultValue = "10") Integer size,
                                                     @RequestParam(value = "no", required = false) String no,
                                                     @RequestParam(value = "settles_status", required = false) Integer settlesStatus,
                                                     @RequestParam(value = "category_id", required = false) Long categoryId,
                                                     @RequestParam(value = "product_search_key", required = false) String productName,
                                                     @RequestParam(value = "product_size", required = false) String productSize,
                                                     @RequestParam(value = "merchant_id", required = false) String merchantId,
                                                     @RequestParam(value = "product_color_name", required = false) String productColorName,
                                                     @RequestParam(value = "accurateProductColorName", required = false) String accurateProductColorName,
                                                     @RequestParam(value = "factory_order_settle_id", required = false) Integer factoryOrderSettleId,
                                                     @RequestParam(value = "early_warning_id", required = false) Long type,
                                                     @RequestParam(value = "color", required = false) String color,
                                                     @RequestParam(value = "is_show_refund", required = false) Integer isShowRefund,
                                                     @RequestParam(value = "origin_type", required = false) String originType,
                                                     @RequestParam(value = "is_over_time", required = false) Integer isOverTime,
                                                     @RequestParam(value = "production_type", required = false) Integer productionType,
                                                     @RequestParam(value = "issuing_bay_area_id", required = false) Long issuingBayAreaId,
                                                     @RequestParam(value = "issuing_bay_id", required = false) Long issuingBayId,
                                                     @RequestParam(value = "manuscript_feedback_status", required = false) Integer manuscriptFeedbackStatus,
                                                     @RequestParam(value = "no_manuscript_feedback_status", required = false) Integer noManuscriptFeedbackStatus,
                                                     @RequestParam(value = "manuscript_feedback_status_search", required = false) Integer manuscript_feedback_status_search,
                                                     @RequestParam(value = "after_service_refuse_type", required = false) String filter,
                                                     @RequestParam(value = "is_down_materrial", required = false) Integer isDownMaterrial,
                                                     @RequestParam(value = "product_id", required = false) Long productId,
                                                     @RequestParam(value = "production_line_id", required = false) Long productionLineId,
                                                     @RequestParam(value = "confirmStartTime", required = false) String confirmStartTime,
                                                     @RequestParam(value = "confirmEndTime", required = false) String confirmEndTime,
                                                     @RequestParam(value = "produceStartTime", required = false) String produceStartTime,
                                                     @RequestParam(value = "produceEndTime", required = false) String produceEndTime,
                                                     @RequestParam(value = "deliverStartTime", required = false) String deliverStartTime,
                                                     @RequestParam(value = "deliverEndTime", required = false) String deliverEndTime
    ) {

        if (originType != null && !originType.equals(OrderOriginType.FBA.getValue())) {
            originType = OrderOriginType.NORMAL.getValue();
        }

        FactoryOrderListDto<FactoryOrderDto> res = this.factoryOrderService.listOfFactory(null, merchantId, filter, completeStartTime, completeEndTime, startTime, endTime, keyword, status, page, no, size, settlesStatus, categoryId, productName,
                productSize, productColorName, ISecurityUtils.getFactoryId(), factoryOrderSettleId, sort, type, false, isShowRefund, isOverTime, productionType, issuingBayAreaId, issuingBayId, originType,
                manuscriptFeedbackStatus, isDownMaterrial, noManuscriptFeedbackStatus, manuscript_feedback_status_search, productId, productionLineId, confirmStartTime, confirmEndTime, produceStartTime, produceEndTime,
                deliverStartTime, deliverEndTime, accurateProductColorName);
        this.factoryOrderService.formatOldFactoryOrder(res.getItems());
        return res;
    }


    @RequestMapping(value = "/single_info", method = RequestMethod.GET)
    public SearchBean<FactoryOrderDto> singleInfo(@RequestParam(value = "no", required = false) String no) {
        Long factoryId = ISecurityUtils.getCurrUser().getFactoryId();
        return this.factoryOrderService.singleInfo(no, factoryId);
    }


    @RequestMapping(value = "/print", method = RequestMethod.POST)
    @ApiOperation("获取生产订单打印信息")
    public List<FactoryOrderDto> print(@RequestBody List<Long> ids) {
        List<FactoryOrderDto> details = this.factoryOrderService.detail(ISecurityUtils.getFactoryId(), ids, 1, true);
        // 根据传入的Id进行排序
        if (CollectionUtil.isEmpty(details)) {
            return details;
        }

        // 待确认订单 -> 生产中
        this.factoryOrderService.sortByIds(details, ids);

        List<Long> unconfimedIds = Lists.newArrayList();
        for (FactoryOrderDto dto : details) {
            if (FactoryOrderStatusEnum.UNCONFIRMED.getStatus() == dto.getStatus()) {
                unconfimedIds.add(dto.getId());
            }
        }
        if (CollectionUtil.isNotEmpty(unconfimedIds)) {
            this.factoryOrderFeign.toProduce(ISecurityUtils.getFactoryId(), ISecurityUtils.getCurrUserId(), ISecurityUtils.getCurrUser().getUsername(), unconfimedIds);
        }

        this.factoryTaskFeign.modifyFactionTaskOrderRelOption(ids, PrintTypeEnum.PRINT.getValue(), ISecurityUtils.getCurrUserId());
        return details;
    }


    @RequestMapping(value = "/infoByNo", method = RequestMethod.GET)
    @ApiOperation("根据no获取工厂单详情")
    public FactoryOrderDto detail(@RequestParam(value = "no") String no) {
        FactoryOrderDto res = this.factoryOrderService.detail(no, ISecurityUtils.getCurrUserId(), ISecurityUtils.getFactoryId());
        Long tenantId = ISecurityUtils.getCurrUser().getTenantId();
        if (!TenantCommonConstant.isSdsdiy(tenantId)) {
            orderFormatService.formatDistributorTenantName(tenantId, res);
        }
        return res;
    }

    @ApiOperation("查看定制")
    @RequestMapping(value = "/{id}/material", method = RequestMethod.GET)
    public List<ImgUrlVo> formatOriginMaterial(@PathVariable(value = "id") Long id) {
        return this.factoryOrderService.formatOriginMaterial(ISecurityUtils.getFactoryId(), id);
    }

    @ApiOperation("查询生产单的生产稿件列表")
    @RequestMapping(value = "/{id}/manuscript", method = RequestMethod.GET)
    public List<ImgUrlVo> formantManuscriptUrl(@PathVariable(value = "id") Long id) {
        return this.factoryOrderService.formantManuscriptUrl(ISecurityUtils.getFactoryId(), id, 1, ISecurityUtils.getUser().getId());
    }

    @RequestMapping(value = "/{id}/remarks", method = RequestMethod.GET)
    @ResponseBody
    public List remarks(@PathVariable("id") Long id) {
        return this.orderRemarkService.findByOrderId(id, OrderRemarkService.TYPE_FACTORY_ORDER);
    }

    @RequestMapping(value = "/{order_id}/remarks", method = RequestMethod.POST)
    @ResponseBody
    public OrderRemark remark(@RequestBody JSONObject jsonObject, @PathVariable("order_id") Long orderId) {
        String remark = jsonObject.getString("remark");
        if (StringUtils.isBlank(remark)) {
            throw new BusinessException("备注内容不能为空");
        }
        OrderRemark orderRemark = new OrderRemark();
        orderRemark.setOrderId(orderId);
        orderRemark.setUserId(ISecurityUtils.getCurrUserId());
        orderRemark.setUsername(ISecurityUtils.getCurrUser().getUsername());
        orderRemark.setRemark(remark);
        orderRemark.setPlatform(Platform.FACTORY.getValue());
        this.orderRemarkService.saveOfFactory(ISecurityUtils.getUser(), orderRemark);
        return orderRemark;
    }

    @RequestMapping(value = "/early_warning_statistics", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Integer> earlyWarningStatistics(@RequestParam(value = "status", required = false) Integer status, @RequestParam(value = "taskId", required = false) Long taskId) {
        Map<String, Integer> map = Maps.newHashMap();
        // 加急
        map.put("urgent", this.factoryOrderService.countWarningNum(ISecurityUtils.getFactoryId(), status, taskId));

        FactoryOrdersStaticReqDto params = FactoryOrdersStaticReqDto.builder().factoryId(ISecurityUtils.getFactoryId()).status(status).taskId(taskId).build();

        FactoryOrdersStaticRespDto result = this.factoryOrderSqlStatFeign.statFactoryOrderWarnings(params);
        map.put("confirmWaring", result.getConfirmWaring());
        map.put("shipWaring", result.getShipWaring());
        map.put("willOverTimeWaring", result.getWillOverTimeWaring());

        map.put("overdue", this.factoryOrderService.countOutDateNum(ISecurityUtils.getFactoryId(), status, taskId));
        return map;
    }

    @RequestMapping(value = "/early_warning_statisticsByEs", method = RequestMethod.GET)
    @ResponseBody
    public FactoryOrdersStaticRespDto earlyWarningStatisticsByEs(@RequestParam(value = "status", required = false) Integer status, @RequestParam(value = "taskId", required = false) Long taskId) {
        return factoryOrderFeign.earlyWarningStatisticsByEs(ISecurityUtils.getFactoryId(), status, taskId);
    }



    @ApiOperation("7.17.6 98 增加是否批量校验")
    @RequestMapping(value = "/status", method = RequestMethod.PUT)
    public void updateStatus(@RequestBody FactoryOrderStatusVo vo) {
        //2生产中 3待发货 4已发货 98取消
        //备注 98必传
        if (!Arrays.asList(2, 3, 4, 98).contains(vo.getStatus())) {
            throw new BusinessException("status is error");
        }

        if (Arrays.asList(98).contains(vo.getStatus())) {
            if (StringUtils.isEmpty(vo.getRemark())) {
                throw new BusinessException("remark is empty");
            }
        }
        if (vo.getNos() == null || vo.getNos().size() == 0) {
            throw new BusinessException("nos is empty");
        }


        this.factoryOrderService.updateStatusOfFactory(ISecurityUtils.getFactoryId(), ISecurityUtils.getCurrUserId(), ISecurityUtils.getUser().getUsername(), vo);
    }


    @RequestMapping(value = "/status", method = RequestMethod.GET)
    public void nextStatusCheck(String nos, int status) {
        //1待确认 2生产中 3待发货 4已发货 5待质检 6已完成 7已驳回 98取消
        if (!Arrays.asList(2, 3, 4, 98).contains(status)) {
            throw new BusinessException("status is error");
        }
        Response response = this.factoryOrderService.nextStatusCheckOfFactory(Arrays.asList(nos.split(",")), status, true);
        if (ResponseCode.SUCCESS.value() != response.getRet()) {
            throw new BusinessException(response.getMsg());
        }
    }


    @ApiOperation("修改订单状态--  3待发货[生产完成]")
    @PutMapping(value = "inner/toComplete")
    public ProductFlowStepCheckRespDTO innerToComplete(@Validated @RequestBody ModifyStatusReqDto vo) {
        Integer whiteNum = this.factoryUserInterfaceWhiteFeign.findPermissionByUserId(ISecurityUtils.getCurrUserId(), ISecurityUtils.getFactoryId());
        Assert.validateTrue(whiteNum == null || whiteNum < 1, "该账号无权限");
        if (!Arrays.asList(3).contains(vo.getStatus())) {
            throw new BusinessException("status is error");
        }
        vo.setFactoryId(ISecurityUtils.getFactoryId());
        vo.setOpUserId(ISecurityUtils.getCurrUserId());
        vo.setUserName(ISecurityUtils.getCurrUser().getUsername());
        ProductFlowStepCheckRespDTO result = this.factoryOrderFeign.checkFlowStepFinish(vo.getNos());
        if (result.getRetCode() != 0) {
            return result;
        }
        this.factoryOrderFeign.toProductCompletionOfFactory(vo);
        return result;
    }


    @ApiOperation("修改订单状态--  3待发货[生产完成] 98取消")
    @PutMapping(value = "/v2/status")
    @RequiresPermissions({"order:status:tocomplete"})
    public ProductFlowStepCheckRespDTO updateStatus(@Validated @RequestBody ModifyStatusReqDto vo) {
        //5待质检 6已完成 7驳回
        if (!Arrays.asList(3, 4).contains(vo.getStatus())) {
            throw new BusinessException("status is error");
        }
        vo.setFactoryId(ISecurityUtils.getFactoryId());
        vo.setOpUserId(ISecurityUtils.getCurrUserId());
        vo.setUserName(ISecurityUtils.getCurrUser().getUsername());
        ProductFlowStepCheckRespDTO result = this.factoryOrderFeign.checkFlowStepFinish(vo.getNos());
        if (result.getRetCode() != 0) {
            return result;
        }
        this.factoryOrderFeign.toProductCompletionOfFactory(vo);

        return result;
    }

    @Deprecated
    @ApiOperation("批量发送物流")
    @RequestMapping(value = "/logistics", method = RequestMethod.PUT)
    @RequiresPermissions("order:status:tosend")
    public ProductFlowStepCheckRespDTO putLogistics(@RequestBody JSONObject param) {
        List<String> nos = param.getJSONArray("nos").toJavaList(String.class);

        String name = param.getString("name");
        String orderNo = param.getString("orderNo");
        Assert.validateTrue(20 < name.length(), "物流渠道不能大于20个字符");
        Assert.validateTrue(32 < orderNo.length(), "物流单号不能大于32个字符");

        ProductFlowStepCheckRespDTO result = this.factoryOrderFeign.checkFlowStepFinish(nos);
        if (result.getRetCode() != 0) {
            return result;
        }

        this.factoryOrderService.putLogistics(null, ISecurityUtils.getUser(), ISecurityUtils.getFactoryId());

        return result;
    }

    @Deprecated
    @ApiOperation("批量完成订单且发送物流")
    @RequestMapping(value = "/completeAndLogistics", method = RequestMethod.PUT)
    @RequiresPermissions({"order:status:tocomplete", "order:status:tosend"})
    public ProductFlowStepCheckRespDTO completeAndLogistics(@RequestBody JSONObject param) {
        List<String> nos = param.getJSONArray("nos").toJavaList(String.class);

        String name = param.getString("name");
        String orderNo = param.getString("orderNo");
        Assert.validateTrue(20 < name.length(), "物流渠道不能大于20个字符");
        Assert.validateTrue(32 < orderNo.length(), "物流单号不能大于32个字符");

        ProductFlowStepCheckRespDTO result = this.factoryOrderFeign.checkFlowStepFinish(nos);
        if (result.getRetCode() != 0) {
            return result;
        }

        this.factoryOrderService.putLogistics(null, ISecurityUtils.getUser(), ISecurityUtils.getFactoryId());
        return result;
    }

    @ApiOperation("批量下载生产稿件")
    @RequestMapping(value = "/download_manuscripts", method = RequestMethod.GET)
    public void downLoad(@RequestParam(value = "nos", required = false) String nos, HttpServletResponse response) {
        List<FileRespon> fileRespons = this.factoryOrderService.downLoadManuscript(nos, ISecurityUtils.getUser().getId(), ISecurityUtils.getFactoryId());
        this.factoryOrderService.down(ISecurityUtils.getFactoryId(), response, fileRespons);
        List<String> noList = com.ps.support.utils.StringUtils.stringToStringList(nos);
        List<Long> orderIds = noList.stream().map(x -> Long.valueOf(x)).collect(Collectors.toList());
        this.factoryTaskFeign.modifyFactionTaskOrderRelOption(orderIds, DownTypeEnum.DOWN.getValue(), ISecurityUtils.getCurrUserId());
    }

    @ApiOperation("根据no修改订单稿件状态")
    @RequestMapping(value = "/downStatus", method = RequestMethod.PUT)
    public void updateDownStatus(@RequestBody FactoryOrderDto params) {
        FactoryOrder details = this.factoryOrderService.findByNo(params.getNo());
        if (details == null || !details.getFactoryId().equals(ISecurityUtils.getFactoryId())) {
            throw new BusinessException("订单不存在");
        }
        //下载生产稿件 消息发送
        FactoryDownloadOrderMessage factoryDownloadOrderMessage = new FactoryDownloadOrderMessage();
        factoryDownloadOrderMessage.setFactoryOrderId(details.getId());
        factoryDownloadOrderMessage.setOrderId(0L);
        factoryDownloadOrderMessage.setEid(details.getId());
        factoryDownloadOrderMessage.setOperatorUid(ISecurityUtils.getCurrUserId());
        factoryDownloadOrderMessage.setSendingTime(new Date());
        this.orderEventService.sendProcessMsg(factoryDownloadOrderMessage, OrderProgressConstant.FACTORY_DOWNLOAD_ORDER);
        this.factoryTaskFeign.modifyFactionTaskOrderRelOption(Lists.newArrayList(details.getId()), DownTypeEnum.DOWN.getValue(), ISecurityUtils.getCurrUserId());
    }

    @ApiOperation("根据生产计划批量下载生产稿件")
    @RequestMapping(value = "/downloadManuscriptsByTaskId", method = RequestMethod.GET)
    public void downLoadByTaskId(@RequestParam(value = "taskId", required = false) Long taskId, @RequestParam(value = "type", required = false) String type, HttpServletResponse response) throws IOException {
        FactoryOrderTaskOptionReqDto params = FactoryOrderTaskOptionReqDto.builder()
                .factoryId(ISecurityUtils.getFactoryId())
                .taskId(taskId)
                .userId(ISecurityUtils.getCurrUserId())
                .userName(ISecurityUtils.getCurrUser().getUsername())
                .type(type)
                .build();
        List<com.sdsdiy.orderdata.dto.FactoryOrderDto> orderDtos = this.factoryTaskFeign.getOrdersDownByFactoryTaskId(params);
        Assert.validateTrue(CollectionUtil.isEmpty(orderDtos), "task order rel is not found");
        List<String> nos = orderDtos.stream().map(x -> x.getNo()).collect(Collectors.toList());

        List<FileRespon> fileRespons = this.factoryOrderService.downLoadManuscript(StringUtils.join(nos, ","), ISecurityUtils.getUser().getId(), ISecurityUtils.getFactoryId());
        this.factoryOrderService.down(ISecurityUtils.getFactoryId(), response, fileRespons);
        List<Long> orderIds = orderDtos.stream().map(x -> x.getId()).collect(Collectors.toList());
        this.factoryTaskFeign.modifyFactionTaskOrderRelOption(orderIds, DownTypeEnum.DOWN.getValue(), ISecurityUtils.getCurrUserId());

    }

    @RequestMapping(value = "/toS3", method = RequestMethod.GET)
    public String toS3(@RequestParam(value = "nos", required = false) String nos, HttpServletResponse response) {
        List<FileRespon> fileRespons = this.factoryOrderService.downLoadManuscript(nos, ISecurityUtils.getUser().getId(), ISecurityUtils.getFactoryId());
        return this.factoryOrderService.upToS3(fileRespons, new ByteArrayOutputStream());
    }

    @RequestMapping(value = "/check_download", method = RequestMethod.GET)
    public void checkDownload(@RequestParam(value = "nos", required = false) String nos) {
        this.factoryOrderService.checkDownload(nos, ISecurityUtils.getUser().getId());
    }

    @RequestMapping(value = "/stock", method = RequestMethod.POST)
    public Response checkStock(@RequestBody List<String> nos) {
        Response response = new Response();
        if (CollectionUtil.isNotEmpty(nos)) {
            List<String> result = this.factoryOrderService.checkStock(ISecurityUtils.getFactoryId(), nos);
            if (result.size() > 0) {
                response.setResponseCode(ResponseCode.STOCK_NOT_ENOUGH);
                response.setData(result);
            }
        }
        return response;
    }


    @RequestMapping(value = "/stock", method = RequestMethod.GET)
    public Response checkStock(@RequestParam(value = "nos") String nos) {
        Response response = new Response();
        if (StrUtil.isNotEmpty(nos)) {
            List<String> result = this.factoryOrderService.checkStock(ISecurityUtils.getFactoryId(), Arrays.asList(nos.split(",")));
            if (result.size() > 0) {
                response.setResponseCode(ResponseCode.STOCK_NOT_ENOUGH);
                response.setData(result);
            }
        }
        return response;
    }

    @Autowired
    FactoryOrderAfterServiceFeign factoryOrderAfterServiceFeign;

    @Autowired
    FactoryOrderSqlStatFeign factoryOrderSqlStatFeign;
    @Autowired
    FactoryOrderRejectHistoryFeign factoryOrderRejectHistoryFeign;
    @Autowired
    FactoryOrderCompensationHistoryFeign factoryOrderCompensationHistoryFeign;
    @Autowired
    private FactoryOrderCustomDownloadService factoryOrderCustomDownloadService;


    @RequestMapping(value = "/timeStatistics", method = RequestMethod.GET)
    public FactoryOrderUrgentTimeStatisticsRespDto timeStatistics() {

        FactoryOrderUrgentTimeStatisticsRespDto respDto = new FactoryOrderUrgentTimeStatisticsRespDto();
        respDto.setInAfterService(this.factoryOrderSqlStatFeign.inAfterServiceQty(ISecurityUtils.getFactoryId()));
        respDto.setInOverTime(this.factoryOrderSqlStatFeign.overTimesQtyByFactoryId(ISecurityUtils.getFactoryId()));
        respDto.setWillOverTime(this.factoryOrderSqlStatFeign.willOverTimesQtyByFactoryId(ISecurityUtils.getFactoryId()));
        respDto.setConfirmWaring(this.factoryOrderSqlStatFeign.confirmWaring(ISecurityUtils.getFactoryId(), null));
        respDto.setShipWaring(this.factoryOrderSqlStatFeign.shipWaring(ISecurityUtils.getFactoryId(), null));
        return respDto;
    }


    @RequestMapping(value = "/revenue_stat", method = RequestMethod.GET)
    public FactoryOrderRevenueStatisticsRespDto revenueStat() {
        Long factoryId = ISecurityUtils.getFactoryId();
        FactoryOrderRevenueStatisticsRespDto respDto = new FactoryOrderRevenueStatisticsRespDto();
        FactoryOrderStatRespDTO dto = this.factoryOrderAfterServiceFeign.afterServiceRefundStat(factoryId);
        respDto.setAfterServiceRefundAmount(dto.getAmountTotal());
        respDto.setAfterServiceRefundCount(dto.getQty());
        respDto.setAfterServiceRefundTotal(dto.getNum());


        dto = this.factoryOrderSqlStatFeign.afterServiceResendStat(factoryId);
        respDto.setAfterServiceResendAmount(dto.getAmountTotal());
        respDto.setAfterServiceResendCount(dto.getQty());
        respDto.setAfterServiceResendTotal(dto.getNum());


        dto = this.factoryOrderCompensationHistoryFeign.stat(factoryId);
        respDto.setCompensationAmount(dto.getAmountTotal());
        respDto.setCompensationCount(dto.getQty());
        respDto.setCompensationTotal(dto.getNum());


        dto = this.factoryOrderSqlStatFeign.inProductStat(factoryId);
        respDto.setProductAmount(dto.getAmountTotal());
        respDto.setProductCount(dto.getQty());
        respDto.setProductTotal(dto.getNum());


        dto = this.factoryOrderSqlStatFeign.finishStat(factoryId);
        respDto.setFinishAmount(dto.getAmountTotal());
        respDto.setFinishCount(dto.getQty());
        respDto.setFinishTotal(dto.getNum());


        dto = this.factoryOrderRejectHistoryFeign.stat(factoryId);
        respDto.setRejectAmount(dto.getAmountTotal());
        respDto.setRejectCount(dto.getQty());
        respDto.setRejectTotal(dto.getNum());
        return respDto;
    }

    @GetMapping("/v2/export")
    public void export(
            @RequestParam(name = "ids", required = true) String factoryOrderIdsStr,
            HttpServletResponse response
    ) throws IOException {
        StopWatch stopWatch = new StopWatch("导出订单");
        List<Long> ids = Arrays.stream(factoryOrderIdsStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ids)) {
            throw new BusinessException("订单id不能为空");
        }
        if (ids.size() > 1000) {
            throw new BusinessException("导出最大数量为1000");
        }

        long factoryId = ISecurityUtils.getFactoryId();
        Long tenantId = ISecurityUtils.getCurrUser().getTenantId();
        stopWatch.start("查询订单");
        List<CompletableFuture<List<FactoryOrderExportDto>>> futures = new ArrayList<>();
        int batchSize = 200;
        for (int i = 0; i < ids.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, ids.size());
            List<Long> subIds = ids.subList(i, endIndex);
            CompletableFuture<List<FactoryOrderExportDto>> future = CompletableFuture.supplyAsync(() -> {
                FactoryOrderExportExcelParam param = new FactoryOrderExportExcelParam();
                param.setFactoryId(factoryId);
                param.setTenantId(tenantId);
                param.setFactoryOrderIds(subIds);
                return this.factoryOrderFeign.generateExportDto(param);
            });
            futures.add(future);
        }
        // 等待所有异步任务完成并合并结果
        List<FactoryOrderExportDto> exportDtoList = new ArrayList<>(ids.size());
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[futures.size()]));
        try {
            // 等待所有任务完成
            allFutures.join();
            // 收集所有结果
            for (CompletableFuture<List<FactoryOrderExportDto>> future : futures) {
                List<FactoryOrderExportDto> batchResult = future.get();
                    if (batchResult != null) {
                        exportDtoList.addAll(batchResult);
                    }

            }
        } catch (Exception e) {
            throw new BusinessException("导出数据获取失败: " + e.getMessage());
        }
        stopWatch.stop();
        if (CollectionUtil.isEmpty(exportDtoList)) {
            throw new BusinessException("无可导出数据");
        }
        // 获取最大生产稿件数量
        int maxProductionManuscripts = 0;
        stopWatch.start("处理生产稿件");
        for (FactoryOrderExportDto dto : exportDtoList) {
            List<String> productionManuscripts = dto.getProductionManuscripts();

            if (productionManuscripts != null) {
                maxProductionManuscripts = Math.max(maxProductionManuscripts, productionManuscripts.size());
                for (int i = 0; i < productionManuscripts.size(); i++) {
                    dto.getProductionManuscriptsMap().put("生产稿件" + (i + 1), productionManuscripts.get(i));
                }
            }
        }
        stopWatch.stop();
        stopWatch.start("处理动态表头");
        // 生成动态表头
        List<List<String>> head = new ArrayList<>();
        Field[] fields = FactoryOrderExportDto.class.getDeclaredFields();
        boolean isSds = TenantCommonConstant.isSdsdiy(tenantId);
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                //是sds工厂的情况，不导出分销商这一列
                ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
                if (isSds && "分销商".equals(annotation.value()[0])) {
                    continue;
                }
                head.add(Collections.singletonList(annotation.value()[0]));
            }
        }
        stopWatch.stop();
        stopWatch.start("生成Excel-准备数据");
        // 添加动态的生产稿件列头
        for (int i = 1; i <= maxProductionManuscripts; i++) {
            head.add(Collections.singletonList("生产稿件" + i));
        }

        // 生成数据
        List<List<Object>> data = new ArrayList<>();
        for (FactoryOrderExportDto dto : exportDtoList) {
            List<Object> rowData = new ArrayList<>();
            for (Field field : fields) {
                if (field.isAnnotationPresent(ExcelProperty.class)) {
                    try {
                        field.setAccessible(true);
                        rowData.add(field.get(dto));
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }

            // 动态填充生产稿件列的数据
            for (int i = 1; i <= maxProductionManuscripts; i++) {
                rowData.add(dto.getProductionManuscriptsMap().get("生产稿件" + i));
            }

            data.add(rowData);
        }
        stopWatch.stop();
        stopWatch.start("生成Excel-写入");
        String fileName = LocalDateTime.now().toLocalDate().toString();
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.reset();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        OutputStream outputStream = response.getOutputStream();
        ImgWriteCellHandler imgWriteCellHandler = new ImgWriteCellHandler();
        ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).head(head)
                .registerWriteHandler(imgWriteCellHandler)
                .build();
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, "工厂订单").build();
        excelWriter.write(data, writeSheet);
        excelWriter.finish();
        stopWatch.stop();

        log.info("订单导出耗时: {}", stopWatch.prettyPrint());
    }

    @ApiOperation("发货")
    @RequestMapping(value = "/delivery", method = RequestMethod.PUT)
    @RequiresPermissions({"order:status:tosend"})
    public ProductFlowStepCheckRespDTO delivery(@RequestBody FactoryOrderDeliveryReqDto dto) {
        Assert.validateTrue(20 < dto.getLogisticsName().length(), "物流渠道不能大于20个字符");
        Assert.validateTrue(100 < dto.getLogisticsNo().length(), "物流单号不能大于100个字符");
        ProductFlowStepCheckRespDTO result = this.factoryOrderFeign.checkFlowStepFinish(dto.getFactoryOrderNos());
        if (result.getRetCode() != 0) {
            return result;
        }
        FactoryOrderDeliveryLabelDto factoryOrderDeliveryLabelDto = this.factoryOrderService.putLogistics(dto, ISecurityUtils.getUser(), ISecurityUtils.getFactoryId());
        result.setDeliveryInfo(factoryOrderDeliveryLabelDto);
        return result;
    }

    @ApiOperation("导出订单二维码")
    @GetMapping(value = "/task/{taskId}/qrCode/batch")
    public void qrCodeBatch(@PathVariable("taskId") Long taskId, @RequestParam(value = "nos") String nos, HttpServletResponse response) {
        List<String> noList = StringUtils.stringToStringList(nos);
        Assert.validateEmpty(noList, "订单号数据异常");
        QrConfig qrConfig = new QrConfig();
        FactoryTaskRespDto one = this.factoryTaskFeign.one(taskId);
        Assert.validateNull(one, "任务id异常！！");
        // 创建一个ZipOutputStream对象
        try (ZipOutputStream zipOut = new ZipOutputStream(new BufferedOutputStream(response.getOutputStream()))) {
            for (String no : noList) {
                BitMatrix matrix = QrCodeUtil.encode(no, qrConfig);
                BufferedImage bufferedImage = MatrixToImageWriter.toBufferedImage(matrix);
                File file = new File(no + ".png");
                ImageIO.write(bufferedImage, "png", file);
                ZipUtils.zipFile(zipOut, file);
                file.delete();
            }
            //设置HttpServletResponse对象
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("订单二维码(" + one.getName() + ")" + DateUtil.format(new Date(), "yyyyMMdd"), "UTF-8") + ".rar");
        } catch (Exception e) {
            throw new BusinessException("导出二维码文件异常！");
        }
    }

    @Resource
    private FileUploadService fileUploadService;


    @ApiOperation("下载定制信息接口")
    @GetMapping(value = "/tasks/{taskId}/downloadCustomInfo")
    public void downloadCustomInfo(@PathVariable("taskId") Long taskId, @RequestParam(value = "downStatus", required = false) String downStatus, HttpServletResponse response) {
        FactoryTaskRespDto one = this.factoryTaskFeign.one(taskId);
        Assert.validateNull(one, "任务id异常！！");
        List<FactoryOrderDownloadCustomInfoResp> customInfos = this.factoryOrderFeign.factoryOrderDownloadCustomInfoByTaskId(taskId, downStatus);
        Assert.validateEmpty(customInfos, "无对应任务的定制信息！");
        try (ZipOutputStream zipOut = new ZipOutputStream(new BufferedOutputStream(response.getOutputStream()))) {
            this.factoryOrderCustomDownloadService.formatCustomInfo(customInfos);
            List<String> fileUrls = customInfos.stream().filter(c -> c.getCustomInfoResp() != null && CollectionUtil.isNotEmpty(c.getFileUrls())).flatMap(c -> c.getFileUrls().stream()).collect(Collectors.toList());
            Map<String, String> urlKeyName = this.fileUploadService.getFileUrlKeyFileNameMap(fileUrls);
            //图片数据到压缩包
            this.factoryOrderCustomDownloadService.customPictureToZip(customInfos, zipOut, urlKeyName);

            //压缩数据
            this.factoryOrderCustomDownloadService.customSummaryToZip(customInfos, zipOut);
            //设置HttpServletResponse对象
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(one.getName() + "任务定制信息", "UTF-8") + ".rar");
            try {
                List<Long> factoryOrderIds = customInfos.stream().map(FactoryOrderDownloadCustomInfoResp::getId).collect(Collectors.toList());
                this.factoryTaskFeign.modifyFactionTaskOrderRelOption(factoryOrderIds, DownTypeEnum.DOWN.getValue(), ISecurityUtils.getCurrUserId());
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            throw new BusinessException("下载定制信息异常！" + e.getMessage());
        }
    }

    @ApiOperation("自定义面单模版列表")
    @GetMapping("printTemplates")
    public List<PrintTemplateRespDto> printTemplates() {
        return this.factoryOrderFeign.printTemplates(ISecurityUtils.getFactoryId());
    }

    @ApiOperation("打印面单所需的信息")
    @PostMapping("printInfos")
    public FactoryOrderPrintInfoRespDto printInfos(@RequestBody FactoryOrderPrintInfoReqDto dto) {
        dto.setTenantId(ISecurityUtils.getCurrUser().getTenantId());
        return this.factoryOrderFeign.printInfos(dto);
    }


}


