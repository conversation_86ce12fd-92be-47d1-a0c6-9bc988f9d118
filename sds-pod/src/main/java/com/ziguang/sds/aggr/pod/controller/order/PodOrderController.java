package com.ziguang.sds.aggr.pod.controller.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ps.dto.order.FbaOrderItemPriceRespDto;
import com.ps.dto.order.PodOrderDTO;
import com.ps.dto.order.RefundAdviseParam;
import com.ps.dto.order.RefundParam;
import com.ps.exception.BusinessException;
import com.ps.ps.feign.OrderCancelFeign;
import com.ps.ps.feign.OrderFeign;
import com.ps.ps.feign.ProgressFeign;
import com.ps.ps.feign.order.*;
import com.ps.ps.service.*;
import com.ps.ps.service.order.OrderFormatService;
import com.ps.support.Assert;
import com.ps.support.redis.LockBatchUtil;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.common.base.enums.SdsPlatformEnum;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.common.dtoconvert.exception.ExceptionConstant;
import com.sdsdiy.logisticsapi.dto.CarriageDeclarationInfoReqDto;
import com.sdsdiy.logisticsapi.dto.CarriageDeclarationInfoResp;
import com.sdsdiy.logisticsdata.dto.base.PutCarriageReqDTO;
import com.sdsdiy.orderapi.dto.progress.ProgressRespDto;
import com.sdsdiy.orderdata.constant.order.OrderLockConstant;
import com.sdsdiy.orderdata.dto.AddressRespDto;
import com.sdsdiy.orderdata.dto.FbaOrderItemSupplyPriceDto;
import com.sdsdiy.orderdata.dto.FbaOrderItemSupplyPriceReqDto;
import com.sdsdiy.orderdata.dto.PodOrderQueryDto;
import com.sdsdiy.orderdata.dto.order.amount.OrderServiceAmountRespDTO;
import com.sdsdiy.orderdata.dto.parcel.OrderDeliveryParcelDetailDTO;
import com.sdsdiy.orderdata.enums.DeliveryTypeEnum;
import com.sdsdiy.orderdata.param.RegenerateCarriageNoParam;
import com.sdsdiy.orderdata.vo.RefreshTrackingNumberByParcelVO;
import com.ziguang.base.dto.*;
import com.ziguang.base.dto.order.MerchantOrderRespDto;
import com.ziguang.base.model.Order;
import com.ziguang.base.model.OrderRemark;
import com.ziguang.base.support.Response;
import com.ziguang.base.support.ResponseCode;
import com.ziguang.base.support.StringUtils;
import com.ziguang.base.support.contant.Platform;
import com.ziguang.base.vo.AfterServiceSubmitVo;
import com.ziguang.base.vo.OrderAddressVo;
import com.ziguang.base.vo.RefundAdviceVo;
import com.ziguang.sds.aggr.pod.myinterface.PreAuthorize;
import com.ziguang.sds.aggr.pod.service.order.PodOrderLogisticsService;
import com.ziguang.sds.aggr.pod.shiro.ISecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Api(tags = "订单管理")
@RestController
@RequestMapping("/orders")
@Slf4j
@RequiredArgsConstructor
public class PodOrderController {
    private final OrderCancelFeign orderCancelFeign;
    private final PodOrderLogisticsService podOrderLogisticsService;
    @Resource
    AfterServiceManage afterServiceManage;
    @Autowired
    private OrderFeign orderFeign;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderCancelService orderCancelService;
    @Resource
    private WarehouseService warehouseService;
    @Resource
    private OrderCarriageFeign orderCarriageFeign;
    @Resource
    private LogisticsService logisticsService;
    @Resource
    private OrderRemarkService orderRemarkService;
    @Resource
    private OrderDeclarationFeign orderDeclarationFeign;
    @Resource
    private OrderParcelFeign orderParcelFeign;
    @Resource
    private OrderFormatService orderFormatService;
    @Resource
    private OrderTestFeign orderTestFeign;
    @Resource
    private ProgressFeign progressFeign;
    @Autowired
    private OrderAmountFeign orderAmountFeign;
    @Autowired
    private OrderAmountService orderAmountService;

    @ApiOperation("解密地址")
    @GetMapping("/{id}/decryptAddress")
    public AddressRespDto decryptAddress(@PathVariable("id") Long orderId) {
        AddressRespDto addressRespDto = null;
        try {
            addressRespDto = this.orderFeign.decryptAddress(orderId);
        } catch (Exception e) {
            //pod端不处理异常
        }
        return addressRespDto;
    }

    @ApiOperation("取消详情")
    @GetMapping("/{id}/cancelDetail")
    public MerchantOrderRespDto cancelDetail(@PathVariable("id") Long orderId) {
        PodOrderQueryDto queryDto = new PodOrderQueryDto();
        queryDto.setOrderIds(Lists.newArrayList(orderId));

        PageResultDto<MerchantOrderRespDto> result = this.orderService.queryPodOrderPage(queryDto);
        Assert.validateTrue(CollUtil.isEmpty(result.getList()), "订单不存在");
        MerchantOrderRespDto merchantOrderRespDto = result.getList().get(0);
        if (DeliveryTypeEnum.JIT.equalsCode(merchantOrderRespDto.getDeliveryType())
                || DeliveryTypeEnum.TEMU_FULLY.equalsCode(merchantOrderRespDto.getDeliveryType())) {
            orderCancelFeign.checkCanCancel(BaseListDto.of(orderId), merchantOrderRespDto.getMerchantStore().getPlatformCode());
        }
        Map<Long, Integer> unShipOrderItemMap = orderCancelFeign.unShipOrderItemQty(orderId);
        List<MerchantOrderRespDto.OrderItem> orderItems = Lists.newArrayList();
        for (MerchantOrderRespDto.OrderItem item : merchantOrderRespDto.getItems()) {
            Integer unshipNum = unShipOrderItemMap.get(item.getId());
            if (NumberUtils.greaterZero(unshipNum)) {
                item.setNum(unshipNum);
                orderItems.add(item);
            }
        }
        merchantOrderRespDto.setItems(orderItems);

        return merchantOrderRespDto;
    }

    @RequestMapping(value = "{no}/warehouse_no")
    @ResponseBody
    public WarehouseDto getwWarehouseNo(@PathVariable("no") Long id) {
        WarehouseDto warehouse = this.warehouseService.findWarehouse(id);
        return warehouse;
    }

    @RequestMapping(value = "/{id}/logistic/parcel/{parcelId}", method = RequestMethod.PUT)
    @ResponseBody
    @RequiresPermissions(value = {"quality:orders:edit:carriageno", "quality:orders:custom:carriageno"}, logical = Logical.OR)
    @ApiOperation("编辑运单号/手动分配运单号")
    public void putCarriage(@PathVariable("id") Long id
            , @PathVariable("parcelId") Long parcelId, @RequestBody PutCarriageReqDTO param) {
        this.podOrderLogisticsService.putCarriage(id, parcelId, param);
    }

    @RequestMapping(value = "/{id}/carriage/parcel/{parcelId}", method = RequestMethod.POST)
    @ResponseBody
    @RequiresPermissions("quality:orders:regenerate:carriageno")
    @ApiOperation("重新生成运单号")
    public void postCarriage(@PathVariable("id") Long id, @PathVariable("parcelId") Long parcelId, @RequestBody RegenerateCarriageNoParam param) {
        this.orderFeign.postCarriage(id, parcelId, ISecurityUtils.getTenantId(), ISecurityUtils.getCurrUserId(), param);
    }

    @RequestMapping(value = "/{id}/after_service", method = RequestMethod.POST)
    @ResponseBody
    public void afterServiceSubmit(@PathVariable("id") Long id, @RequestBody AfterServiceSubmitVo vo) {
        if (!Arrays.asList(1, 2).contains(vo.getType())) {
            throw new BusinessException("type value error!");
        }
        if (vo.getTransactionalCode() == null) {
            throw new BusinessException("transaction_code value error!");
        }
        this.afterServiceManage.adminAfterService(id, vo, ISecurityUtils.getUser());
    }


    @ApiOperation(value = "查询订单备注列表")
    @RequestMapping(value = "/{id}/remarks", method = RequestMethod.GET)
    @ResponseBody
    public List<OrderRemarkDto> remarks(@PathVariable("id") Long id) {
        return this.orderService.remarks(id);
    }

    @GetMapping(value = "/{id}")
    @ApiOperation("pod订单详情")
    public PodOrderDTO get(@PathVariable("id") Long id) {
        Order order = this.orderService.format(id);
        this.orderService.formatIsDecryptAddress(order);
        this.orderAmountService.checkAmountPermission(id, ISecurityUtils.getTenantId());
        PodOrderDTO podOrderDTO = BeanUtil.toBean(order, PodOrderDTO.class);
        this.orderFormatService.formatOrderItemQty(podOrderDTO.getItems());
        this.orderFormatService.formatPodOrderParcelLogisticsList(podOrderDTO);
        this.orderFormatService.formatPodOrderDtoExtraInfo(podOrderDTO);
        this.orderFormatService.formatMerchantStore(podOrderDTO);
        this.orderFormatService.formatPlatformExtendMap(podOrderDTO);
        this.orderFormatService.formatMerchant(ISecurityUtils.getTenantId(),podOrderDTO);
        return podOrderDTO;
    }

    @RequestMapping(value = "/{id}/items/{ids}/refund_advise", method = RequestMethod.GET)
    @ResponseBody
    public RefundAdviceVo refundAdvise(@PathVariable("id") Long id, @PathVariable("ids") String ids) {
        return this.orderCancelService.refundAdviseByOrderItemIds(id, StringUtils.stringToLongList(ids));
//        return this.orderService.refundAdvise(id, StringUtils.stringToLongList(ids));
    }

    @RequestMapping(value = "/{id}/items/refund_advise", method = RequestMethod.POST)
    @ResponseBody
    public RefundAdviceVo refundAdvise(@PathVariable("id") Long id,
                                       @RequestBody RefundAdviseParam param) {
        return this.orderCancelService.refundAdviseByOrderItemIds(id, param);
//        return this.orderService.refundAdvise(id, StringUtils.stringToLongList(ids));
    }

    @RequestMapping(value = "/{id}/items/{ids}/cancel", method = RequestMethod.PUT)
    @ResponseBody
    @RequiresPermissions("order:manager:viewandmanage")
    public void updateStatus(@PathVariable("id") Long id, @PathVariable("ids") String ids, @RequestBody RefundParam refundParam) {
        LockBatchUtil.tryLockWithUnlock(OrderLockConstant.ORDER_ID_Z_SET_LOCK_KEY, TimeUnit.MINUTES.toSeconds(15)
                , id, "订单状态已变更", () -> {
                    orderCancelFeign.checkCanCancel(BaseListDto.of(id), "");
                    Double serviceAmount = refundParam.getServiceAmount();
                    if (serviceAmount == null) {
                        refundParam.setServiceAmount(0D);
                    }
                    Double materialServiceAmount = refundParam.getMaterialServiceAmount();
                    if (materialServiceAmount == null) {
                        refundParam.setMaterialServiceAmount(0D);
                    }
                    log.info("refundParam={}", JSON.toJSONString(refundParam));
                    this.afterServiceManage.adminCancel(ISecurityUtils.getUser(), id, StringUtils.stringToLongList(ids), refundParam);
                });
    }

    @GetMapping(value = "{id}/track_info")
    public Response queryExpressInfo(@PathVariable("id") Long id
            , @ApiParam("是否尾程：1-是") @RequestParam(value = "isTail", defaultValue = "0") Integer isTail) {
        return this.logisticsService.queryExpressInfo(id, null, isTail);
    }

    @ApiOperation("物流追踪信息")
    @GetMapping(value = "{id}/orderParcels/{orderParcelId}/track_info")
    public Response queryExpressInfo(@PathVariable("id") Long id
            , @PathVariable("orderParcelId") Long orderParcelId
            , @ApiParam("是否尾程：1-是") @RequestParam(value = "isTail", defaultValue = "0") Integer isTail) {
        return this.logisticsService.queryExpressInfo(id, orderParcelId, isTail);
    }

    @ApiOperation("修改订单详情")
    @RequestMapping(value = "/{id}/fba_factory_orders", method = RequestMethod.GET)
    @ResponseBody
    @RequiresPermissions("order:manager:edit")
    public FbaOrderUpdateDto getFbaFactoryOrders(@PathVariable("id") Long id) {
        this.orderAmountService.checkAmountPermission(id, ISecurityUtils.getTenantId());
        return this.orderService.getFbaFactoryOrders(id);

    }

    @RequestMapping(value = "/{id}/fba_factory_orders", method = RequestMethod.PUT)
    @ResponseBody
    public void updateFbaFactoryOrders(@PathVariable("id") Long id, @RequestParam(value = "status", required = false) Integer status,
                                       @RequestBody @Valid FbaOrderUpdateDto fbaOrderUpdateDto) {
        Assert.validateNull(status, "订单状态必传");
        this.orderService.updateFbaFactoryOrders(id, ISecurityUtils.getTenantId(), ISecurityUtils.getUser(), status, fbaOrderUpdateDto);
    }

    @RequestMapping(value = "/{id}/label_print/parcel/{parcelId}", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("打印面单")
    public Map<String, String> labelPrint(@PathVariable("id") Long id, @PathVariable("parcelId") Long parcelId) {
        String url = this.orderService.getLabelPrint(id, parcelId);
        Map<String, String> map = new HashMap<>(1);
        map.put("label_url", url);
        return map;
    }

    @GetMapping("/{id}/progress")
    public ProgressRespDto progresslist(@PathVariable("id") Long orderId) {
        return this.orderFeign.progresslist(orderId, SdsPlatformEnum.POD.getCode());
    }

    @ApiOperation(value = "新增订单备注")
    @RequestMapping(value = "/{order_id}/remark", method = RequestMethod.POST)
    @ResponseBody
    public OrderRemark remark(@RequestBody OrderRemarkDto orderRemarkDto, @PathVariable("order_id") Long orderId) {
        String remark = orderRemarkDto.getRemark();
        Integer isRemind = orderRemarkDto.getIsRemind();
        if (StringUtils.isBlank(remark)) {
            throw new BusinessException("备注内容不能为空");
        }
        OrderRemark orderRemark = new OrderRemark();
        orderRemark.setOrderId(orderId);
        orderRemark.setUserId(ISecurityUtils.getCurrUserId());
        orderRemark.setUsername(ISecurityUtils.getCurrUser().getUserName());
        orderRemark.setRemark(remark);
        orderRemark.setPlatform(Platform.ADMIN.getValue());
        orderRemark.setType(OrderRemarkService.TYPE_ORDER);
        orderRemark.setIsRemind(isRemind == null ? 0 : isRemind);
        this.orderRemarkService.save(ISecurityUtils.getUser(), orderRemark);
        return orderRemark;
    }

    @RequestMapping(value = "/{id}/address", method = RequestMethod.POST)
    @ResponseBody
    @RequiresPermissions("order:manager:viewandmanage")
    public void modifyOrderAddress(@PathVariable Long id, @RequestBody OrderAddressVo vo) {
        vo.setId(id);
        String errMsg = "param can`t empty of ";
        Validator.validateNotEmpty(vo.getPostcode(), errMsg + "postcode");
        if (vo.getPostcode().length() > 20) {
            throw new BusinessException("邮政编码过长!");
        }

        if (vo.getCarriageNo() == null) {
            vo.setCarriageNo("");
        }
        Validator.validateNotEmpty(vo.getReceiver(), errMsg + "receiver");
        Assert.validateNull(vo.getCarriageNo(), "旧运单号必传");
        Assert.validateNull(vo.getOrderStatus(), "旧订单状态必传");
        Assert.validateNull(vo.getItemIds(), "旧子单信息必传");

        if (vo.getReceiver().length() > 64) {
            throw new BusinessException("收件人姓名过长!");
        }
        Validator.validateNotEmpty(vo.getMobilePhone(), errMsg + "mobile_phone");
        if (vo.getMobilePhone().length() > 64) {
            throw new BusinessException("联系电话过长!");
        }
        Validator.validateNotNull(vo.getLogisticsId(), errMsg + "logistics_id");
        Validator.validateNotNull(vo.getCountryExpressInfoId(), errMsg + "country_express_info_id");
        Validator.validateNotEmpty(vo.getCountry(), errMsg + "country");
        Validator.validateNotEmpty(vo.getProvince(), errMsg + "province");
        if (vo.getProvince().length() > 50) {
            throw new BusinessException("省过长!");
        }
        Validator.validateNotEmpty(vo.getCity(), errMsg + "city");
        if (vo.getCity().length() > 50) {
            throw new BusinessException("市过长!");
        }
        Validator.validateNotEmpty(vo.getAddressDetail1(), errMsg + "addressDetail1");
        if (vo.getAddressDetail1().length() > 500) {
            throw new BusinessException("详细地址1超过500!");
        }
        if (StringUtils.isNotBlank(vo.getAddressDetail2()) && vo.getAddressDetail2().length() > 500) {
            throw new BusinessException("详细地址2超过500!");
        }
        if (StringUtils.isNotBlank(vo.getAddressDetail3()) && vo.getAddressDetail3().length() > 500) {
            throw new BusinessException("详细地址3超过500!");
        }
        this.orderService.modifyOrderAddress(vo, false);
    }

    @RequestMapping(value = "{id}/after_service", method = RequestMethod.PUT)
    @ResponseBody
    public void openAfterService(@PathVariable("id") Long id) {
        this.orderService.setOrderAfterServiceToMixPeriod(id);
    }

    /**
     * v6.0 shanbin_sun 运营订单列表-售后
     */
    @RequestMapping(value = "/{id}/after_service", method = RequestMethod.GET)
    @ResponseBody
    public AfterServiceShowDto afterServiceShow(@PathVariable("id") Long id) {
        return this.orderService.afterServiceShow(id, Platform.ADMIN.getValue());
    }


    /**
     * v6.0 shanbin_sun
     *
     * @return
     */
    @RequestMapping(value = "/{id}/refundAdvise", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("pod端fba退款建议")
    public RefundAdviceVo refundAdvise(@RequestBody List<RefundAdviseReqDto> refundAdviseReqDtos, @PathVariable Long id) {
        return this.orderCancelService.podFbaAfterServiceAdvise(refundAdviseReqDtos, this.orderService.format(id));
    }

    /**
     * 功能描述: 获取运单失败明细列表
     *
     * @Author: lin_bin
     * @Date: 2020/7/3 11:43
     */
    @ResponseBody
    @RequestMapping(value = "merchants/carriageFailDetailList", method = RequestMethod.GET)
    @PreAuthorize("waybill:fail")
    public CarriageFailDetailListRespDto getCarriageFailDetailList(@RequestParam(value = "code", required = false) String code) {
        CarriageFailDetailListRespDto respDto = new CarriageFailDetailListRespDto();
        List<CarriageFailDetailRespDto> list = this.orderService.getCarriageFailDetailList(code);
        respDto.setList(list);
        return respDto;
    }

    @ResponseBody
    @RequestMapping(value = "merchants/carriageFailDetailList/download", method = RequestMethod.GET)
    public void download(@RequestParam(value = "code", required = false) String code, HttpServletResponse response) throws IOException {
        List<CarriageFailDetailRespDto> list = this.orderService.getCarriageFailDetailList(code);
        List<Map<String, String>> rowList = this.getDownList(list);
        ExcelWriter excelWriter = new ExcelWriter();
        excelWriter.write(rowList, true);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        String fileName = UUID.randomUUID().toString().replaceAll("-", "");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xls");
        excelWriter.flush(response.getOutputStream());
    }


    private List<Map<String, String>> getDownList(List<CarriageFailDetailRespDto> list) {
        List<Map<String, String>> rowList = Lists.newArrayList();
        list.forEach(resp -> {
                    HashMap<String, String> hashMap = Maps.newLinkedHashMap();
                    hashMap.put("订单号", resp.getNo());
                    hashMap.put("包裹", resp.getParcelName());
                    hashMap.put("商家号", resp.getMerchant().getCode());
                    hashMap.put("错误信息", resp.getCarriageNoRecode().getErrorInfo());
                    rowList.add(hashMap);
                }
        );
        return rowList;
    }

    @RequestMapping(value = "/{id}/shipments/parcel/{parcelId}", method = RequestMethod.PUT)
    @ResponseBody
    @ApiOperation("打印面单确认发货")
    public void putShipments(@PathVariable("id") Long id, @PathVariable("parcelId") Long parcelId) {
        try {
            this.orderService.putShipmentsByParcelId(id, parcelId, ISecurityUtils.getCurrUserId());
        } catch (Exception e) {
            log.error("shipments error", e);
            throw new BusinessException(ResponseCode.PUT_SHIPMENT_ERROR, null, ExceptionConstant.trueErrorMsg(e));
        }
    }

    @RequestMapping(value = "{id}/refer", method = RequestMethod.GET)
    @ResponseBody
    public OrderReferDto refer(@PathVariable Long id) {
        return this.orderService.refer(id, null, false);
    }

    @RequestMapping(value = "{id}/parcels/{parcelId}/refer", method = RequestMethod.GET)
    @ResponseBody
    public OrderReferDto refer(@PathVariable Long id, @PathVariable Long parcelId) {
        return this.orderService.refer(id, parcelId, null, false);
    }

    @ApiOperation("修改工厂获取供应价")
    @RequestMapping(value = "getFbaOrderItemSupplyPrice", method = RequestMethod.POST)
    @ResponseBody
    @RequiresPermissions("order:manager:edit")
    public List<FbaOrderItemSupplyPriceDto> getFbaOrderItemSupplyPrice(@RequestBody List<FbaOrderItemSupplyPriceReqDto> dtos) {
        return this.orderService.getFbaOrderItemSupplyPrice(dtos);

    }

    @ApiOperation("刷新运单号")
    @GetMapping("/parcel/{id}/refreshTrackingNumber")
    public RefreshTrackingNumberByParcelVO refreshTrackingNumber(@PathVariable("id") Long parcelId) {
        return this.orderCarriageFeign.findTrackingNumberByParcelId(parcelId);
    }

    @ApiOperation("获取包裹申报信息")
    @GetMapping("/{orderId}/parcel/{parcelId}/declarationInfo")
    public CarriageDeclarationInfoResp declarationInfo(@PathVariable("orderId") Long orderId,
                                                       @PathVariable("parcelId") Long parcelId) {

        return this.orderDeclarationFeign.getParcelDeclarationAfterEdit(orderId, parcelId);
    }

    @ApiOperation("修改申报信息")
    @PutMapping("/updateDeclareInfo")
    public void updateDeclareInfo(@RequestBody CarriageDeclarationInfoReqDto carriageDeclarationInfoReqDto) {
        this.orderParcelFeign.updateDeclareInfo(ISecurityUtils.getCurrUserId(), carriageDeclarationInfoReqDto);
    }

    @ApiOperation("发货包裹明细")
    @GetMapping("/{orderId}/delivery/parcelDetail")
    public OrderDeliveryParcelDetailDTO orderDeliveryParcelDetail(@PathVariable("orderId") Long orderId) {
        return this.orderParcelFeign.orderDeliveryParcelDetail(orderId);
    }

    @ApiOperation("根据客户订单号 获取未结清的补款单")
    @GetMapping("/getOfflinePayRecord")
    List<Long> getOfflinePayRecordByOrderId(@RequestParam("orderNo") String orderNo) {
        return this.orderFeign.listUnPaidOfflinePayRecord(orderNo);
    }

    @PostMapping("/pod/test/orderEndCalDifference/{orderId}")
    public void orderEndCalDifference(@PathVariable Long orderId) {
        orderTestFeign.orderEndCalDifference(orderId);
    }

    @PostMapping("/pod/test/checkAndEndOrder/{orderId}")
    public void checkAndEndOrder(@PathVariable Long orderId) {
        orderTestFeign.checkAndEndOrder(orderId);
    }

    @PostMapping("/pod/createOrderFinishProgress")
    public void createOrderFinishProgress(@RequestBody List<String> orderNos) {
        progressFeign.createOrderFinishProgress(orderNos);
    }

    @GetMapping("{id}/fba_factory_orders/price")
    public List<FbaOrderItemPriceRespDto> fbaFactoryOrdersPrice(@PathVariable("id") Long id) {
        return this.orderService.fbaFactoryOrdersPrice(id);
    }

    @GetMapping("{id}/serviceAmount")
    public OrderServiceAmountRespDTO orderServiceAmountCal(@PathVariable("id") Long orderId) {
        return this.orderAmountFeign.orderServiceAmountCal(orderId);
    }
}
