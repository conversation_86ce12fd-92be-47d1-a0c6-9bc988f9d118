package com.ziguang.sds.aggr.pod.controller.paymant;

import com.ps.exception.BusinessException;
import com.ps.ps.feign.payment.AdminOperateWalletBalanceFeign;
import com.ps.ps.feign.payment.TenantDistributionWalletFeign;
import com.ps.ps.feign.user.TenantSysUserFeign;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.param.distribution.AdminOperateWalletBalanceParam;
import com.sdsdiy.paymentapi.param.distribution.TenantDisWalletRechargeParam;
import com.ziguang.sds.aggr.pod.shiro.ISecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/pod/tenantDisWallet")
@RequiredArgsConstructor
@Api(value = "POD-租户授信钱包")
public class PodTenantDisWalletController {

    private final TenantDistributionWalletFeign tenantDistributionWalletFeign;
    private final AdminOperateWalletBalanceFeign adminOperateWalletBalanceFeign;
    private final TenantSysUserFeign tenantSysUserFeign;

    @ApiOperation(value = "钱包-后台充值", notes = "")
    @PostMapping("/adminRecharge")
    public void walletAdminRecharge(@RequestBody AdminOperateWalletBalanceParam param) {
        Long currentTenantId = ISecurityUtils.getTenantId();
        if (!currentTenantId.equals(param.getSupTenantId())) {
            throw new BusinessException("当前租户不是分销租户");
        }
        if (null == param.getBalance() && null == param.getBonus()) {
            throw new BusinessException("余额和赠送金至少填写一项");
        }
        this.tenantSysUserFeign.checkPasswd(currentTenantId, param.getPassword());
        param.setOperateUserId(ISecurityUtils.getCurrUserId());

        adminOperateWalletBalanceFeign.tenantDisRecharge(param);
    }

    @PostMapping("/adminWithdraw")
    @ApiOperation(value = "钱包-后台提现", notes = "")
    public void walletAdminWithdraw(@RequestBody AdminOperateWalletBalanceParam param) {
        Long currentTenantId = ISecurityUtils.getTenantId();
        if (!currentTenantId.equals(param.getSupTenantId())) {
            throw new BusinessException("当前租户不是分销租户");
        }
        if (null == param.getBalance() && null == param.getBonus()) {
            throw new BusinessException("余额和赠送金至少填写一项");
        }
        param.setOperateUserId(ISecurityUtils.getCurrUserId());
        this.tenantSysUserFeign.checkPasswd(currentTenantId, param.getPassword());
        adminOperateWalletBalanceFeign.tenantDisWithdraw(param);
    }

    @PostMapping("/recharge")
    @ApiOperation(value = "钱包-充值", notes = "")
    public PaymentDto walletRecharge(@RequestBody TenantDisWalletRechargeParam param) {
        param.setOperateUserId(ISecurityUtils.getCurrUserId());
        return tenantDistributionWalletFeign.recharge(param);
    }
}
