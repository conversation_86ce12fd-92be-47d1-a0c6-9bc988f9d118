package com.ziguang.sds.aggr.pod.controller.paymant;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ps.ps.feign.payment.MerchantBillFeign;
import com.ps.ps.feign.stat.DataExportRecordFeign;
import com.ps.tool.EasyExcelUtil;
import com.sdsdiy.common.base.constant.CommonConstant;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.BillMonthlyAmountOfMerchantVo;
import com.sdsdiy.paymentapi.dto.MerchantBillVo;
import com.sdsdiy.paymentapi.dto.TenantTotalBillMonthlyAmountDto;
import com.sdsdiy.paymentapi.param.bill.BillQueryParam;
import com.sdsdiy.statdata.constant.DataExportRecordTypeEnum;
import com.sdsdiy.statdata.dto.bill.CrossTenantSupplyMonthBillDTO;
import com.sdsdiy.statdata.dto.bill.TenantProductDistributionMonthBillDTO;
import com.sdsdiy.statdata.dto.export.DataExportRecordCreateDTO;
import com.ziguang.sds.aggr.pod.feign.stat.TenantDisBalanceStatFeign;
import com.ziguang.sds.aggr.pod.shiro.ISecurityUtils;
import com.ziguang.sds.aggr.pod.shiro.ShiroUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/pod/merchantBill")
@Slf4j
@RequiredArgsConstructor
@Api("POD-全部账单")
public class PodBillController {

    private final TenantDisBalanceStatFeign tenantDisBalanceStatFeign;
    private final MerchantBillFeign merchantBillFeign;
    private final DataExportRecordFeign dataExportRecordFeign;

    @PostMapping("/queryTenantToSaasBill")
    @ApiOperation(value = "POD saas收支明细，租户和saas间的交易", notes = "当前租户和saas间的交易")
    public PageResultDto<MerchantBillVo> queryTenantToSaasBill(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());
        return merchantBillFeign.billQueryPage(queryParam);
    }


    @PostMapping("/merchantBill/queryMonthlyAmount")
    @ApiOperation(value = "商户账单-本月金额数据", notes = "查看商户的交易，relatedMerchantId必传")
    public BillMonthlyAmountOfMerchantVo merchantBillQueryMonthlyAmount(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());
        return merchantBillFeign.monthlyAmountStatForMerchant(queryParam);
    }

    @PostMapping("/merchantBill/query")
    @ApiOperation(value = "POD-商户账单", notes = "当前商户的交易，relatedMerchantId必传")
    public PageResultDto<MerchantBillVo> merchantBillQuery(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());
        return merchantBillFeign.billQueryPage(queryParam);
    }

    @GetMapping("/merchantBill/export")
    @ApiOperation(value = "POD-商户账单", notes = "当前商户的交易，relatedMerchantId必传")
    public void merchantBillExport(
        BillQueryParam queryParam,
        HttpServletResponse response
    ) {
        log.info("导出POD商户账单 param={}", JSON.toJSONString(queryParam));
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());

        // 1. 查询月度汇总金额数据
        BillMonthlyAmountOfMerchantVo monthlyAmount = merchantBillFeign.monthlyAmountStatForMerchant(queryParam);
        List<MerchantBillVo> vos = merchantBillFeign.billQueryList(queryParam);

        // 3. 选择导出模板
        BillExcelTemplate template = BillExcelTemplate.MERCHANT_BILL_FOR_TENANT_OR_SAAS;
        String fileName = template.getFileName();
        String templatePath = template.getTemplate();

        // 4. 执行导出
        try {
            EasyExcelUtil.exportExcel(fileName, templatePath, monthlyAmount, vos, response);
        } catch (Exception e) {
            log.error("POD商户账单导出异常", e);
            throw new RuntimeException("导出失败");
        }
    }

    @PostMapping("/queryTenantToSaasBill/export")
    @ApiOperation(value = "POD saas收支明细-导出", notes = "当前租户和saas间的交易")
    public void queryTenantToSaasBillExport(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());
        ShiroUser currUser = ISecurityUtils.getCurrUser();
        StringBuilder builder = new StringBuilder();
        builder.append("账单月份：").append(DateUtil.formatDate(new Date(queryParam.getBeginCreateTime())), 0, 7);
        builder.append(CommonConstant.NEWLINE)
            .append("时间：")
            .append(DateUtil.formatDate(new Date(queryParam.getBeginCreateTime())))
            .append("~")
            .append(DateUtil.formatDate(new Date(queryParam.getEndCreateTime())));
        builder.append(CommonConstant.NEWLINE).append("搜索：全部");
        builder.append(CommonConstant.NEWLINE).append("用途：全部");
        builder.append(CommonConstant.NEWLINE).append("操作对象：全部");
        builder.append(CommonConstant.NEWLINE).append("操作对象商户号：全部");
        builder.append(CommonConstant.NEWLINE).append("支付方式：全部");
        builder.append(CommonConstant.NEWLINE).append("变动类型：全部");

        DataExportRecordCreateDTO createDTO = new DataExportRecordCreateDTO();
        createDTO.setTenantId(ISecurityUtils.getTenantId())
            .setUsername(currUser.getUserName())
            .setUserId(currUser.getId())
            .setExportType(DataExportRecordTypeEnum.POD_TENANT_NORMAL_BILL)
            .setExportCondition(JSON.toJSONString(queryParam))
            .setConditionDesc(builder.toString());
        Long id = this.dataExportRecordFeign.createAndSendMsg(createDTO);
    }

    @PostMapping("/queryTenantDisBill/currentIsSupTenant")
    @ApiOperation(value = "分销账单", notes = "当前租户是供应商,relatedSupTenantId必传")
    public PageResultDto<MerchantBillVo> currentIsSupTenantQueryTenantDisBill(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());
        return this.merchantBillFeign.billQueryPage(queryParam);
    }

    @PostMapping("/queryTenantDisBill/currentIsSupTenant/export")
    @ApiOperation(value = "分销账单-导出", notes = "当前租户是供应商,relatedSupTenantId必传")
    public void currentIsSupTenantQueryTenantDisBillExport(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());
        ShiroUser currUser = ISecurityUtils.getCurrUser();
        StringBuilder builder = new StringBuilder();
        builder.append("账单月份：")
            .append(DateUtil.formatDate(new Date(queryParam.getBeginCreateTime())))
            .append("~")
            .append(DateUtil.formatDate(new Date(queryParam.getEndCreateTime())));
        if (StrUtil.isNotBlank(queryParam.getKeyword())) {
            builder.append(CommonConstant.NEWLINE).append("订单号：").append(queryParam.getKeyword());
        }
        if (StrUtil.isNotBlank(queryParam.getPurposeType())) {
            builder.append(CommonConstant.NEWLINE).append("用途：")
                .append(PurposeType.findByCode(queryParam.getPurposeType(), true).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getAmountChangeRole())) {
            builder.append(CommonConstant.NEWLINE).append("账单对象类型：")
                .append(PaymentRoleEnum.getByCodeOrException(queryParam.getAmountChangeRole()).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getPaymentMethod())) {
            builder.append(CommonConstant.NEWLINE).append("支付方式：")
                .append(PaymentMethodEnum.findByCode(queryParam.getPaymentMethod(), true).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getAmountChangeTypeOfSaas())) {
            builder.append(CommonConstant.NEWLINE).append("变动类型：")
                .append(AmountChangeType.findByCode(queryParam.getAmountChangeTypeOfSupTenant(), true).getDesc());
        }
        DataExportRecordCreateDTO createDTO = new DataExportRecordCreateDTO();
        createDTO.setTenantId(ISecurityUtils.getTenantId())
            .setUsername(currUser.getUserName())
            .setUserId(currUser.getId())
            .setExportType(DataExportRecordTypeEnum.POD_TENANT_DIS_BILL_CURRENT_SUP)
            .setExportCondition(JSON.toJSONString(queryParam))
            .setConditionDesc(builder.toString());
        Long id = this.dataExportRecordFeign.createAndSendMsg(createDTO);
    }

    @GetMapping("/queryTenantDisBill/currentIsSupTenant/monthStat")
    @ApiOperation(value = "分销账单-统计")
    public TenantProductDistributionMonthBillDTO currentIsSupTenantQueryTenantDisBillMothStat() {
        return this.tenantDisBalanceStatFeign.supTenantDisBillMothStat(null);
    }

    @PostMapping("/queryTenantDisBill/currentIsDisTenant")
    @ApiOperation(value = "跨租户供应账单", notes = "当前租户是分销商，relatedDisTenantId必传")
    public PageResultDto<MerchantBillVo> currentIsDisTenantQueryTenantDisBill(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());
        return this.merchantBillFeign.billQueryPage(queryParam);
    }

    @PostMapping("/queryTenantDisBill/currentIsDisTenant/export")
    @ApiOperation(value = "跨租户供应账单-导出", notes = "当前租户是分销商，relatedDisTenantId必传")
    public void currentIsDisTenantQueryTenantDisBillExport(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());
        ShiroUser currUser = ISecurityUtils.getCurrUser();
        StringBuilder builder = new StringBuilder();
        builder.append("账单月份：")
            .append(DateUtil.formatDate(new Date(queryParam.getBeginCreateTime())))
            .append("~")
            .append(DateUtil.formatDate(new Date(queryParam.getEndCreateTime())));
        if (StrUtil.isNotBlank(queryParam.getKeyword())) {
            builder.append(CommonConstant.NEWLINE).append("订单号：").append(queryParam.getKeyword());
        }
        if (StrUtil.isNotBlank(queryParam.getPurposeType())) {
            builder.append(CommonConstant.NEWLINE).append("用途：")
                .append(PurposeType.findByCode(queryParam.getPurposeType(), true).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getAmountChangeRole())) {
            builder.append(CommonConstant.NEWLINE).append("账单对象类型：")
                .append(PaymentRoleEnum.getByCodeOrException(queryParam.getAmountChangeRole()).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getPaymentMethod())) {
            builder.append(CommonConstant.NEWLINE).append("支付方式：")
                .append(PaymentMethodEnum.findByCode(queryParam.getPaymentMethod(), true).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getAmountChangeTypeOfSaas())) {
            builder.append(CommonConstant.NEWLINE).append("变动类型：")
                .append(AmountChangeType.findByCode(queryParam.getAmountChangeTypeOfSupTenant(), true).getDesc());
        }
        DataExportRecordCreateDTO createDTO = new DataExportRecordCreateDTO();
        createDTO.setTenantId(ISecurityUtils.getTenantId())
            .setUsername(currUser.getUserName())
            .setUserId(currUser.getId())
            .setExportType(DataExportRecordTypeEnum.POD_TENANT_DIS_BILL_CURRENT_DIS)
            .setExportCondition(JSON.toJSONString(queryParam))
            .setConditionDesc(builder.toString());
        Long id = this.dataExportRecordFeign.createAndSendMsg(createDTO);
    }

    @GetMapping("/queryTenantDisBill/currentIsDisTenant/monthStat")
    @ApiOperation(value = "跨租户供应账单-统计")
    public CrossTenantSupplyMonthBillDTO currentIsDisTenantQueryTenantDisBillMothStat() {
        return this.tenantDisBalanceStatFeign.disTenantDisBillMothStat(null);
    }


    @PostMapping("/tenantBill/query")
    @ApiOperation(value = "POD-平台账单-分页查询，租户和saas/商户间交易", notes = "relatedTenantId必传=当前租户ID")
    public PageResultDto<MerchantBillVo> tenantBillQuery(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());
        return merchantBillFeign.billQueryPage(queryParam);
    }

    @PostMapping("/tenantBill/amountStat")
    @ApiOperation(value = "POD-平台账单-金额统计，租户和saas/商户间交易", notes = "relatedTenantId必传=当前租户ID")
    public TenantTotalBillMonthlyAmountDto tenantBillAmountStat(@RequestBody BillQueryParam queryParam) {
        return null;
    }

    @PostMapping("/tenantBill/export")
    @ApiOperation(value = "POD-平台账单-导出，租户和saas/商户间交易", notes = "relatedTenantId必传=当前租户ID")
    public PageResultDto<MerchantBillVo> tenantBillExport(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.TENANT.getCode());
        queryParam.setCurrentViewRoleId(ISecurityUtils.getTenantId());
        return merchantBillFeign.billQueryPage(queryParam);
    }
}
