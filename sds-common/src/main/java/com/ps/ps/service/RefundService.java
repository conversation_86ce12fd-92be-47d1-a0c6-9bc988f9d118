package com.ps.ps.service;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.ps.base.entity.SearchBean;
import com.ps.base.service.TKBaseService;
import com.ps.dto.OrderRefundParam;
import com.ps.exception.BusinessException;
import com.ps.ps.alipay.AliPayService;
import com.ps.ps.dao.*;
import com.ps.ps.feign.OrderPaymentTypeFeign;
import com.ps.ps.feign.order.OrderAmountFeign;
import com.ps.ps.feign.payment.MerchantBillFeign;
import com.ps.ps.feign.payment.RefundFeign;
import com.ps.ps.service.payment.MerchantUserAccountService;
import com.ps.support.Encodes;
import com.ps.support.utils.ConvertUtil;
import com.ps.support.utils.MathUtils;
import com.ps.support.utils.OrderCodeUtil;
import com.ps.tool.Digest;
import com.ps.tool.cbt.common.util.StringUtil;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.orderdata.dto.order.update.OrderDecreaseTenantFreeGoldDTO;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentapi.dto.RefundDto;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentapi.param.RefundsCreateParam;
import com.sdsdiy.paymentapi.param.bill.MerchantBillCreateParam;
import com.sdsdiy.paymentapi.param.bill.MerchantBillsCreateParam;
import com.sdsdiy.userapi.constant.EnumNotificationTitle;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.dto.UserAccountBalanceResp;
import com.ziguang.base.constant.AdminConfigsConst;
import com.ziguang.base.dto.PasswordDto;
import com.ziguang.base.dto.RefundRecordDto;
import com.ziguang.base.model.*;
import com.ziguang.base.support.DateUtil;
import com.ziguang.base.support.DoubleUtils;
import com.ziguang.base.support.StringUtils;
import com.ziguang.base.support.contant.*;
import com.ziguang.base.vo.RefundVo;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2019/3/4 0004
 */
@Service
public class RefundService extends TKBaseService<RefundRecordDao, RefundRecord> {
    protected org.slf4j.Logger logger = LoggerFactory.getLogger(this.getClass());
    private static final int REFUND_SUCCESS = 2;
    @Autowired
    MerchantUserAccountService merchantUserAccountService;
    @Autowired
    private RefundRecordDao refundRecordDao;
    @Autowired
    private OrderDao orderDao;

    @Resource
    OrderAmountFeign orderAmountFeign;
    @Autowired
    private ApplicationEventPublisher publisher;
    @Autowired
    private RefundRecordItemDao refundRecordItemDao;

    @Resource
    OrderPaymentTypeFeign orderPaymentTypeFeign;
    @Resource
    RefundFeign refundFeign;
    @Autowired
    @Lazy
    private ProductService productService;
    @Autowired
    private AdminConfigService adminConfigService;

    @Autowired
    private MerchantDao merchantDao;
    @Autowired
    private FactoryOrderService factoryOrderService;
    @Autowired
    private OrderItemDao orderItemDao;
    @Autowired
    @Lazy
    private MerchantService merchantService;
    @Autowired
    @Lazy
    private OrderService orderService;
    @Autowired
    @Lazy
    private PaymentService paymentService;
    @Autowired
    private MerchantBillService merchantBillService;
    @Autowired
    private MerchantBillFeign merchantBillFeign;
    @Autowired
    private WebPushService webPushService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private AliPayService aliPayService;
    @Autowired
    private ProductDao productDao;
    @Resource
    private AfterServiceAmountRefundService afterServiceAmountRefundService;

    public static int getRefundSuccess() {
        return REFUND_SUCCESS;
    }

//    @Transactional(rollbackFor = Exception.class)
//    public void addRefundRecord(Long userId, String linkman, String phone, String remark,
//                                double price,
//                                double carriagePrice,
//                                double servicePrice,
//                                double refundPrice,
//                                Order order, RefundType type) {
//        addRefundRecord(userId, linkman, phone, remark, price, carriagePrice,servicePrice, refundPrice, order, type, false, 0);
//    }


//    @Transactional(rollbackFor = Exception.class)
//    public void addRefundRecord(Long userId, String linkman, String phone, String remark,
//                                double price,
//                                double carriagePrice,
//                                double servicePrice,
//                                double refundPrice,
//                                Order order, RefundType type, boolean operationRefund, double charge) {
//        addRefundRecord(userId, linkman, phone, remark, price, carriagePrice,servicePrice, refundPrice, order, type, operationRefund, charge, null,0L);
//    }


    private static void checkAmountLimit(boolean refundToTenant, double carriagePrice, double servicePrice, double materialServicePrice, Order order) {
        boolean fbaAndRefundToTenant = com.sdsdiy.orderapi.constant.OrderOriginType.isFba(order.getOriginType()) && refundToTenant;

        if (MathUtils.priceEqual(carriagePrice, order.getCarriageAmount()) && order.getCarriageAmount() < carriagePrice) {
            if(fbaAndRefundToTenant){
                throw new BusinessException("租户赔付商户：退款的物流金额不能大于订单本身的物流金额");
            }else{
                throw new BusinessException("退款的物流金额不能大于订单本身的物流金额");
            }
        }
        double leftServicePrice = DoubleUtils.sub(order.getServiceAmount(), order.getRefundServiceAmount());
        if (leftServicePrice < servicePrice) {
            if(fbaAndRefundToTenant){
                throw new BusinessException("租户赔付商户：退款的服务费金额不能大于订单剩余的服务费金额");
            }else{
                throw new BusinessException("退款的服务费金额不能大于订单剩余的服务费金额");
            }
        }
        double leftMaterialServicePrice = DoubleUtils.sub(order.getMaterialServiceAmount(), order.getRefundMaterialServiceAmount());
        if (leftMaterialServicePrice < materialServicePrice) {
            if(fbaAndRefundToTenant){
                throw new BusinessException("租户赔付商户：退款的素材服务费金额不能大于订单剩余的素材服务费金额");
            }else{
                throw new BusinessException("退款的素材服务费金额不能大于订单剩余的素材服务费金额");
            }
        }
    }

    public void addRefundRecord(OrderRefundParam orderRefundParam,Long userId, String linkman, String phone, String remark,
                                double price,
                                double carriagePrice,
                                double servicePrice,
                                double materialServicePrice,
                                double refundPrice,
                                Order order, RefundType type, boolean operationRefund, double charge, Long afterServiceAuditId,
                                Long adminUserId) {
        addRefundRecordFinal(orderRefundParam,userId, linkman, phone, remark, price, carriagePrice,servicePrice, materialServicePrice,refundPrice,
                order, type, operationRefund, charge, afterServiceAuditId, null,adminUserId);
    }

    public static void main(String[] args) {
        boolean b = NumberUtil.equals(new BigDecimal("5.02"), new BigDecimal("5.01"));
        System.out.println(b);

    }

    /**
     * 添加退款订单
     *
     * @param linkman             联系人
     * @param phone               联系手机
     * @param remark              备注
     * @param productPrice               产品金额
     * @param carriagePrice       运费
     * @param refundPrice         退款金额
     * @param order               母订单 注:items请放入指定退款的子单
     * @param type                RefundType
     * @param operationRefund     是否记录并直接退款 为true则要尽量保证此函数后无异常
     * @param afterServiceAuditId
     * @param charge 手续费
     * @param dutyAffiliation 责任归属
     */
    public void addRefundRecordFinal(OrderRefundParam refundParam, Long userId, String linkman, String phone, String remark,
                                     Double productPrice,
                                     double carriagePrice,
                                     double servicePrice,
                                     double materialServicePrice,
                                     double refundPrice,
                                     Order order, RefundType type, boolean operationRefund, double charge, Long afterServiceAuditId, Integer dutyAffiliation,
                                     Long adminUserId) {
        refundPrice = DoubleUtils.scale(refundPrice);
        carriagePrice = DoubleUtils.scale(carriagePrice);
        servicePrice = DoubleUtils.scale(servicePrice);
        materialServicePrice = DoubleUtils.scale(materialServicePrice);
        productPrice = DoubleUtils.scale(productPrice);
        Double productUsedGiven = 0d;
        Double productAmount = 0D;
        for (OrderItem orderItem : order.getItems()) {
            productUsedGiven = DoubleUtils.add(productUsedGiven, orderItem.getUsedFreeGold());
            productAmount = DoubleUtils.add(productAmount, orderItem.getAmount());
        }
        checkAmountLimit(refundParam.isRefundToTenant(), carriagePrice, servicePrice, materialServicePrice, order);
        if (!MathUtils.priceEqual(productPrice, productAmount)) {
            Double temp = NumberUtil.mul(productPrice, productUsedGiven);
            productUsedGiven = NumberUtil.div(temp, productAmount,4);
        }
        RefundRecord record = new RefundRecord();
        record.setPaymentAccount(refundParam.getOrderAmountRespDTO().getPaymentMethod());
        record.setPhone(phone);
        record.setUserId(userId);
        record.setRemark(remark);
        record.setNo(order.getNo());
        record.setMerchantNo(order.getMerchant().getCode());
        record.setMerchantId(order.getMerchantId());
        record.setPaymentOutNo("");
        record.setPaymentMethod(refundParam.getOrderAmountRespDTO().getPaymentMethod());
        record.setLinkman(linkman);
        record.setType(type.getType());
        record.setAfterServiceAuditId(afterServiceAuditId);
        record.setDutyAffiliation(dutyAffiliation);
        record.setTotalPrice(productPrice);
        record.setCarriagePrice(carriagePrice);
        record.setServicePrice(servicePrice);
        record.setMaterialServicePrice(materialServicePrice);
        //record.setTenantCarriagePrice(tenantCarriagePrice);


        Double leftAmount = DoubleUtils.sub(order.getTotalAmount(),order.getRefundAmount());
        if (leftAmount < refundPrice) {
            throw new BusinessException("退款金额不能大于订单的总金额");
        }
        Double balance = 0d;
        Double freeGold = 0d;
        Double leftFreeGold = DoubleUtils.sub(order.getUsedFreeGold(), order.getRefundFreeGold());
        //要回退的余额应该是总金额减去剩余的赠送金
        Double leftBalance = DoubleUtils.sub(leftAmount, leftFreeGold);
        logger.info("calculate left leftFreeGold : " + leftFreeGold);
        if (NumberUtil.equals(NumberUtil.toBigDecimal(leftAmount), NumberUtil.toBigDecimal(refundPrice))) {
            freeGold = leftFreeGold;
            balance = leftBalance;
        } else {
            freeGold = productUsedGiven;
            logger.info("calculate else  freeGold: " + freeGold);
            if (freeGold > leftFreeGold) {
                freeGold = leftFreeGold;
                logger.info("calculate else  leftFreeGold: " + leftFreeGold);
            }
            balance = DoubleUtils.sub(refundPrice, freeGold);
            logger.info("calculate else  balance: " + balance);
        }

        record.setBalance(balance);
        record.setFreeGold(freeGold);
        this.decoratorTenantRefund(refundParam,record,order);

        //统计退款
        List<RefundRecordItem> refundItems = new ArrayList<>();
        List<Long> orderIds = new ArrayList<>();
        for (OrderItem item : order.getItems()) {
            RefundRecordItem refund = new RefundRecordItem();
            refund.setOrderItemId(item.getId());
            orderIds.add(item.getId());
            refundItems.add(refund);
        }
        record.setRefundPrice(refundPrice);


        refundRecordDao.insertSelective(record);
        Long recordId = record.getId();

        //设置ID
        for (RefundRecordItem item : refundItems) {
            item.setRefundRecordId(recordId);
            refundRecordItemDao.insertSelective(item);
        }

        //直接退款
        if (operationRefund) {
            double currentCarriageMoney = DoubleUtils.scale(order.getCurrentCarriageAmount()-record.getCarriagePrice()-charge);
            if(currentCarriageMoney < 0){
                throw new BusinessException("物流金额不足!");
            }
            orderDao.updateCurrentCarriage(order.getId(), currentCarriageMoney, charge);
            refund(refundParam,recordId, order.getId(),null, remark, null, true, true,adminUserId);
        }
    }

    public void decoratorTenantRefund(OrderRefundParam refundParam,RefundRecord refundRecord,Order order){
        Double tenantRefundProductMoney = null != refundParam.getTenantRefundProductMoney() ? refundParam.getTenantRefundProductMoney() : 0D;
        Double tenantRefundServiceMoney = null != refundParam.getTenantRefundServiceMoney() ? refundParam.getTenantRefundServiceMoney() : 0D;
        Double tenantRefundCarriageMoney = null != refundParam.getTenantRefundCarriageMoney() ? refundParam.getTenantRefundCarriageMoney() : 0D;
        Double tenantRefundMaterialServiceMoney = null != refundParam.getTenantRefundMaterialServiceMoney() ? refundParam.getTenantRefundMaterialServiceMoney() : 0D;
        Double freeGold = null != refundParam.getTenantRefundProductFreeGold() ? refundParam.getTenantRefundProductFreeGold() : 0D;

        refundRecord.setTenantBalance(0d);
        refundRecord.setTenantFreeGold(0d);
        if (!refundParam.isRefundToTenant()) {
            //租户责任不赔付，所以不更新orderamount的tenantfreegold
            if (!refundParam.isTenantDuty() && refundParam.isUpdateTenantAmount()) {
                refundParam.setTenantFreeGoldAmount(refundParam.getTenantRefundProductFreeGold());
            } else {
                refundParam.setTenantBalanceAmount(0d);
                refundParam.setTenantFreeGoldAmount(0d);
            }
            return;
        }
        //退款总金额
        Double refundPrice = DoubleUtils.add(tenantRefundProductMoney, tenantRefundCarriageMoney, tenantRefundServiceMoney, tenantRefundMaterialServiceMoney);

        Double balance = 0d;

        Double leftFreeGold = refundParam.getOrderAmountRespDTO().getTenantUsedFreeGold();
        logger.info("calculate else  freeGold: " + freeGold);
        if (freeGold > leftFreeGold) {
            freeGold = leftFreeGold;
            logger.info("calculate else  leftFreeGold: " + leftFreeGold);
        }
        balance = DoubleUtils.sub(refundPrice, freeGold);
        logger.info("calculate else  balance: " + balance);

        refundParam.setTenantBalanceAmount(balance);
        refundParam.setTenantFreeGoldAmount(freeGold);
        //退租户的钱
        refundRecord.setTenantBalance(balance);
        refundRecord.setTenantFreeGold(freeGold);
        refundRecord.setTenantProductPrice(tenantRefundProductMoney);
        refundRecord.setTenantServicePrice(tenantRefundServiceMoney);
        refundRecord.setTenantCarriagePrice(tenantRefundCarriageMoney);
        refundRecord.setTenantMaterialServicePrice(tenantRefundMaterialServiceMoney);
        refundRecord.setTenantRefundPrice(refundPrice);
    }

    private double getTotalLeftMoney(Long orderId){
        List<Payment> payments = paymentService.getPaymentsByOrderId(orderId);
        double totalLeftMoney = 0D;
        for(Payment one:payments){
            totalLeftMoney += one.getTotalAmount()-one.getRefundTotalAmount();
        }
        return DoubleUtils.scale(totalLeftMoney);
    }

    /**
     * @param id
     * @param password
     * @param remark
     * @param voucherImgUrls
     * @param alipayToAccount 直接退款到账户
     * @param nopassword      不要密码
     */
    public void refund(OrderRefundParam orderRefundParam,Long id,Long orderId, String password, String remark,
                       List<String> voucherImgUrls, boolean alipayToAccount, boolean nopassword,Long adminUserId) {
        RefundRecord refundRecord = this.getById(id);
        if (!nopassword) {
            merchantService.checkPassword(password, AdminConfigsConst.OPERATION_PASSWORD);
        }
        if (refundRecord.getStatus() == REFUND_SUCCESS) {
            throw new BusinessException("已退款成功");
        }
        orderDao.updateRefundStatusByNo(REFUND_SUCCESS, refundRecord.getNo());
        orderDao.addRefundAmount(refundRecord.getRefundPrice(), refundRecord.getTotalPrice(), refundRecord.getCarriagePrice(),refundRecord.getServicePrice(), refundRecord.getMaterialServicePrice(),refundRecord.getFreeGold(), refundRecord.getBalance(), refundRecord.getNo());
        if(NumberUtils.greaterZero(orderRefundParam.getTenantFreeGoldAmount())){
            orderAmountFeign.decreaseTenantUsedFreeGold(OrderDecreaseTenantFreeGoldDTO.builder().orderId(orderId).decreaseTenantFreeGold(orderRefundParam.getTenantFreeGoldAmount()).build());
        }
        RefundRecord record = new RefundRecord();
        record.setId(id);
        record.setVoucherImgUrls(JSONUtil.toJsonStr(voucherImgUrls));
        record.setRemark(remark);
        record.setVerifyDate(System.currentTimeMillis());
        record.setUpdateTime(refundRecord.getUpdateTime());
        factoryOrderService.updateAfeteServiceTimeByOrderNo(refundRecord.getNo());
        record.setStatus(REFUND_SUCCESS);
        record.setDisposeTime(new Date());
        refundRecordDao.updateByPrimaryKeySelective(record);
        //更新子单退单状态
        List<RefundRecordItem> refundRecordItems = refundRecordItemDao.getByRecordId(id);
        for (RefundRecordItem item : refundRecordItems) {
            OrderItem orderItem = new OrderItem();
            orderItem.setId(item.getOrderItemId());
            orderItem.setRefundStatus(2);
            orderItemDao.update(orderItem);
        }
        toAccount(orderRefundParam,orderId,refundRecord, refundRecordItems, alipayToAccount,adminUserId);
    }

    private void toAccount(OrderRefundParam orderRefundParam,Long orderId,RefundRecord refundRecord, List<RefundRecordItem> refundRecordItems, boolean alipayToAccount,Long adminUserId){
        webPushService.pushMsgToWebByMerchantId(MsgModule.MERCHANT_PROFILE, refundRecord.getMerchantId());
        UserAccountBalanceResp userAccountBalanceResp =merchantUserAccountService.getUserAccountBalance(refundRecord.getMerchantId(),orderId);
        orderRefundParam.setUserId(userAccountBalanceResp.getId());
        //  this.refundToAccount(orderRefundParam, userAccountBalanceResp, refundRecord, refundRecordItems);
        afterServiceAmountRefundService.refundToAccount(orderRefundParam, refundRecord, refundRecordItems, userAccountBalanceResp);
    }

    private RefundParam generateRefundDtoForMerchantPay(OrderRefundParam orderRefundParam, Double balance,Double freeGold,UserAccountBalanceResp userAccountBalanceResp) {
        RefundParam refundParam = new RefundParam();
//        PaymentParam paymentParam = new PaymentParam();
        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        //租户一定是通过余额付给商户
        refundParam.setBalance(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(freeGold, BigDecimal.ROUND_CEILING));
        refundParam.setPayMethod(orderRefundParam.getPaymentMethod());

        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        refundParam.setSubject("退款");
        Double totalAmount = com.sdsdiy.common.base.helper.DoubleUtils.add(balance,freeGold);

        refundParam.setTotalAmount(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(totalAmount, BigDecimal.ROUND_CEILING));
        refundParam.setTargetTenantId(orderRefundParam.getMerchant().getTenantId());
        refundParam.setTargetMerchantId(orderRefundParam.getMerchant().getId());
        refundParam.setTargetUserId(orderRefundParam.getUserId());
        refundParam.setTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        if(orderRefundParam.isSaasRefundToMerchant()){
            //saas 退款给商户
            refundParam.setSourceMerchantId(0L);
            refundParam.setSourceTenantId(0L);
            refundParam.setSourceRole(PaymentRoleEnum.SAAS.getCode());
        } else {
            //租户退款给商户
            refundParam.setSourceMerchantId(0L);
            refundParam.setSourceTenantId(refundParam.getTargetTenantId());
            refundParam.setSourceRole(PaymentRoleEnum.TENANT.getCode());
        }
        if(MerchantUserAccountConstant.ALLOCATION.equalsIgnoreCase(userAccountBalanceResp.getBalanceType())){
            refundParam.setTargetUserId(userAccountBalanceResp.getId());
            refundParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType());
        }else {
            refundParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.COMMON_BALANCE.getUsedType());
        }
        refundParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_ORDER.getCode());
        return refundParam;
    }
    private RefundParam generateRefundDtoForTenantPay(OrderRefundParam orderRefundParam, Double balance,Double freeGold) {
        RefundParam refundParam = new RefundParam();
        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        //租户一定是通过余额付给商户
        Double totalAmount = com.sdsdiy.common.base.helper.DoubleUtils.add(balance,freeGold);
        refundParam.setBalance(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(freeGold, BigDecimal.ROUND_CEILING));
        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        refundParam.setPayMethod(PaymentMethodEnum.BALANCE.getCode());
        refundParam.setSubject("退款");
        refundParam.setTotalAmount(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(totalAmount, BigDecimal.ROUND_CEILING));
        refundParam.setTargetTenantId(orderRefundParam.getMerchant().getTenantId());
        refundParam.setTargetMerchantId(orderRefundParam.getMerchant().getId());
        refundParam.setTargetUserId(orderRefundParam.getUserId());
        refundParam.setTargetRole(PaymentRoleEnum.TENANT.getCode());
        refundParam.setSourceMerchantId(0L);
        refundParam.setSourceTenantId(0L);
        refundParam.setSourceRole(PaymentRoleEnum.SAAS.getCode());
//        refundParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.NO_USE_BALANCE.getUsedType());
        refundParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_ORDER.getCode());
        return refundParam;
    }

//    public void refundToAccount(OrderRefundParam orderRefundParam,UserAccountBalanceResp userAccountBalanceResp,RefundRecord refundRecord,List<RefundRecordItem> refundRecordItems){
//        StringBuffer orderName = new StringBuffer("退款");
//        int num = 0;
//        if (refundRecordItems.size() > 0) {
//            Long productId = null;
//            for (RefundRecordItem refundRecordItem : refundRecordItems) {
//                OrderItem orderItem = orderItemDao.findById(refundRecordItem.getOrderItemId());
//                num += orderItem.getNum();
//                if (productId == null) {
//                    productId = orderItem.getProductId();
//                }
//
//            }
//            List<String> getName = productService.getName(productId);
//            orderName.append(getName.get(0));
//        }
//        if (num > 1) {
//            orderName.append("...等 " + num + "件商品");
//        } else {
//            orderName.append("1件商品");
//        }
//        String object = orderName.toString();
//
//        List<RefundParam> refundParams = Lists.newArrayList();
//        if (NumberUtils.greaterZero(refundRecord.getBalance()) || NumberUtils.greaterZero(refundRecord.getFreeGold())) {
//            //商户退款
//            refundParams.add(this.generateRefundDtoForMerchantPay(orderRefundParam,refundRecord.getBalance(),refundRecord.getFreeGold(),userAccountBalanceResp));
//        }
//        if(NumberUtils.greaterZero(refundRecord.getTenantBalance()) || NumberUtils.greaterZero(refundRecord.getTenantFreeGold())){
//            //租户退款
//            refundParams.add(this.generateRefundDtoForTenantPay(orderRefundParam,refundRecord.getTenantBalance(),refundRecord.getTenantFreeGold()));
//        }
//        if(CollectionUtils.isNotEmpty(refundParams)) {
//            List<MerchantBillCreateParam> merchantBillCreateParams = Lists.newArrayList();
//            List<RefundDto> refundDtos = refundFeign.batchCreate(new RefundsCreateParam(refundParams));
//            MerchantBillDisposeMoney merchantBillDisposeMoney = merchantUserAccountService.getDisposeMoney(orderRefundParam.getMerchant().getTenantId(),orderRefundParam.getMerchant().getId(),userAccountBalanceResp.getId());
//            for (RefundDto refundDto : refundDtos) {
//                if(refundDto.getTargetRole().equalsIgnoreCase(PaymentRoleEnum.MERCHANT.getCode())){
//                    //商户退款生成账单
//                    merchantBillCreateParams.add(MerchantBillGenerateService.genMerchantRefund(orderRefundParam,refundDto,merchantBillDisposeMoney,object,refundDto.getTotalAmount(),refundDto.getGift(),refundRecord.getNo()));
//                }else if(refundDto.getTargetRole().equalsIgnoreCase(PaymentRoleEnum.TENANT.getCode())){
//                    //租户付款生成租户账单
//                    merchantBillCreateParams.add(MerchantBillGenerateService.genTenantRefund(orderRefundParam,refundDto,merchantBillDisposeMoney,object,refundDto.getTotalAmount(),refundDto.getGift(),refundRecord.getNo()));
//                }
//            }
//            if(CollectionUtils.isNotEmpty(merchantBillCreateParams)){
//                merchantBillFeign.batchCreate(new MerchantBillsCreateParam(merchantBillCreateParams));
//            }
//        }
//    }


    public void refundSuccessNotify(RefundRecord refundRecord, PaymentMethod paymentMethod, List<RefundRecordItem> refundRecordItems, Long adminUserId) {
        Order order = orderService.findByNo(refundRecord.getNo());
        String specificPurpose;
        StringBuffer orderName;
        if (RefundType.CHANGE_ADDRESS.getType().equals(refundRecord.getType())) {
            orderName = new StringBuffer("运费变动退差价");
            specificPurpose = MoneyPurpose.ORDER_MODIFY.getCode();
        }else{
            specificPurpose = MoneyPurpose.PRODUCT_REFUND.getCode();
            orderName = new StringBuffer("退款");
            int num = 0;
            if (refundRecordItems.size() > 0) {
                Long productId = null;
                for (RefundRecordItem refundRecordItem : refundRecordItems) {
                    OrderItem orderItem = orderItemDao.findById(refundRecordItem.getOrderItemId());
                    num += orderItem.getNum();
                    if (productId == null) {
                        productId = orderItem.getProductId();
                    }

                }
                List<String> getName = productService.getName(productId);
                orderName.append(getName.get(0));
            }
            if (num > 1) {
                orderName.append("...等 " + num + "件商品");
            } else {
                orderName.append("1件商品");

            }
        }

        merchantBillService.saveBill(refundRecord.getNo(), order.getCustomerId(), purposeTypeStatus.REFUNC.getStatus(),
                refundRecord.getRefundPrice(), refundRecord.getFreeGold(), paymentMethod.getAccount(), orderName.toString(), specificPurpose, refundRecord.getMerchantId(),adminUserId);
    }

    public RefundRecord getById(Long id) {
        return refundRecordDao.selectByPrimaryKey(id);

    }

    public List<RefundRecord> getRefundRecourdByNo(String no) {
        RefundRecord refundRecord = new RefundRecord();
        refundRecord.setNo(no);
        refundRecord.setStatus(2);
        return refundRecordDao.select(refundRecord);
    }

    public List<RefundRecord> getRefundRecourdByNos(List<String> nos) {
        return refundRecordDao.findByNos(nos);
    }


    public SearchBean<RefundRecordDto> list(String orderNo, String merchantNo,
                                            String keyword, Integer status,
                                            Integer orderStatus, String type,
                                            Date gmtVerifyBegin, Date gmtVerifyEnd,
                                            Date gmtCreatedBegin, Date gmtCreatedEnd,
                                            Integer page, Integer size, String sort, String designStatus) {
        String tableName = "refund_record";
        SearchBean<RefundRecord> sb = new SearchBean<>(page, size);
        sb.setTable(tableName);
        sb.addLeftJoin("`order` o on main.no=o.no");
        if (StringUtil.isBlank(sort)) {
            sb.setSort("create_time  desc");
        } else {
            sb.setSort(StringUtils.getOrderByToSort(sort));
        }
        if (StringUtils.isNotEmpty(merchantNo)) {
            sb.addSql("merchant_no like #{condition.merchant_no}");
            sb.addConditon("merchant_no", "%" + merchantNo + "%");
        }
        if (StringUtils.isNotEmpty(orderNo)) {
            sb.addSql("main.no like #{condition.order_no}");
            sb.addConditon("order_no", "%" + orderNo + "%");
        }
        if (Arrays.asList(1, 2).contains(status)) {
            sb.addSql("main.status = " + status);
        }

        if (gmtCreatedBegin != null && gmtCreatedEnd != null) {
            sb.addSql("main.create_time <=  #{condition.end_time}");
            sb.addSql("main.create_time >=  #{condition.create_time}");
            sb.addConditon("end_time", gmtCreatedEnd);
            sb.addConditon("create_time", gmtCreatedBegin);
        }

        if (StringUtils.isNotEmpty(keyword)) {

            sb.addSql("main.merchant_no like #{condition.keyword} or main.no like #{condition.keyword} or o.product_name like #{condition.keyword} ");
            sb.addConditon("keyword", "%" + keyword + "%");
        }

        if (orderStatus != null) {
            sb.addSql("o.status = #{condition.order_status}");
            sb.addConditon("order_status", orderStatus);
            if (orderStatus == FactoryOrderStatus.UNCONFIRMED.getStatus()) {
                if (StringUtils.isNotBlank(designStatus)) {
                    sb.addSql("o.design_status =#{condition.design_status}");
                    sb.addConditon("design_status", designStatus);
                } else {
                    throw new BusinessException("请输入订单状态");
                }
            }
        }
        if (StringUtils.isNotBlank(type)) {
            sb.addSql("main.type=#{condition.type}");
            sb.addCondition("type", type);
        }


        if (gmtVerifyBegin != null & gmtVerifyEnd != null) {
            sb.addSql("main.verify_date <=  #{condition.verify_end_time}");
            sb.addSql("main.verify_date >=  #{condition.verify_create_time}");
            sb.addConditon("verify_end_time", gmtVerifyEnd);
            sb.addConditon("verify_create_time", gmtVerifyBegin);
        }

        List<RefundRecord> list = refundRecordDao.list(sb);
        List<RefundRecordDto> dtos = ConvertUtil.dtoConvert(list, RefundRecordDto.class);
        setRemark(dtos);
        this.ORM(dtos);
        SearchBean<RefundRecordDto> searchBean = new SearchBean<>(page, size);
        searchBean.setItems(dtos);
        searchBean.setTotalCount(refundRecordDao.count(sb));
        return searchBean;
    }

    private void setRemark(List<RefundRecordDto> dtos) {
        for(RefundRecordDto dto:dtos){
            if(dto.getDutyAffiliation()==FactoryOrderService.FACTORY_DUTY){
                dto.setRemarks("工厂责任");
            }else if(dto.getDutyAffiliation()==FactoryOrderService.PLATFORM_DUTY){
                dto.setRemarks("平台责任");
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 脚本 order表添加productname字段
     **/
    public void load() {
        int count = orderDao.countAll();
        Order emp = new Order();
        for (int i = 0; i <= (count / 1000) + 1; i++) {
            List<Order> orders = orderDao.findbyAll(i, 1000);
            for (Order order : orders) {
                List<OrderItem> items = orderItemDao.findByOrderId(order.getId());
                StringBuffer sb = new StringBuffer();
                for (OrderItem item : items) {
                    sb.append(item.getProductName()).append(",");
                }
                if (StringUtils.isNotBlank(sb)) {
                    emp.setId(order.getId());
                    orderDao.update(emp);
                }
            }
        }
    }

    public Map<String, Object> detail(Long id) {
        Map<String, Object> map = new HashMap();

        RefundRecord record = new RefundRecord();
        record.setId(id);
        record = refundRecordDao.get(record);
        RefundRecordDto refundRecord = ConvertUtil.dtoConvert(record, RefundRecordDto.class);
        if (StringUtils.isNotBlank(record.getVoucherImgUrls())) {
            refundRecord.setVoucherImgUrls(JSONUtil.parseArray(record.getVoucherImgUrls()));
        }
        this.ORM(refundRecord);
        map.put("detail", refundRecord);
        List<RefundRecordItem> items = refundRecordItemDao.getByRecordId(id);
        List<Long> orderIds = new ArrayList<>();
        for (RefundRecordItem item : items) {
            orderIds.add(item.getOrderItemId());
        }

        map.put("products", orderService.queryOrderItemsById(orderIds));
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addConsume(Long id) {
        RefundRecord refundRecord = this.getById(id);
        if (refundRecord == null) {
            return;
        }
        if (refundRecord.getStatus() != REFUND_SUCCESS) {
            return;
        }
        if (refundRecordDao.updateConsume(refundRecord.getId()) == 0) {
            return;
        }
        Double consume = DoubleUtils.sub(refundRecord.getRefundPrice(), refundRecord.getFreeGold());

        if (consume > 0) {
            merchantDao.addConsume(refundRecord.getMerchantId(), 0 - consume);
            String size = getSize(id);

            if (refundRecord.getType().equals(RefundType.CANCEL.getType())) {
                String content = String.format("您申请取消产品：%s ；退款金额：%s 元，已到账！请注意查收！", size, consume);
                notificationService.save(content, EnumNotificationTitle.ORDER_FEFUND_SUCCESS, refundRecord.getMerchantId(), refundRecord.getUserId(), refundRecord.getNo(), null);
            }
            if (refundRecord.getType().equals(RefundType.AFTER_SALE.getType())) {

                Order order = orderDao.findByNo(refundRecord.getNo());
                if (null != order && order.getOriginType().equals(OrderOriginType.FBA.getValue())){
                    //FBA运营直接售后
                    String content = String.format("您申请售后产品：%s ；退款金额：%s 元，已到账！请注意查收！", size, consume);
                    notificationService.save(content, EnumNotificationTitle.ORDER_FBA_AFTERMARKET_PASS, refundRecord.getMerchantId(), refundRecord.getUserId(), refundRecord.getNo(), null);
                }else{
                    String content = String.format("您申请售后产品：%s ；退款金额：%s 元，已到账！请注意查收！", size, consume);
                    notificationService.save(content, EnumNotificationTitle.ORDER_AFTERMARKET_SUCCESS, refundRecord.getMerchantId(), refundRecord.getUserId(), refundRecord.getNo(), null);
                }


            }

        }
    }

    public String getSize(Long refundRecordId) {
        List<RefundRecordItem> items = refundRecordItemDao.getByRecordId(refundRecordId);
        StringBuilder productInfo = new StringBuilder();
        boolean passFirst = false;
        for (RefundRecordItem one : items) {
            if (passFirst) {
                productInfo.append(";");
            }
            OrderItem order = orderItemDao.findById(one.getOrderItemId());

            Product product = productDao.findById(order.getProductId());
            if (product == null) {
                continue;
            }
            productInfo.append(product.getName())
                    .append(" ")
                    .append(StringUtils.ifnullToString(product.getColorName()))
                    .append("/").
                    append(StringUtils.ifnullToString(product.getSize()));

            passFirst = true;

        }
        return productInfo.toString();
    }


    public void finishRefund(RefundVo refundVo, Long id) {
        String password = refundVo.getPassword();
        if (StringUtil.isEmpty(password)) {
            throw new BusinessException("密码不能为空");
        }
        if (refundVo.getVoucherImgUrls() == null && refundVo.getVoucherImgUrls().size() == 0) {
            throw new BusinessException("请上传凭证");
        }
        AdminConfigs adminConfigs = adminConfigService.getCodeValue("pay_password");
        PasswordDto passwordDto = JSONUtil.toBean(adminConfigs.getValue(), PasswordDto.class);
        byte[] salt = Encodes.decodeHex(passwordDto.getSalt());
        String passWord = Encodes.encodeHex(Digest.sha1(password.getBytes(), salt, 1024));
        if (!passwordDto.getPassword().equals(passWord)) {
            throw new BusinessException("支付密码不正确");
        }
        RefundRecord refundRecord = new RefundRecord();
        refundRecord.setId(id);
        refundRecord.setRemark(refundVo.getRemark());
        Gson gson = new Gson();
        refundRecord.setVoucherImgUrls(gson.toJson(refundVo.getVoucherImgUrls()));
        refundRecordDao.update(refundRecord);
        webPushService.pushMsgToWebByMerchantId(MsgModule.FINANCIAL_REMITTANCE, refundRecord.getMerchantId());


    }

    public RefundRecordDto find(Long id) {
        RefundRecord refundRecord = new RefundRecord();
        refundRecord.setId(id);
        RefundRecord result = refundRecordDao.get(refundRecord);
        RefundRecordDto dto = ConvertUtil.dtoConvert(result, RefundRecordDto.class);
        return this.ORM(dto);

    }

    public RefundRecordDto findByAfterServiceAuditId(Long afterServiceAuditId) {
        RefundRecord refundRecord = new RefundRecord();
        refundRecord.setAfterServiceAuditId(afterServiceAuditId);
        RefundRecord result = refundRecordDao.selectOne(refundRecord);
        RefundRecordDto dto = ConvertUtil.dtoConvert(result, RefundRecordDto.class);
        return dto;

    }

    public ExcelWriter export(List<RefundRecordDto> items) {
       List<Long> paymentIds=items.stream().map(RefundRecordDto::getPaymentId).collect(Collectors.toList());
       List<Payment> payments=paymentService.findByIds(paymentIds);
       Map<Long,Payment> paymentMap=new HashMap<>();
       for(Payment payment:payments){
           paymentMap.put(payment.getId(),payment);
       }
        List<String> head = Arrays.asList("订单号", "类型", "支付时间", "支付方式", "取消时间", "退款金额", "商户号", "联系方式", "退款状态", "备注");

        ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter(true);

        writer.setColumnWidth(0, 20);
        writer.setColumnWidth(1, 20);
        writer.setColumnWidth(2, 20);
        writer.setColumnWidth(3, 15);
        writer.setColumnWidth(4, 20);
        writer.setColumnWidth(5, 20);
        writer.setColumnWidth(6, 20);
        writer.setColumnWidth(7, 20);
        writer.setColumnWidth(8, 40);

        writer.writeRow(head);


        for (RefundRecordDto one : items) {
            Payment payment=paymentMap.get(one.getPaymentId());
            String type="";
            if(one.getType().equals("CANCEL")){
                type="取消";
            }
            if(one.getType().equals("AFTER_SALE")){
                type="售后";
            }
            if(one.getType().equals("CHANGE_ADDRESS")){
                type="运费变动";
            }
            String remark="";
            if(one.getDutyAffiliation()==FactoryOrderService.FACTORY_DUTY){
                remark="工厂责任";
            }else if(one.getDutyAffiliation()==FactoryOrderService.PLATFORM_DUTY){
                remark="平台责任";
            }

            List<Object> rowData = Arrays.asList(
                    one.getNo(),
                    type,
                    payment==null?"":DateUtil.longToString(payment.getPayTime()),
                    one.getPaymentMethod(),
                    one.getGmtVerify(),
                    one.getRefundPrice(),
                    one.getMerchantNo(),
                    one.getPhone(),
                    one.getStatus()==REFUND_SUCCESS?"已退款":"未退款",
                    remark


            );
            writer.writeRow(rowData);
        }
        return writer;

    }

}
