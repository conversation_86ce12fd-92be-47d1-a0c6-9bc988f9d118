package com.ps.ps.service;

import com.ps.base.service.TKBaseService;
import com.ps.exception.BusinessException;
import com.ps.ps.dao.MerchantDao;
import com.ps.ps.dao.PlatformGoodOrderDao;
import com.ps.ps.feign.MerchantPlatformPermissionSetMealFeign;
import com.ps.ps.feign.payment.TransactionFeign;
import com.ps.ps.feign.platformGoods.PlatformGoodsFeign;
import com.ps.ps.feign.tenant.TenantFeign;
import com.ps.ps.feign.tenant.TenantPodFeign;
import com.ps.ps.feign.user.MerchantBehaviorRecordFeign;
import com.ps.ps.feign.user.MerchantPlatformPermissionFeign;
import com.ps.support.Assert;
import com.ps.support.Encodes;
import com.ps.support.utils.OrderCodeUtil;
import com.ps.system.service.MerchantUserService;
import com.ps.tool.Digest;
import com.ps.tool.cbt.common.util.StringUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.helper.CompareUtils;
import com.sdsdiy.orderapi.constant.EnumPaymentMethod;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.SubjectContentParam;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.constant.PlatformGoodsConstant;
import com.sdsdiy.userapi.constant.PlatformSetMealConstant;
import com.sdsdiy.userapi.constant.PlatformSetMealTypeEnum;
import com.sdsdiy.userapi.dto.GenSetMealParamReqDto;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.PlatformGoodsRespDto;
import com.sdsdiy.userapi.dto.PlatformPermissionSetMealRespDto;
import com.sdsdiy.userapi.dto.payment.PaymentReqDto;
import com.sdsdiy.userapi.dto.platformgoods.BalanceBuyPlatformGoodsDto;
import com.sdsdiy.userdata.enums.MemberLevelEnum;
import com.sdsdiy.userdata.enums.MerchantBehaviorEnum;
import com.sdsdiy.userdata.enums.PlatformGoodsTypeEnum;
import com.ziguang.base.bo.platformGoods.PlatformGoodOrderBO;
import com.ziguang.base.dto.MerchantPlatformPermissionSetMealDto;
import com.ziguang.base.dto.PlatformGoodOrderDto;
import com.ziguang.base.model.*;
import com.ziguang.base.support.DoubleUtils;
import com.ziguang.base.support.contant.OrderStatus;
import com.ziguang.base.support.contant.PurposeNameStatus;
import com.ziguang.base.support.contant.purposeTypeStatus;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.ion.Decimal;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


@Service
public class PlatformGoodOrderService extends TKBaseService<PlatformGoodOrderDao, PlatformGoodOrder> {
    private static final int LIMIT_LEVEL_FREE = 1;
    private static final int LIMIT_LEVEL_FEE = 2;

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantUserService merchantUserService;
    @Autowired
    private PlatformGoodOrderDao platformGoodOrderDao;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private MerchantDao merchantDao;
    @Autowired
    private MerchantPlatformPermissionSetMealService merchantPlatformPermissionSetMealService;
    @Autowired
    private com.ps.ps.dao.MerchantBillDao merchantBillDao;
    @Autowired
    private MerchantPlatformPermissionSetMealFeign merchantPlatformPermissionSetMealFeign;
    @Resource
    private MerchantPlatformPermissionFeign merchantPlatformPermissionFeign;
    @Resource
    private TenantPodFeign tenantPodFeign;
    @Resource
    private TenantFeign tenantFeign;
    @Resource
    private PlatformGoodsFeign platformGoodsFeign;
    @Resource
    private MerchantBehaviorRecordFeign merchantBehaviorRecordFeign;
    @Resource
    private TransactionFeign transactionFeign;

    public static final String BALANCE_PAY = "BALANCE_PAY";
    public static final String ALI_PAY = "ALI_PAY";
    public static final int IS_YES_USE_BALANCE = 1;
    public static final int IS_NO_USE_BALANCE = 2;
    public static final int PAYMENT_TYPE = 1;

    public static final String PLATFORMACCOUNT = "平台账户";
    public static final String ALLPAY = "支付宝";

    public static final String MATERIAL_CAPACITY_CODE = "material_capacity";


    /**
     * 商户开通会员或增值服务
     *
     * @param merchantId
     * @param userId
     * @param tenantId
     * @param orderDto
     * @return
     */
    public PaymentDto addOrder(PlatformGoodOrderBO orderBO) {
        PlatformGoodOrderDto orderDto = orderBO.getOrderDto();
        PlatformGoodsRespDto goodsRespDto = this.platformGoodsFeign.findGoodsDtoById(orderDto.getPlatformGoodsId(), "baseSetMealList");
        orderBO.setGoodsDto(goodsRespDto);
        this.paramCheck(orderBO);
        Long merchantId = orderBO.getMerchantId();
        Long userId = orderBO.getUserId();
        //创建payment
        Merchant merchant = this.merchantService.getById(merchantId);
        this.checkPassword(userId, orderDto.getPassword(), orderDto.getPaymentMethod(), merchant);
        // 开启线上付款，商户-租户-saas，未开启，商户-saas
        boolean openOnlinePay = BasePoConstant.YES.equals(merchant.getTenantOpenOnlinePay());
        String paymentSubject = this.getPaymentSubject(orderDto, merchant.getName(), orderBO.getSetMealDto());
        List<PaymentParam> paymentList = new ArrayList<>();
        if(openOnlinePay){
            //开启线上付款，商户-租户-saas
            PaymentParam merchantPaymentReqDto = this.genPayMerchantToTenantForBuyService(orderBO, paymentSubject);
            PaymentParam tenantPaymentReqDto = this.genPayTenantToSaasForBuyService(orderBO, paymentSubject);
            paymentList.add(merchantPaymentReqDto);
            paymentList.add(tenantPaymentReqDto);
        }else{
            //未开启，商户-saas
            PaymentParam merchantPaymentReqDto = this.genPayMerchantToSaasForBuyService(orderBO, paymentSubject);
            paymentList.add(merchantPaymentReqDto);
        }
        MultiTransactionCreateParam param=new MultiTransactionCreateParam();
        param.setPaymentList(paymentList);
        PaymentDto paymentDto = transactionFeign.createPayment(param);
        orderBO.setPaymentDto(paymentDto);
        //创建购买记录
        PlatformGoodOrder platformGoodOrder = this.savePlatformGoodOrder(orderBO);
        //支付宝支付或拉卡拉且未支付，轮询
        if (PaymentMethodEnum.needWaitCustomerPaid(paymentDto.getMethod())) {
            return paymentDto;
        }
        BalanceBuyPlatformGoodsDto balanceBuyPlatformGoodsDto = this.getBalanceBuyPlatformGoodsDto(merchantId, userId, platformGoodOrder, paymentDto);
        return this.tenantFeign.balanceBuyPlatformGoods(balanceBuyPlatformGoodsDto);
    }

    private String getPaymentSubject(PlatformGoodOrderDto orderDto, String merchantName, PlatformPermissionSetMealRespDto setMealRespDto) {
        SubjectContentParam param = new SubjectContentParam();
        param.setYear(orderDto.getValue());
        param.setCount(orderDto.getValue());
        param.setMonth(orderDto.getValue());
        param.setMerchantName(merchantName);
        param.setMembershipPackageName(orderDto.getName());
        SubjectContentType subjectContentType = PlatformGoodsConstant.platformGoodsIdToMerchantSubjectMap.get(orderDto.getPlatformGoodsId());
        if (subjectContentType == SubjectContentType.MERCHANT_BUY_MEMBERSHIP
                && MemberLevelEnum.isMonth(setMealRespDto.getPeriod())) {
            subjectContentType = SubjectContentType.MERCHANT_BUY_MEMBERSHIP_MONTH;
        }
        return SubjectContentType.generateContent(subjectContentType, param);
    }

    private void checkPassword(Long userId, String password, String paymentmethod, Merchant merchant) {
        if (!paymentmethod.equals(PaymentMethodEnum.BALANCE.getCode())) {
            return;
        }
        if (StringUtil.isEmpty(password)) {
            throw new BusinessException("密码不能为空");
        }
        User user = this.merchantUserService.findById(userId);
        if (merchant == null || user == null) {
            throw new BusinessException("用户不存在不能付款！");
        }
        byte[] salt = Encodes.decodeHex(user.getSalt());
        String hashPassword = Encodes.encodeHex(Digest.sha1(password.getBytes(), salt, 1024));
        if (!user.getPassword().equals(hashPassword)) {
            throw new BusinessException("支付密码不正确");
        }
    }

    public PlatformGoodOrder savePlatformGoodOrder(PlatformGoodOrderBO orderBO) {
        PlatformGoodOrder platformGoodOrder = this.getPlatformGoodsOrder(orderBO);
        this.platformGoodOrderDao.insertSelective(platformGoodOrder);
        return platformGoodOrder;
    }

    public PlatformGoodOrder getPlatformGoodsOrder(PlatformGoodOrderBO orderBO) {
        PaymentDto paymentDto = orderBO.getPaymentDto();
        PlatformGoodOrderDto orderDto = orderBO.getOrderDto();
        PlatformGoodOrder platformGoodOrder = new PlatformGoodOrder();
        platformGoodOrder.setCreateTime(System.currentTimeMillis());
        platformGoodOrder.setSourceTenantId(0L);
        platformGoodOrder.setSourceMerchantId(orderBO.getMerchantId());
        platformGoodOrder.setTenantId(0L);
        platformGoodOrder.setMerchantId(orderBO.getMerchantId());
        platformGoodOrder.setNo(paymentDto.getBizNo());
        platformGoodOrder.setPaymentId(paymentDto.getId());
        platformGoodOrder.setPaymentMethod(orderDto.getPaymentMethod());
        platformGoodOrder.setPlatformGoodsId(orderBO.getGoodsDto().getId());
        platformGoodOrder.setPlatformGoodsName(orderBO.getGoodsDto().getName());
        platformGoodOrder.setPlatformPermissionSetMealId(orderBO.getSetMealDto().getId());
        platformGoodOrder.setValue(orderDto.getValue());
        if (orderDto.getNumPeriod() == null) {
            platformGoodOrder.setNumPeriod(1);
        } else {
            platformGoodOrder.setNumPeriod(orderDto.getNumPeriod());
        }
        platformGoodOrder.setProductPrice(orderBO.getSetMealDto().getPrice().doubleValue());
        platformGoodOrder.setTotalAmount(platformGoodOrder.getTotalAmount());
        platformGoodOrder.setStatus(EnumPaymentMethod.EMPTY.getValue().equals(orderDto.getPaymentMethod()) ? PaymentStatusEnum.PAID.getStatus() : PaymentStatusEnum.WAIT_PAY.getStatus());
        platformGoodOrder.setUserId(orderBO.getUserId());
        if (BALANCE_PAY.equals(orderDto.getPaymentMethod())) {
            platformGoodOrder.setUsableBalance(paymentDto.getBalance().doubleValue());
            platformGoodOrder.setUsableFreeGold(paymentDto.getBonus().doubleValue());
        }
        platformGoodOrder.setTotalAmount(orderDto.getTotalAmount().doubleValue());
        platformGoodOrder.setUpdateTime(System.currentTimeMillis());
        return platformGoodOrder;
    }

    private BalanceBuyPlatformGoodsDto getBalanceBuyPlatformGoodsDto(Long merchantId, Long userId, PlatformGoodOrder platformGoodOrder, PaymentDto paymentDto) {
        BalanceBuyPlatformGoodsDto balanceBuyPlatformGoodsDto = new BalanceBuyPlatformGoodsDto();
        com.sdsdiy.userapi.dto.PlatformGoodOrderDto userPlatformOrderGoodsDto = this.getPlatformGoodOrderDto(platformGoodOrder);
        balanceBuyPlatformGoodsDto.setPlatformGoodOrderDto(userPlatformOrderGoodsDto);
        balanceBuyPlatformGoodsDto.setPaymentDto(paymentDto);
        balanceBuyPlatformGoodsDto.setSourceMerchantId(merchantId);
        balanceBuyPlatformGoodsDto.setSourceTenantId(0L);
        balanceBuyPlatformGoodsDto.setSourceUserId(userId);
        balanceBuyPlatformGoodsDto.setTargetMerchantId(merchantId);
        return balanceBuyPlatformGoodsDto;
    }

    private com.sdsdiy.userapi.dto.PlatformGoodOrderDto getPlatformGoodOrderDto(PlatformGoodOrder reqDto) {
        com.sdsdiy.userapi.dto.PlatformGoodOrderDto platformGoodOrderDto = new com.sdsdiy.userapi.dto.PlatformGoodOrderDto();
        BeanUtils.copyProperties(reqDto, platformGoodOrderDto);
        return platformGoodOrderDto;
    }

    private PaymentReqDto getMerchantBuyMemberPaymentParam(PlatformGoodOrderBO orderBO,
                                                           Integer openOnlinePay, String paymentSubject) {
        PaymentReqDto paymentReqDto = new PaymentReqDto();
        paymentReqDto.setMethod(orderBO.getOrderDto().getPaymentMethod());
        paymentReqDto.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        BigDecimal totalAmount = orderBO.getOrderDto().getTotalAmount();
        paymentReqDto.setBalance(totalAmount);
        paymentReqDto.setFreeGold(BigDecimal.ZERO);
        paymentReqDto.setSubject(paymentSubject);
        paymentReqDto.setTotalAmount(totalAmount);
        paymentReqDto.setSourceTenantId(orderBO.getTenantId());
        paymentReqDto.setSourceMerchantId(orderBO.getMerchantId());
        paymentReqDto.setSourceUserId(0L);
        paymentReqDto.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setTargetMerchantId(0L);
        paymentReqDto.setTargetTenantId(this.getTargetTenantId(openOnlinePay, orderBO.getTenantId()));
        paymentReqDto.setTargetRole(this.getTargetRole(openOnlinePay));
        paymentReqDto.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.COMMON_BALANCE.getUsedType());
        paymentReqDto.setDetailPurpose(DetailPurpose.MERCHANT_BUY_SERVICE.getCode());
        paymentReqDto.setPurposeType(PurposeType.BUY_SERVICE.getCode());
        paymentReqDto.setOperateUserId(orderBO.getUserId());
        paymentReqDto.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setOperateTargetRoleId(orderBO.getMerchantId());
        if (PlatformGoodsTypeEnum.MEMBER.equalsCode(orderBO.getGoodsDto().getType())) {
            paymentReqDto.setAlipaySupportPayeeType(AlipaySupportPayeeTypeEnum.BUY_VIP.getPayeeType());
        } else {
            paymentReqDto.setAlipaySupportPayeeType(AlipaySupportPayeeTypeEnum.BUY_SERVICE.getPayeeType());
        }
        return paymentReqDto;
    }
    private PaymentParam genPayMerchantToTenantForBuyService(PlatformGoodOrderBO goodOrderBO, String paymentSubject) {
        String paymentMethod = goodOrderBO.getOrderDto().getPaymentMethod();
        PaymentParam paymentReqDto = new PaymentParam();
        paymentReqDto.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        paymentReqDto.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        paymentReqDto.setBillType(PaymentBillTypeEnum.DIRECT.getStatus());
        paymentReqDto.setDetailPurpose(DetailPurpose.MERCHANT_BUY_SERVICE.getCode());
        paymentReqDto.setPurposeType(PurposeType.BUY_SERVICE.getCode());
        Integer balanceType = MerchantUserAccountConstant.getMerchantBalanceTypeForPaymentOrRefund(MerchantUserAccountConstant.IN_COMMON, paymentMethod);
        paymentReqDto.setBalanceType(balanceType);
        paymentReqDto.setMethod(paymentMethod);
        paymentReqDto.setBalance(goodOrderBO.getOrderDto().getTotalAmount());
        paymentReqDto.setBonus(BigDecimal.ZERO);
        paymentReqDto.setTitle(paymentSubject);

        paymentReqDto.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setSourceTenantId(goodOrderBO.getTenantId());
        paymentReqDto.setSourceMerchantId(goodOrderBO.getMerchantId());
        Long sourceUserId = MerchantUserAccountConstant.getSourceUserId(MerchantUserAccountConstant.IN_COMMON, goodOrderBO.getUserId(), paymentMethod);
        paymentReqDto.setSourceUserId(sourceUserId);

        paymentReqDto.setTargetRole(PaymentRoleEnum.TENANT.getCode());
        paymentReqDto.setTargetMerchantId(0L);
        paymentReqDto.setTargetTenantId(goodOrderBO.getTenantId());

        paymentReqDto.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setOperateUserId(goodOrderBO.getUserId());
        paymentReqDto.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setOperateTargetRoleId(goodOrderBO.getMerchantId());
        return paymentReqDto;
    }
    private PaymentParam genPayTenantToSaasForBuyService(PlatformGoodOrderBO orderBO, String paymentSubject) {
        PaymentParam paymentReqDto = new PaymentParam();
        paymentReqDto.setPayType(TransactionPayTypeEnum.SUB.getValue());
        paymentReqDto.setBillType(PaymentBillTypeEnum.DIRECT.getStatus());
        paymentReqDto.setDetailPurpose(DetailPurpose.MERCHANT_BUY_SERVICE.getCode());
        paymentReqDto.setPurposeType(PurposeType.BUY_SERVICE.getCode());
        paymentReqDto.setBalanceType(BalanceUsedType.TENANT_BALANCE.getUsedType());
        paymentReqDto.setMethod(PaymentMethodEnum.BALANCE.getCode());
        paymentReqDto.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        paymentReqDto.setBalance(orderBO.getOrderDto().getTotalAmount());
        paymentReqDto.setBonus(BigDecimal.ZERO);
        paymentReqDto.setTitle(paymentSubject);

        paymentReqDto.setSourceRole(PaymentRoleEnum.TENANT.getCode());
        paymentReqDto.setSourceTenantId(orderBO.getTenantId());
        paymentReqDto.setSourceMerchantId(0L);
        paymentReqDto.setSourceUserId(0L);

        paymentReqDto.setTargetRole(PaymentRoleEnum.SAAS.getCode());
        paymentReqDto.setTargetMerchantId(0L);
        paymentReqDto.setTargetTenantId(TenantCommonConstant.SAAS_TENANT_ID);

        paymentReqDto.setOperateRole(PaymentRoleEnum.SYSTEM.getCode());
        paymentReqDto.setOperateUserId(orderBO.getUserId());
        paymentReqDto.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setOperateTargetRoleId(orderBO.getMerchantId());
        return paymentReqDto;
    }
    private PaymentParam genPayMerchantToSaasForBuyService(PlatformGoodOrderBO goodOrderBO, String paymentSubject) {
        String paymentMethod = goodOrderBO.getOrderDto().getPaymentMethod();

        PaymentParam paymentReqDto = new PaymentParam();
        paymentReqDto.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        paymentReqDto.setBillType(PaymentBillTypeEnum.DIRECT.getStatus());
        paymentReqDto.setDetailPurpose(DetailPurpose.MERCHANT_BUY_SERVICE.getCode());
        paymentReqDto.setPurposeType(PurposeType.BUY_SERVICE.getCode());
        Integer balanceType = MerchantUserAccountConstant.getMerchantBalanceTypeForPaymentOrRefund(MerchantUserAccountConstant.IN_COMMON, paymentMethod);
        paymentReqDto.setBalanceType(balanceType);
        paymentReqDto.setMethod(goodOrderBO.getOrderDto().getPaymentMethod());
        paymentReqDto.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        paymentReqDto.setBalance(goodOrderBO.getOrderDto().getTotalAmount());
        paymentReqDto.setBonus(BigDecimal.ZERO);
        paymentReqDto.setTitle(paymentSubject);

        paymentReqDto.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setSourceTenantId(goodOrderBO.getTenantId());
        paymentReqDto.setSourceMerchantId(goodOrderBO.getMerchantId());
        Long sourceUserId = MerchantUserAccountConstant.getSourceUserId(MerchantUserAccountConstant.IN_COMMON, goodOrderBO.getUserId(), paymentMethod);
        paymentReqDto.setSourceUserId(sourceUserId);

        paymentReqDto.setTargetRole(PaymentRoleEnum.SAAS.getCode());
        paymentReqDto.setTargetMerchantId(0L);
        paymentReqDto.setTargetTenantId(TenantCommonConstant.SAAS_TENANT_ID);

        paymentReqDto.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setOperateUserId(goodOrderBO.getUserId());
        paymentReqDto.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setOperateTargetRoleId(goodOrderBO.getMerchantId());
        return paymentReqDto;
    }

    public Long getTargetTenantId(Integer openOnlinePay, Long tenantId) {
        if (this.isOpenOnlinePay(openOnlinePay)) {
            return tenantId;
        }
        return TenantCommonConstant.SAAS_TENANT_ID;
    }

    public String getTargetRole(Integer openOnlinePay) {
        if (this.isOpenOnlinePay(openOnlinePay)) {
            return PaymentRoleEnum.TENANT.getCode();
        }
        return PaymentRoleEnum.SAAS.getCode();
    }

    private boolean isOpenOnlinePay(Integer openOnlinePay) {
        return null != openOnlinePay && openOnlinePay.equals(BasePoConstant.YES);
    }

    private void paramCheck(PlatformGoodOrderBO orderBO) {
        PlatformGoodOrderDto orderDto = orderBO.getOrderDto();
        if (!MerchantService.check(orderDto.getValue().toString())) {
            throw new BusinessException("只能纯数字");
        }
        PlatformGoodsRespDto goodsDto = orderBO.getGoodsDto();
        this.getCheckPrice(orderBO);
        if (goodsDto.getLimitLevel() == LIMIT_LEVEL_FEE) {
            if (!this.merchantPlatformPermissionSetMealService.isFeeUser(orderBO.getMerchantId())) {
                Assert.wrong("只有付费会员才可购买");
            }
        }
        orderDto.setName(goodsDto.getName());

        boolean isMember = PlatformGoodsTypeEnum.MEMBER.equalsCode(goodsDto.getType());
        if (isMember) {
            if (orderDto.getValue() >= 100) {
                throw new BusinessException("购买数量最大不能超过99");
            }
        } else {
            if (orderDto.getValue() >= 100000) {
                throw new BusinessException("购买数量最大不能超过99999");
            }
        }
        //购买会员套餐时，当前会员等级不等于所购买的会员套餐等级，报错
        if (isMember && !this.setMealLevelEqual(orderBO)) {
            throw new BusinessException("暂不支持等级自主变更，请联系客服");
        }
    }

    private boolean setMealLevelEqual(PlatformGoodOrderBO orderBO) {
        MerchantPlatformPermissionSetMealDto currentSetMeal = this.merchantPlatformPermissionSetMealService.usingSetMealInfoRetreatment(orderBO.getMerchantId());
        //免费套餐都可购买，付费套餐只能购买同等级的套餐
        if (null != currentSetMeal && null != orderBO.getSetMealDto() &&
                (currentSetMeal.getCode().equals(PlatformSetMealConstant.FREE_SET_MEAL) || currentSetMeal.getLevel().compareTo(orderBO.getSetMealDto().getLevel()) == 0)) {
            return true;
        } else {
            return false;
        }
    }

    public void getCheckPrice(PlatformGoodOrderBO orderBO) {
        PlatformGoodsRespDto goodsDto = orderBO.getGoodsDto();
        if (goodsDto == null || goodsDto.getBaseSetMealList() == null) {
            throw new BusinessException("商品信息不存在!");
        }
        PlatformGoodOrderDto orderDto = orderBO.getOrderDto();
        PlatformPermissionSetMealRespDto setMealRespDto = goodsDto.getBaseSetMealList().stream()
                .filter(i -> i.getId().equals(orderDto.getPlatformPermissionSetMealId()))
                .findFirst().orElse(null);
        if (setMealRespDto == null) {
            throw new BusinessException("商品套餐不存在!");
        }
        BigDecimal price = setMealRespDto.getPrice();
        if (MemberLevelEnum.V1.eq(setMealRespDto.getLevel()) && MemberLevelEnum.isMonth(setMealRespDto.getPeriod()) && CompareUtils.gtZero(setMealRespDto.getFirstPrice())) {
            // v1 月套餐
            boolean bought = this.merchantBehaviorRecordFeign.checkBehavior(orderBO.getMerchantId(), MerchantBehaviorEnum.BUY_MEMBER_V1_FIRST_PRICE);
            if (!bought) {
                Assert.validateFalse(BasePoConstant.INTEGER_ONE.equals(orderDto.getValue()), "只有首月才能享受优惠价");
                // 没买过的，第一次享受优惠
                price = setMealRespDto.getFirstPrice();
            } else {
                // 买过
                Assert.validateTrue(setMealRespDto.getFirstPrice().compareTo(orderDto.getTotalAmount()) == 0, "请勿重复购买");
            }
        }
        if (CompareUtils.gtZero(setMealRespDto.getSalePrice())) {
            // 促销价 判断促销时间
            Long endTime = setMealRespDto.getSalePriceEndTime();
            boolean sale = CompareUtils.isIn(setMealRespDto.getSalePriceStartTime()
                    , endTime > 0 ? (endTime + TimeUnit.DAYS.toMillis(1)) : null
                    , System.currentTimeMillis());
            if (sale) {
                // 取较低的价格
                price = price.min(setMealRespDto.getSalePrice());
            }
        }
        Integer numPeriod = orderDto.getNumPeriod();
        if (numPeriod == null || !PlatformSetMealTypeEnum.setMealTypeOfIncrease().equals(setMealRespDto.getSetMealType()) && setMealRespDto.getPeriod() > 0) {
            numPeriod = 1;
        } else {
            if (!MerchantService.check(numPeriod.toString())) {
                throw new BusinessException("周期只能纯数字");
            }
        }
        BigDecimal realTotal = price.multiply(new BigDecimal(orderDto.getValue())).multiply(new BigDecimal(numPeriod));
        if (0 != realTotal.compareTo(orderDto.getTotalAmount())) {
            throw new BusinessException("应付金额和后台价格不一样！");
        }
        orderBO.setSetMealDto(setMealRespDto);
    }


    public void checkBalance(Long userId, Merchant merchant, Double balance, Double freeGold, String password) {
        this.checkBalance(userId, merchant, balance, freeGold, password, null, true);
    }

    public void checkBalance(Long userId, Merchant merchant, Double balance, Double freeGold, String password, Boolean checkUser, Boolean needPassword) {
        if (checkUser != null && !checkUser) {
            return;
        }
        //免密支付
        if (needPassword && StringUtil.isEmpty(password)) {
            throw new BusinessException("密码不能为空");
        }
        User user = this.merchantUserService.findById(userId);
        if (merchant == null || user == null) {
            throw new BusinessException("用户不存在不能付款！");
        }
//        if (!merchant.getBalance().equals(balance) || !merchant.getFreeGold().equals(freeGold)) {
//            throw new BusinessException("您的账户余额或赠送金额发生了变化，请重新刷新付款");
//        }

        //免密支付
        if (needPassword) {
            byte[] salt = Encodes.decodeHex(user.getSalt());
            String hashPassword = Encodes.encodeHex(Digest.sha1(password.getBytes(), salt, 1024));
            if (!user.getPassword().equals(hashPassword)) {
                throw new BusinessException("支付密码不正确");
            }
        }

    }

    public void checkBalance(Long userId, MerchantRespDto merchant, String password, Boolean checkUser, Boolean needPassword) {
        if (checkUser != null && !checkUser) {
            return;
        }
        //免密支付
        if (needPassword && StringUtil.isEmpty(password)) {
            throw new BusinessException("密码不能为空");
        }
        User user = this.merchantUserService.findById(userId);
        if (merchant == null || user == null) {
            throw new BusinessException("用户不存在不能付款！");
        }

        //免密支付
        if (needPassword) {
            byte[] salt = Encodes.decodeHex(user.getSalt());
            String hashPassword = Encodes.encodeHex(Digest.sha1(password.getBytes(), salt, 1024));
            if (!user.getPassword().equals(hashPassword)) {
                throw new BusinessException("支付密码不正确");
            }
        }

    }

    /**
     * 插入payment表
     *
     * @return
     */
    public Payment addPayment(Long merchantId, String imgUrl, String payMethod, Double totalMoney, Integer paymentType, Double balance, Double freeGold) {
        Double totalAmount = 0D;
        totalAmount = DoubleUtils.add(totalAmount, totalMoney);
        String outOrderNo = OrderCodeUtil.getOrderCode(System.currentTimeMillis());
        Payment payment = this.insertPayment(merchantId, imgUrl, payMethod, totalAmount, outOrderNo, paymentType, balance, freeGold);
        return payment;
    }

    /**
     * @param goodOrder
     */
    @Transactional
    public void balanceAliCommon(PlatformGoodOrder goodOrder) {
        PlatformGoodsRespDto goodsDto = this.platformGoodsFeign.findGoodsDtoById(goodOrder.getPlatformGoodsId(), "");
        PlatformPermissionSetMealRespDto setMealDto = this.platformGoodsFeign.findSetMealDtoById(goodOrder.getPlatformPermissionSetMealId(), "platformPermissionList");
        if (goodsDto == null || setMealDto == null) {
            throw new BusinessException("获取订单信息有误！");
        }
        // 购买个数
        Integer value = goodOrder.getValue();
        PurposeNameStatus purposeNameStatus = PurposeNameStatus.getByCode(setMealDto.getCode());
        String orderType = purposeNameStatus.getSpecificPurpose();
        String orderName = purposeNameStatus.getOrderName().replaceAll("%S", goodsDto.getName()).replaceAll("%N", value.toString());

        if (PlatformSetMealTypeEnum.INCREASE_FEE.getMealCode().equals(setMealDto.getCode())) {
            // 增值服务
            //购买%N%S%S
            String unit = setMealDto.getUnit();
            String detailCode = setMealDto.getPlatformPermissionList().get(0).getCode();
            if (MATERIAL_CAPACITY_CODE.equals(detailCode)) {
                orderName = "购买" + value + unit + "/" +
                        goodOrder.getNumPeriod() + setMealDto.getPeriodUnits() + goodsDto.getName();
            } else {
                orderName = "购买" + value + unit + goodsDto.getName();
            }
        }
        this.insertMyBill(goodOrder, orderName, orderType, purposeTypeStatus.PAY.getStatus(), goodOrder.getMerchantId());
    }

    public Payment insertPayment(Long merchantId, String imgUrl, String paymentMethod, Double totalAmount, String outOrderNo, Integer payType, Double balance, Double freeGold) {
        Payment payment = new Payment();
        payment.setNo(outOrderNo);
        payment.setImgUrl(imgUrl);
        payment.setMethod(paymentMethod);
        payment.setTotalAmount(totalAmount);
        if (BALANCE_PAY.equals(paymentMethod)) {
            payment.setPayTime(System.currentTimeMillis());
        }
        payment.setCreatedTime(System.currentTimeMillis());
        payment.setPayType(payType);
        payment.setBalance(balance);
        payment.setFreeGold(freeGold);
        payment.setMerchantId(merchantId);
        this.paymentService.save(payment);
        return payment;
    }

    public GenSetMealParamReqDto getParam(Long merchantId, PlatformGoodOrder platformGoodOrder) {
        GenSetMealParamReqDto genSetMealParam = new GenSetMealParamReqDto();
        genSetMealParam.setMerchantId(merchantId);
        genSetMealParam.setNum(platformGoodOrder.getValue());
        genSetMealParam.setPlatformGoodOrderId(platformGoodOrder.getId());
        genSetMealParam.setPlatformGoodsId(platformGoodOrder.getPlatformGoodsId());
        genSetMealParam.setPlatformPermissionSetMealId(platformGoodOrder.getPlatformPermissionSetMealId());
        return genSetMealParam;
    }


    public MerchantBill insertMyBill(PlatformGoodOrder platformGoodOrder, String orderName, String orderType, Integer purposeType, Long currentUserId) {
        return this.insertMyBill(platformGoodOrder, orderName, orderType, purposeType, null, null, currentUserId);
    }

    public MerchantBill insertMyBill(PlatformGoodOrder platformGoodOrder, String orderName, String orderType, Integer purposeType, Double leffBalance, Double leftFreeGold, Long currentUserId) {
        Merchant merchant = this.merchantService.findById(platformGoodOrder.getMerchantId());
        MerchantBill merchantBill = new MerchantBill();
        merchantBill.setUserId(platformGoodOrder.getUserId());
        if (ALI_PAY.equals(platformGoodOrder.getPaymentMethod())) {
            merchantBill.setAccount(ALLPAY);
        } else {
            merchantBill.setAccount(PLATFORMACCOUNT);
        }
        merchantBill.setCreatedTime(new Date());
        merchantBill.setOrderNo(platformGoodOrder.getNo());
        merchantBill.setOrderMoney(platformGoodOrder.getTotalAmount());
        merchantBill.setTotalMoney(leffBalance == null ? Decimal.valueOf(merchant.getBalance()) : Decimal.valueOf(leffBalance));
        merchantBill.setFreeGold(leftFreeGold == null ? BigDecimal.valueOf(merchant.getFreeGold()) : BigDecimal.valueOf(leftFreeGold));
        merchantBill.setOrderName(orderName);
        merchantBill.setSpecificPurpose(orderType);
        merchantBill.setPurpose(purposeType);
        merchantBill.setRemarks(platformGoodOrder.getRemark());
        merchantBill.setMerchantId(platformGoodOrder.getMerchantId());
        merchantBill.setPurchaserId(currentUserId);
        if (platformGoodOrder.getUsableFreeGold() != null) {
            merchantBill.setUsableFreeGold(BigDecimal.valueOf(platformGoodOrder.getUsableFreeGold()));
        }
        this.merchantBillDao.insertSelective(merchantBill);
        return merchantBill;
    }


    @Async
    public void paySuccess(Long paymentId) {
        this.platformGoodOrderDao.updateStatusByPaymentId(paymentId, OrderStatus.PAIN.getStatus());
        PlatformGoodOrder platformGoodOrder = this.platformGoodOrderDao.selectOneByPaymentId(paymentId);
        // 更新该用户的状态
        this.balanceAliCommon(platformGoodOrder);
        //插入套餐
        GenSetMealParamReqDto genSetMealParamReqDto = this.getParam(platformGoodOrder.getMerchantId(), platformGoodOrder);
        genSetMealParamReqDto.setNumPeriod(null == platformGoodOrder.getNumPeriod() ? 1 : platformGoodOrder.getNumPeriod());
        genSetMealParamReqDto.setOptionType(PlatformSetMealConstant.OPTION_TYPE_RENEW);
        genSetMealParamReqDto.setCode(null);
//        List<MerchantPlatformPermissionDto> permissionDtoList =
        this.merchantPlatformPermissionSetMealFeign.generateSetMeal(platformGoodOrder.getMerchantId(), genSetMealParamReqDto);
//        this.merchantPlatformPermissionFeign.capacityReorderByPermissions(platformGoodOrder.getMerchantId(), new BaseListDto<>(permissionDtoList));
        //更新购买时间
        if (this.merchantPlatformPermissionSetMealFeign.isFeeMember(0L, platformGoodOrder.getPlatformPermissionSetMealId())) {
            Long time = System.currentTimeMillis();
            this.merchantDao.updateLastByGoodsTime(platformGoodOrder.getMerchantId(), time);
        }
    }

}
