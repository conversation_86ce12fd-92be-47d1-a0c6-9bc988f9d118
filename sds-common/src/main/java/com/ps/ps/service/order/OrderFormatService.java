package com.ps.ps.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.beust.jcommander.internal.Maps;
import com.google.common.collect.Sets;
import com.ps.dto.order.PodOrderDTO;
import com.ps.ps.feign.MerchantFeign;
import com.ps.ps.feign.logistics.TenantLogisticsFeign;
import com.ps.ps.feign.order.*;
import com.ps.ps.feign.tenant.TenantFeign;
import com.ps.ps.service.MerchantStoreService;
import com.ps.ps.service.OrderService;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.logisticsapi.constant.TenantLogisticsConstant;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderapi.dto.tenant.TenantLogisticsOrderRespDto;
import com.sdsdiy.orderdata.dto.onlineorder.PlatformOrderExtendDto;
import com.sdsdiy.orderdata.dto.order.item.OrderItemCancelQtyDTO;
import com.sdsdiy.orderdata.dto.parcel.ParcelLogisticsDTO;
import com.sdsdiy.orderdata.enums.OrderItemChangeTypeEnum;
import com.sdsdiy.orderdata.enums.OrderStatus;
import com.sdsdiy.userapi.constant.enums.DistributionProductLogisticsSourceEnum;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.tenant.TenantListReq;
import com.sdsdiy.userapi.dto.tenant.TenantRespDto;
import com.ziguang.base.dto.FactoryOrderDto;
import com.ziguang.base.model.*;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: bin_lin
 * @date: 2022/7/20 16:11
 * @desc:
 */
@Service
@RequiredArgsConstructor
public class OrderFormatService {
    private final OrderItemChangeHistoryFeign orderItemChangeHistoryFeign;
    private final OrderParcelFeign orderParcelFeign;
    private final OrderImportExtraInfoFeign orderImportExtraInfoFeign;
    private final OrderExtendInfoFeign orderExtendInfoFeign;
    @Resource
    private TenantLogisticsOrderFeign tenantLogisticsOrderFeign;
    @Resource
    private OrderAmountFeign orderAmountFeign;
    @Resource
    private TenantFeign tenantFeign;
    @Resource
    private TenantLogisticsFeign tenantLogisticsFeign;
    @Resource
    @Lazy
    private MerchantStoreService merchantStoreService;
    @Resource
    private PlatformOrderExtendFeign platformOrderExtendFeign;
    @Resource
    @Lazy
    private OrderService orderService;
    @Resource
    private MerchantFeign merchantFeign;

    public void formatShipmentBelongingTenantName(List<Order> orders) {
        if (CollUtil.isEmpty(orders)) {
            return;
        }
        Set<Long> tenantIds = Sets.newHashSet();
        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        IdsSearchHelper idsSearchHelper = new IdsSearchHelper();
        idsSearchHelper.setIds(orderIds);

        List<OrderAmountRespDTO> orderAmounts = this.orderAmountFeign.findByIds(idsSearchHelper);
        tenantIds.addAll(orderAmounts.stream().map(OrderAmountRespDTO::getProductTenantId).collect(Collectors.toSet()));
        tenantIds.addAll(orderAmounts.stream().map(OrderAmountRespDTO::getTenantId).collect(Collectors.toSet()));

        Map<Long, OrderAmountRespDTO> amountMap = orderAmounts.stream().collect(Collectors.toMap(OrderAmountRespDTO::getId, Function.identity()));
        Map<Long, TenantLogisticsOrderRespDto> tenantLogisticsOrderMap = Maps.newHashMap();
        //分销订单
        List<Order> orderOrderTenants = orders.stream().filter(o -> DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(o.getLogisticsSource())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(orderOrderTenants)) {
            List<Long> orderOrderTenantIds = orderOrderTenants.stream().map(Order::getId).collect(Collectors.toList());
            List<TenantLogisticsOrderRespDto> tenantLogisticsOrders = this.tenantLogisticsOrderFeign.getByIds(orderOrderTenantIds);
            tenantLogisticsOrderMap = tenantLogisticsOrders.stream().collect(Collectors.toMap(TenantLogisticsOrderRespDto::getId, Function.identity()));
        }
        List<TenantRespDto> tenants = this.tenantFeign.getByIds(new ArrayList<>(tenantIds));
        List<Long> logisticsIds = orders.stream().map(Order::getLogisticsId).filter(logisticsId -> logisticsId != 0).collect(Collectors.toList());
        Map<Long, TenantLogisticsRespDto> logisticsIdMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(logisticsIds)) {
            logisticsIdMap = this.tenantLogisticsFeign.findByIds(new BaseListReqDto(logisticsIds)).stream().collect(Collectors.toMap(TenantLogisticsRespDto::getId, Function.identity()));
        }
        Map<Long, String> tenantIdKeyNameMap = tenants.stream().collect(Collectors.toMap(TenantRespDto::getId, TenantRespDto::getName));
        for (Order order : orders) {
            TenantLogisticsRespDto tenantLogistics = logisticsIdMap.get(order.getLogisticsId());
            if (tenantLogistics != null) {
                order.setLogistics(BeanUtil.toBean(tenantLogistics, Logistics.class));
            }
            OrderAmountRespDTO amountAmount = amountMap.get(order.getId());
            if (amountAmount == null) {
                continue;
            }
            order.setShipmentBelongingTenantName(tenantIdKeyNameMap.get(amountAmount.getProductTenantId()));
            if (DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(order.getLogisticsSource())) {
                TenantLogisticsOrderRespDto logisticsOrder = tenantLogisticsOrderMap.get(order.getId());
                if (logisticsOrder != null && TenantLogisticsConstant.ShipmentPlaceTypeEnum.TENANT.name().equals(logisticsOrder.getShipmentPlaceType())) {
                    order.setShipmentBelongingTenantName(tenantIdKeyNameMap.get(amountAmount.getTenantId()));
                }
            }
        }
    }

    public void formatMerchantName(List<FactoryOrderDto> factoryOrderDtos){
        if (CollUtil.isEmpty(factoryOrderDtos)) {
            return;
        }
        List<String> orderNos = factoryOrderDtos.stream().map(i -> i.getMerchantOrderNo()).distinct().collect(Collectors.toList());
        List<Order> orders = orderService.findByNos(orderNos);
        Map<String, Order> orderNoMap = ListUtil.toMap(Order::getNo, orders);
        List<Long> orderIds = orders.stream().map(i -> i.getId()).collect(Collectors.toList());
        List<OrderAmountRespDTO> orderAmountRespDTOS = orderAmountFeign.findByIds(IdsSearchHelper.of(orderIds));
        Map<Long, OrderAmountRespDTO> orderAmountMap = ListUtil.toMap(OrderAmountRespDTO::getId, orderAmountRespDTOS);
        List<Long> merchantIds = orders.stream().map(i -> i.getMerchantId()).distinct().collect(Collectors.toList());
        List<MerchantRespDto> merchantRespDtos = merchantFeign.getListByMerchantIds(IdsSearchHelper.of(merchantIds));
        Map<Long, MerchantRespDto> merchantMap = ListUtil.toMap(MerchantRespDto::getId, merchantRespDtos);
        for (FactoryOrderDto factoryOrderDto : factoryOrderDtos) {
            Order order = orderNoMap.get(factoryOrderDto.getMerchantOrderNo());
            OrderAmountRespDTO amountAmount = orderAmountMap.get(order.getId());
            if (amountAmount == null) {
                continue;
            }
            Long tenantId = amountAmount.getTenantId();
            Long productTenantId = amountAmount.getProductTenantId();
            boolean isDistribution = NumberUtils.greaterZero(tenantId) && NumberUtils.greaterZero(productTenantId) && !tenantId.equals(productTenantId);
            if(isDistribution){
                MerchantRespDto merchantRespDto = merchantMap.get(order.getMerchantId());
                factoryOrderDto.setMerchantName(null!=merchantRespDto?merchantRespDto.getMerchantNo():"");
            }
        }
    }

    public void formatOrderItemQty(List<OrderItem> orderItemList) {
        List<Long> orderItemIds = ListUtil.toValueList(OrderItem::getId, orderItemList);
        if (CollUtil.isEmpty(orderItemIds)) {
            return;
        }
        List<OrderItemCancelQtyDTO> cancelQtyList = this.orderItemChangeHistoryFeign.findCancelQtyDtoByOrderItemIds(BaseListDto.of(orderItemIds));
        Map<Long, OrderItemCancelQtyDTO> cancelQtyMap = ListUtil.toMap(OrderItemCancelQtyDTO::getOrderItemId, cancelQtyList);
        for (OrderItem item : orderItemList) {
            OrderItemCancelQtyDTO cancelQtyDTO = cancelQtyMap.get(item.getId());
            if (cancelQtyDTO != null) {
                item.setNum(item.getNum() + cancelQtyDTO.getCancelTotalQty());
                Map<String, Integer> typeQtyMap = cancelQtyDTO.getTypeQtyMap();
                if (typeQtyMap != null) {
                    item.setMerchantCancelQty(typeQtyMap.get(OrderItemChangeTypeEnum.MERCHANT_CANCEL.code));
                    item.setRejectCancel(typeQtyMap.get(OrderItemChangeTypeEnum.QC_REJECT_CANCEL.code));
                    item.setLoseCancel(typeQtyMap.get(OrderItemChangeTypeEnum.QC_LESS_CANCEL.code));
                }
            } else if (OrderStatus.CANCEL.equalsStatus(item.getStatus())) {
                // 兼容旧数据
                item.setMerchantCancelQty(item.getNum());
            }
        }
    }

    public void formatPodOrderParcelLogisticsList(PodOrderDTO podOrderDTO) {
        podOrderDTO.setParcelLogisticsList(this.orderParcelFeign.findParcelLogisticsDTO(podOrderDTO.getId()));
        if (podOrderDTO.getParcelLogisticsList().size() > 1) {
            Logistics logistics = podOrderDTO.getLogistics();
            if (logistics != null) {
                ParcelLogisticsDTO logisticsDTO = new ParcelLogisticsDTO();
                logisticsDTO.setParcelId(0L);
                logisticsDTO.setParcelName("原物流");
                logisticsDTO.setLogisticsId(logistics.getId());
                logisticsDTO.setCarriageNo(podOrderDTO.getCarriageNo());
                logisticsDTO.setCarriageName(logistics.getName());
                logisticsDTO.setCarriageAmount(BigDecimal.valueOf(podOrderDTO.getCarriageAmount()));
                logisticsDTO.setServiceProviderId(logistics.getServiceProviderId());
                podOrderDTO.getParcelLogisticsList().add(0, logisticsDTO);
            }
        }
    }

    public void formatPodOrderDtoExtraInfo(PodOrderDTO podOrderDTO) {
        podOrderDTO.setImportExtraInfo(this.orderImportExtraInfoFeign.findById(podOrderDTO.getId(), ""));
        podOrderDTO.setExtendMap(this.orderExtendInfoFeign.mapByOrderId(podOrderDTO.getId()));
    }

    public void formatMerchantStore(PodOrderDTO podOrderDTO){
        if(null==podOrderDTO.getMerchantStore()){
            MerchantStore merchantStore = this.merchantStoreService.get(podOrderDTO.getMerchantStoreId());
            if(null!=merchantStore){
                MerchantStore store=new MerchantStore();
                store.setId(merchantStore.getId());
                store.setName(merchantStore.getName());
                store.setMerchantStorePlatformCode(merchantStore.getMerchantStorePlatformCode());
                store.setIsPopChoice(merchantStore.getIsPopChoice());
                store.setExpressBindLogisticId(merchantStore.getExpressBindLogisticId());
                store.setBindLogisticId(merchantStore.getBindLogisticId());
                podOrderDTO.setMerchantStore(store);
            }
        }
    }

    public void formatPlatformExtendMap(PodOrderDTO podOrderDTO) {
        List<PlatformOrderExtendDto> extendList = platformOrderExtendFeign.getExtendList(podOrderDTO.getOutOrderNo(),podOrderDTO.getOriginType());
        Map<String, String> extendKeyMap = extendList.stream()
                .collect(Collectors.toMap(PlatformOrderExtendDto::getExtendKey, PlatformOrderExtendDto::getExtendValue, (a, b) -> b));
        podOrderDTO.setPlatformExtendMap(extendKeyMap);
    }
    public void formatMerchant(Long currentTenantId,PodOrderDTO podOrderDTO) {
        Merchant merchant = podOrderDTO.getMerchant();
        Merchant simpleMerchant=new Merchant();
        boolean isDistribution = !podOrderDTO.getTenantId().equals(podOrderDTO.getProductTenantId());
        boolean sdsdiy = TenantCommonConstant.isSdsdiy(currentTenantId);
        String name = isDistribution && !sdsdiy? merchant.getMerchantNo() : merchant.getName();
        simpleMerchant.setName(name);
        simpleMerchant.setId(merchant.getId());
        podOrderDTO.setMerchant(simpleMerchant);
    }

    public void formatDistributorTenantName(Long tenantId, List<FactoryOrderDto> items) {
        if (CollUtil.isEmpty(items)) {
            return;
        }
        List<Long> distributorTenantIds = items
                .stream()
                .map(FactoryOrderDto::getTenantId)
                .filter(a -> !tenantId.equals(a))
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(distributorTenantIds)) {
            return;
        }
        TenantListReq req = new TenantListReq();
        req.setInIds(distributorTenantIds);
        List<TenantRespDto> tenantRespDtos = tenantFeign.listDto(req);
        Map<Long, String> distributorIdNameMap = tenantRespDtos.stream().collect(Collectors.toMap(TenantRespDto::getId, TenantRespDto::getName));
        for (FactoryOrderDto dto : items) {
            dto.setDistributorName(distributorIdNameMap.get(dto.getTenantId()));
        }
    }
    public void formatDistributorTenantName(Long tenantId, FactoryOrderDto dto) {
        if (Objects.isNull(dto)) {
            return;
        }
        Long distributorTenantId = dto.getTenantId();
        if (TenantCommonConstant.isSdsdiy(tenantId) || !NumberUtils.greaterZero(distributorTenantId) || tenantId.equals(distributorTenantId)) {
            return;
        }

        TenantListReq req = new TenantListReq();
        req.setInIds(Collections.singletonList(distributorTenantId));
        List<TenantRespDto> tenantRespDtos = tenantFeign.listDto(req);
        Map<Long, String> distributorIdNameMap = tenantRespDtos.stream().collect(Collectors.toMap(TenantRespDto::getId, TenantRespDto::getName));
        dto.setDistributorName(distributorIdNameMap.get(dto.getTenantId()));
    }
}
