package com.ps.ps.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ps.amazon.s3.S3ServiceV2;
import com.ps.base.dao.TkExample;
import com.ps.base.entity.SearchBean;
import com.ps.base.service.TKBaseService;
import com.ps.dto.AfterServiceAuditPageParam;
import com.ps.dto.OrderRefundParam;
import com.ps.exception.BusinessException;
import com.ps.ps.dao.*;
import com.ps.ps.feign.FactoryOrderCreateTaskFeign;
import com.ps.ps.feign.OrderAmountHistoryFeign;
import com.ps.ps.feign.OrderCancelFeign;
import com.ps.ps.feign.ProductFeign;
import com.ps.ps.feign.application.ApplicationFeign;
import com.ps.ps.feign.logistics.CountryExpressInfoNewFeign;
import com.ps.ps.feign.order.*;
import com.ps.ps.feign.payment.AdminOperateWalletBalanceFeign;
import com.ps.ps.feign.product.FlowStepUserLogFeign;
import com.ps.ps.feign.product.ProductSupplyFeign;
import com.ps.ps.feign.tenant.TenantFeign;
import com.ps.ps.feign.user.TenantSysUserFeign;
import com.ps.ps.service.linstener.OrderEventService;
import com.ps.support.Assert;
import com.ps.support.IdGenerator;
import com.ps.support.utils.ConvertUtil;
import com.ps.support.utils.MathUtils;
import com.ps.support.utils.OrderCodeUtil;
import com.ps.support.utils.StringUtils;
import com.ps.system.dao.MerchantUserDao;
import com.ps.tool.DataExportHistoryUtil;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseDownloadDTO;
import com.sdsdiy.common.base.entity.dto.BaseIdQtyDTO;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.IdMsg;
import com.sdsdiy.common.base.enums.ApplicationCodeEnum;
import com.sdsdiy.common.base.enums.PlatformPermissionCodeEnum;
import com.sdsdiy.common.base.enums.SdsPlatformEnum;
import com.sdsdiy.common.base.helper.BeanUtils;
import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.logisticsapi.constant.LogisticsConstant;
import com.sdsdiy.logisticsapi.constant.TenantLogisticsConstant;
import com.sdsdiy.logisticsapi.dto.LogisticsFreightRespDto;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.LogisticsAndLogisticsSourceResp;
import com.sdsdiy.logisticsapi.enums.LogisticsCodeIdEnum;
import com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum;
import com.sdsdiy.logisticsdata.dto.base.CountryExpressInfoNewRespDto;
import com.sdsdiy.logisticsdata.util.CarriageAmountUtil;
import com.sdsdiy.orderapi.constant.AddressConstant;
import com.sdsdiy.orderapi.constant.EnumOrderItemTransferType;
import com.sdsdiy.orderapi.constant.EnumOrderPayType;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.constant.event.message.admin.AdminAdoptRefundAfterSaleOrderMessage;
import com.sdsdiy.orderapi.constant.event.message.admin.AdminAdoptResendAfterSaleOrderMessage;
import com.sdsdiy.orderapi.constant.event.message.admin.AdminRejectRefundAfterSaleOrderMessage;
import com.sdsdiy.orderapi.constant.event.message.admin.AdminRejectResendAfterSaleOrderMessage;
import com.sdsdiy.orderapi.constant.order.OrderAmountHistoryConstant;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderapi.dto.OrderItemMapDto;
import com.sdsdiy.orderapi.dto.order.*;
import com.sdsdiy.orderapi.dto.orderitem.OrderItemFactoryReqDto;
import com.sdsdiy.orderapi.dto.tenant.TenantLogisticsOrderReqDto;
import com.sdsdiy.orderapi.dto.tenant.TenantLogisticsOrderRespDto;
import com.sdsdiy.orderapi.enums.OrderOriginEnum;
import com.sdsdiy.orderdata.constant.order.AfterServiceAuditStatusEnum;
import com.sdsdiy.orderdata.constant.order.AfterServiceAuditTypeEnum;
import com.sdsdiy.orderdata.constant.order.SupplyChainTypeEnum;
import com.sdsdiy.orderdata.consts.FlowStepLogTypeEnum;
import com.sdsdiy.orderdata.dto.OrderImportExtraInfoDto;
import com.sdsdiy.orderdata.dto.*;
import com.sdsdiy.orderdata.dto.msg.OrderRefreshMsg;
import com.sdsdiy.orderdata.dto.order.OrderItemSupplyChainDTO;
import com.sdsdiy.orderdata.dto.order.amount.OrderAmountCalResultDTO;
import com.sdsdiy.orderdata.dto.order.amount.OrderRefundReqDTO;
import com.sdsdiy.orderdata.dto.order.amount.PodOrderRefundReqDTO;
import com.sdsdiy.orderdata.dto.order.refund.OrderRefundAdviseRespDTO;
import com.sdsdiy.orderdata.dto.parcel.message.OrderParcelPackMessageDTO;
import com.sdsdiy.orderdata.dto.product.flow.FlowLogProceduresReqDTO;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import com.sdsdiy.paymentapi.param.TenantAmountChangeParam;
import com.sdsdiy.paymentapi.param.distribution.AdminOperateWalletBalanceParam;
import com.sdsdiy.productapi.dto.ProductSupplyDTO;
import com.sdsdiy.productapi.dto.product.ProductPriceResp;
import com.sdsdiy.productapi.myenum.EnumProductSupplyBackupType;
import com.sdsdiy.productdata.dto.price.OrderItemProductPriceDto;
import com.sdsdiy.productdata.dto.price.OrderProductPriceParam;
import com.sdsdiy.productdata.enums.ProductPreferentialActivityOrderType;
import com.sdsdiy.userapi.constant.EnumNotificationTitle;
import com.sdsdiy.userapi.constant.enums.DistributionProductLogisticsSourceEnum;
import com.sdsdiy.userapi.dto.application.TenantApplicationScenarioRelRespDto;
import com.sdsdiy.userapi.dto.tenant.TenantConsumePermissionReqDto;
import com.sdsdiy.userapi.dto.tenant.TenantReturnPermissionReqDto;
import com.sdsdiy.userapi.dto.tenant.req.TenantApplicationReqDto;
import com.sdsdiy.userapi.dto.tenant.req.TenantGroupConsumePermissionReqDto;
import com.sdsdiy.userapi.dto.tenant.req.TenantGroupReturnPermissionReqDto;
import com.ziguang.base.constant.AdminConfigsConst;
import com.ziguang.base.dto.AfterServiceAuditItemDto;
import com.ziguang.base.dto.FactoryOrderDto;
import com.ziguang.base.dto.*;
import com.ziguang.base.model.*;
import com.ziguang.base.support.DoubleUtils;
import com.ziguang.base.support.JsonUtil;
import com.ziguang.base.support.contant.*;
import com.ziguang.base.vo.AfterServiceAuditVo;
import com.ziguang.base.vo.AfterServiceSubmitVo;
import io.seata.spring.annotation.GlobalTransactional;
import io.seata.tm.api.transaction.TransactionHookAdapter;
import io.seata.tm.api.transaction.TransactionHookManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.NO;
import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.orderdata.constant.order.AfterServiceAuditTypeEnum.AFTER_SERVICE_REFUND;
import static com.sdsdiy.orderdata.constant.order.AfterServiceAuditTypeEnum.AFTER_SERVICE_RESEND;

/**
 * <AUTHOR>
 * 2019/11/21 0021
 */
@Slf4j
@Service
public class AfterServiceAuditService extends TKBaseService<AfterServiceAuditDao, AfterServiceAudit> {

    @Autowired
    FactoryOrderCreateTaskFeign factoryOrderCreateTaskFeign;
    @Autowired
    @Lazy
    private RefundService refundService;
    @Autowired
    private OrderImportExtraInfoFeign orderImportExtraInfoFeign;

    @Resource
    @Lazy
    private AfterServiceManage afterServiceManage;

    @Lazy
    @Resource
    private OrderService orderService;

    @Autowired
    OrderCarriageFeign orderCarriageFeign;
    @Autowired
    private OrderDao orderDao;

    @Autowired
    private OrderItemDao orderItemDao;

    @Autowired
    @Lazy
    private PaymentService paymentService;

    @Autowired
    private AfterServiceAuditItemDao afterServiceAuditItemDao;

    @Autowired
    private MerchantDao merchantDao;

    @Autowired
    private MerchantUserDao merchantUserDao;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private FactoryOrderDao factoryOrderDao;

    @Lazy
    @Autowired
    private FactoryOrderService factoryOrderService;

    @Autowired
    @Lazy
    private MerchantService merchantService;
    @Autowired
    private TenantSysUserFeign tenantSysUserFeign;

    @Autowired
    @Lazy
    private ProductService productService;

    @Autowired
    @Lazy
    private CountryExpressInfoService countryExpressInfoService;

    
    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private OrderEventService orderEventService;


    @Autowired
    private OrderRemarkService orderRemarkService;

    @Autowired
    private OrderSearchService orderSearchService;
    @Autowired
    private TenantLogisticsOrderFeign tenantLogisticsOrderFeign;
    @Autowired
    private ProductFeign productFeign;
    @Autowired
    private AdminOperateWalletBalanceFeign adminOperateWalletBalanceFeign;
    @Autowired
    private ApplicationFeign applicationFeign;
    @Autowired
    private TenantFeign tenantFeign;
    @Autowired
    private OrderAmountFeign orderAmountFeign;
    @Resource
    private OrderItemPriceFeign orderItemPriceFeign;
    @Resource
    private OrderManualLogisticsFeign orderManualLogisticsFeign;
    @Resource
    private FlowStepUserLogFeign flowStepUserLogFeign;
    @Resource
    private OrderAmountHistoryFeign orderAmountHistoryFeign;
    @Resource
    private OrderCancelFeign orderCancelFeign;
    @Resource
    private OrderItemService orderItemService;
    @Resource
    private OrderAmountDetailFeign orderAmountDetailFeign;
    @Resource
    private OrderItemFactoryFeign orderItemFactoryFeign;
    @Resource
    private DataExportHistoryUtil dataExportHistoryUtil;
    @Autowired
    private OrderItemMapService orderItemMapService;
    /**
     * type
     * 退款类型:1 取消退款 2售后退款 3售后重发货物
     */
    public static final int TYPE_CANCEL_REFUND = 1;
    public static final int TYPE_AFTER_SERVICE_REFUND = 2;
    public static final int TYPE_AFTER_SERVICE_RESEND = 3;

    /**
     * status
     * 状态: 1处理中 2待财务打款 3处理成功 4处理失败
     */
    public static final int STATUS_DISPOSING = 1;
    public static final int STATUS_REMITTING = 2;
    public static final int STATUS_SUCCESS = 3;
    public static final int STATUS_REFUSED = 4;

    /**
     * item status
     * 状态: 1处理中 2处理成功 4处理失败
     */
    public static final int ITEM_STATUS_DISPOSING = 1;
    public static final int ITEM_STATUS_SUCCESS = 2;
    public static final int ITEM_STATUS_REFUSED = 4;

    public static final int DUTY_AFFILIATION_ADMIN = 1;
    public static final int DUTY_AFFILIATION_FACTORY = 2;
    public static final int DUTY_AFFILIATION_TENANT = 3;

    public static final int MAX_EXPORT_NUM = 10000;

    public boolean isReturnOrderQuotaDuty(Integer duty) {
        return DUTY_AFFILIATION_ADMIN == duty || DUTY_AFFILIATION_FACTORY == duty;
    }

    public List<AfterServiceAudit> findByIds(List<Long> ids) {
        return this.selectByIds(ids);
    }

    /**
     * 导出跟这个用的
     */
    public SearchBean<AfterServiceAuditDto> list(AfterServiceAuditPageParam param) {
        SearchBean<AfterServiceAuditDto> sb = new SearchBean<>(param.getPage(), param.getSize());
        AfterServiceAudit query = new AfterServiceAudit();
        query.setCreatePlatform(param.getRequestPlatform());
        query.setOrderDesignStatus(param.getDesignStatus());
        query.setStatus(param.getStatus());
        query.setTenantId(param.getTenantId());
        query.setProductTenantId(param.getProductTenantId());

        if (StrUtil.isNotEmpty(param.getKeyword())) {
            param.setKeyword("%" + param.getKeyword() + "%");
        }

        String noCol = "no";
        String orderNoCol = "orderNo";
        String merchantNoCol = "merchantNo";
        String createUserContactCol = "createUserContact";
        String createTimeCol = "createTime";
        String orderStatusCol = "orderStatus";
        String isAdvanceCol = "isAdvance";

        TkExample example = new TkExample(AfterServiceAudit.class);
        if (!StringUtils.isBlank(param.getKeyword())) {
            if (SdsPlatformEnum.POD.getCode().equalsIgnoreCase(param.getSdsPlatform())) {
                example.and(example.createCriteria()
                        .andLike(orderNoCol, param.getKeyword())
                        .orLike(noCol, param.getKeyword()));
            } else {
                example.and(example.createCriteria()
                        .andLike(orderNoCol, param.getKeyword())
                        .orLike(noCol, param.getKeyword())
                        .orLike(merchantNoCol, param.getKeyword())
                        .orLike(createUserContactCol, param.getKeyword()));
            }
        }
        if (param.getOrderStatus() != null) {
            if (OrderStatus.FINISH.getStatus() == param.getOrderStatus()) {
                example.and(example.createCriteria().andEqualTo(orderStatusCol, param.getOrderStatus()).orEqualTo(isAdvanceCol,
                        OrderService.ADVANCE));
            } else {
                example.and(example.createCriteria().andEqualTo(orderStatusCol, param.getOrderStatus()).andEqualTo(isAdvanceCol,
                        OrderService.NO_ADVANCE));
            }
        }
        if (YES.equals(param.getIsLastThreeMonths())) {
            example.and(example.createCriteria().andGreaterThanOrEqualTo(createTimeCol, DateUtil.offsetMonth(new Date(), -3)));
        } else if (NO.equals(param.getIsLastThreeMonths())) {
            example.and(example.createCriteria().andLessThan(createTimeCol, DateUtil.offsetMonth(new Date(), -3)));
        }

        if (param.getAuditStartTime() != null) {
            example.and(example.createCriteria().andGreaterThan("auditTime", param.getAuditStartTime().getTime()));
        }

        if (param.getAuditEndTime() != null) {
            example.and(example.createCriteria().andLessThan("auditTime", param.getAuditEndTime().getTime()));
        }
        if (SdsPlatformEnum.POD.getCode().equalsIgnoreCase(param.getSdsPlatform())) {
            if (param.getType() == null) {
                example.and(example.createCriteria().andIn("type", Arrays.asList(TYPE_AFTER_SERVICE_REFUND, TYPE_AFTER_SERVICE_RESEND)));
            } else {
                query.setType(param.getType());
            }
        } else {
            if (param.getType() == null || 1 == param.getType()) {
                query.setType(param.getType());
            } else {
                example.and(example.createCriteria().andIn("type", Arrays.asList(TYPE_AFTER_SERVICE_REFUND, TYPE_AFTER_SERVICE_RESEND)));
            }
        }
        example.and(example.createCriteria().andBetween(createTimeCol, param.getStartTime(), param.getEndTime()).andEqualTo(query));
        example.setOrderBySort(param.getSort());

        example.setSearchBeanContent(this.dao, sb, AfterServiceAuditDto.class);

        this.formatPaymentType(sb);
        this.formatMerchantAndTenant(sb, param.getSdsPlatform());
        if (param.getFormatLogisticsSourceAndShip()) {
            this.formatLogisticsSourceAndShipMethod(sb.getItems());
        }
        return sb;
    }

    private void formatMerchantAndTenant(SearchBean<AfterServiceAuditDto> sb, String sdsPlatform) {
        if (CollUtil.isEmpty(sb.getItems()) || !SdsPlatformEnum.POD.getCode().equalsIgnoreCase(sdsPlatform)) {
            return;
        }
        Set<Long> tenantIds = sb.getItems().stream().map(AfterServiceAuditDto::getTenantId).collect(Collectors.toSet());
        Set<Long> merchantIds = sb.getItems().stream().map(AfterServiceAuditDto::getMerchantId).collect(Collectors.toSet());
        Map<Long, String> tenantNameMap = ListUtil.toMapByBaseIdAndName(this.tenantFeign.findNameByIds(BaseListDto.of(tenantIds)));
        Map<Long, String> merchantIdKeyNoMap = merchantService.findByIds(merchantIds).stream().collect(Collectors.toMap(m -> m.getId(), m -> m.getMerchantNo()));
        for (AfterServiceAuditDto item : sb.getItems()) {
            item.setTenantName(tenantNameMap.getOrDefault(item.getTenantId(), ""));
            item.setMerchantNoCode(merchantIdKeyNoMap.getOrDefault(item.getMerchantId(), ""));
        }

    }

    private void formatPaymentType(SearchBean<AfterServiceAuditDto> sb) {
        if (CollUtil.isEmpty(sb.getItems())) {
            return;
        }
        List<Long> orderIds = sb.getItems().stream().map(AfterServiceAuditDto::getOrderId).collect(Collectors.toList());
        IdsSearchHelper idsSearchHelper = new IdsSearchHelper();
        idsSearchHelper.setIds(orderIds);
        List<OrderAmountRespDTO> orderAmountRespDTOS = orderAmountFeign.findByIds(idsSearchHelper);
        Map<Long, OrderAmountRespDTO> orderIdOrderAmountMap = orderAmountRespDTOS.stream().collect(Collectors.toMap(i -> i.getId(), i -> i, (a, b) -> b));
        for (AfterServiceAuditDto item : sb.getItems()) {
            if (orderIdOrderAmountMap.get(item.getOrderId()) == null) {
                continue;
            }
            item.setPaymentType(orderIdOrderAmountMap.get(item.getOrderId()).getPaymentType());
        }
    }


    private void formatLogisticsSourceAndShipMethod(List<AfterServiceAuditDto> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<Long> orderIds = items.stream().map(AfterServiceAuditDto::getOrderId).collect(Collectors.toList());
        List<Order> orders = orderService.findByIds(orderIds);
        Map<Long, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        for (AfterServiceAuditDto item : items) {
            item.setLogisticsSource(orderMap.getOrDefault(item.getOrderId(), new Order()).getLogisticsSource());
            item.setShipmentPlaceType(TenantLogisticsConstant.ShipmentPlaceTypeEnum.ISSUING_BAY.name());
        }
        List<Long> orderTenantOrderIds = orders.stream()
                .filter(o -> DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(o.getLogisticsSource()))
                .map(Order::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderTenantOrderIds)) {
            return;
        }
        List<TenantLogisticsOrderRespDto> tenantLogisticsOrders = tenantLogisticsOrderFeign.getByIds(orderTenantOrderIds);
        Map<Long, TenantLogisticsOrderRespDto> respDtoMap = tenantLogisticsOrders.stream().collect(Collectors.toMap(TenantLogisticsOrderRespDto::getId, Function.identity()));
        for (AfterServiceAuditDto item : items) {
            if (respDtoMap.get(item.getOrderId()) == null) {
                continue;
            }
            item.setShipmentPlaceType(respDtoMap.get(item.getOrderId()).getShipmentPlaceType());
        }
    }


    public AfterServiceAuditDto getById(Long id) {
        AfterServiceAuditDto one = ConvertUtil.dtoConvert(this.dao.selectByPrimaryKey(id), AfterServiceAuditDto.class);
        this.ORM(one);
        RefundRecordDto record = refundService.findByAfterServiceAuditId(one.getId());
        getResendForLose(one);
        one.setRefundUsedFreeGold(0D);
        if (record != null) {
            one.setRefundUsedFreeGold(record.getFreeGold());
            one.setRefundTenantUsedFreeGold(record.getTenantFreeGold());
        }
        Map<Long, OrderItem> orderItemMap = Maps.newHashMap();
        if (one.getOrder() != null) {
            formatLogisticsId(one.getOrder());
            orderService.format(one.getOrder());
            for (OrderItem item : one.getOrder().getItems()) {
                orderItemMap.put(item.getId(), item);
            }
        }
        if (one.getResendOrder() != null) {
            orderService.format(one.getResendOrder());
        }
        if (one.getItems() != null && one.getItems().size() > 0) {
            List<FactoryOrder> factoryOrders = factoryOrderService.findByOrderItemIds(one.getItems().stream().map(AfterServiceAuditItemDto::getOrderItemId).collect(Collectors.toList()));
            for (AfterServiceAuditItemDto dto : one.getItems()) {
                if (orderItemMap.get(dto.getOrderItemId()) != null) {
                    dto.setOrderItem(orderItemMap.get(dto.getOrderItemId()));
                }
                for (FactoryOrder factoryOrder : factoryOrders) {
                    if (dto.getOrderItemId().equals(factoryOrder.getOrderItemId())) {
                        dto.setFactoryOrderDto(ConvertUtil.dtoConvert(factoryOrder, FactoryOrderDto.class));

                    }
                }
            }
        }

        //物流信息
        if (one.getAddress() != null && one.getCountryExpressInfoId() > 0) {
            one.setCarriageInfo(countryExpressInfoService.findReferByLogisticsId(extractReferDto(one), one.getOrder().getLogisticsId()));
        }
        return one;
    }

    private void formatLogisticsId(Order order) {
        if (null == order) {
            return;
        }
        if (order.getLogisticsId() > 0) {
            return;
        }
        OrderManualLogisticsRespDto orderManualLogisticsRespDto = orderManualLogisticsFeign.getById(order.getId());
        if (null != orderManualLogisticsRespDto && orderManualLogisticsRespDto.getOriginLogisticsId() != null) {
            order.setLogisticsId(orderManualLogisticsRespDto.getOriginLogisticsId());
        }
    }

    @Autowired
    AfterServiceAuditAdminImgService afterServiceAuditAdminImgService;

    /**
     * 填充补件订单ITEM（售后重发完成）
     *
     * @param one
     */
    private void getResendForLose(AfterServiceAuditDto one) {
        if (one.getStatus() == STATUS_SUCCESS) {
            List<OrderItem> rflOrderItems = orderService.findOrderItemByOriginalRflIds(one.getItems().stream().map(AfterServiceAuditItemDto::getOrderItemId).collect(Collectors.toList()));
            List<AfterServiceAuditItemDto> list = new ArrayList<>();
            for (AfterServiceAuditItemDto item : one.getItems()) {
                list.add(item);
                for (OrderItem i : rflOrderItems) {
                    if (item.getOrderItemId().equals(i.getOriginalRflId())
                            && one.getItems().stream().noneMatch(innerI -> innerI.getOrderItemId().equals(i.getId()))) {
                        AfterServiceAuditItemDto cloneItem = ObjectUtil.clone(item);
                        cloneItem.setOrderItem(i);
                        cloneItem.setOrderItemId(i.getId());
                        cloneItem.setRealNum(i.getNum());
                        cloneItem.setRequestNum(i.getNum());
                        list.add(cloneItem);
                    }
                }
            }
            one.setItems(list);
        }
    }

    @Autowired
    FactoryOrderAfterServiceService factoryOrderAfterServiceService;

    @Autowired
    AfterServiceAuditItemDetailService afterServiceAuditItemDetailService;

    public double getProductUsedFreeGold(AfterServiceAuditDto one) {
        if (!PaymentMethod.BALANCEPAY.getValue().equals(one.getOrderPayMethod())) {
            return 0;
        }
        double price = one.getRealRefundProductMoney();
        Order order = one.getOrder();
        orderService.format(order);
        Double productUsedBalancd = 0d;
        Double productUsedGiven = 0d;
        Double productAmount = 0D;
        for (OrderItem orderItem : order.getItems()) {
            productUsedBalancd = DoubleUtils.add(productUsedBalancd, orderItem.getUsedBalance());
            productUsedGiven = DoubleUtils.add(productUsedGiven, orderItem.getUsedFreeGold());
            productAmount = DoubleUtils.add(productAmount, orderItem.getAmount());
        }
        if (!MathUtils.priceEqual(price, productAmount)) {
            Double rate = DoubleUtils.divide(price, productAmount, 4);
            productUsedGiven = DoubleUtils.mul(productUsedGiven, rate);
        }
        return productUsedGiven;
    }

    @GlobalTransactional
    public void testss(String remark) {
        test(remark);
    }

    @GlobalTransactional
    public void auditing(Long id, AfterServiceAuditVo vo, User user) {
        if (StrUtil.isEmpty(vo.getPassword())) {
            Assert.wrong("password can`t empty!");
        }
        if (StringUtils.isNotBlank(vo.getRemark())) {
            vo.setRemark(vo.getRemark().replace("%", "百分比"));
        }
        if (SdsPlatformEnum.POD.getCode().equalsIgnoreCase(vo.getSdsPlatform())) {
            tenantSysUserFeign.checkPasswd(vo.getTenantId(), vo.getPassword());
        } else {
            merchantService.checkPassword(vo.getPassword(), AdminConfigsConst.OPERATION_PASSWORD);
        }
        AfterServiceAudit afterServiceAudit = this.dao.selectByPrimaryKey(id);
        if (afterServiceAudit.getStatus() == STATUS_DISPOSING) {
            afterSaleTreatment(id, vo, user, afterServiceAudit);
        }

    }

    private void test(String remark) {
        AfterServiceAudit afterServiceAudit = new AfterServiceAudit();
        afterServiceAudit.setId(54197L);
        afterServiceAudit.setAuditRemark(remark);
        this.dao.updateByPrimaryKeySelective(afterServiceAudit);
        int a = 0 / 0;
    }

    private void afterSaleTreatment(Long id, AfterServiceAuditVo vo, User user, AfterServiceAudit afterServiceAudit) {
        Map<Long, AfterServiceAuditItemFactoryOrderDto> auditItemFactoryMap = Maps.newHashMap();
        OrderRemarkOperation operation = null;
        Order order = orderService.format(afterServiceAudit.getOrderId());
        List<OrderAmountRespDTO> orderAmountRespDTOS = orderAmountFeign.findByIds(IdsSearchHelper.of(order.getId()));
        Map<Long, OrderAmountRespDTO> longOrderAmountRespDTOMap = ListUtil.toMap(OrderAmountRespDTO::getId, orderAmountRespDTOS);
        OrderAmountRespDTO orderAmountRespDTO = longOrderAmountRespDTOMap.get(order.getId());
        Boolean isDistribution = orderAmountRespDTO.getIsDistribution();

        //物流类型,租户物流且租户发货
        boolean tenantSend = false;
        boolean isTenantLogistics = order.getLogisticsSource().equalsIgnoreCase(DistributionProductLogisticsSourceEnum.ORDER_TENANT.name());
        boolean isSds = TenantCommonConstant.isSdsdiy(order.getTenantId());
        TenantLogisticsOrderRespDto tenantLogisticsOrderRespDto = tenantLogisticsOrderFeign.getById(afterServiceAudit.getOrderId(), "shipmentPlaceType");
        if (isTenantLogistics) {
            tenantSend = tenantLogisticsOrderRespDto.getShipmentPlaceType().equalsIgnoreCase(TenantLogisticsConstant.ShipmentPlaceTypeEnum.TENANT.name());
        }
        if (null != vo.getRealRefundTenantCarriageMoney() && vo.getRealRefundTenantCarriageMoney().compareTo(BigDecimal.ZERO) > 0 &&
                isTenantLogistics && null != vo.getDutyAffiliation() && vo.getDutyAffiliation() == DUTY_AFFILIATION_TENANT) {
            throw new BusinessException("用租户物流的分销订单，租户责任平台不赔付租户物流费");
        }
        //租户物流，才有saas给租户填写赔付物流金额
        if (isSds || !isTenantLogistics) {
            vo.setRealRefundTenantCarriageMoney(null);
        }
        boolean pass = vo.getPass() == 1;
        //售后责任
        Integer dutyAffiliation = vo.getDutyAffiliation();

        //租户物流且租户发货 才有租户责任
        if (pass && !tenantSend && dutyAffiliation == DUTY_AFFILIATION_TENANT) {
            throw new BusinessException("责任错误，非租户发货，没有租户责任");
        }
        // 租户物流佣金比例
        BigDecimal tenantCarriageCommissionRate = BigDecimal.ZERO;
        if (!isSds && !isTenantLogistics){
            // 分销订单，sds物流
            OrderImportExtraInfoDto importExtraInfoDto = orderImportExtraInfoFeign.findById(order.getId(), "");
            if (importExtraInfoDto != null) {
                tenantCarriageCommissionRate = importExtraInfoDto.getTenantCarriageCommissionRate();
            }
        }

        boolean isTenantDuty = (pass && dutyAffiliation == DUTY_AFFILIATION_TENANT);

        List<AfterServiceAuditItem> items = findItemsById(afterServiceAudit.getId());
        List<Long> factoryOrderIds = Lists.newArrayList();
        for (AfterServiceAuditVo.Item voItem : vo.getItems()) {
            for (AfterServiceAuditItemFactoryOrderDto factoryOrder : voItem.getFactoryOrders()) {
                factoryOrderIds.add(factoryOrder.getId());
            }
            for (AfterServiceAuditItem item : items) {
                if (item.getId().equals(voItem.getId())) {
                    item.setFactoryBackupType(voItem.getFactoryBackupType());
                    item.setFactoryOrders(voItem.getFactoryOrders());
                }
            }
        }


        List<FactoryOrder> list = factoryOrderDao.findByIds(factoryOrderIds);
        Map<Long, FactoryOrder> factoryOrderIdMap = list.stream().collect(Collectors.toMap(FactoryOrder::getId, Function.identity()));

        boolean refundFlag = false;
        boolean resendFlag = false;
        for (AfterServiceAuditItem item : items) {
            for (AfterServiceAuditItemFactoryOrderDto factoryOrder : item.getFactoryOrders()) {
                factoryOrder.setFactoryBackupType(item.getFactoryBackupType());
                auditItemFactoryMap.put(factoryOrder.getId(), factoryOrder);

            }
        }
        Map<Long, Integer> factoryOrderRefundNumMap = Maps.newHashMap();
        Order resendNewOrder = null;
        if (!pass) {
            switch (afterServiceAudit.getType()) {
                case TYPE_CANCEL_REFUND:
                    operation = OrderRemarkOperation.REFUSE_CANCEL;
                    break;
                case TYPE_AFTER_SERVICE_REFUND:
                    operation = OrderRemarkOperation.REFUSE_AFTER_SERVICE_REFUND;
                    break;
                case TYPE_AFTER_SERVICE_RESEND:
                    operation = OrderRemarkOperation.REFUSE_AFTER_SERVICE_RESEND;
                    break;
            }
            if (StrUtil.isEmpty(vo.getRemark())) {
                Assert.wrong("remark can`t empty!");
            }
            afterServiceAudit.setStatus(STATUS_REFUSED);
            afterServiceAudit.setRealRefundProductMoney(0D);
            afterServiceAudit.setRealRefundCarriageMoney(0D);
            afterServiceAudit.setRealRefundServiceMoney(0D);
            afterServiceAudit.setRealRefundMaterialServiceMoney(0D);
            afterServiceAudit.setRealRefundAmount(0D);
            for (AfterServiceAuditItem one : items) {
                one.setStatus(ITEM_STATUS_REFUSED);
                one.setRealNum(0);
            }
        } else {
            switch (afterServiceAudit.getType()) {
                case TYPE_CANCEL_REFUND:
                    operation = OrderRemarkOperation.PASS_CANCEL;
                    break;
                case TYPE_AFTER_SERVICE_REFUND:
                    operation = OrderRemarkOperation.PASS_AFTER_SERVICE_REFUND;
                    break;
                case TYPE_AFTER_SERVICE_RESEND:
                    operation = OrderRemarkOperation.PASS_AFTER_SERVICE_RESEND;
                    break;
            }
            if (StrUtil.isEmpty(vo.getRemark())) {
//                        Assert.wrong("remark can`t empty!");
                vo.setRemark("");
            }

            checkParam(vo, afterServiceAudit);
            afterServiceAudit.setCauseType(vo.getCauseType());
            afterServiceAudit.setDutyAffiliation(vo.getDutyAffiliation());
            //子项处理
            for (AfterServiceAuditItem one : items) {
                one.setStatus(ITEM_STATUS_SUCCESS);
                if (afterServiceAudit.getType().equals(TYPE_AFTER_SERVICE_RESEND)) {
                    Optional<AfterServiceAuditVo.Item> itemOptional = vo.getItems().stream().filter(item -> one.getId().equals(item.getId())).findFirst();
                    if (itemOptional.isPresent()) {
                        Integer num = 0;
                        for (AfterServiceAuditItemFactoryOrderDto factoryOrder : itemOptional.get().getFactoryOrders()) {
                            num += factoryOrder.getNum();
                        }
                        one.setRealNum(num);
                        one.setFactoryBackupType(itemOptional.get().getFactoryBackupType());
                    } else {
                        Assert.wrong("One item no match!");
                    }
                    if (one.getRequestNum() < one.getRealNum()) {
                        Assert.wrong("退货数量不能超过申请数量!");
                    }
                } else if (afterServiceAudit.getType().equals(TYPE_AFTER_SERVICE_REFUND)) {
                    Optional<AfterServiceAuditVo.Item> itemOptional = vo.getItems().stream().filter(item -> one.getId().equals(item.getId())).findFirst();
                    if (itemOptional.isPresent()) {
                        if (NumberUtils.greaterZero(itemOptional.get().getNum())) {
                            for (AfterServiceAuditItemFactoryOrderDto factoryOrder : itemOptional.get().getFactoryOrders()) {
                                factoryOrderRefundNumMap.put(factoryOrder.getId(), itemOptional.get().getNum());
                            }
                            one.setRealNum(itemOptional.get().getNum());
                        }
                    } else {
                        Assert.wrong("One item no match!");
                    }
                    if (one.getRequestNum() < one.getRealNum()) {
                        Assert.wrong("退款数量不能超过申请数量!");
                    }
                }

            }

            //退货
            if (afterServiceAudit.getType().equals(TYPE_AFTER_SERVICE_RESEND)) {
                if (vo.getItems().size() != items.size()) {
                    Assert.wrong("The num of item no match!");
                }
                if (vo.getCountryExpressInfoId() == null) {
                    Assert.wrong("物流信息不能为空!");
                }
                vo.getAddress().setMerchantId(afterServiceAudit.getMerchantId());
                vo.getAddress().setUserId(afterServiceAudit.getCreateUserId());
                vo.getAddress().setType(AddressConstant.TypeEnum.NORMAL.getCode());
                vo.getAddress().setId(null);
                checkAddress(vo.getAddress());
                Address address = orderService.getOrSaveAddress(vo.getAddress());
                afterServiceAudit.setAddressId(address.getId());
                afterServiceAudit.setCountryExpressInfoId(vo.getCountryExpressInfoId());
                afterServiceAudit.setStatus(STATUS_SUCCESS);
                afterServiceAudit.setRealRefundProductMoney(0D);
                afterServiceAudit.setRealRefundCarriageMoney(0D);
                afterServiceAudit.setRealRefundServiceMoney(0D);
                afterServiceAudit.setRealRefundMaterialServiceMoney(0D);
                afterServiceAudit.setRealRefundTenantCarriageMoney(0D);
                afterServiceAudit.setRealRefundAmount(0D);
                resendNewOrder = this.createAfterServiceOrder(afterServiceAudit, address, order, items, list,vo);
                resendFlag = true;
            } else {
                if (vo.getProductMoney() == null || vo.getProductMoney() < 0) {
                    Assert.wrong("productMoney error!");
                } else if (vo.getCarriageMoney() == null || vo.getCarriageMoney() < 0) {
                    Assert.wrong("carriageMoney error!");
                } else if (!(vo.getProductMoney() > 0 || vo.getCarriageMoney() > 0)) {
//                        Assert.wrong("either the carriageMoney and the productMoney must bigger than 0!");
//                        Assert.wrong("金额不能为0!");
                }
                if (vo.getCarriageMoney() > DoubleUtils.scale(order.getCarriageAmount() - order.getRefundCarriageAmount())) {
                    Assert.wrong("超出可退物流金额！");
                }
                if (vo.getProductMoney() > DoubleUtils.scale(order.getProductAmount() - order.getRefundProductAmount())) {
                    Assert.wrong("超出可退产品金额！");
                }
                if (vo.getMaterialServiceMoney() > DoubleUtils.scale(order.getMaterialServiceAmount() - order.getRefundMaterialServiceAmount())) {
                    Assert.wrong("超出可退素材服务费金额！");
                }

                afterServiceAudit.setRealRefundProductMoney(vo.getProductMoney());
                afterServiceAudit.setRealRefundCarriageMoney(vo.getCarriageMoney());
                afterServiceAudit.setRealRefundServiceMoney(vo.getServiceMoney());
                afterServiceAudit.setRealRefundMaterialServiceMoney(vo.getMaterialServiceMoney());
                if (isTenantLogistics) {
                    Double tenantCarriagePriceScale = null != vo.getRealRefundTenantCarriageMoney() ? DoubleUtils.scale(vo.getRealRefundTenantCarriageMoney()) : 0D;
                    afterServiceAudit.setRealRefundTenantCarriageMoney(tenantCarriagePriceScale);
                } else {
                    // sds物流 根据下单时的比例，算出租户费用
                    BigDecimal realRefundTenantCarriageMoney = CarriageAmountUtil
                            .getTenantCarriageAmount(BigDecimal.valueOf(vo.getCarriageMoney()), tenantCarriageCommissionRate);
                    afterServiceAudit.setRealRefundTenantCarriageMoney(realRefundTenantCarriageMoney.doubleValue());
                }

                afterServiceAudit.setRealRefundAmount(DoubleUtils.scale(afterServiceAudit.getRealRefundProductMoney() + afterServiceAudit.getRealRefundCarriageMoney() + afterServiceAudit.getRealRefundServiceMoney() + afterServiceAudit.getRealRefundMaterialServiceMoney()));
                afterServiceAudit.setStatus(STATUS_SUCCESS);
                refundFlag = true;
            }
        }

        afterServiceAudit.setAuditTime(System.currentTimeMillis());
        afterServiceAudit.setAuditRemark(vo.getRemark());
        this.dao.updateByPrimaryKeySelective(afterServiceAudit);
        for (AfterServiceAuditItem one : items) {
            afterServiceAuditItemDao.updateByPrimaryKeySelective(one);
            for (AfterServiceAuditItemFactoryOrderDto factoryOrder : one.getFactoryOrders()) {
                afterServiceAuditItemDetailService.save(one.getId(), factoryOrderIdMap.get(factoryOrder.getId()), factoryOrder.getNum());
            }
        }

        List<AfterServiceAuditItemDto> dtoItems = ConvertUtil.dtoConvert(items, AfterServiceAuditItemDto.class);
        this.ORM(dtoItems);
        notification(afterServiceAudit, dtoItems, pass, vo.getAuditImgs());
        orderService.addCancelRemark(user, order.getId(),
                OrderRemarkOperation.getAfterServiceJsonInfo(order, afterServiceAudit, items, operation)
                , Platform.ADMIN, operation);

        if (refundFlag) {
            String role = PaymentRoleEnum.SAAS.getCode();
            if (SdsPlatformEnum.POD.getCode().equalsIgnoreCase(vo.getSdsPlatform())) {
                role = PaymentRoleEnum.TENANT.getCode();
            }
            OrderRefundParam refundParam = afterServiceManage.buildRefundParam(role, user.getId(), order, order.getId(), order.getMerchantId(), isTenantDuty);
            //租户物流，服务费=0，运费取运营填写的
            if (isTenantLogistics) {
                refundParam.setTenantRefundServiceMoney(0D);
                refundParam.setTenantRefundCarriageMoney(afterServiceAudit.getRealRefundTenantCarriageMoney());
            } else {
                refundParam.setTenantRefundServiceMoney(vo.getServiceMoney());
                refundParam.setTenantRefundCarriageMoney(afterServiceAudit.getRealRefundTenantCarriageMoney());
            }
            AfterServiceManage.decoratorTenantRefundByAfterServiceItems(refundParam, order, items);
            refund(refundParam, afterServiceAudit, vo, items, user.getId());
            //售后退款，租户物流，退订单额度
            BigDecimal returnOrderQuota = returnOrderQuota(vo, afterServiceAudit, isTenantLogistics, tenantLogisticsOrderRespDto, dutyAffiliation, user.getId(), order.getTenantId());
            if(NumberUtil.isGreater(returnOrderQuota,BigDecimal.ZERO)){
                this.dao.updateRefundOrderQuota(afterServiceAudit.getId(),returnOrderQuota);
            }
            afterServiceAudit.setRealRefundTenantProductMoney(refundParam.getTenantRefundProductMoney());
            this.dao.updateByPrimaryKeySelective(afterServiceAudit);

        }
        if (resendFlag) {
            //重发，租户责任,租户物流 1：扣租户产品货款，2：扣租户订单额度，物流为运营填的租户物流,租户自提等，服务费转化为额度，租户物流，件数转化为额度
            boolean resendOrderIsTenantLogistics = resendNewOrder.getLogisticsSource().equalsIgnoreCase(DistributionProductLogisticsSourceEnum.ORDER_TENANT.name());
            if (resendOrderIsTenantLogistics && !isTenantDuty) {
                //非租户责任，却用租户物流，报错
                throw new BusinessException("平台责任需选择sds物流售后");
            }
            if (isDistribution && isTenantDuty && resendOrderIsTenantLogistics) {
                consumeTenantAmountAndOrderQuota(afterServiceAudit, user, order, items, list, resendNewOrder);
            }

            saveOrderItemFactorys(list, resendNewOrder,user);
        }

        if (dutyAffiliation != null && dutyAffiliation.equals(DUTY_AFFILIATION_FACTORY)) {
            String images = afterServiceAudit.getEvidenceImgs();
            List<String> urls = Lists.newArrayList();
            if (StringUtils.isNotBlank(images)) {
                urls = JsonUtil.toList(images, String.class);
            }
            if (afterServiceAudit.getType().equals(TYPE_AFTER_SERVICE_REFUND)) {
                for (FactoryOrder factoryOrder : list) {
                    Integer num = factoryOrderRefundNumMap.get(factoryOrder.getId());
                    num = num == null || factoryOrder.getNum() < num ? factoryOrder.getNum() : num;
                    factoryOrderAfterServiceService.insertByFactoryOrder(factoryOrder, id, vo.getRemark(), num, DoubleUtils.mul(factoryOrder.getPrice(), num), afterServiceAudit.getRequestRemark(), urls, vo.getAuditImgs());
                }
            } else if (afterServiceAudit.getType().equals(TYPE_AFTER_SERVICE_RESEND)) {
                for (FactoryOrder factoryOrder : list) {
                    AfterServiceAuditItemFactoryOrderDto afterServiceAuditItem = auditItemFactoryMap.get(factoryOrder.getId());
                    //备用工厂，才添加信息
                    if (EnumProductSupplyBackupType.BACKUP.getStatus().equalsIgnoreCase(afterServiceAuditItem.getFactoryBackupType())) {
                        factoryOrderAfterServiceService.insertByFactoryOrder(factoryOrder, id, vo.getRemark(), afterServiceAuditItem.getNum(), afterServiceAuditItem.getNum() * factoryOrder.getPrice().doubleValue(), afterServiceAudit.getRequestRemark(), urls, vo.getAuditImgs());
                    }

                    try {
                        // 工厂责任 并且售后重发 重新质检工序
                        FlowLogProceduresReqDTO log = FlowLogProceduresReqDTO.builder()
                                .factoryOrderId(factoryOrder.getId())
                                .type(FlowStepLogTypeEnum.AFTER_FACTORY.getCode())
                                .num(afterServiceAuditItem.getNum() * -1)
                                .build();
                        flowStepUserLogFeign.deduction(log);
                    } catch (Exception e) {
                        logger.error("after service flow error factory order id:" + factoryOrder.getId() + " afterServiceAuditItem:" + afterServiceAuditItem.getNum());
                    }

                }


            }

        }

        if (vo.getAuditImgs() != null) {
            afterServiceAuditAdminImgService.add(id, vo.getAuditImgs());
        }

        orderSearchService.updateAfterServiceStatus(order, afterServiceAudit.getStatus());

        /***<AUTHOR> @date 2020/7/16 11:00  消息发送*/
        sendMessage(id, user, dutyAffiliation, afterServiceAudit, operation, items, list,resendNewOrder);
    }

    private void saveOrderItemFactorys(List<FactoryOrder> list, Order resendNewOrder,User user) {
        List<OrderItemFactoryReqDto> orderItemFactoryReqDtos=Lists.newArrayList();
        Map<Long, FactoryOrder> productIdFactoryOrderMap = ListUtil.toMap(FactoryOrder::getProductId, list);
        List<OrderItem> resendOrderItems = resendNewOrder.getItems();
        for (OrderItem resendOrderItem : resendOrderItems) {
            FactoryOrder factoryOrder = productIdFactoryOrderMap.get(resendOrderItem.getProductId());
            if(null==factoryOrder){
                continue;
            }
            OrderItemFactoryReqDto orderItemFactoryReqDto=new OrderItemFactoryReqDto();
            orderItemFactoryReqDto.setOrderId(resendNewOrder.getId());
            orderItemFactoryReqDto.setOrderItemId(resendOrderItem.getId());
            orderItemFactoryReqDto.setFactoryId(factoryOrder.getFactoryId());
            orderItemFactoryReqDto.setCreateUid(user.getId());
            orderItemFactoryReqDto.setUpdateUid(user.getId());
            orderItemFactoryReqDtos.add(orderItemFactoryReqDto);
        }
        if(CollUtil.isNotEmpty(orderItemFactoryReqDtos)){
            orderItemFactoryFeign.batchSave(orderItemFactoryReqDtos);
        }
    }


    private void consumeTenantAmountAndOrderQuota(AfterServiceAudit afterServiceAudit, User user, Order order,
                                                  List<AfterServiceAuditItem> items, List<FactoryOrder> list, Order resendNewOrder) {
        //获取saas给租户的价格
        List<OrderItemProductPriceDto> reqList = getOrderItemProductPriceDtos(items, list);
        Map<Long, BigDecimal> productIdTenantPriceMap = Maps.newHashMap();
        BigDecimal scaleTenantToSaasTotalProductAmount = getTenantToSaasTotalProductAmount(order, productIdTenantPriceMap, resendNewOrder, reqList);
        logger.info("scaleTenantToSaasTotalProductAmount={}", JSON.toJSONString(scaleTenantToSaasTotalProductAmount));
        IdsSearchHelper idsSearchHelper = new IdsSearchHelper();
        idsSearchHelper.setIds(Lists.newArrayList(order.getId()));
        List<OrderAmountRespDTO> orderAmountRespDTOS = orderAmountFeign.findByIds(idsSearchHelper);
        OrderAmountRespDTO orderAmountRespDTO = orderAmountRespDTOS.get(0);
        //获取订单额度
        List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos = getTenantLogisticsOrderReqDtos(user, order,orderAmountRespDTO, resendNewOrder, reqList);
        //扣减额度
        TenantGroupConsumePermissionReqDto reqDto = getTenantGroupConsumePermissionReqDto(user.getId(), order.getTenantId(), tenantLogisticsOrderReqDtos);
        tenantFeign.consumePermissionByGourp(order.getTenantId(), reqDto);
        //保存新订单额度扣减情况
        logger.info("tenantLogisticsOrderReqDtos={}", JSON.toJSONString(tenantLogisticsOrderReqDtos));
        tenantLogisticsOrderFeign.saveOrUpdate(tenantLogisticsOrderReqDtos);
        //扣租户金额
        TenantAmountChangeParam rechargeParam = new TenantAmountChangeParam();
        String subjectFormat = "订单：%s因租户原因造成售后重发，重新支付货款";
        String subject = String.format(subjectFormat, order.getNo());
        AdminOperateWalletBalanceParam param = new AdminOperateWalletBalanceParam();
        param.setBizNo(resendNewOrder.getNo());
        param.setSupTenantId(orderAmountRespDTO.getProductTenantId());
        param.setDisTenantId(order.getTenantId());
        param.setBalance(scaleTenantToSaasTotalProductAmount);
        param.setBonus(new BigDecimal(0));
        param.setOperateUserId(user.getId());
        param.setTitle(subject);
        logger.info("tenantAmountChangeParam={}", JSON.toJSONString(param));
        adminOperateWalletBalanceFeign.afterResendTenantToTenant(param);
        //保存到售后单中
        AfterServiceAudit updateAfterServiceAudit = new AfterServiceAudit();
        updateAfterServiceAudit.setId(afterServiceAudit.getId());
        updateAfterServiceAudit.setResendTenantToSaasProductMoney(scaleTenantToSaasTotalProductAmount);
        this.dao.updateByPrimaryKeySelective(updateAfterServiceAudit);
        //更新原订单orderamount

        BigDecimal totalResendTenantToSaasProductMoney = NumberUtil.add(orderAmountRespDTO.getResendTenantToSaasProductMoney(), scaleTenantToSaasTotalProductAmount);
        BigDecimal scaleTotalResendTenantToSaasProductMoney = NumberUtil.round(totalResendTenantToSaasProductMoney, 2);
        OrderAmountDTO orderAmountDTO = new OrderAmountDTO();
        orderAmountDTO.setId(orderAmountRespDTO.getId());
        orderAmountDTO.setResendTenantToSaasProductMoney(scaleTotalResendTenantToSaasProductMoney);
        orderAmountFeign.updateResendTenantToSaasProductMoney(orderAmountDTO);
        //售后新订单orderamount
        OrderAmountAfterOrderDto dto = new OrderAmountAfterOrderDto();
        dto.setId(resendNewOrder.getId());
        dto.setTenantId(resendNewOrder.getTenantId());
        dto.setProductTenantId(orderAmountRespDTO.getProductTenantId());
        dto.setTenantProductAmount(scaleTenantToSaasTotalProductAmount.doubleValue());
        orderAmountFeign.createOrUpdateAfterServiceAmount(dto);
        //更新orderitemprice的tenantprice
        updateOrderItemPrice(resendNewOrder, productIdTenantPriceMap);
    }

    public void afterResendTenantToSaas(AfterServiceAudit afterServiceAudit) {
    }

    private void updateOrderItemPrice(Order resendNewOrder, Map<Long, BigDecimal> productIdTenantPriceMap) {
        List<OrderItem> newOrderItems = resendNewOrder.getItems();
        Map<Long, Long> orderItemIdIdMap = getOrderItemIdIdMap(newOrderItems);
        List<OrderItemPriceRespDto> dtos = getOrderItemPriceUpdateDtos(productIdTenantPriceMap, newOrderItems, orderItemIdIdMap);
        if (CollUtil.isNotEmpty(dtos)) {
            orderItemPriceFeign.batchUpdate(dtos);
        }
    }

    @NotNull
    private List<OrderItemPriceRespDto> getOrderItemPriceUpdateDtos(Map<Long, BigDecimal> productIdTenantPriceMap, List<OrderItem> newOrderItems, Map<Long, Long> orderItemIdIdMap) {
        List<OrderItemPriceRespDto> dtos = Lists.newArrayList();
        for (OrderItem newOrderItem : newOrderItems) {
            Long newOrderItemId = newOrderItem.getId();
            Long productId = newOrderItem.getProductId();
            BigDecimal tenantPrice = productIdTenantPriceMap.get(productId);
            if (null == tenantPrice) {
                continue;
            }
            Long id = orderItemIdIdMap.get(newOrderItemId);
            if (null == id) {
                continue;
            }
            OrderItemPriceRespDto orderItemPriceRespDto = new OrderItemPriceRespDto();
            orderItemPriceRespDto.setId(id);
            orderItemPriceRespDto.setTenantPrice(tenantPrice);
            orderItemPriceRespDto.setTenantAmount(DoubleUtils.mul(tenantPrice, newOrderItem.getNum()));
            dtos.add(orderItemPriceRespDto);
        }
        return dtos;
    }

    @NotNull
    private Map<Long, Long> getOrderItemIdIdMap(List<OrderItem> newOrderItems) {
        Map<Long, Long> orderItemIdIdMap = Maps.newHashMap();
        String newOrderItemIds = newOrderItems.stream().map(i -> String.valueOf(i.getId())).collect(Collectors.joining(","));
        List<OrderItemPriceRespDto> existOrderItemPriceDtos = orderItemPriceFeign.findByItemIds(newOrderItemIds);
        if (CollUtil.isNotEmpty(existOrderItemPriceDtos)) {
            orderItemIdIdMap = existOrderItemPriceDtos.stream().collect(Collectors.toMap(p -> p.getOrderItemId(), p -> p.getId(), (a, b) -> b));
        }
        return orderItemIdIdMap;
    }


    @NotNull
    private TenantGroupConsumePermissionReqDto getTenantGroupConsumePermissionReqDto(Long operateUserId, Long tenantId, List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos) {
        List<TenantConsumePermissionReqDto> tenantConsumePermissionReqDtoList = Lists.newArrayList();
        for (TenantLogisticsOrderReqDto logisticsOrderReqDto : tenantLogisticsOrderReqDtos) {
            TenantConsumePermissionReqDto tenantConsumePermissionReqDto = new TenantConsumePermissionReqDto();
            tenantConsumePermissionReqDto.setSourceCode(logisticsOrderReqDto.getQuotaType());
            tenantConsumePermissionReqDto.setConsumeValue(logisticsOrderReqDto.getDeductTotalValue());
            tenantConsumePermissionReqDtoList.add(tenantConsumePermissionReqDto);
        }
        TenantGroupConsumePermissionReqDto reqDto = new TenantGroupConsumePermissionReqDto();
        reqDto.setTenantId(tenantId);
        reqDto.setCode(PlatformPermissionCodeEnum.order_amount.getCode());
        reqDto.setUserId(operateUserId);
        reqDto.setTenantConsumePermissionReqDtoList(tenantConsumePermissionReqDtoList);
        return reqDto;
    }

    @NotNull
    private TenantGroupReturnPermissionReqDto getTenantGroupReturnPermissionReqDto(Long operateUserId, Long tenantId, List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos) {
        List<TenantReturnPermissionReqDto> permissionReqDtoList = Lists.newArrayList();
        for (TenantLogisticsOrderReqDto logisticsOrderReqDto : tenantLogisticsOrderReqDtos) {
            TenantReturnPermissionReqDto permissionReqDto = new TenantReturnPermissionReqDto();
            permissionReqDto.setSourceCode(logisticsOrderReqDto.getQuotaType());
            permissionReqDto.setReturnValue(logisticsOrderReqDto.getDeductTotalValue());
            permissionReqDtoList.add(permissionReqDto);
        }
        permissionReqDtoList = permissionReqDtoList.stream().collect(Collectors.toMap(item -> item.getSourceCode(), item -> item, (a, b) -> {
            a.setReturnValue(NumberUtil.roundDown(NumberUtil.add(a.getReturnValue(), b.getReturnValue()), 2));
            return a;
        })).values().stream().collect(Collectors.toList());
        TenantGroupReturnPermissionReqDto reqDto = new TenantGroupReturnPermissionReqDto();
        reqDto.setTenantId(tenantId);
        reqDto.setCode(PlatformPermissionCodeEnum.order_amount.getCode());
        reqDto.setUserId(operateUserId);
        reqDto.setTenantReturnPermissionReqDtoList(permissionReqDtoList);
        return reqDto;
    }

    @NotNull
    private List<TenantLogisticsOrderReqDto> getTenantLogisticsOrderReqDtos(User user, Order order,OrderAmountRespDTO orderAmountRespDTO,
                                                                            Order resendNewOrder, List<OrderItemProductPriceDto> reqList) {

        TenantApplicationReqDto tenantApplicationReqDto = new TenantApplicationReqDto();
        tenantApplicationReqDto.setTenantId(order.getTenantId());
        tenantApplicationReqDto.setApplicationCodeList(ApplicationCodeEnum.orderCountApplicationCodeList);
        List<TenantApplicationScenarioRelRespDto> tenantApplicationScenarioRelRespDtos = applicationFeign.getByCodeList(order.getTenantId(),tenantApplicationReqDto);
        Map<String, BigDecimal> appCodeValueMap = tenantApplicationScenarioRelRespDtos.stream().collect(Collectors.toMap(t -> t.getApplicationCode(), t -> t.getValue(), (a, b) -> b));
        boolean sdsProduct = orderAmountRespDTO.getIsSdsProduct();
        Logistics logistics = resendNewOrder.getLogistics();
        String appCode = "";
        String shipmentPlaceType = "";
        BigDecimal num = BigDecimal.ZERO;
        if(!sdsProduct){
            appCode = ApplicationCodeEnum.DISTRIBUTE_OTHER_TENANT_ORDER.getCode();
            shipmentPlaceType = TenantLogisticsConstant.ShipmentPlaceTypeEnum.ISSUING_BAY.name();
            int productNum = reqList.stream().mapToInt(i -> i.getNum()).sum();
            num = BigDecimal.valueOf(1L);
        }else{
            if (LogisticsCodeIdEnum.isZtOrConsignment(logistics.getCodeId()) ||
                LogisticsConstant.isOnlineLogistics(logistics.getServiceProviderId())) {
                //自提等订单额度
                appCode = ApplicationCodeEnum.SDS_ORDER_PREPAID_OR_ONLINE_LOGISTICS_OR_SELF_PACKING.getCode();
                shipmentPlaceType = TenantLogisticsConstant.ShipmentPlaceTypeEnum.ISSUING_BAY.name();
                //服务费
                LogisticsFreightRespDto logisticsFreightRespDto = getLogisticsFreightRespDto(order, resendNewOrder, reqList);
                Double serviceAmount = logisticsFreightRespDto.getLogistics().getServiceAmount();
                num = BigDecimal.valueOf(serviceAmount);
            } else {
                LogisticsAndLogisticsSourceResp logisticsSourceResp = resendNewOrder.getLogisticsAndLogisticsSourceResp();
                int productNum = reqList.stream().mapToInt(i -> i.getNum()).sum();
                num = BigDecimal.valueOf(productNum);
                if (logisticsSourceResp.getShipmentPlaceType().equalsIgnoreCase(TenantLogisticsConstant.ShipmentPlaceTypeEnum.TENANT.name())) {
                    appCode = ApplicationCodeEnum.SDS_ORDER_TENANT_ACCOUNT_TENANT_SHIPMENT.getCode();
                    shipmentPlaceType = TenantLogisticsConstant.ShipmentPlaceTypeEnum.TENANT.name();
                } else {
                    appCode = ApplicationCodeEnum.SDS_ORDER_TENANT_ACCOUNT_SDS_SHIPMENT.getCode();
                    shipmentPlaceType = TenantLogisticsConstant.ShipmentPlaceTypeEnum.ISSUING_BAY.name();
                }
            }
        }

        List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos = Lists.newArrayList();
        TenantLogisticsOrderReqDto tenantLogisticsOrderReqDto = new TenantLogisticsOrderReqDto();
        tenantLogisticsOrderReqDto.setId(resendNewOrder.getId());
        tenantLogisticsOrderReqDto.setQuotaType(appCode);
        tenantLogisticsOrderReqDto.setQuotaValue(appCodeValueMap.get(appCode));
        tenantLogisticsOrderReqDto.setNum(num);
        tenantLogisticsOrderReqDto.setShipmentPlaceType(shipmentPlaceType);
        tenantLogisticsOrderReqDto.setTotalValue(NumberUtil.roundDown(NumberUtil.mul(tenantLogisticsOrderReqDto.getQuotaValue(), tenantLogisticsOrderReqDto.getNum()), 2));
        tenantLogisticsOrderReqDto.setDeductTotalValue(tenantLogisticsOrderReqDto.getTotalValue());
        tenantLogisticsOrderReqDto.setCreateUid(user.getId());
        tenantLogisticsOrderReqDto.setUpdateUid(user.getId());
        tenantLogisticsOrderReqDtos.add(tenantLogisticsOrderReqDto);
        return tenantLogisticsOrderReqDtos;
    }

    private LogisticsFreightRespDto getLogisticsFreightRespDto(Order order, Order resendNewOrder, List<OrderItemProductPriceDto> reqList) {
        Address address = resendNewOrder.getAddress();
        ReferDto referDto = new ReferDto();
        referDto.setCityCode(address.getCityCode());
        referDto.setCity(address.getCity());
        referDto.setProvince(address.getProvince());
        referDto.setProvinceCode(address.getProvinceCode());
        referDto.setPostcode(address.getPostcode());
        referDto.setMerchantId(order.getMerchantId());
        referDto.setIssuingBayAreaId(resendNewOrder.getIssuingBayAreaId());
        referDto.setLogisticsType(OrderOriginEnum.isFba(resendNewOrder.getOriginType()) ? 1 : 2);
        referDto.setClientSelectedLogistics(resendNewOrder.getClientSelectedLogistics());
        referDto.setMerchantStorePlatformCode(resendNewOrder.getMerchantStorePlatformCode());
        referDto.setOutOrderNo(resendNewOrder.getOutOrderNo());
        referDto.setCountry(address.getCountry());
        referDto.setProductInfo(new ArrayList<>());
        referDto.setTenantId(order.getTenantId());
        for (OrderItemProductPriceDto orderItemProductPriceDto : reqList) {
            ReferProductInfoDto info = new ReferProductInfoDto();
            info.setProductId(orderItemProductPriceDto.getProductId());
            info.setNum(orderItemProductPriceDto.getNum());
            referDto.getProductInfo().add(info);
        }
        LogisticsFreightRespDto logisticsFreightRespDto = countryExpressInfoService.findLogisticsReferByLogisticsId(referDto, resendNewOrder.getLogisticsId());
        return logisticsFreightRespDto;
    }

    @NotNull
    private List<OrderItemProductPriceDto> getOrderItemProductPriceDtos(List<AfterServiceAuditItem> items, List<FactoryOrder> list) {
        List<OrderItemProductPriceDto> reqList = Lists.newArrayList();
        Map<Long, FactoryOrder> factoryOrderMap = ListUtil.toMap(FactoryOrder::getId, list);
        for (AfterServiceAuditItem afterServiceAuditItem : items) {
            if (CollectionUtils.isEmpty(afterServiceAuditItem.getFactoryOrders())) {
                continue;
            }
            for (AfterServiceAuditItemFactoryOrderDto afterServiceAuditItemFactoryOrderDto : afterServiceAuditItem.getFactoryOrders()) {
                FactoryOrder factoryOrder = factoryOrderMap.get(afterServiceAuditItemFactoryOrderDto.getId());
                OrderItemProductPriceDto currentProductPriceReq = new OrderItemProductPriceDto();
                currentProductPriceReq.setNum(afterServiceAuditItemFactoryOrderDto.getNum());
                currentProductPriceReq.setProductId(factoryOrder.getProductId());
                reqList.add(currentProductPriceReq);
            }
        }
        reqList = reqList.stream().collect(Collectors.toMap(item -> item.getProductId(), item -> item, (a, b) -> {
            a.setNum(a.getNum() + b.getNum());
            return a;
        })).values().stream().collect(Collectors.toList());
        return reqList;
    }

    @NotNull
    private BigDecimal getTenantToSaasTotalProductAmount(Order order, Map<Long, BigDecimal> productIdTenantPriceMap,
                                                         Order resendNewOrder, List<OrderItemProductPriceDto> reqList) {

        Long orderType = order.getOrigin().equals(com.sdsdiy.orderapi.constant.OrderOriginType.FBA.getValue()) ? ProductPreferentialActivityOrderType.FBA.value : ProductPreferentialActivityOrderType.NORMAL.value;
        OrderProductPriceParam orderProductPriceParam = new OrderProductPriceParam();
        orderProductPriceParam.setItems(reqList);
        orderProductPriceParam.setMerchantId(order.getMerchantId());
        orderProductPriceParam.setOrderType(orderType);
        orderProductPriceParam.setLogisticsId(resendNewOrder.getLogisticsId());
        List<ProductPriceResp> productPriceList = productFeign.getCurrentOrderProductPriceByParent(orderProductPriceParam, order.getMerchantId());
        Map<Long, ProductPriceResp> productIdPriceMap = productPriceList.stream().collect(Collectors.toMap(p -> p.getId(), p -> p, (a, b) -> b));

        BigDecimal tenantToSaasTotalProductAmount = BigDecimal.ZERO;
        for (OrderItemProductPriceDto itemProductPriceDto : reqList) {
            ProductPriceResp priceResp = productIdPriceMap.get(itemProductPriceDto.getProductId());
            if (null == priceResp) {
                continue;
            }
            productIdTenantPriceMap.putIfAbsent(priceResp.getId(), priceResp.getTenantCurrentPrice());
            BigDecimal result = NumberUtil.mul(priceResp.getTenantCurrentPrice(), itemProductPriceDto.getNum());
            tenantToSaasTotalProductAmount = NumberUtil.add(tenantToSaasTotalProductAmount, result);
        }
        BigDecimal scaleTenantToSaasTotalProductAmount = NumberUtil.round(tenantToSaasTotalProductAmount, 2);
        return scaleTenantToSaasTotalProductAmount;
    }

    private TenantAmountChangeParam getTenantAmountChangeParam(Long tenantId, Long operateUserId, String orderNo, String newOrderNo, BigDecimal costAmount) {
        TenantAmountChangeParam rechargeParam = new TenantAmountChangeParam();
        String subjectFormat = "订单：%s因租户原因造成售后重发，重新支付货款";
        String subject = String.format(subjectFormat, orderNo);
        rechargeParam.setBizNo(newOrderNo);
        rechargeParam.setTenantId(tenantId);
        rechargeParam.setSubject(subject);
        rechargeParam.setChangeBalance(null != costAmount ? costAmount : BigDecimal.ZERO);
        rechargeParam.setChangeGiftMoney(BigDecimal.ZERO);
        rechargeParam.setOperateUserId(operateUserId);
        rechargeParam.setRemark("");
        return rechargeParam;
    }

//    @Nullable
//    private Long getProductId(Map<Long, Long> factoryOrderIdOrderItemIdMap, Map<Long, Long> orderItemIdProductIdMap, Long factoryOrderId) {
//        Long productId = null;
//        Long orderItemId = factoryOrderIdOrderItemIdMap.get(factoryOrderId);
//        if (null != orderItemId) {
//            productId = orderItemIdProductIdMap.get(orderItemId);
//        }
//        return productId;
//    }

    private BigDecimal returnOrderQuota(AfterServiceAuditVo vo, AfterServiceAudit afterServiceAudit, boolean isTenantLogistics,
                                        TenantLogisticsOrderRespDto tenantLogisticsOrderRespDto, Integer dutyAffiliation, Long operateUserId, Long tenantId) {
        boolean returnOrderQuota = !TenantCommonConstant.isSdsdiy(tenantId) && afterServiceAudit.getType() == TYPE_AFTER_SERVICE_REFUND && isTenantLogistics && isReturnOrderQuotaDuty(dutyAffiliation);
        if (!returnOrderQuota) {
            return BigDecimal.ZERO;
        }
        BigDecimal quotaValue = tenantLogisticsOrderRespDto.getQuotaValue();
        BigDecimal num = BigDecimal.ZERO;
        boolean isSpecialLogistics = tenantLogisticsOrderRespDto.getQuotaType().equalsIgnoreCase(ApplicationCodeEnum.SDS_ORDER_PREPAID_OR_ONLINE_LOGISTICS_OR_SELF_PACKING.getCode());
        if (isSpecialLogistics) {
            //服务费
            num = BigDecimal.valueOf(vo.getServiceMoney());
        } else {
            //件数
            int allNum = vo.getItems().stream().mapToInt(AfterServiceAuditVo.Item::getNum).sum();
            num = BigDecimal.valueOf(allNum);
        }
        BigDecimal refundTotalQuota = NumberUtil.roundDown(NumberUtil.mul(quotaValue, num), 2);

        TenantLogisticsOrderReqDto tenantLogisticsOrderReqDto = new TenantLogisticsOrderReqDto();
        BeanUtils.copyProperties(tenantLogisticsOrderRespDto, tenantLogisticsOrderReqDto);
        tenantLogisticsOrderReqDto.setDeductTotalValue(refundTotalQuota);
        //扣减
        TenantGroupReturnPermissionReqDto tenantGroupReturnPermissionReqDto = getTenantGroupReturnPermissionReqDto(operateUserId, tenantId, Lists.newArrayList(tenantLogisticsOrderReqDto));
        tenantFeign.returnPermissionByGourp(tenantId, tenantGroupReturnPermissionReqDto);
        return refundTotalQuota;
    }

    private void sendMessage(Long id, User user, Integer dutyAffiliation, AfterServiceAudit afterServiceAudit,
                             OrderRemarkOperation operation, List<AfterServiceAuditItem> items, List<FactoryOrder> list,
                             Order resendNewOrder) {
        Map<Long, FactoryOrder> factoryOrderMap = Maps.newConcurrentMap();
        for (FactoryOrder factoryOrder : list) {
            factoryOrderMap.put(factoryOrder.getId(), factoryOrder);
        }
        List<com.sdsdiy.orderapi.constant.event.Product> productList = Lists.newArrayList();
        int group = 1;
        for (AfterServiceAuditItem afterServiceAuditItem : items) {

            if (CollectionUtils.isEmpty(afterServiceAuditItem.getFactoryOrders())) {
                continue;
            }
            for (AfterServiceAuditItemFactoryOrderDto afterServiceAuditItemFactoryOrderDto : afterServiceAuditItem.getFactoryOrders()) {
                if (factoryOrderMap.containsKey(afterServiceAuditItemFactoryOrderDto.getId())) {
                    FactoryOrder factoryOrder = factoryOrderMap.get(afterServiceAuditItemFactoryOrderDto.getId());
                    if (!factoryOrder.getStatus().equals(FactoryOrderStatus.CANCEL.getStatus())) {
                        com.sdsdiy.orderapi.constant.event.Product product = new com.sdsdiy.orderapi.constant.event.Product();
                        product.setName(factoryOrder.getProductName());
                        product.setProductNum(afterServiceAuditItemFactoryOrderDto.getNum());
                        product.setColor(factoryOrder.getProductColorName());
                        product.setSpecifications(factoryOrder.getProductSize());
                        product.setFactoryOrderId(factoryOrder.getId());
                        product.setFactoryBackupType(afterServiceAuditItem.getFactoryBackupType());
                        product.setAmount(afterServiceAuditItemFactoryOrderDto.getNum() * factoryOrder.getPrice().doubleValue());
                        product.setGroup(group);
                        productList.add(product);
                    }
                }
            }
            group++;
        }

        if (OrderRemarkOperation.PASS_AFTER_SERVICE_RESEND == operation) {
            //通过售后重发
            AdminAdoptResendAfterSaleOrderMessage adminAdoptResendAfterSaleOrderMessage = new AdminAdoptResendAfterSaleOrderMessage();
            adminAdoptResendAfterSaleOrderMessage.setEid(id);
            adminAdoptResendAfterSaleOrderMessage.setOperatorUid(user.getId());
            adminAdoptResendAfterSaleOrderMessage.setSendingTime(new Date());
            adminAdoptResendAfterSaleOrderMessage.setOrderId(afterServiceAudit.getOrderId());
            adminAdoptResendAfterSaleOrderMessage.setAfterServiceAuditId(afterServiceAudit.getId());
            adminAdoptResendAfterSaleOrderMessage.setRemarks(afterServiceAudit.getAuditRemark());
            adminAdoptResendAfterSaleOrderMessage.setProductNum(afterServiceAudit.getOrderItemTotalNum());
            adminAdoptResendAfterSaleOrderMessage.setProductList(productList);
            adminAdoptResendAfterSaleOrderMessage.setEvidenceImgs(afterServiceAudit.getEvidenceImgs());
            adminAdoptResendAfterSaleOrderMessage.setCompensationReasons(afterServiceAudit.getRequestRemark());
            adminAdoptResendAfterSaleOrderMessage.setDutyAffiliation(dutyAffiliation);
            adminAdoptResendAfterSaleOrderMessage.setResendNewOrderId(resendNewOrder.getId());
            adminAdoptResendAfterSaleOrderMessage.setResendNewOrderNo(resendNewOrder.getNo());
            orderEventService.sendProcessMsg(adminAdoptResendAfterSaleOrderMessage, OrderProgressConstant.ADMIN_ADOPT_RESEND_AFTER_SALE_ORDER);
            // 售后单不用支付，所以要主动触发生成包裹数据
//            mqTemplate.sendMessageAfterCommit(ParcelConstant.ORDER_PARCEL_PACK_V2_TOPIC, new OrderParcelPackMessageDTO(resendNewOrder.getId(), BasePoConstant.YES));
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.ORDER_PARCEL_PACK_V2_TOPIC, new OrderParcelPackMessageDTO(resendNewOrder.getId(), YES));
            IdMsg idMsg = new IdMsg();
            idMsg.setId(id);
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderProgressConstant.ADMIN_ADOPT_RESEND_AFTER_SALE_ORDER, idMsg);

            OrderEsSyncSqsMsg msg = new OrderEsSyncSqsMsg();
            msg.setOrderId(afterServiceAudit.getResendOrderId());
            msg.setRefreshBayId(true);
//            orderEventService.sendMessage(msg, OrderUpdateConstant.ORDER_UPDATE_TOPIC, OrderUpdateConstant.EVENT_ORDER_LOGISTICS_UPDATE);
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.EVENT_ORDER_LOGISTICS_UPDATE, msg);
        }
        if (OrderRemarkOperation.PASS_AFTER_SERVICE_REFUND == operation) {
            //通过售后退款
            AdminAdoptRefundAfterSaleOrderMessage adminAdoptRefundAfterSaleOrderMessage = new AdminAdoptRefundAfterSaleOrderMessage();
            adminAdoptRefundAfterSaleOrderMessage.setEid(id);
            adminAdoptRefundAfterSaleOrderMessage.setOperatorUid(user.getId());
            adminAdoptRefundAfterSaleOrderMessage.setSendingTime(new Date());
            adminAdoptRefundAfterSaleOrderMessage.setOrderId(afterServiceAudit.getOrderId());
            adminAdoptRefundAfterSaleOrderMessage.setAfterServiceAuditId(afterServiceAudit.getId());
            adminAdoptRefundAfterSaleOrderMessage.setRefundAmount(afterServiceAudit.getRequestRefundAmount());
            adminAdoptRefundAfterSaleOrderMessage.setRealRefundAmount(afterServiceAudit.getRealRefundAmount());
            adminAdoptRefundAfterSaleOrderMessage.setCompensationReasons(afterServiceAudit.getRequestRemark());
            adminAdoptRefundAfterSaleOrderMessage.setRemarks(afterServiceAudit.getAuditRemark());
            adminAdoptRefundAfterSaleOrderMessage.setProductNum(afterServiceAudit.getOrderItemTotalNum());
            adminAdoptRefundAfterSaleOrderMessage.setEvidenceImgs(afterServiceAudit.getEvidenceImgs());
            adminAdoptRefundAfterSaleOrderMessage.setProductList(productList);
            adminAdoptRefundAfterSaleOrderMessage.setDutyAffiliation(dutyAffiliation);
            orderEventService.sendProcessMsg(adminAdoptRefundAfterSaleOrderMessage, OrderProgressConstant.ADMIN_ADOPT_REFUND_AFTER_SALE_ORDER);
            IdMsg idMsg = new IdMsg();
            idMsg.setId(id);
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderProgressConstant.ADMIN_ADOPT_REFUND_AFTER_SALE_ORDER, idMsg);
        }
        if (OrderRemarkOperation.REFUSE_AFTER_SERVICE_REFUND == operation) {
            //驳回售后退款
            AdminRejectRefundAfterSaleOrderMessage adminRejectRefundAfterSaleOrderMessage = new AdminRejectRefundAfterSaleOrderMessage();
            adminRejectRefundAfterSaleOrderMessage.setEid(id);
            adminRejectRefundAfterSaleOrderMessage.setOperatorUid(user.getId());
            adminRejectRefundAfterSaleOrderMessage.setSendingTime(new Date());
            adminRejectRefundAfterSaleOrderMessage.setOrderId(afterServiceAudit.getOrderId());
            adminRejectRefundAfterSaleOrderMessage.setRefundAmount(afterServiceAudit.getRequestRefundAmount());
            adminRejectRefundAfterSaleOrderMessage.setRemarks(afterServiceAudit.getAuditRemark());
            adminRejectRefundAfterSaleOrderMessage.setProductNum(afterServiceAudit.getOrderItemTotalNum());
            adminRejectRefundAfterSaleOrderMessage.setCompensationReasons(afterServiceAudit.getRequestRemark());

            adminRejectRefundAfterSaleOrderMessage.setProductList(productList);
            orderEventService.sendProcessMsg(adminRejectRefundAfterSaleOrderMessage, OrderProgressConstant.ADMIN_REJECT_REFUND_AFTER_SALE_ORDER);
        }
        if (OrderRemarkOperation.REFUSE_AFTER_SERVICE_RESEND == operation) {
            //驳回售后重发
            AdminRejectResendAfterSaleOrderMessage adminRejectResendAfterSaleOrderMessage = new AdminRejectResendAfterSaleOrderMessage();
            adminRejectResendAfterSaleOrderMessage.setEid(id);
            adminRejectResendAfterSaleOrderMessage.setOperatorUid(user.getId());
            adminRejectResendAfterSaleOrderMessage.setSendingTime(new Date());
            adminRejectResendAfterSaleOrderMessage.setOrderId(afterServiceAudit.getOrderId());
            adminRejectResendAfterSaleOrderMessage.setRemarks(afterServiceAudit.getAuditRemark());
            adminRejectResendAfterSaleOrderMessage.setProductNum(afterServiceAudit.getOrderItemTotalNum());
            adminRejectResendAfterSaleOrderMessage.setCompensationReasons(afterServiceAudit.getRequestRemark());

            adminRejectResendAfterSaleOrderMessage.setProductList(productList);
            orderEventService.sendProcessMsg(adminRejectResendAfterSaleOrderMessage, OrderProgressConstant.ADMIN_REJECT_RESEND_AFTER_SALE_ORDER);
        }

        orderEventService.financeFresh(afterServiceAudit.getOrderId());
        if(resendNewOrder != null){
            orderEventService.financeFresh(resendNewOrder.getId());
        }
    }

    private void checkParam(AfterServiceAuditVo vo, AfterServiceAudit afterServiceAudit) {
        //取消
        if (afterServiceAudit.getType().equals(TYPE_CANCEL_REFUND)) {

        } else {
            //售后
            if (vo.getCauseType() == null || !AfterServiceCauseType.getValues().contains(vo.getCauseType())) {
                Assert.wrong("cause_type error!");
            }
            if (vo.getDutyAffiliation() == null || !Arrays.asList(DUTY_AFFILIATION_ADMIN, DUTY_AFFILIATION_FACTORY, DUTY_AFFILIATION_TENANT).contains(vo.getDutyAffiliation())) {
                Assert.wrong("duty_affiliation can`t empty!");
            }
        }
    }

    private void checkAddress(Address vo) {
        String errMsg = "param can`t empty of ";
        Validator.validateNotEmpty(vo.getPostcode(), errMsg + "postcode");
        if (vo.getPostcode().length() > 20) {
            throw new BusinessException("邮政编码过长!");
        }

        Validator.validateNotEmpty(vo.getConsignee(), errMsg + "consignee");

        if (vo.getConsignee().length() > 64) {
            throw new BusinessException("收件人姓名过长!");
        }
        Validator.validateNotEmpty(vo.getMobilePhone(), errMsg + "mobile_phone");
        if (vo.getMobilePhone().length() > 64) {
            throw new BusinessException("联系电话过长!");
        }
        Validator.validateNotEmpty(vo.getCountry(), errMsg + "country");
        Validator.validateNotEmpty(vo.getProvince(), errMsg + "province");
        if (vo.getProvince().length() > 50) {
            throw new BusinessException("省过长!");
        }
        Validator.validateNotEmpty(vo.getCity(), errMsg + "city");
        if (vo.getCity().length() > 50) {
            throw new BusinessException("市过长!");
        }
//        Validator.validateNotEmpty(vo.getDetail(), errMsg + "detail");
        Validator.validateNotEmpty(vo.getAddressDetail1(), errMsg + "addressDetail1");
        if (vo.getAddressDetail1().length() > 500) {
            throw new BusinessException("详细地址1超过500!");
        }
        if (com.ziguang.base.support.StringUtils.isNotBlank(vo.getAddressDetail2()) && vo.getAddressDetail2().length() > 500) {
            throw new BusinessException("详细地址2超过500!");
        }
        if (com.ziguang.base.support.StringUtils.isNotBlank(vo.getAddressDetail3()) && vo.getAddressDetail3().length() > 500) {
            throw new BusinessException("详细地址3超过500!");
        }
        
    }

    @Autowired
    OrderItemTransferHistoryService orderItemTransferHistoryService;

    @Autowired
    ProductSupplyFeign productSupplyFeign;
    @Autowired
    OrderItemSupplyChainFeign orderItemSupplyChainFeign;
    @Autowired
    CountryExpressInfoNewFeign countryExpressInfoNewFeign;

    /**
     * 创建一个新的售后订单并设置 售后与订单OriginalAsId
     *
     * @param afterServiceAudit
     * @param address
     * @param originalOrder
     * @param items
     */
    private Order createAfterServiceOrder(AfterServiceAudit afterServiceAudit, Address address, Order originalOrder
            , List<AfterServiceAuditItem> items, List<FactoryOrder> factoryOrders,AfterServiceAuditVo afterServiceAuditVo) {
        List<Long> factoryIds = factoryOrders.stream().map(FactoryOrder::getFactoryId).collect(Collectors.toList());
        Map<Long, ProductSupplyDTO> productSupplyDTOMap = Maps.newHashMap();
        List<OrderItemTransferHistoryDto> batchSaveList = Lists.newArrayList();
        Map<Long, Long> factoryOrderOrderItemIdMap = factoryOrders.stream().collect(Collectors.toMap(FactoryOrder::getId, FactoryOrder::getOrderItemId));
        List<Long> orderItemIds = originalOrder.getItems().stream().map(OrderItem::getId).collect(Collectors.toList());
        Map<Long, OrderItemSupplyChainDTO> itemIdSupplyChainDtoMap = orderItemSupplyChainFeign.getMapByIds(orderItemIds);
        //创建一个新的售后订单
        Order asOrder = new Order();
        asOrder.setItems(new ArrayList<>());
        for (AfterServiceAuditItem item : items) {
            for (AfterServiceAuditItemFactoryOrderDto factoryOrder : item.getFactoryOrders()) {
                if (factoryOrder.getNum() <= 0) {
                    continue;
                }
                Long orderItemId = factoryOrderOrderItemIdMap.get(factoryOrder.getId());
                OrderItem io = new OrderItem();
                Optional<OrderItem> optional = originalOrder.getItems().stream().filter(i -> i.getId().equals(orderItemId)).findFirst();
                if (!optional.isPresent()) {
                    throw new BusinessException("the order item id match exception!");
                }
                OrderItem one = optional.get();
                io.setId(IdGenerator.nextId());
                io.setProductId(one.getProductId());
                io.setCompoundId(one.getCompoundId());
                io.setEndProductId(one.getEndProductId());
                io.setNum(factoryOrder.getNum());
                io.setOriginalAsId(one.getId());
                String afterServiceItemCode = IdGenerator.nextStringId();
                io.setAfterServiceItemCode(afterServiceItemCode);

                //产能线
                OrderItemSupplyChainDTO originalItemSupplyChain = itemIdSupplyChainDtoMap.get(one.getId());
                String supplyChainType = null != originalItemSupplyChain ? originalItemSupplyChain.getSupplyType() : SupplyChainTypeEnum.ONE_PIECE.name();
                io.setSupplyChainType(supplyChainType);

                if (item.getFactoryBackupType().equalsIgnoreCase(EnumProductSupplyBackupType.BACKUP.getStatus())) {
                    ProductSupplyDTO productSupplyDTO = productSupplyDTOMap.get(io.getProductId());
                    if (productSupplyDTO == null) {
                        productSupplyDTO = productSupplyFeign.findBackupFactoryProductIdAreaId(io.getProductId(), originalOrder.getIssuingBayAreaId(), supplyChainType);
                        Assert.validateNull(productSupplyDTO, one.getNo() + "没有对应的备用工厂");
                        productSupplyDTOMap.put(one.getProductId(), productSupplyDTO);
                    }
                    batchSaveList.add(orderItemTransferHistoryService.genParam(one, factoryOrder.getNum(), productSupplyDTO.getFactoryId(), EnumProductSupplyBackupType.BACKUP.getStatus(), EnumOrderItemTransferType.AFTER_SERVICE.getValue(), afterServiceItemCode));
                } else {
                    batchSaveList.add(orderItemTransferHistoryService.genParam(one, factoryOrder.getNum(), null, EnumProductSupplyBackupType.NONE.getStatus(), EnumOrderItemTransferType.AFTER_SERVICE.getValue(), afterServiceItemCode));
                }
                asOrder.getItems().add(io);
            }

        }
        asOrder.setId(IdGenerator.nextId());
        asOrder.setMerchantStorePlatformCode(originalOrder.getMerchantStorePlatformCode());
        asOrder.setMerchantStoreId(originalOrder.getMerchantStoreId());
        asOrder.setAddress(address);
        asOrder.setCountryExpressInfoId(afterServiceAudit.getCountryExpressInfoId());
        asOrder.setOriginalAsNo(originalOrder.getNo());
        asOrder.setOriginalAsId(originalOrder.getId());
        asOrder.setDutyAffiliation(afterServiceAudit.getDutyAffiliation());
        asOrder.setExpressIncome(0D);
        asOrder.setExpressCost(originalOrder.getCarriageAmount());
        asOrder.setIssuingBayAreaId(originalOrder.getIssuingBayAreaId());
        asOrder.setMerchantId(originalOrder.getMerchantId());
        //加急
        asOrder.setExpressType(1);
        asOrder.setTenantId(originalOrder.getTenantId());
        logger.info("售后的getCountryExpressInfoId:" + afterServiceAudit.getCountryExpressInfoId());
        if (afterServiceAudit.getCountryExpressInfoId() != null && afterServiceAudit.getCountryExpressInfoId() > 0) {
            CountryExpressInfoNewRespDto infoNewRespDto = countryExpressInfoNewFeign.checkById(afterServiceAudit.getCountryExpressInfoId());
            asOrder.setLogisticsId(infoNewRespDto.getLogisticsId());
        }
        //工厂责任/平台责任 加上售后标识（售后订单商户不可见）
        asOrder.setBeAfterServiceOrder(true);
        Long issuingBayId = orderService.choseIssuingBayToAfterOrder(asOrder, factoryIds);
        asOrder.setIssuingBayId(issuingBayId);

        if (CollectionUtils.isNotEmpty(batchSaveList)) {
            orderItemTransferHistoryService.batchSave(batchSaveList);
        }
        //添加订单备注 工厂：工厂责任售后重发，不与工厂结算货款 平台：平台责任售后重发，与工厂结算货款
        //   Order resultOrder = orderService.save(merchantUserDao.getById(order.getCustomerId()), order.getMerchant(), OrderOrigin.AFTER_SERVICE.getValue(), null, Arrays.asList(asOrder), false).get(0);
        Order resultOrder = orderService.saveByAfterService(merchantUserDao.getById(originalOrder.getCustomerId()), originalOrder.getMerchant()
                , OrderOrigin.AFTER_SERVICE.getValue(), null, Collections.singletonList(asOrder), originalOrder,afterServiceAuditVo).get(0);
        addRemark(resultOrder.getId(), DUTY_AFFILIATION_ADMIN == afterServiceAudit.getDutyAffiliation() ? "平台责任售后重发，需与工厂结算货款" :
                DUTY_AFFILIATION_FACTORY == afterServiceAudit.getDutyAffiliation() ? "工厂责任售后重发，不与工厂结算货款" : "");
        //售后订单与售后绑定
        afterServiceAudit.setResendOrderId(resultOrder.getId());
        resultOrder.getItems().forEach(io -> items.forEach(i -> {
            if (io.getOriginalAsId() == null) {
                throw new BusinessException("original after service id is lose!");
            }
            if (io.getOriginalAsId().equals(i.getOrderItemId())) {
                i.setResendOrderItemId(io.getId());
            }
        }));
        //添加空支付信息
        Payment payment = paymentService.emptyPay(originalOrder.getMerchantId());
        asOrder.setPaymentId(payment.getId());
        Order updateOrder = new Order();
        updateOrder.setId(asOrder.getId());
        updateOrder.setTenantId(originalOrder.getTenantId());
        updateOrder.setPaymentId(payment.getId());
        updateOrder.setStatus(OrderStatus.PAIN.getStatus());
        updateOrder.setDesignStatus(OrderDesignStatus.FINISH.name());
        asOrder.setPayTime(System.currentTimeMillis());
        //特别设置
        updateOrder.setTotalAmount(0D);
        for (OrderItem item : resultOrder.getItems()) {
            OrderItem updateItem = new OrderItem();
            updateItem.setId(item.getId());
            updateItem.setAmount(0D);
            updateItem.setTransferType(EnumOrderItemTransferType.AFTER_SERVICE.getValue());
            updateItem.setStatus(OrderStatus.PAIN.getStatus());
            orderItemDao.update(updateItem);
        }
        orderDao.update(updateOrder);
        OrderCarriageInitParam param = new OrderCarriageInitParam();
        param.setOrderId(resultOrder.getId());
        param.setOrderNo(resultOrder.getNo());
        param.setLogisticsId(resultOrder.getLogisticsId());
        param.setMerchantId(originalOrder.getMerchantId());
//        orderCarriageFeign.orderCarriageInit(param);

        OrderRefreshMsg orderRefreshMsg = new OrderRefreshMsg();
        orderRefreshMsg.setId(resultOrder.getId());
        
        TransactionHookManager.registerHook(new TransactionHookAdapter() {
            @Override
            public void afterCommit() {
//                orderEventService.sendMessage(orderRefreshMsg, OrderEventRefreshConstant.AFTER_SERVICE_ORDER_CREATE, OrderEventRefreshConstant.EVENT_AFTER_SERVICE_ORDER_CREATE);
                rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.AFTER_SERVICE_ORDER_CREATE, orderRefreshMsg);
            }
        });
        return resultOrder;
    }


    @Autowired
    AfterCommitExecutor afterCommitExecutor;

    /**
     * @param orderId
     * @param remark
     */
    private void addRemark(Long orderId, String remark) {
        //添加备注
        OrderRemark orderRemark = new OrderRemark();
        orderRemark.setOrderId(orderId);
        orderRemark.setUserId(0L);
        orderRemark.setUsername("系统");
        orderRemark.setRemark(remark);
        orderRemark.setPlatform(Platform.ADMIN.getValue());
        orderRemark.setType(OrderRemarkService.TYPE_ORDER);
        orderRemarkService.save(null, orderRemark);
    }

    private void refund(OrderRefundParam refundParam, AfterServiceAudit afterServiceAudit, AfterServiceAuditVo vo, List<AfterServiceAuditItem> items, Long adminUserId) {
        Order order = orderService.format(afterServiceAudit.getOrderId());
        Merchant merchant = merchantDao.findById(afterServiceAudit.getMerchantId());
        //计算退款金额与赠送金
        afterServiceAudit.setRealRefundProductMoney(vo.getProductMoney());
        afterServiceAudit.setRealRefundCarriageMoney(vo.getCarriageMoney());
        afterServiceAudit.setRealRefundServiceMoney(vo.getServiceMoney());
        afterServiceAudit.setRealRefundMaterialServiceMoney(vo.getMaterialServiceMoney());

        afterServiceAudit.setRealRefundAmount(vo.getProductMoney() + vo.getCarriageMoney() + vo.getServiceMoney() + vo.getMaterialServiceMoney());
        double productAmount = afterServiceAudit.getRealRefundProductMoney();
        double carriageAmount = afterServiceAudit.getRealRefundCarriageMoney();
        double serviceAmount = afterServiceAudit.getRealRefundServiceMoney();
        double materialServiceAmount = afterServiceAudit.getRealRefundMaterialServiceMoney();
        double totalAmount = afterServiceAudit.getRealRefundAmount();

        RefundType refundType = afterServiceAudit.getType() == TYPE_CANCEL_REFUND ? RefundType.CANCEL : RefundType.AFTER_SALE;
        List<OrderItem> keepOrderItems = new ArrayList<>();
        for (AfterServiceAuditItem one : items) {
            for (OrderItem orderItem : order.getItems()) {
                if (one.getOrderItemId().equals(orderItem.getId())) {
                    keepOrderItems.add(orderItem);
                }
            }
        }
        //剃除此次申请以外的item项
        order.setItems(keepOrderItems);
        refundService.addRefundRecordFinal(refundParam, order.getCustomerId(), merchant.getContactName(), merchant.getContactTel(), afterServiceAudit.getRequestRemark(),
                productAmount, carriageAmount, serviceAmount, materialServiceAmount, totalAmount
                , order, refundType, true,
                afterServiceAudit.getCharge(), afterServiceAudit.getId(), afterServiceAudit.getDutyAffiliation(), adminUserId);
    }

    @Autowired
    S3ServiceV2 s3ServiceV2;

    private void notification(AfterServiceAudit afterServiceAudit, List<AfterServiceAuditItemDto> items, boolean pass, List<String> fileCodes) {
        String content = "";
        EnumNotificationTitle title = null;

        if (!pass) {
            if (afterServiceAudit.getType() == TYPE_CANCEL_REFUND) {
                content += "您申请取消产品:%s";
                content += ",申请退款金额:" + afterServiceAudit.getRequestRefundAmount();
                content += "元!被驳回原因:" + afterServiceAudit.getAuditRemark();
                title = EnumNotificationTitle.ORDER_CANCEL_REFUSE;
            } else if (afterServiceAudit.getType() == TYPE_AFTER_SERVICE_REFUND) {
                content += "您申请售后产品:%s";
                content += ",申请退款金额:" + afterServiceAudit.getRequestRefundAmount();
                content += "元!被驳回原因:" + afterServiceAudit.getAuditRemark();
                title = EnumNotificationTitle.ORDER_AFTERMARKET_REFUSE;
            } else if (afterServiceAudit.getType() == TYPE_AFTER_SERVICE_RESEND) {
                content += "您申请售后产品:%s";
                content += ",被驳回原因:" + afterServiceAudit.getAuditRemark();
                title = EnumNotificationTitle.ORDER_APPLY_REFUND;
            }
        } else {
            if (afterServiceAudit.getType() == TYPE_CANCEL_REFUND) {
                content += "您申请取消产品:%s";
                content += ",退款金额:" + afterServiceAudit.getRealRefundAmount();
                content += "元!退款将在1-5个工作日内原路返还!";
                title = EnumNotificationTitle.ORDER_CANCEL_PASS;
            } else if (afterServiceAudit.getType() == TYPE_AFTER_SERVICE_REFUND) {
                content += "您申请售后产品:%s";
                content += ",退款金额:" + afterServiceAudit.getRealRefundAmount();
                content += "元!退款将在1-5个工作日内原路返还!";
                if (afterServiceAudit.getOriginType().equals(OrderOriginType.FBA.getValue())) {

                }
                title = EnumNotificationTitle.ORDER_AFTERMARKET_PASS;
            } else if (afterServiceAudit.getType() == TYPE_AFTER_SERVICE_RESEND) {
                content += "您申请售后产品:%s";
                title = EnumNotificationTitle.ORDER_APPLY_PASS;
            }
            if (StrUtil.isNotEmpty(afterServiceAudit.getAuditRemark())) {
                content += " 备注:" + afterServiceAudit.getAuditRemark();
            }
        }

        if (title != null) {
            StringBuilder productInfo = new StringBuilder();
            boolean passFirst = false;
            for (AfterServiceAuditItemDto one : items) {
                if (passFirst) {
                    productInfo.append(";");
                }
                if (one.getOrderItem().getProduct() == null) {
                    one.getOrderItem().setProduct(productService.findById(one.getOrderItem().getProductId()));
                }
                Product product = one.getOrderItem().getProduct();
                if (product == null) {
                    continue;
                }
                productInfo.append(product.getName())
                        .append(" ")
                        .append(StringUtils.ifnullToString(product.getColorName()))
                        .append("/").
                        append(StringUtils.ifnullToString(product.getSize()));
                if (afterServiceAudit.getType() == TYPE_AFTER_SERVICE_RESEND) {
                    if (EnumNotificationTitle.ORDER_APPLY_REFUND.equals(title)) {
                        productInfo.append("(").append("重发").append(one.getRequestNum()).append("件").append(")");
                    } else {
                        productInfo.append(",重发").append(one.getRealNum()).append("件");
                    }
                }
                passFirst = true;
            }
            List<String> images = Lists.newArrayList();

            if (fileCodes != null) {
                for (String fileCode : fileCodes) {
                    images.add(s3ServiceV2.getDownloadUrl(fileCode));
                }
            }
            content = String.format(content, productInfo.toString());
            notificationService.saveNew(content, title, afterServiceAudit.getMerchantId(),
                    afterServiceAudit.getCreatePlatform().equalsIgnoreCase(Platform.MERCHANT.getValue()) ? afterServiceAudit.getCreateUserId() : null,
                    afterServiceAudit.getOrderId(), afterServiceAudit.getOrderNo(), images
            );
        }
    }

    public boolean hasCancelingOrderItem(Long orderId) {
        List<AfterServiceAuditDto> list = getDtoListByOrderId(orderId);
        if (list.size() > 0) {
            for (AfterServiceAuditDto one : list) {
                if (one.getStatus().equals(STATUS_DISPOSING) || one.getStatus().equals(STATUS_REMITTING)) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<AfterServiceAuditItem> findItemsById(Long id) {
        AfterServiceAuditItem query = new AfterServiceAuditItem();
        query.setAfterServiceAuditId(id);
        return afterServiceAuditItemDao.select(query);
    }

    public List<AfterServiceAuditDto> getDtoListByOrderId(Long orderId) {
        return getDtoListByOrderIds(Collections.singletonList(orderId));
    }

    private ReferDto extractReferDto(AfterServiceAuditDto dto) {
        Address address = dto.getAddress();
        ReferDto referDto = new ReferDto();
        referDto.setCityCode(address.getCityCode());
        referDto.setCity(address.getCity());
        referDto.setProvince(address.getProvince());
        referDto.setProvinceCode(address.getProvinceCode());
        referDto.setPostcode(address.getPostcode());
        referDto.setMerchantId(dto.getMerchantId());
        referDto.setIssuingBayAreaId(dto.getOrder().getIssuingBayAreaId());
        referDto.setTenantId(dto.getOrder().getTenantId());
        referDto.setLogisticsType(OrderOriginEnum.isFba(dto.getOrder().getOriginType()) ? 1 : 2);
        referDto.setClientSelectedLogistics(dto.getOrder().getClientSelectedLogistics());
        referDto.setMerchantStorePlatformCode(dto.getOrder().getMerchantStorePlatformCode());
        referDto.setOutOrderNo(dto.getOrder().getOutOrderNo());
        referDto.setCountry(address.getCountry());
        referDto.setAddressType(address.getType());
        referDto.setOrderOriginType(dto.getOrder().getOriginType());
        //售后不要temu的线上物流
        referDto.setNonServiceProviderIds(LogisticsServiceProviderEnum.temuOnlineIds());
        referDto.setProductInfo(new ArrayList<>());
        for (AfterServiceAuditItemDto item : dto.getItems()) {
            ReferProductInfoDto info = new ReferProductInfoDto();
            info.setProductId(item.getOrderItem().getProductId());
            info.setEndProductId(item.getOrderItem().getEndProductId());
            info.setNum(item.getRealNum() > 0 ? item.getRealNum() : item.getRequestNum());
            info.setCurrentPrice(BigDecimal.valueOf(item.getOrderItem().getPrice()));
            referDto.getProductInfo().add(info);
        }
        if (null != dto.getOrder()) {
            referDto.setLogisticsSource(dto.getOrder().getLogisticsSource());
        }
        return referDto;
    }

    /**
     * 获取申请中的金额
     *
     * @param orderId
     * @param type    1 总金额 2 物流金额
     * @return
     */
    public Double calculateApplyingMoneyById(Long orderId, int type) {
        List<AfterServiceAuditDto> applyingData = getDtoListByOrderId(orderId);
        double money = 0;
        for (AfterServiceAuditDto one : applyingData) {
            if (one.getStatus() == STATUS_DISPOSING || one.getStatus() == STATUS_REMITTING) {
                if (type == 1) {
                    money += one.getRequestRefundAmount();
                } else if (type == 2) {
                    money += one.getRequestRefundCarriageMoney();
                } else {
                    Assert.wrong("type error!");
                }
                money += calculateCharge(ConvertUtil.dtoConvert(one, AfterServiceAudit.class));
            }

        }
        return money;
    }

    public List<AfterServiceAuditDto> getDtoListByOrderIds(List<Long> orderIds) {
        if (orderIds == null || orderIds.size() == 0) {
            return Collections.emptyList();
        }
        AfterServiceAudit query = new AfterServiceAudit();
//        query.setOrderId(orderId);
        TkExample example = new TkExample(AfterServiceAudit.class);
        example.createCriteria().andIn("orderId", orderIds).andEqualTo(query);
        example.setOrderBySort("-createTime");
        List<AfterServiceAuditDto> result = ConvertUtil.dtoConvert(dao.selectByExample(example), AfterServiceAuditDto.class);
        this.ORM(result);
        return result;
    }
    public List<AfterServiceAudit> getListByOrderIds(List<Long> orderIds) {
        if (orderIds == null || orderIds.size() == 0) {
            return Collections.emptyList();
        }
        AfterServiceAudit query = new AfterServiceAudit();
        TkExample example = new TkExample(AfterServiceAudit.class);
        example.createCriteria().andIn("orderId", orderIds).andEqualTo(query);
        example.setOrderBySort("-createTime");
        return dao.selectByExample(example);
    }
    public List<AfterServiceAudit> getListByOrderId(Long id) {
        return getListByOrderIds(Lists.newArrayList(id));
    }

    public List<AfterServiceAuditDto> findById(List<Long> ids) {
        if (ids == null || ids.size() == 0) {
            return new ArrayList<>();
        }
        return afterServiceAuditItemDao.findByItemId(ids);

    }

    public List<AfterServiceAuditDto> getDtoListByResendOrderIds(List<Long> orderIds) {
        if (orderIds == null || orderIds.size() == 0) {
            return Collections.emptyList();
        }
        AfterServiceAudit query = new AfterServiceAudit();
//        query.setOrderId(orderId);
        TkExample example = new TkExample(AfterServiceAudit.class);
        example.createCriteria().andIn("resendOrderId", orderIds).andEqualTo(query);
        example.setOrderBySort("-createTime");
        List<AfterServiceAuditDto> result = ConvertUtil.dtoConvert(dao.selectByExample(example), AfterServiceAuditDto.class);
        this.ORM(result);
        return result;
    }

//    public List<AfterServiceAuditDto> getDtoListByOrderNos(List<String> orderNos) {
//        if (orderNos == null || orderNos.size() == 0) {
//            return Collections.emptyList();
//        }
//        AfterServiceAudit query = new AfterServiceAudit();
////        query.setOrderId(orderId);
//        TkExample example = new TkExample(AfterServiceAudit.class);
//        example.createCriteria().andIn("orderNo", orderNos).andEqualTo(query);
//        example.setOrderBySort("-createTime");
//        List<AfterServiceAuditDto> result = ConvertUtil.dtoConvert(dao.selectByExample(example), AfterServiceAuditDto.class);
//        this.ORM(result);
//        return result;
//    }

    @NotNull
    private static List<OrderAmountHistoryReqDto> getToMerchantRefundOrderAmountHistoryReqDtos(OrderRefundParam orderRefundParam,
                                                                                               Order order, AfterServiceAudit afterServiceAudit,
                                                                                               OrderAmountCalResultDTO.Amount original,
                                                                                               OrderAmountDetailDTO orderAmountDetailDTO) {
        if (null == original) {
            return Collections.emptyList();
        }
        String origin = OrderAmountHistoryConstant.OriginEnum.TO_MERCHANT.getCode();
        List<OrderAmountHistoryReqDto> orderAmountHistoryReqDtos = Lists.newArrayList();
        if (NumberUtils.greaterZero(afterServiceAudit.getRealRefundProductMoney())) {
            OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
            orderAmountHistoryReqDto.setOrderId(order.getId());
            orderAmountHistoryReqDto.setOriginAmount(original.getProductAmount());
            orderAmountHistoryReqDto.setDifferentAmount(BigDecimal.valueOf(afterServiceAudit.getRealRefundProductMoney()).negate());
            orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.PRODUCT.getCode());
            orderAmountHistoryReqDto.setOrigin(origin);

            orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);
        }
        if (NumberUtils.greaterZero(afterServiceAudit.getRealRefundCarriageMoney())) {
            OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
            orderAmountHistoryReqDto.setOrderId(order.getId());
            orderAmountHistoryReqDto.setOriginAmount(original.getCarriageAmount());
            orderAmountHistoryReqDto.setDifferentAmount(BigDecimal.valueOf(afterServiceAudit.getRealRefundCarriageMoney()).negate());
            orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.LOGISTICS.getCode());
            orderAmountHistoryReqDto.setOrigin(origin);

            orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);

        }
        if (NumberUtils.greaterZero(afterServiceAudit.getRealRefundServiceMoney())) {
            OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
            orderAmountHistoryReqDto.setOrderId(order.getId());
            orderAmountHistoryReqDto.setOriginAmount(Optional.of(original.getServiceAmount()).map(i -> i.getTotalServiceAmount()).orElse(BigDecimal.ZERO));
            orderAmountHistoryReqDto.setDifferentAmount(BigDecimal.valueOf(afterServiceAudit.getRealRefundServiceMoney()).negate());
            orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.SERVICE.getCode());
            orderAmountHistoryReqDto.setOrigin(origin);

            orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);

        }
        if (NumberUtils.greaterZero(afterServiceAudit.getRealRefundMaterialServiceMoney())) {
            OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
            orderAmountHistoryReqDto.setOrderId(order.getId());
            orderAmountHistoryReqDto.setOriginAmount(original.getMaterialServiceAmount());
            orderAmountHistoryReqDto.setDifferentAmount(BigDecimal.valueOf(afterServiceAudit.getRealRefundMaterialServiceMoney()).negate());
            orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.MATERIAL_SERVICE.getCode());
            orderAmountHistoryReqDto.setOrigin(origin);

            orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);
        }
        if (NumberUtils.greaterZero(orderRefundParam.getLabelAmount())) {
            OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
            orderAmountHistoryReqDto.setOrderId(order.getId());
            orderAmountHistoryReqDto.setOriginAmount(Optional.of(original.getServiceAmount()).map(i -> i.getFbaLabelAmount()).orElse(BigDecimal.ZERO));
            orderAmountHistoryReqDto.setDifferentAmount(orderRefundParam.getLabelAmount().negate());
            orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.LABEL.getCode());
            orderAmountHistoryReqDto.setOrigin(origin);

            orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);
        }
        if (null != orderAmountDetailDTO) {
            Double fbaBoxAmount = orderAmountDetailDTO.getFbaBoxAmount();
            Double takeSelfAmount = orderAmountDetailDTO.getTakeSelfAmount();
            if (NumberUtils.greaterZero(orderRefundParam.getBoxAmount())) {
                OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
                orderAmountHistoryReqDto.setOrderId(order.getId());
                orderAmountHistoryReqDto.setOriginAmount(NumberUtil.toBigDecimal(fbaBoxAmount));
                orderAmountHistoryReqDto.setDifferentAmount(orderRefundParam.getBoxAmount().negate());
                orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.BOX.getCode());
                orderAmountHistoryReqDto.setOrigin(origin);

                orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);
            }
            if (NumberUtils.greaterZero(orderRefundParam.getTakeSelfAmount())) {
                OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
                orderAmountHistoryReqDto.setOrderId(order.getId());
                orderAmountHistoryReqDto.setOriginAmount(NumberUtil.toBigDecimal(takeSelfAmount));
                orderAmountHistoryReqDto.setDifferentAmount(orderRefundParam.getTakeSelfAmount().negate());
                orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.TAKE_SELF.getCode());
                orderAmountHistoryReqDto.setOrigin(origin);

                orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);
            }
        }
        return orderAmountHistoryReqDtos;
    }

    private List<OrderAmountHistoryReqDto> getToTenantRefundOrderAmountHistoryReqDtos(OrderRefundParam orderRefundParam,
                                                                                      Order order, AfterServiceAudit afterServiceAudit,
                                                                                      OrderAmountCalResultDTO.Amount original,
                                                                                      OrderAmountDetailDTO orderAmountDetailDTO) {
        if(null==original){
            return Collections.emptyList();
        }
        String origin = OrderAmountHistoryConstant.OriginEnum.TO_TENANT.getCode();
        List<OrderAmountHistoryReqDto> orderAmountHistoryReqDtos = Lists.newArrayList();
        if (NumberUtils.greaterZero(afterServiceAudit.getRealRefundTenantProductMoney())) {
            OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
            orderAmountHistoryReqDto.setOrderId(order.getId());
            orderAmountHistoryReqDto.setOriginAmount(original.getProductAmount());
            orderAmountHistoryReqDto.setDifferentAmount(BigDecimal.valueOf(afterServiceAudit.getRealRefundTenantProductMoney()).negate());
            orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.PRODUCT.getCode());
            orderAmountHistoryReqDto.setOrigin(origin);
            orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);
        }
        if (NumberUtils.greaterZero(afterServiceAudit.getRealRefundTenantCarriageMoney())) {
            OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
            orderAmountHistoryReqDto.setOrderId(order.getId());
            orderAmountHistoryReqDto.setOriginAmount(original.getCarriageAmount());
            orderAmountHistoryReqDto.setDifferentAmount(BigDecimal.valueOf(afterServiceAudit.getRealRefundTenantCarriageMoney()).negate());
            orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.LOGISTICS.getCode());
            orderAmountHistoryReqDto.setOrigin(origin);
            orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);

        }
        if (NumberUtils.greaterZero(afterServiceAudit.getRealRefundTenantServiceMoney())) {
            OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
            orderAmountHistoryReqDto.setOrderId(order.getId());
            orderAmountHistoryReqDto.setOriginAmount(Optional.of(original.getServiceAmount()).map(i -> i.getTotalServiceAmount()).orElse(BigDecimal.ZERO));
            orderAmountHistoryReqDto.setDifferentAmount(BigDecimal.valueOf(afterServiceAudit.getRealRefundTenantServiceMoney()).negate());
            orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.SERVICE.getCode());
            orderAmountHistoryReqDto.setOrigin(origin);
            orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);

        }
        if (NumberUtils.greaterZero(afterServiceAudit.getRealRefundTenantMaterialServiceMoney())) {
            OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
            orderAmountHistoryReqDto.setOrderId(order.getId());
            orderAmountHistoryReqDto.setOriginAmount(original.getMaterialServiceAmount());
            orderAmountHistoryReqDto.setDifferentAmount(BigDecimal.valueOf(afterServiceAudit.getRealRefundTenantMaterialServiceMoney()).negate());
            orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.MATERIAL_SERVICE.getCode());
            orderAmountHistoryReqDto.setOrigin(origin);
            orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);
        }
        if (NumberUtils.greaterZero(orderRefundParam.getLabelAmount())) {
            OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
            orderAmountHistoryReqDto.setOrderId(order.getId());
            orderAmountHistoryReqDto.setOriginAmount(Optional.of(original.getServiceAmount()).map(i -> i.getFbaLabelAmount()).orElse(BigDecimal.ZERO));
            orderAmountHistoryReqDto.setDifferentAmount(orderRefundParam.getLabelAmount().negate());
            orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.LABEL.getCode());
            orderAmountHistoryReqDto.setOrigin(origin);
            orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);
        }
        if (null != orderAmountDetailDTO) {
            Double fbaBoxAmount = orderAmountDetailDTO.getFbaBoxAmount();
            Double takeSelfAmount = orderAmountDetailDTO.getTakeSelfAmount();
            if (NumberUtils.greaterZero(orderRefundParam.getBoxAmount())) {
                OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
                orderAmountHistoryReqDto.setOrderId(order.getId());
                orderAmountHistoryReqDto.setOriginAmount(NumberUtil.toBigDecimal(fbaBoxAmount));
                orderAmountHistoryReqDto.setDifferentAmount(orderRefundParam.getBoxAmount().negate());
                orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.BOX.getCode());
                orderAmountHistoryReqDto.setOrigin(origin);
                orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);
            }
            if (NumberUtils.greaterZero(orderRefundParam.getTakeSelfAmount())) {
                OrderAmountHistoryReqDto orderAmountHistoryReqDto = new OrderAmountHistoryReqDto();
                orderAmountHistoryReqDto.setOrderId(order.getId());
                orderAmountHistoryReqDto.setOriginAmount(NumberUtil.toBigDecimal(takeSelfAmount));
                orderAmountHistoryReqDto.setDifferentAmount(orderRefundParam.getTakeSelfAmount().negate());
                orderAmountHistoryReqDto.setAmountType(OrderAmountHistoryConstant.AmountTypeEnum.TAKE_SELF.getCode());
                orderAmountHistoryReqDto.setOrigin(origin);
                orderAmountHistoryReqDtos.add(orderAmountHistoryReqDto);
            }
        }
        return orderAmountHistoryReqDtos;
    }

    /**
     * 生成售后记录
     * 注：待确认订单不用审核，会直接置为已处理
     *  @param oldOrderStatus order修改前的状态
     * @param order          完整的ORDER
     * @param param          param
     * @param user           用户(商户/运营)
     * @return
     */
    public long addRecordByMerchantOrder(OrderRefundParam orderRefundParam, Integer oldOrderStatus, Order order, CreateParam param, User user) {
        logger.info("addRecordByMerchantOrder in");
        if (param.getType() == null || !Arrays.asList(TYPE_CANCEL_REFUND, TYPE_AFTER_SERVICE_REFUND, TYPE_AFTER_SERVICE_RESEND).contains(param.type)) {
            logger.info("addRecordByMerchantOrder void return");
            return 0;
        }
        OrderAmountRespDTO orderAmountRespDTO = orderService.getOrderAmountRespDTO(order);
        AfterServiceAudit afterServiceAudit = new AfterServiceAudit();
        afterServiceAudit.setTenantId(orderAmountRespDTO.getTenantId());
        afterServiceAudit.setProductTenantId(orderAmountRespDTO.getProductTenantId());
        afterServiceAudit.setCreatePlatform(param.getPlatform().getValue());
        afterServiceAudit.setCreateUserContact(user.getPhone() == null ? "" : user.getPhone());
        afterServiceAudit.setCreateUserId(user.getId());
        afterServiceAudit.setAddressId(order.getAddressId());
        afterServiceAudit.setCountryExpressInfoId(order.getCountryExpressInfoId());
        afterServiceAudit.setCreateUserName(user.getUsername());
        afterServiceAudit.setEvidenceImgs(param.getEvidenceImgsArrayJson());
        afterServiceAudit.setMerchantId(param.getMerchantId());
        afterServiceAudit.setMerchantStoreId(order.getMerchantStoreId());
        afterServiceAudit.setMerchantNo(param.getMerchantNo());
        afterServiceAudit.setNo(OrderCodeUtil.getOrderIdByUUId());
        afterServiceAudit.setOrderItemTotalNum(0);
        afterServiceAudit.setRequestRemark(param.getRemark());
        param.getItems().stream().map(AfterServiceSubmitVo.Item::getNum).forEach(integer -> {
            afterServiceAudit.setOrderItemTotalNum(afterServiceAudit.getOrderItemTotalNum() + integer);
        });
        afterServiceAudit.setOrderId(order.getId());
        afterServiceAudit.setIsAdvance(param.getIsAdvance());
        afterServiceAudit.setOrderNo(order.getNo());
        afterServiceAudit.setOrderPayMethod(orderRefundParam.getOrderAmountRespDTO().getPaymentMethod());
        afterServiceAudit.setOrderDesignStatus(order.getDesignStatus());
        afterServiceAudit.setOrderStatus(oldOrderStatus);
        afterServiceAudit.setRequestRemark(param.getRemark());

        afterServiceAudit.setRequestRefundAmount(param.getRefundAmount());
        afterServiceAudit.setRequestRefundCarriageMoney(param.getRefundCarriageMoney());
        afterServiceAudit.setRequestRefundProductMoney(param.getRefundProductMoney());
        afterServiceAudit.setRequestRefundServiceMoney(param.getRefundServiceMoney());
        afterServiceAudit.setRequestRefundMaterialServiceMoney(param.getRefundMaterialServiceMoney());

        afterServiceAudit.setRealRefundAmount(afterServiceAudit.getRequestRefundAmount());
        afterServiceAudit.setRealRefundCarriageMoney(afterServiceAudit.getRequestRefundCarriageMoney());
        afterServiceAudit.setRealRefundProductMoney(afterServiceAudit.getRequestRefundProductMoney());
        afterServiceAudit.setRealRefundServiceMoney(afterServiceAudit.getRequestRefundServiceMoney());
        afterServiceAudit.setRealRefundMaterialServiceMoney(afterServiceAudit.getRequestRefundMaterialServiceMoney());

        //请求退租户钱
        afterServiceAudit.setRequestRefundTenantProductMoney(param.getTenantRefundProductMoney());
        afterServiceAudit.setRequestRefundTenantServiceMoney(param.getTenantRefundServiceMoney());
        afterServiceAudit.setRequestRefundTenantCarriageMoney(param.getTenantRefundCarriageMoney());
        afterServiceAudit.setRequestRefundTenantMaterialServiceMoney(param.getTenantRefundMaterialServiceMoney());
        afterServiceAudit.setRequestRefundTenantAmount(param.getTenantRefundAmount());

        //实际退租户钱
        afterServiceAudit.setRealRefundTenantProductMoney(param.getTenantRefundProductMoney());
        afterServiceAudit.setRealRefundTenantServiceMoney(param.getTenantRefundServiceMoney());
        afterServiceAudit.setRealRefundTenantCarriageMoney(param.getTenantRefundCarriageMoney());
        afterServiceAudit.setRealRefundTenantMaterialServiceMoney(param.getTenantRefundMaterialServiceMoney());
        afterServiceAudit.setRealRefundTenantAmount(param.getTenantRefundAmount());


        logger.info("取消，realRefundTenantCarriageMoney={}", afterServiceAudit.getRealRefundTenantCarriageMoney());
        afterServiceAudit.setType(param.getType());
        afterServiceAudit.setCauseType(param.getCauseType());
        afterServiceAudit.setStatus(param.getType() == TYPE_CANCEL_REFUND && oldOrderStatus.equals(OrderStatus.PAIN.getStatus()) ? STATUS_SUCCESS : STATUS_DISPOSING);

        if (param.isComplete()) {
            afterServiceAudit.setStatus(STATUS_SUCCESS);
        }
        //current_carriage calculate
        if (param.getType() == TYPE_CANCEL_REFUND) {
//            RefundAdviceVo refundAdviceVo = orderService.refundAdvise(order, param.getItems().stream().map(AfterServiceSubmitVo.Item::getId).collect(Collectors.toList()));
//            afterServiceAudit.setOrderCarriageAmount(refundAdviceVo.getLeftCarriageAmount());
//            afterServiceAudit.setOrderNewCarriageAmount(refundAdviceVo.getNewCarriageAmount());
            afterServiceAudit.setCharge(0d);
            if (DistributionProductLogisticsSourceEnum.isTenantLogistics(order.getLogisticsSource())) {
                afterServiceAudit.setRealRefundTenantCarriageMoney(0D);
                afterServiceAudit.setRealRefundTenantServiceMoney(0D);
            }
        }
        afterServiceAudit.setOriginType(order.getOriginType());
        logger.info("取消，afterServiceAudit={}", JSON.toJSONString(afterServiceAudit));

        // 金额变动记录
        String tradeNo = saveOrderAmountHistory(orderRefundParam, order, afterServiceAudit, param.getItems());
        afterServiceAudit.setTradeNo(tradeNo);
        this.dao.insertSelective(afterServiceAudit);
        long id = afterServiceAudit.getId();

        List<Long> orderItemIds = new ArrayList<>();
        for (AfterServiceSubmitVo.Item one : param.getItems()) {
            AfterServiceAuditItem item = new AfterServiceAuditItem();
            item.setAfterServiceAuditId(id);
            item.setOrderItemId(one.getId());
            orderItemIds.add(one.getId());
            item.setRequestNum(one.getNum());
            item.setRealNum(item.getRequestNum());
            item.setType(param.getType());
            item.setStatus(param.getType() == TYPE_CANCEL_REFUND && oldOrderStatus.equals(OrderStatus.PAIN) ? ITEM_STATUS_SUCCESS : ITEM_STATUS_DISPOSING);
            if (param.isComplete()) {
                item.setStatus(ITEM_STATUS_SUCCESS);
            }
            afterServiceAuditItemDao.insertSelective(item);
        }

        if (param.isComplete()) {
            double charge = calculateCharge(afterServiceAudit);
            //提取退款订单
            order.setItems(order.getItems().stream().filter(orderItem -> orderItemIds.contains(orderItem.getId())).collect(Collectors.toList()));
            refundService.addRefundRecord(
                orderRefundParam,
                order.getCustomerId(),
                orderRefundParam.getMerchant().getContactName(),
                orderRefundParam.getMerchant().getContactTel(),
                afterServiceAudit.getRequestRemark(),
                afterServiceAudit.getRealRefundProductMoney(),
                afterServiceAudit.getRealRefundCarriageMoney(),
                afterServiceAudit.getRealRefundServiceMoney(),
                afterServiceAudit.getRealRefundMaterialServiceMoney(),
                afterServiceAudit.getRealRefundAmount(),
                order, RefundType.CANCEL, true, charge, afterServiceAudit.getId(), user.getId());
        }
        return id;

    }

    public OrderAmountCalResultDTO getOrderAmountCalResultDTO(Long orderId, List<Long> orderItemIds, boolean refundProductAmount) {
        List<OrderItem> orderItems = orderItemService.findByIds(orderItemIds);
        Map<Long, Integer> unShipNumMap = orderCancelFeign.unShipOrderItemQty(orderId);
        List<BaseIdQtyDTO> cancelItemList = Lists.newArrayList();
        for (OrderItem orderItem : orderItems) {
            Integer unshipNum = unShipNumMap.get(orderItem.getId());
            BaseIdQtyDTO cancelItem = new BaseIdQtyDTO();
            if (!NumberUtils.greaterZero(unshipNum)) {
                continue;
            }
            cancelItem.setQty(unshipNum);
            cancelItem.setId(orderItem.getId());
            cancelItemList.add(cancelItem);
        }

        Order order = orderService.findById(orderId);
        Boolean isFba = OrderOriginEnum.isFba(order.getOriginType());
        PodOrderRefundReqDTO refundReqDTO = new PodOrderRefundReqDTO();
        refundReqDTO.setOrderId(orderId);
        refundReqDTO.setWholeOrderCancel(false);
        refundReqDTO.setCancelItemList(cancelItemList);
        refundReqDTO.setForceRefundProductAmount(refundProductAmount);
        refundReqDTO.setIsFba(isFba);
        OrderAmountCalResultDTO calResultDTO = this.orderCancelFeign.podRefundAdvise(refundReqDTO);
        return calResultDTO;
    }

    private String saveOrderAmountHistory(OrderRefundParam orderRefundParam, Order order, AfterServiceAudit afterServiceAudit,
                                        List<AfterServiceSubmitVo.Item> items) {
        List<Long> orderItemIds = items.stream().map(i -> i.getId()).collect(Collectors.toList());
        OrderAmountCalResultDTO orderAmountCalResultDTO = getOrderAmountCalResultDTO(order.getId(), orderItemIds, orderRefundParam.isForcedRefundProductAmount());
        OrderAmountCalResultDTO.Amount original = orderAmountCalResultDTO.getOriginal();
        OrderAmountCalResultDTO.Amount tenantOriginal = orderAmountCalResultDTO.getTenantOriginal();

        Map<Long, OrderAmountDetailDTO> orderAmountDetailDTOMap = orderAmountDetailFeign.findByOrderIds(Lists.newArrayList(order.getId()));
        OrderAmountDetailDTO orderAmountDetailDTO = orderAmountDetailDTOMap.get(order.getId());
        List<OrderAmountHistoryReqDto> toMerchantOrderAmountHistoryReqDtos = getToMerchantRefundOrderAmountHistoryReqDtos(orderRefundParam, order, afterServiceAudit, original,orderAmountDetailDTO);
        List<OrderAmountHistoryReqDto> toTenantOrderAmountHistoryReqDtos = getToTenantRefundOrderAmountHistoryReqDtos(orderRefundParam, order, afterServiceAudit, tenantOriginal,orderAmountDetailDTO);

        List<OrderAmountHistoryReqDto> orderAmountHistoryReqDtos = Lists.newArrayList();
        if (CollUtil.isNotEmpty(toMerchantOrderAmountHistoryReqDtos)) {
            orderAmountHistoryReqDtos.addAll(toMerchantOrderAmountHistoryReqDtos);
        }
        if (CollUtil.isNotEmpty(toTenantOrderAmountHistoryReqDtos)) {
            orderAmountHistoryReqDtos.addAll(toTenantOrderAmountHistoryReqDtos);
        }
       return orderAmountHistoryFeign.batchSave(orderAmountHistoryReqDtos);
    }


    /**
     * 运营直接售后
     * v6.0 shanbin_sun
     */
    public void adminAfterSales(OrderRefundParam refundParam, Integer oldOrderStatus, Order order, AfterServiceSubmitVo param, User user, AfterServiceAuditVo vo) {
        logger.info("AdminAfterSales in");

        checkParamete(oldOrderStatus, order, vo, refundParam);

        if (StrUtil.isEmpty(vo.getRemark())) {
            vo.setRemark("");
        }
        Merchant merchant = merchantDao.findById(order.getMerchantId());
        OrderAmountRespDTO amountRespDTO = orderService.getOrderAmountRespDTO(order);
        AfterServiceAudit afterServiceAudit = saveAfterServiceAudit(refundParam, amountRespDTO, oldOrderStatus, order, param, user, vo, merchant);
        Long id = afterServiceAudit.getId();

        //保存item
        List<AfterServiceAuditItem> afterServiceAuditItems = saveAfterServiceAuditItem(param, vo, id);


        //售后责任
        Integer dutyAffiliation = vo.getDutyAffiliation();
        List<AfterServiceAuditItem> items = findItemsById(afterServiceAudit.getId());

        List<Long> orderItemIds = vo.getItems().stream().map(AfterServiceAuditVo.Item::getId).collect(Collectors.toList());
        List<FactoryOrder> factoryOrders = factoryOrderDao.findByOrderItemIds(orderItemIds);
        Map<Long, List<FactoryOrder>> factoryOrdersMap = Maps.newHashMap();
        for (FactoryOrder factoryOrder : factoryOrders) {
            List<FactoryOrder> factoryOrderList = factoryOrdersMap.getOrDefault(factoryOrder.getOrderItemId(), Lists.newArrayList());
            factoryOrderList.add(factoryOrder);
            factoryOrdersMap.put(factoryOrder.getOrderItemId(), factoryOrderList);
        }
        Map<Long, Integer> factoryOrderRefundNumMap = Maps.newHashMap();

        for (AfterServiceAuditVo.Item voItem : vo.getItems()) {

            List<FactoryOrder> factoryOrderList = factoryOrdersMap.get(voItem.getId());
            List<AfterServiceAuditItemFactoryOrderDto> afterServiceAuditItemFactoryOrderDtos = Lists.newArrayList();
            for (FactoryOrder factoryOrder : factoryOrderList) {
                AfterServiceAuditItemFactoryOrderDto afterServiceAuditItemFactoryOrderDto = new AfterServiceAuditItemFactoryOrderDto();
                afterServiceAuditItemFactoryOrderDto.setId(factoryOrder.getId());
                afterServiceAuditItemFactoryOrderDto.setNum(voItem.getNum());
                factoryOrderRefundNumMap.put(factoryOrder.getId(), voItem.getNum());
                afterServiceAuditItemFactoryOrderDto.setFactoryBackupType("none");
                afterServiceAuditItemFactoryOrderDtos.add(afterServiceAuditItemFactoryOrderDto);
            }
            voItem.setFactoryOrders(afterServiceAuditItemFactoryOrderDtos);

            for (AfterServiceAuditItem item : items) {
                if (item.getOrderItemId().equals(voItem.getId())) {
                    item.setFactoryBackupType(voItem.getFactoryBackupType());
                    item.setFactoryOrders(voItem.getFactoryOrders());
                }
            }
        }

        List<FactoryOrder> list = saveAfterServiceAuditItemDetail(items, factoryOrders);

        //List<AfterServiceAuditItemDto> dtoItems = ConvertUtil.dtoConvert(items, AfterServiceAuditItemDto.class);
        //this.ORM(dtoItems);
        //消息
        //notification(afterServiceAudit, dtoItems, pass, vo.getAuditImgs());

        OrderRemarkOperation operation = OrderRemarkOperation.PASS_AFTER_SERVICE_REFUND;
        orderService.addCancelRemark(user, order.getId(), OrderRemarkOperation.getAfterServiceJsonInfo(order, afterServiceAudit, items, operation), Platform.ADMIN, operation);
        refundParam.setTenantRefundProductMoney(vo.getTenantProductAmount());
        AfterServiceManage.decoratorTenantRefundByAfterServiceItems(refundParam, order, afterServiceAuditItems);
        if (afterServiceManage.isFbaAndRefundToTenant(refundParam, order)) {
            //分销fba订单运营售后退款，退租户产品金额按照传入的金额进行退款
            //退款/退赠送金X=退总金额/退总赠送金，计算退赠送金X
            Double temp = NumberUtil.mul(vo.getTenantProductAmount(), refundParam.getTenantRefundProductFreeGold());
            Double tenantRefundProductFreeGold = NumberUtil.div(temp, refundParam.getTenantRefundProductMoney(), 4);

            refundParam.setTenantRefundProductMoney(vo.getTenantProductAmount());
            refundParam.setTenantRefundProductFreeGold(tenantRefundProductFreeGold);
            refundParam.setTenantRefundServiceMoney(vo.getTenantServiceAmount());
            refundParam.setTenantRefundCarriageMoney(vo.getTenantCarriageAmount());
            refundParam.setTenantRefundMaterialServiceMoney(vo.getTenantMaterialServiceAmount());
        }
        //退款
        refund(refundParam, afterServiceAudit, vo, items, user.getId());


        if (dutyAffiliation != null && dutyAffiliation.equals(DUTY_AFFILIATION_FACTORY)) {
            String images = afterServiceAudit.getEvidenceImgs();
            List<String> urls = Lists.newArrayList();
            if (StringUtils.isNotBlank(images)) {
                urls = JsonUtil.toList(images, String.class);
            }
            if (afterServiceAudit.getType().equals(TYPE_AFTER_SERVICE_REFUND)) {
                for (FactoryOrder factoryOrder : list) {
                    Integer num = factoryOrderRefundNumMap.get(factoryOrder.getId());
                    num = num == null || factoryOrder.getNum() < num ? factoryOrder.getNum() : num;
                    factoryOrderAfterServiceService.insertByFactoryOrder(factoryOrder, id, vo.getRemark(), num, DoubleUtils.mul(factoryOrder.getPrice(), num), afterServiceAudit.getRequestRemark(), urls, vo.getAuditImgs());
                }
            }
        }

        if (vo.getAuditImgs() != null) {
            afterServiceAuditAdminImgService.add(id, vo.getAuditImgs());
        }

        orderSearchService.updateAfterServiceStatus(order, afterServiceAudit.getStatus());

        /***<AUTHOR> @date 2020/7/16 11:00  消息发送*/
        sendMessage(id, user, dutyAffiliation, afterServiceAudit, operation, items, list, null);

    }

    private List<AfterServiceAuditItem> saveAfterServiceAuditItem(AfterServiceSubmitVo param, AfterServiceAuditVo vo, Long id) {
        List<AfterServiceAuditItem> afterServiceAuditItems = Lists.newArrayList();
        for (AfterServiceAuditVo.Item one : vo.getItems()) {
            AfterServiceAuditItem item = new AfterServiceAuditItem();
            item.setAfterServiceAuditId(id);
            item.setOrderItemId(one.getId());
            item.setRequestNum(one.getNum());
            item.setRealNum(item.getRequestNum());
            item.setType(param.getType());
            item.setStatus(ITEM_STATUS_SUCCESS);
            afterServiceAuditItemDao.insertSelective(item);
            afterServiceAuditItems.add(item);
        }
        return afterServiceAuditItems;
    }

    private List<FactoryOrder> saveAfterServiceAuditItemDetail(List<AfterServiceAuditItem> items, List<FactoryOrder> list) {


        //List<FactoryOrder> list = factoryOrderDao.findByIds(factoryOrderIds);
        Map<Long, FactoryOrder> factoryOrderIdMap = list.stream().collect(Collectors.toMap(FactoryOrder::getId, Function.identity()));
        for (AfterServiceAuditItem item : items) {
            for (AfterServiceAuditItemFactoryOrderDto factoryOrder : item.getFactoryOrders()) {
                factoryOrder.setFactoryBackupType(item.getFactoryBackupType());
            }
        }

        for (AfterServiceAuditItem one : items) {
            afterServiceAuditItemDao.updateByPrimaryKeySelective(one);
            for (AfterServiceAuditItemFactoryOrderDto factoryOrder : one.getFactoryOrders()) {
                afterServiceAuditItemDetailService.save(one.getId(), factoryOrderIdMap.get(factoryOrder.getId()), factoryOrder.getNum());
            }
        }
        return list;
    }

    private AfterServiceAudit saveAfterServiceAudit(OrderRefundParam orderRefundParam, OrderAmountRespDTO amountRespDTO, Integer oldOrderStatus, Order order
            , AfterServiceSubmitVo param, User user, AfterServiceAuditVo vo, Merchant merchant) {
        AfterServiceAudit afterServiceAudit = new AfterServiceAudit();
        afterServiceAudit.setTenantId(amountRespDTO.getTenantId());
        afterServiceAudit.setProductTenantId(amountRespDTO.getProductTenantId());
        afterServiceAudit.setCreatePlatform(Platform.ADMIN.getValue());
        afterServiceAudit.setCreateUserContact(user.getPhone() == null ? "" : user.getPhone());
        afterServiceAudit.setCreateUserId(user.getId());
        afterServiceAudit.setAddressId(order.getAddressId());
        afterServiceAudit.setCountryExpressInfoId(order.getCountryExpressInfoId());
        afterServiceAudit.setCreateUserName("平台");
        //afterServiceAudit.setEvidenceImgs(param.getEvidenceImgsArrayJson());
        afterServiceAudit.setMerchantId(merchant.getId());
        afterServiceAudit.setMerchantStoreId(order.getMerchantStoreId());
        afterServiceAudit.setMerchantNo(merchant.getMerchantNo());
        afterServiceAudit.setNo(OrderCodeUtil.getOrderIdByUUId());
        afterServiceAudit.setOrderItemTotalNum(0);
        afterServiceAudit.setRequestRemark(vo.getRemark());
        vo.getItems().stream().map(AfterServiceAuditVo.Item::getNum).forEach(integer -> {
            afterServiceAudit.setOrderItemTotalNum(afterServiceAudit.getOrderItemTotalNum() + integer);
        });
        afterServiceAudit.setOrderId(order.getId());
        afterServiceAudit.setIsAdvance(order.getIsAdvance());
        afterServiceAudit.setOrderNo(order.getNo());
        afterServiceAudit.setOrderPayMethod(orderRefundParam.getOrderAmountRespDTO().getPaymentMethod());
        afterServiceAudit.setOrderDesignStatus(order.getDesignStatus());
        afterServiceAudit.setOrderStatus(oldOrderStatus);
        afterServiceAudit.setRequestRemark("");


        afterServiceAudit.setRealRefundAmount(afterServiceAudit.getRequestRefundAmount());
        afterServiceAudit.setRealRefundCarriageMoney(afterServiceAudit.getRequestRefundCarriageMoney());
        afterServiceAudit.setType(param.getType());
        afterServiceAudit.setCauseType(param.getCauseType());

        afterServiceAudit.setRealRefundProductMoney(vo.getProductMoney());
        afterServiceAudit.setRealRefundCarriageMoney(vo.getCarriageMoney());
        afterServiceAudit.setRealRefundServiceMoney(vo.getServiceMoney());
        afterServiceAudit.setRealRefundMaterialServiceMoney(vo.getMaterialServiceMoney());

        afterServiceAudit.setRealRefundTenantCarriageMoney(vo.getTenantCarriageAmount());

        afterServiceAudit.setRequestRefundProductMoney(vo.getProductMoney());
        afterServiceAudit.setRequestRefundCarriageMoney(vo.getCarriageMoney());
        afterServiceAudit.setRequestRefundServiceMoney(vo.getServiceMoney());
        afterServiceAudit.setRequestRefundMaterialServiceMoney(vo.getMaterialServiceMoney());

        if (param.getType().equals(AfterServiceAuditService.TYPE_AFTER_SERVICE_REFUND)) {
            if (afterServiceManage.isFbaAndRefundToTenant(orderRefundParam, order)) {
                //退租户的总金额
                Double refundTenantAmount = DoubleUtils.scale(vo.getTenantProductAmount() + vo.getTenantServiceAmount() +
                    vo.getTenantCarriageAmount() + vo.getTenantMaterialServiceAmount());
                //请求退租户钱
                afterServiceAudit.setRequestRefundTenantProductMoney(vo.getTenantProductAmount());
                afterServiceAudit.setRequestRefundTenantServiceMoney(vo.getTenantServiceAmount());
                afterServiceAudit.setRequestRefundTenantCarriageMoney(vo.getTenantCarriageAmount());
                afterServiceAudit.setRequestRefundTenantMaterialServiceMoney(vo.getTenantMaterialServiceAmount());
                afterServiceAudit.setRequestRefundTenantAmount(refundTenantAmount);

                //实际退租户钱
                afterServiceAudit.setRealRefundTenantProductMoney(vo.getTenantProductAmount());
                afterServiceAudit.setRealRefundTenantServiceMoney(vo.getTenantServiceAmount());
                afterServiceAudit.setRealRefundTenantCarriageMoney(vo.getTenantCarriageAmount());
                afterServiceAudit.setRealRefundTenantMaterialServiceMoney(vo.getTenantMaterialServiceAmount());
                afterServiceAudit.setRealRefundTenantAmount(refundTenantAmount);
            }
        }


        Double sumMoney = DoubleUtils.scale(afterServiceAudit.getRealRefundProductMoney() + afterServiceAudit.getRealRefundCarriageMoney() + afterServiceAudit.getRealRefundServiceMoney() + afterServiceAudit.getRealRefundMaterialServiceMoney());
        afterServiceAudit.setRequestRefundAmount(sumMoney);
        afterServiceAudit.setRealRefundAmount(sumMoney);
        afterServiceAudit.setStatus(STATUS_SUCCESS);
        afterServiceAudit.setAuditTime(System.currentTimeMillis());
        afterServiceAudit.setAuditRemark(vo.getRemark());
        afterServiceAudit.setDutyAffiliation(vo.getDutyAffiliation());
        //手续费
        afterServiceAudit.setCharge(0.00);
        afterServiceAudit.setOriginType(order.getOriginType());
        //this.dao.updateByPrimaryKeySelective(afterServiceAudit);


        this.dao.insertSelective(afterServiceAudit);
        return afterServiceAudit;

    }

    private void checkParamete(Integer oldOrderStatus, Order order, AfterServiceAuditVo vo, OrderRefundParam refundParam) {
        if (oldOrderStatus == null || CollectionUtils.isEmpty(vo.getItems())) {
            logger.info("AdminAfterSales void return");
            return;
        }
        if (vo.getDutyAffiliation() == null || !Arrays.asList(DUTY_AFFILIATION_ADMIN, DUTY_AFFILIATION_FACTORY).contains(vo.getDutyAffiliation())) {
            Assert.wrong("duty_affiliation can`t empty!");
        }

        if (vo.getProductMoney() == null || vo.getProductMoney() < 0) {
            Assert.wrong("productMoney error!");
        } else if (vo.getCarriageMoney() == null || vo.getCarriageMoney() < 0) {
            Assert.wrong("carriageMoney error!");
        }

        boolean fbaAndRefundToTenant = afterServiceManage.isFbaAndRefundToTenant(refundParam, order);
        if (vo.getProductMoney() > DoubleUtils.scale(order.getProductAmount() - order.getRefundProductAmount())) {
            if(fbaAndRefundToTenant){
                Assert.wrong("租户赔付商户：超出可退产品金额！");
            }else{
                Assert.wrong("超出可退产品金额！");
            }
        }
        if (vo.getCarriageMoney() > DoubleUtils.scale(order.getCarriageAmount() - order.getRefundCarriageAmount())) {
            if(fbaAndRefundToTenant){
                Assert.wrong("租户赔付商户：超出可退物流金额！");
            }else{
                Assert.wrong("超出可退物流金额！");
            }
        }
        if (fbaAndRefundToTenant) {
            OrderAmountRespDTO orderAmountRespDTO = refundParam.getOrderAmountRespDTO();
            List<RefundRecord> refundRecords = refundService.getRefundRecourdByNo(order.getNo());
            double tenantRefundProductAmount = CollUtil.isNotEmpty(refundRecords) ? refundRecords.stream().mapToDouble(i -> i.getTenantProductPrice()).sum() : 0D;
            double tenantRefundCarriageAmount = CollUtil.isNotEmpty(refundRecords) ? refundRecords.stream().mapToDouble(i -> i.getTenantCarriagePrice()).sum() : 0D;
            double tenantRefundServiceAmount = CollUtil.isNotEmpty(refundRecords) ? refundRecords.stream().mapToDouble(i -> i.getTenantServicePrice()).sum() : 0D;
            if (vo.getTenantProductAmount() > DoubleUtils.scale(orderAmountRespDTO.getTenantProductAmount() - tenantRefundProductAmount)) {
                Assert.wrong("Saas赔付租户：超出可退产品金额！");
            }
            if (vo.getTenantCarriageAmount() > DoubleUtils.scale(orderAmountRespDTO.getTenantCarriageAmount() - tenantRefundCarriageAmount)) {
                Assert.wrong("Saas赔付租户：超出可退物流金额！");
            }
            if (vo.getTenantServiceAmount() > DoubleUtils.scale(orderAmountRespDTO.getTenantServiceAmount() - tenantRefundServiceAmount)) {
                Assert.wrong("Saas赔付租户：退款的服务费金额不能大于订单剩余的服务费金额！");
            }
            //目前租户没有官方素材，所有素材服务费是0，如果后序有，这里需要修改
            if (vo.getTenantMaterialServiceAmount() > 0D) {
                Assert.wrong("Saas赔付租户：退款的素材服务费金额不能大于订单剩余的素材服务费金额!");
            }

        }

    }

    public double calculateCharge(AfterServiceAudit afterServiceAudit) {
        return 0d;
//        double charge = afterServiceAudit.getCharge();
//        if (afterServiceAudit.getOrderCarriageAmount() - afterServiceAudit.getOrderNewCarriageAmount() - afterServiceAudit.getCharge() < 0) {
//            charge = afterServiceAudit.getOrderCarriageAmount() - afterServiceAudit.getOrderNewCarriageAmount();
//        }
//        return charge;
    }

    public List<AfterServiceAuditItem> findItemsByOrderItemId(Collection<Long> orderItemIds) {
        if (orderItemIds == null || orderItemIds.size() == 0) {
            return new ArrayList<>();
        }
        return afterServiceAuditItemDao.findItemsByOrderItemId(orderItemIds);
    }

    public List<AfterServiceAuditItem> findItemsByAfterServiceIds(List<Long> ids) {
        if (ids == null || ids.size() == 0) {
            return new ArrayList<>();
        }
        return afterServiceAuditItemDao.findItemsByAfterServiceAuditIds(ids);
    }


    public BaseDownloadDTO exportPod(AfterServiceAuditPageParam param) {
        List<AfterServiceAuditExportPodDto> exportDataToPod = getExportDataToPod(param);
        LinkedHashMap<String, Integer> mergeNameColumnIndexMap = Maps.newLinkedHashMap();
        try {
            String url = dataExportHistoryUtil.uploadFileToS3(exportDataToPod, AfterServiceAuditExportPodDto.class,
                    "售后订单导出" + com.ziguang.base.support.DateUtil.longToString(System.currentTimeMillis()) + ".xlsx", mergeNameColumnIndexMap, null);
            BaseDownloadDTO baseDownloadDTO = new BaseDownloadDTO();
            baseDownloadDTO.setDownloadUrl(url);
            return baseDownloadDTO;
        } catch (Exception e) {
            log.error("售后订单导出异常", e);
            throw new BusinessException("售后订单导出异常{}", e.getMessage());
        }
    }

    public List<AfterServiceAuditExportPodDto> getExportDataToPod(AfterServiceAuditPageParam param) {
        param.setSdsPlatform(SdsPlatformEnum.POD.getCode());
        param.setFormatLogisticsSourceAndShip(false);
        param.setSize(MAX_EXPORT_NUM * 5);
        param.setPage(1);
        //查询符合条件的退款售后单列表
        SearchBean<AfterServiceAuditDto> searchBean = this.list(param);
        // 校验最大导出数据量
        if (searchBean.getTotalCount() > MAX_EXPORT_NUM * 5) {
            throw new BusinessException("导出数量超过50000条，请联系管理员导出");
        }
        List<AfterServiceAuditDto> afterServiceAuditDtoList = searchBean.getItems();

        List<AfterServiceAuditExportPodDto> afterServiceAuditExportDtoList = Lists.newArrayList();
        if (CollUtil.isEmpty(afterServiceAuditDtoList)) {
            return afterServiceAuditExportDtoList;
        }

        List<Long> resendOrderId = afterServiceAuditDtoList.stream()
                .filter(a -> AFTER_SERVICE_RESEND.getCode().equals(a.getType()))
                .filter(a -> AfterServiceAuditStatusEnum.SUCCESS.getCode().equals(a.getStatus()))
                .map(AfterServiceAuditDto::getResendOrderId).collect(Collectors.toList());
        Map<Long, Order> resendOrderIdMap = CollectionUtils.isEmpty(resendOrderId) ? Maps.newHashMap() : orderService.findByIds(resendOrderId).stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        // 生成导出数据列表
        afterServiceAuditDtoList.forEach(afterServiceAuditDto -> {
            AfterServiceAuditExportPodDto afterServiceAuditExportDto = new AfterServiceAuditExportPodDto();
            afterServiceAuditExportDto.setOrderNo(afterServiceAuditDto.getOrderNo());
            afterServiceAuditExportDto.setGmtCreated(afterServiceAuditDto.gmtCreated());
            afterServiceAuditExportDto.setMerchantNoCode(afterServiceAuditDto.getMerchantNoCode());
            if (afterServiceAuditDto.getTenantId().equals(afterServiceAuditDto.getProductTenantId())) {
                afterServiceAuditExportDto.setMerchantNoCode(afterServiceAuditDto.getMerchantNo());
            }
            afterServiceAuditExportDto.setTenantName(afterServiceAuditDto.getTenantName());
            afterServiceAuditExportDto.setOrderPayMethod(PaymentMethod.getByValud(afterServiceAuditDto.getOrderPayMethod()).getDesc());
            afterServiceAuditExportDto.setTypeName(AfterServiceAuditTypeEnum.getByCode(afterServiceAuditDto.getType()).getDesc());
            afterServiceAuditExportDto.setStatus(AfterServiceAuditStatusEnum.getByCode(afterServiceAuditDto.getStatus()).getDesc());
            afterServiceAuditExportDto.setRequestRefundAmount(BigDecimal.valueOf(afterServiceAuditDto.getRequestRefundAmount()));
            if (AFTER_SERVICE_REFUND.getCode().equals(afterServiceAuditDto.getType())) {
                if (AfterServiceAuditStatusEnum.REFUSED.getCode().equals(afterServiceAuditDto.getStatus())) {
                    afterServiceAuditExportDto.setRequestRefundAmount(BigDecimal.ZERO);
                } else {
                    afterServiceAuditExportDto.setRequestRefundAmount(BigDecimal.valueOf(afterServiceAuditDto.getRealRefundAmount()));
                }
            }
            if (AFTER_SERVICE_RESEND.getCode().equals(afterServiceAuditDto.getType())) {
                Order resendOrder = resendOrderIdMap.get(afterServiceAuditDto.getResendOrderId());
                if (AfterServiceAuditStatusEnum.REFUSED.getCode().equals(afterServiceAuditDto.getStatus()) || resendOrder == null) {
                    afterServiceAuditExportDto.setRequestRefundAmount(BigDecimal.ZERO);
                } else {
                    //分销订单 物流来源是租户物流来源或责任是租户自己的不算物流费
                    if (!Objects.equals(afterServiceAuditDto.getTenantId(), afterServiceAuditDto.getProductTenantId())
                            && (DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(resendOrder.getLogisticsSource())
                            || AfterServiceAuditService.DUTY_AFFILIATION_TENANT == afterServiceAuditDto.getDutyAffiliation())) {
                        afterServiceAuditExportDto.setRequestRefundAmount(BigDecimal.valueOf(NumberUtil.add(resendOrder.getProductAmount(), resendOrder.getServiceAmount())));
                    } else {
                        afterServiceAuditExportDto.setRequestRefundAmount(NumberUtil.add(resendOrder.getCarriageAmount(), resendOrder.getProductAmount(), resendOrder.getServiceAmount()));
                    }
                }
            }
            afterServiceAuditExportDtoList.add(afterServiceAuditExportDto);
        });
        return afterServiceAuditExportDtoList;
    }


    public List<AfterServiceAuditExportDto> getExportDataList(String keyword, String platform, Integer orderStatus, String designStatus, Integer status, Integer type,
                                                              Date startTime, Date endTime, Date auditStartTime, Date auditEndTime, String sort) {
        List<AfterServiceAuditExportDto> afterServiceAuditExportDtoList = Lists.newArrayList();
        //查询符合条件的退款售后单列表
        SearchBean<AfterServiceAuditDto> searchBean = this.list(
                new AfterServiceAuditPageParam(keyword, platform, orderStatus, designStatus, status, type, startTime, endTime, auditStartTime, auditEndTime, sort, 1, MAX_EXPORT_NUM, false));
        // 校验最大导出数据量
        if (searchBean.getTotalCount() > MAX_EXPORT_NUM) {
            throw new BusinessException("导出数量超过10000条，请联系管理员导出");
        }
        List<AfterServiceAuditDto> afterServiceAuditDtoList = searchBean.getItems();

        if (CollUtil.isEmpty(afterServiceAuditDtoList)) {
            return afterServiceAuditExportDtoList;
        }
        List<Long> resendOrderId = afterServiceAuditDtoList.stream()
                .filter(a -> AFTER_SERVICE_RESEND.getCode().equals(a.getType()))
                .filter(a -> AfterServiceAuditStatusEnum.SUCCESS.getCode().equals(a.getStatus()))
                .map(AfterServiceAuditDto::getResendOrderId).collect(Collectors.toList());
        Map<Long, Order> resendOrderIdMap = CollectionUtils.isEmpty(resendOrderId) ? Maps.newHashMap() : orderService.findByIds(resendOrderId).stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        // 生成导出数据列表
        afterServiceAuditDtoList.forEach(afterServiceAuditDto -> {
            AfterServiceAuditExportDto afterServiceAuditExportDto = new AfterServiceAuditExportDto();
            afterServiceAuditExportDto.setOrderNo(afterServiceAuditDto.getOrderNo());
            afterServiceAuditExportDto.setMerchantNo(afterServiceAuditDto.getMerchantNo());
            afterServiceAuditExportDto.setTypeName(AfterServiceAuditTypeEnum.getByCode(afterServiceAuditDto.getType()).getDesc());
            afterServiceAuditExportDto.setRequestRefundAmount(BigDecimal.valueOf(afterServiceAuditDto.getRequestRefundAmount()));
            afterServiceAuditExportDto.setReason(afterServiceAuditDto.getAuditRemark());
            if (AFTER_SERVICE_RESEND.getCode().equals(afterServiceAuditDto.getType())) {
                Order resendOrder = resendOrderIdMap.get(afterServiceAuditDto.getResendOrderId());
                if (!AfterServiceAuditStatusEnum.SUCCESS.getCode().equals(afterServiceAuditDto.getStatus()) || resendOrder == null) {
                    afterServiceAuditExportDto.setRequestRefundAmount(BigDecimal.ZERO);
                } else {
                    //分销订单 物流来源是租户物流来源或责任是租户自己的不算物流费
                    if (!TenantCommonConstant.isSdsdiy(resendOrder.getTenantId()) && (DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(resendOrder.getLogisticsSource())
                            || AfterServiceAuditService.DUTY_AFFILIATION_TENANT == afterServiceAuditDto.getDutyAffiliation())) {
                        afterServiceAuditExportDto.setRequestRefundAmount(BigDecimal.valueOf(NumberUtil.add(resendOrder.getProductAmount(), resendOrder.getServiceAmount())));
                    } else {
                        afterServiceAuditExportDto.setRequestRefundAmount(NumberUtil.add(resendOrder.getCarriageAmount(), resendOrder.getProductAmount(), resendOrder.getServiceAmount()));
                    }
                }
            }
            afterServiceAuditExportDto.setGmtCreated(afterServiceAuditDto.gmtCreated());
            afterServiceAuditExportDto.setStatus(AfterServiceAuditStatusEnum.getByCode(afterServiceAuditDto.getStatus()).getDesc());
            afterServiceAuditExportDtoList.add(afterServiceAuditExportDto);
        });

        return afterServiceAuditExportDtoList;
    }

    @NotNull
    private static BigDecimal getRequestRefundAmount(Map<Long, Order> resendOrderMap, AfterServiceAuditDto afterServiceAuditDto) {
        BigDecimal requestRefundAmount = BigDecimal.valueOf(afterServiceAuditDto.getRequestRefundAmount());
        if (AfterServiceAuditTypeEnum.getByCode(afterServiceAuditDto.getType()).equals(AFTER_SERVICE_RESEND)) {
            Order resendOrder = resendOrderMap.get(afterServiceAuditDto.getResendOrderId());
            if (null != resendOrder) {
                double amount = NumberUtil.add(resendOrder.getProductAmount(), resendOrder.getCarriageAmount());
                requestRefundAmount=BigDecimal.valueOf(amount);
            }
        }
        return requestRefundAmount;
    }


    public static class CreateParam {
        private Long merchantId;
        private String merchantNo;
        private Double refundAmount;
        private Double refundCarriageMoney;
        private Double refundProductMoney;
        private Double refundServiceMoney;
        private Double refundMaterialServiceMoney;

        private Double tenantRefundAmount;
        private Double tenantRefundCarriageMoney;
        private Double tenantRefundProductMoney;
        private Double tenantRefundServiceMoney;
        private Double tenantRefundMaterialServiceMoney;
        private String remark;
        //退款类型:1 取消退款 2售后退款 3售后重发货物
        private Integer type;
        private Integer isAdvance;
        private Integer causeType;
        private Platform platform;
        private String evidenceImgsArrayJson;
        private boolean complete = false;

        private List<AfterServiceSubmitVo.Item> items;

        public Double getTenantRefundAmount() {
            return tenantRefundAmount;
        }

        public void setTenantRefundAmount(Double tenantRefundAmount) {
            this.tenantRefundAmount = tenantRefundAmount;
        }

        public Double getTenantRefundCarriageMoney() {
            return tenantRefundCarriageMoney;
        }

        public void setTenantRefundCarriageMoney(Double tenantRefundCarriageMoney) {
            this.tenantRefundCarriageMoney = tenantRefundCarriageMoney;
        }

        public Double getTenantRefundProductMoney() {
            return tenantRefundProductMoney;
        }

        public void setTenantRefundProductMoney(Double tenantRefundProductMoney) {
            this.tenantRefundProductMoney = tenantRefundProductMoney;
        }

        public Double getTenantRefundServiceMoney() {
            return tenantRefundServiceMoney;
        }

        public void setTenantRefundServiceMoney(Double tenantRefundServiceMoney) {
            this.tenantRefundServiceMoney = tenantRefundServiceMoney;
        }

        public Double getTenantRefundMaterialServiceMoney() {
            return tenantRefundMaterialServiceMoney;
        }

        public void setTenantRefundMaterialServiceMoney(Double tenantRefundMaterialServiceMoney) {
            this.tenantRefundMaterialServiceMoney = tenantRefundMaterialServiceMoney;
        }

        public Double getRefundMaterialServiceMoney() {
            return refundMaterialServiceMoney;
        }

        public void setRefundMaterialServiceMoney(Double refundMaterialServiceMoney) {
            this.refundMaterialServiceMoney = refundMaterialServiceMoney;
        }

        public Integer getIsAdvance() {
            return isAdvance;
        }

        public void setIsAdvance(Integer isAdvance) {
            this.isAdvance = isAdvance;
        }

        public Long getMerchantId() {
            return merchantId;
        }

        public void setMerchantId(Long merchantId) {
            this.merchantId = merchantId;
        }

        public String getMerchantNo() {
            return merchantNo;
        }

        public void setMerchantNo(String merchantNo) {
            this.merchantNo = merchantNo;
        }

        public Double getRefundAmount() {
            return refundAmount;
        }

        public void setRefundAmount(Double refundAmount) {
            this.refundAmount = refundAmount;
        }

        public Double getRefundCarriageMoney() {
            return refundCarriageMoney;
        }

        public void setRefundCarriageMoney(Double refundCarriageMoney) {
            this.refundCarriageMoney = refundCarriageMoney;
        }

        public Double getRefundServiceMoney() {
            return refundServiceMoney;
        }

        public void setRefundServiceMoney(Double refundServiceMoney) {
            this.refundServiceMoney = refundServiceMoney;
        }

        public Double getRefundProductMoney() {
            return refundProductMoney;
        }

        public void setRefundProductMoney(Double refundProductMoney) {
            this.refundProductMoney = refundProductMoney;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public Integer getCauseType() {
            return causeType;
        }

        public void setCauseType(Integer causeType) {
            this.causeType = causeType;
        }

        public Platform getPlatform() {
            return platform;
        }

        public void setPlatform(Platform platform) {
            this.platform = platform;
        }

        public String getEvidenceImgsArrayJson() {
            return evidenceImgsArrayJson;
        }

        public void setEvidenceImgsArrayJson(String evidenceImgsArrayJson) {
            this.evidenceImgsArrayJson = evidenceImgsArrayJson;
        }

        public boolean isComplete() {
            return complete;
        }

        public void setComplete(boolean complete) {
            this.complete = complete;
        }

        public List<AfterServiceSubmitVo.Item> getItems() {
            return items;
        }

        public void setItems(List<AfterServiceSubmitVo.Item> items) {
            this.items = items;
        }
    }

    public Order isCancel(Order order) {
        List<AfterServiceAuditDto> services = this.getDtoListByOrderId(order.getId());
        List<OrderItem> orderItems = order.getItems();
        int i = 0;
        for (AfterServiceAuditDto dto : services) {
            if (dto.getType() == AfterServiceAuditService.TYPE_CANCEL_REFUND && dto.getStatus() == AfterServiceAuditService.STATUS_DISPOSING) {
                throw new BusinessException("含有取消中的订单!");
            }
            if (dto.getType() == AfterServiceAuditService.TYPE_CANCEL_REFUND && dto.getStatus() == AfterServiceAuditService.ITEM_STATUS_SUCCESS) {

                i++;
            }


        }
        if (services.size() != 0 && i == services.size()) {
            throw new BusinessException("该订单全部订单已被取消!");
        }
        order.setItems(orderItems);
        return order;

    }

    /**
     * 根据售后订单查询
     *
     * @param resendOrderId
     * @return
     */
    public AfterServiceAudit findByResendOrderId(Long resendOrderId) {
        AfterServiceAudit afterServiceAudit = new AfterServiceAudit();
        afterServiceAudit.setResendOrderId(resendOrderId);
        return dao.selectOne(afterServiceAudit);
    }

    @NotNull
    public AfterServiceAuditDto getAfterServiceAuditDetail(Long id) {
        AfterServiceAuditDto dto = this.getById(id);
        Long areaId = dto.getOrder().getIssuingBayAreaId();
        List<Long> productIds = Lists.newArrayList();
        List<Long> orderItemIds = Lists.newArrayList();
        List<BaseIdQtyDTO> afterServiceItemList = new ArrayList<>();
        for (AfterServiceAuditItemDto item : dto.getItems()) {
            if (item.getOrderItem() != null) {
                productIds.add(item.getOrderItem().getProductId());
                orderItemIds.add(item.getOrderItemId());
                afterServiceItemList.add(new BaseIdQtyDTO(item.getOrderItemId(), item.getRealNum()));
            }
        }
        if (AfterServiceAuditStatusEnum.DISPOSING.equalsCode(dto.getStatus())) {
            // 退款金额建议
            OrderRefundReqDTO refundReqDTO = new OrderRefundReqDTO();
            refundReqDTO.setOrderId(dto.getOrder().getId())
                    .setCancelItemList(afterServiceItemList);
            OrderRefundAdviseRespDTO adviseRespDTO = orderCancelFeign.saasAfterServiceAdvise(refundReqDTO);
            dto.setRefundAdvise(adviseRespDTO);

            dto.setCustomRefundProductAmount(!EnumOrderPayType.payTenantToTenant(adviseRespDTO.getOrderPaymentType()));
        }
        // 供应关系
        List<ProductSupplyDTO> productSupplyList = productSupplyFeign.findBackupFactoryProductIdsAreaId(com.ziguang.base.support.StringUtils.listConvertToString(productIds), areaId);
        Map<Long, Long> productBackFactory = ListUtil.toMap(ProductSupplyDTO::getProductId, ProductSupplyDTO::getFactoryId, productSupplyList);
        // 子单关联关系
        List<OrderItemMapDto> orderItemMapList = orderItemMapService.getByItemIds(orderItemIds);
        Map<Long, List<Long>> orderItemNewItemIdMap = ListUtil.toMapValueList(OrderItemMapDto::getOrderItemId, OrderItemMapDto::getNewOrderItemId, orderItemMapList);

        for (AfterServiceAuditItemDto item : dto.getItems()) {
            List<Long> itemIds = orderItemNewItemIdMap.getOrDefault(item.getOrderItemId(), new ArrayList<>());
            itemIds.add(item.getOrderItemId());
            Integer realNum = 0;

            List<AfterServiceAuditItemDetail> afterServiceAuditItemDetails = afterServiceAuditItemDetailService.findByItemId(item.getId());
            List<AfterServiceAuditItemFactoryOrderDto> auditItemFactoryOrderDtos;

            if (CollectionUtils.isNotEmpty(afterServiceAuditItemDetails)) {
                auditItemFactoryOrderDtos = buildFactoryOrderDto(afterServiceAuditItemDetails);
            } else {
                auditItemFactoryOrderDtos = buildFactoryOrderDto(item, itemIds);
                if (!dto.getStatus().equals(AfterServiceAuditService.STATUS_SUCCESS)) {
                    for (AfterServiceAuditItemFactoryOrderDto auditItemFactoryOrderDto : auditItemFactoryOrderDtos) {
                        realNum += auditItemFactoryOrderDto.getNum();
                    }
                    item.setRealNum(realNum);
                }
            }

            item.setFactoryOrders(auditItemFactoryOrderDtos);
            //已完成的从 转移记录中查 未完成的则实时查 ，什么逻辑恶心死了
            if (dto.getStatus().equals(AfterServiceAuditService.STATUS_SUCCESS)) {
                List<OrderItemTransferHistoryDto> orderItemTransferHistoryDtos = orderItemTransferHistoryService
                        .getByItemId(item.getOrderItemId(), EnumOrderItemTransferType.AFTER_SERVICE.getValue()
                                , null, "factoryOrderId,newOrderItemId,factoryBackupType,factoryId,newFactoryId");
                if (CollectionUtils.isNotEmpty(orderItemTransferHistoryDtos)) {
                    item.setSupplyBackup(orderItemTransferHistoryDtos.get(0).getFactoryBackupType());
                } else {
                    item.setSupplyBackup(EnumProductSupplyBackupType.BACKUP.getStatus());
                }
            } else if (item.getFactoryOrderDto() != null && item.getOrderItem() != null) {
                Long productId = item.getOrderItem().getProductId();
                Long factoryId = item.getFactoryOrderDto().getFactoryId();
                Long backupFactoryId = productBackFactory.get(productId);
                if (backupFactoryId != null && !backupFactoryId.equals(factoryId)) {
                    item.setSupplyBackup(EnumProductSupplyBackupType.BACKUP.getStatus());
                } else {
                    item.setSupplyBackup(EnumProductSupplyBackupType.NONE.getStatus());
                }
            }
            if (dto.getStatus() == AfterServiceAuditService.STATUS_SUCCESS || dto.getStatus() == AfterServiceAuditService.STATUS_REFUSED) {
                dto.setAuditImgs(afterServiceAuditAdminImgService.findImages(id));
            }
        }
        return dto;
    }

    public List<AfterServiceAuditItemFactoryOrderDto> buildFactoryOrderDto(AfterServiceAuditItemDto item, List<Long> itemIds) {
        List<AfterServiceAuditItemFactoryOrderDto> auditItemFactoryOrderDtos = Lists.newArrayList();
        List<FactoryOrder> factoryOrders = factoryOrderService.findByOrderItemIds(itemIds);
        for (FactoryOrder order : factoryOrders) {
            //取消单不需要进来
            if (FactoryOrderStatus.CANCEL.getStatus() == order.getStatus()) {
                continue;
            }
            if (0 < order.getNum()) {
                AfterServiceAuditItemFactoryOrderDto factoryOrder = new AfterServiceAuditItemFactoryOrderDto();
                Factory factory = new Factory();
                factory.setId(order.getFactoryId());
                factory.setName(order.getFactoryName());
                factoryOrder.setFactory(factory);
                factoryOrder.setNum(order.getNum() < item.getRequestNum() ? order.getNum() : item.getRequestNum());
                factoryOrder.setId(order.getId());
                auditItemFactoryOrderDtos.add(factoryOrder);
            }
        }
        return auditItemFactoryOrderDtos;
    }


    public List<AfterServiceAuditItemFactoryOrderDto> buildFactoryOrderDto(List<AfterServiceAuditItemDetail> afterServiceAuditItemDetails) {
        List<AfterServiceAuditItemFactoryOrderDto> auditItemFactoryOrderDtos = Lists.newArrayList();
        for (AfterServiceAuditItemDetail detail : afterServiceAuditItemDetails) {
            AfterServiceAuditItemFactoryOrderDto factoryOrder = new AfterServiceAuditItemFactoryOrderDto();
            Factory factory = new Factory();
            factory.setId(detail.getFactoryId());
            factory.setName(detail.getFactoryName());
            factoryOrder.setFactory(factory);
            factoryOrder.setNum(detail.getNum());
            factoryOrder.setId(detail.getFactoryOrderId());
            auditItemFactoryOrderDtos.add(factoryOrder);
        }
        return auditItemFactoryOrderDtos;
    }
}
