package com.ps.ps.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ps.base.entity.SearchBean;
import com.ps.ps.dao.RefundRecordDao;
import com.ps.ps.feign.MerchantFeign;
import com.ps.ps.feign.order.OrderAmountDetailFeign;
import com.ps.ps.feign.order.OrderFbaCarriageDetailFeign;
import com.ps.ps.feign.order.OrderItemChangeHistoryFeign;
import com.ps.ps.feign.order.OrderItemSupplyChainFeign;
import com.ps.ps.feign.payment.LogisticsReconciliationImportFeign;
import com.ps.ps.feign.tenant.TenantFeign;
import com.ps.ps.feign.user.CrmMerchantClueFeign;
import com.ps.ps.feign.wallet.TenantWalletFeign;
import com.ps.support.Assert;
import com.ps.support.utils.ConvertUtil;
import com.ps.support.utils.StringUtils;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.logisticsapi.constant.LogisticsConstant;
import com.sdsdiy.logisticsapi.enums.LogisticsCodeIdEnum;
import com.sdsdiy.orderapi.constant.EnumOrderPayType;
import com.sdsdiy.orderapi.constant.OrderConstant;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderapi.dto.order.OrderCarriageRespDto;
import com.sdsdiy.orderapi.dto.orderfba.OrderFbaCarriageDetailRespDto;
import com.sdsdiy.orderdata.dto.OrderAmountDetailDTO;
import com.sdsdiy.orderdata.dto.OrderItemPriceRespDto;
import com.sdsdiy.orderdata.dto.order.OrderItemSupplyChainDTO;
import com.sdsdiy.orderdata.dto.order.item.OrderItemCancelQtyDTO;
import com.sdsdiy.orderdata.enums.OrderItemChangeTypeEnum;
import com.sdsdiy.paymentapi.dto.TenantWalletDto;
import com.sdsdiy.userapi.constant.enums.DistributionProductLogisticsSourceEnum;
import com.sdsdiy.userapi.dto.crm.resp.CrmUserRespDTO;
import com.sdsdiy.userapi.dto.tenant.TenantRespDto;
import com.ziguang.base.dto.AfterServiceAuditDto;
import com.ziguang.base.dto.FactoryOrderDto;
import com.ziguang.base.model.*;
import com.ziguang.base.support.DoubleUtils;
import com.ziguang.base.support.ListUtils;
import com.ziguang.base.support.contant.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.NO;
import static com.sdsdiy.common.base.constant.BasePoConstant.YES;


@Service
@EnableAsync
@Slf4j
public class OrderSettleService {

    @Resource
    AfterServiceAuditService afterServiceAuditService;

    @Resource
    OrderService orderService;

    @Resource
    CategoryService categoryService;

    @Resource
    TenantFeign tenantFeign;

    @Resource
    CrmMerchantClueFeign crmMerchantClueFeign;

    @Resource
    FactoryOrderService factoryOrderService;

    @Autowired
    LogisticsReconciliationImportFeign logisticsReconciliationImportFeign;

    @Resource
    OrderItemPriceManage orderItemPriceFeign;

    @Resource
    TenantWalletFeign tenantWalletFeign;
    @Resource
    LogisticsService logisticsService;
    @Resource
    RefundRecordDao refundRecordDao;
    @Resource
    private OrderItemSupplyChainFeign orderItemSupplyChainFeign;
    @Resource
    private OrderFbaCarriageDetailFeign orderFbaCarriageDetailFeign;
    @Resource
    private OrderAmountDetailFeign orderAmountDetailFeign;
    @Resource
    private MerchantFeign merchantFeign;
    @Resource
    private OrderItemChangeHistoryFeign orderItemChangeHistoryFeign;

    @Resource
    private OrderAmountService orderAmountService;

    public SearchBean<Order> tenantSettleList(
            Long tenantId
            , Integer payChannelMethod
            , String productName
            , Long categoryId
            , Long merchantId
            , String orderNo
            , Long logisticsId
            , String status
            , String designStatus
            , Integer carriageStatus
            , Date createdDateBegin
            , Date createdDateEnd
            , Date finishedDateBegin
            , Date finishedDateEnd
            , String originType
            , Integer page
            , Integer size
            , String sort
            , String orderType
    ) {
        SearchBean<Order> searchBean = null;
        Boolean isGroup = false;

        boolean innerJoinAmount = false;
        if (page == null || size == null) {
            searchBean = new SearchBean<>();
        } else {
            searchBean = new SearchBean<>(page, size);
        }
        // searchBean.addWhere("tenant_id", tenantId);
        TenantWalletDto tenantWalletDto = tenantWalletFeign.get(tenantId);
        searchBean.addInnerJoin(" sds_mc_order.order_amount as amount on main.id = amount.id");
        searchBean.addSql("main.tenant_id="+tenantId+" or amount.product_tenant_id="+tenantId);

        // if (tenantWalletDto.getOpenOnlinePay().equals(BasePoConstant.NO)) {
        //     //未开启不展示分销订单
        //     innerJoinAmount = true;
        //     searchBean.addNoWhere("amount", "product_tenant_id", TenantCommonConstant.SDSDIY_TENANT_ID);
        // }

        searchBean.addSql("main.`compensation_amount` > 0 OR main.factory_compensation_amount   OR main.`status` IN (2, 3, 4, 5) or main.total_amount > main.refund_amount");
        if (finishedDateBegin != null) {
            searchBean.addWhere("finish_time", SqlWhereCharater.GE, finishedDateBegin.getTime());
        }
        if (finishedDateEnd != null) {
            searchBean.addWhere("finish_time", SqlWhereCharater.LE, finishedDateEnd.getTime());
        }
        if (finishedDateBegin != null || finishedDateEnd != null) {
            //status,finish_time,option_user 索引增加性能
            searchBean.addWhere("status", OrderStatus.FINISH.getStatus());
        }
        if (createdDateBegin != null) {
            searchBean.addSql("main.pay_time > #{condition.createdDateBegin}");
            searchBean.addConditon("createdDateBegin", createdDateBegin.getTime());
        } else {
            searchBean.addWhere("pay_time", SqlWhereCharater.GT, NumberUtils.longZero());
        }

        if (createdDateEnd != null) {
            searchBean.addSql("main.pay_time < #{condition.createdDateEnd}");
            searchBean.addConditon("createdDateEnd", createdDateEnd.getTime());
        }

        if (StringUtils.isNotBlank(designStatus)) {
            searchBean.addWhere("design_status", designStatus);
        }

        if (StringUtils.isNotBlank(orderNo)) {
            searchBean.addSql(" (main.no like #{condition.orderNo} )");
            searchBean.addConditon("orderNo", orderNo + "%");
        }
        if (logisticsId != null && logisticsId > 0) {
            searchBean.addWhere("logistics_id", logisticsId);
        }

        if (merchantId != null && merchantId > 0) {
            searchBean.addWhere("merchant_id", merchantId);
        }
        if (StringUtils.isNotBlank(productName) || categoryId != null) {
            searchBean.addLeftJoin(" order_item as oi on oi.order_id = main.id");
            searchBean.addLeftJoin(" product as p on oi.product_id = p.id");
            searchBean.setGroup("main.id");
            isGroup = true;
        }

        if (StringUtils.isNotBlank(productName)) {
            searchBean.addSql(" (oi.product_name like #{condition.product_name} or p.sku like #{condition.product_name} or main.no like #{condition.product_name}) ");
            searchBean.addConditon("product_name", "%" + productName + "%");
        }
        if (StringUtils.isNotBlank(originType)) {
            OrderOriginType orderOriginType = OrderOriginType.check(originType);
            if (orderOriginType.equals(OrderOriginType.NORMAL)) {
                searchBean.addNoWhere("origin_type", OrderOriginType.FBA.getValue());
            } else {
                searchBean.addWhere("origin_type", originType);
            }
        }

        if (status != null) {
            List<Long> longList = StringUtils.stringToLongList(status);
            searchBean.addIn("main.status", longList);
        }

        if (categoryId != null) {
            List<Long> categoryIds = categoryService.childrenIds(categoryId);
            categoryIds.add(-1L);
            searchBean.addIn("p.category_id", categoryIds);
        }

        if (payChannelMethod != null) {
            String paymentMethodCode = orderService.getPayMethodCode(payChannelMethod);
            searchBean.addWhere("amount", "payment_method", paymentMethodCode);
        }
        //自营订单：selfOwned，订单租户是自己，产品租户也是自己
        //跨租户供应订单：crossTenant，订单租户是自己，产品租户是别人
        //分销订单：distribution，订单租户是别人，产品租户是自己
        if (StrUtil.isNotBlank(orderType)) {
            if(OrderConstant.ORDER_TYPE_SELF_OWNED.equals(orderType)){
                searchBean.addWhere("tenant_id", tenantId);
                searchBean.addWhere("amount", "product_tenant_id", tenantId);
            }else if(OrderConstant.ORDER_TYPE_CROSS_TENANT.equals(orderType)){
                searchBean.addWhere("tenant_id", tenantId);
                searchBean.addNoWhere("amount", "product_tenant_id", tenantId);
            }else if(OrderConstant.DISTRIBUTION.equals(orderType)){
                searchBean.addNoWhere("tenant_id", tenantId);
                searchBean.addWhere("amount", "product_tenant_id", tenantId);
            }
        }

        if (StringUtils.isNotBlank(sort)) {
            searchBean.setSort(StringUtils.getOrderByToSort(sort));
        } else {
            searchBean.setSort("(main.id+0) desc");
        }
        searchBean = orderService.list(searchBean, isGroup);
        if (CollectionUtils.isEmpty(searchBean.getItems())) {
            return searchBean;
        }
        afterServiceEntranceShow(searchBean.getItems());


        if (CollectionUtils.isNotEmpty(searchBean.getItems())) {
            List<List<Order>> ordersList = ListUtil.spliceArrays(searchBean.getItems(),1000);
            for (List<Order> orders : ordersList) {
                orderService.formatOrderItems(orders);
            }
            orderService.formatMerchant(searchBean.getItems());
            orderService.formatFactory(searchBean.getItems());
        }
        orderService.formatCustomer(searchBean.getItems());
        this.buildSpliceLogisticsCarriage(searchBean.getItems());
        this.buildSpliceItemNum(searchBean.getItems());

        int index = 0;
        List<OrderItem> items = new ArrayList<>();
        List<Long> itemIds = Lists.newArrayList();
        List<Long> orderIds = Lists.newArrayList();
        for (Order order : searchBean.getItems()) {
            orderIds.add(order.getId());
            if (order.getLogisticsId() != null && order.getLogisticsId() == 0 && StrUtil.isNotEmpty(order.getCarriageName())) {
                order.setLogistics(new Logistics());
                order.getLogistics().setName(order.getCarriageName());
            }
            for (OrderItem orderItem : order.getItems()) {
                index++;
                items.add(orderItem);
                itemIds.add(orderItem.getId());

            }
        }

        if (!items.isEmpty()) {
            orderService.ORM(items);
        }

        Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap = orderAmountService.getAmountMap(orderIds);
        Map<Long, OrderItemPriceRespDto> orderItemPriceRespDtoMap = this.getItemPrice(itemIds);
        Map<Long, OrderItemSupplyChainDTO> orderItemSupplyChainMap = orderItemSupplyChainFeign.getMapByIds(itemIds);
        List<Order> orders = searchBean.getItems();
        List<Long> afterServiceIds = Lists.newArrayList();
        for (Order order : orders) {
            for (OrderItem orderItem : order.getItems()) {
                if (orderItem.getAfterServiceAuditItemDto() != null) {
                    afterServiceIds.add(orderItem.getAfterServiceAuditItemDto().getAfterServiceAuditId());
                }
            }
        }
        Map<Long, AfterServiceAudit> afterServiceAuditMap = Maps.newHashMap();
        Map<Long, RefundRecord> refundRecordMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(afterServiceIds)) {
            List<AfterServiceAudit> afterServiceAudits = afterServiceAuditService.findByIds(afterServiceIds);
            for (AfterServiceAudit afterServiceAudit : afterServiceAudits) {
                afterServiceAuditMap.put(afterServiceAudit.getId(), afterServiceAudit);
            }
            List<String> orderNos = afterServiceAudits.stream().map(a -> a.getOrderNo()).collect(Collectors.toList());
            List<RefundRecord> refundRecords = refundRecordDao.findByNos(orderNos);
            refundRecordMap = refundRecords.stream().collect(Collectors.toMap(r -> r.getAfterServiceAuditId(), r -> r, (a, b) -> b));
        }
        Map<Long, TenantRespDto> tenantMap = getTenantRespDtoMap(orderAmountRespDTOMap, orders);

        for (Order order : orders) {
            Double productCost = 0D;
            OrderAmountRespDTO orderAmountRespDTO = orderAmountRespDTOMap.get(order.getId());
            setOrderPayment(order, orderAmountRespDTO);
            order.setPaymentType(orderAmountRespDTO.getPaymentType());
            List<AfterServiceAudit> afterServiceAudits = Lists.newArrayList();
            for (OrderItem orderItem : order.getItems()) {
                OrderItemPriceRespDto orderItemPriceRespDto = orderItemPriceRespDtoMap.get(orderItem.getId());
                orderItem.setProductCost(0D);
                if (orderItem.getStatus() != OrderStatus.CANCEL.getStatus() && orderItem.getFactoryOrder() == null && order.getStatus() == OrderStatus.SHELVE.getStatus()) {
                    FactoryOrder factoryOrder = factoryOrderService.getByNoAll(orderItem.getNo());
                    if (factoryOrder != null) {
                        orderItem.setFactoryOrder(ConvertUtil.dtoConvert(factoryOrder, FactoryOrderDto.class));
                    }
                }

                if (orderItem.getProductSupply() != null) {
                    OrderItemSupplyChainDTO supplyChain = orderItemSupplyChainMap.get(orderItem.getId());
                    int mergeNum = supplyChain == null ? orderItem.getNum() : supplyChain.getMergeNum();
                    orderItem.setProductUnitCost(SupplyService.getSupplyPrice(orderItem.getProductSupply(), mergeNum));
                }
                if (orderItem.getFactoryOrder() != null) {
                    FactoryOrderDto factoryOrderDto = new FactoryOrderDto();
                    factoryOrderDto.setCompensationAmount(orderItem.getFactoryOrder().getCompensationAmount());
                    factoryOrderDto.setNum(orderItem.getFactoryOrder().getNum());
                    factoryOrderDto.setPrice(orderItem.getFactoryOrder().getPrice());
                    factoryOrderDto.setStatus(orderItem.getFactoryOrder().getStatus());
                    orderItem.setProductCost(0D);
                    orderItem.setProductUnitCost(0D);
                    if (orderItem.getFactoryOrder().getStatus() != OrderStatus.CANCEL.getStatus()) {
                        if (EnumOrderPayType.MERCHANT_OWNTENANT.getValue().equalsIgnoreCase(orderAmountRespDTO.getPaymentType())) {
                            orderItem.setProductCost(orderItem.getFactoryOrder().getTotalPrice());
                            orderItem.setProductUnitCost(orderItem.getFactoryOrder().getPrice());
                        } else if (orderItemPriceRespDto != null) {
                            orderItem.setProductCost(orderItemPriceRespDto.getTenantAmount());
                            orderItem.setProductUnitCost(orderItemPriceRespDto.getTenantPrice().doubleValue());
                        }
                    }
                    orderItem.setShowFactoryOrder(factoryOrderDto);
                }
                orderItem.setProductIncome(orderItem.getAmount());

                if (orderItem.getStatus() == OrderStatus.CANCEL.getStatus() && orderItem.getRefundStatus() == RefundService.getRefundSuccess()) {
                    //如果没有工厂订单或者工厂订单已取消则未0
                    orderItem.setProductIncome(0D);
                }

                if (orderItem.getAfterServiceAuditItemDto() != null) {
                    AfterServiceAudit afterServiceAudit = afterServiceAuditMap.get(orderItem.getAfterServiceAuditItemDto().getAfterServiceAuditId());
                    if (afterServiceAudit != null && afterServiceAudit.getType() == AfterServiceAuditService.TYPE_AFTER_SERVICE_REFUND) {
                        orderItem.setProductIncome(0D);
                    }
                    if (!afterServiceAudits.contains(afterServiceAudit)) {
                        afterServiceAudits.add(afterServiceAudit);
                    }
                }

                productCost = DoubleUtils.add(orderItem.getProductCost(), productCost);
            }
            Double refundTenantToMerchantCarriageMoney = 0D;
            for (AfterServiceAudit afterServiceAudit : afterServiceAudits) {
                if (afterServiceAudit.getType() == AfterServiceAuditService.TYPE_AFTER_SERVICE_REFUND && afterServiceAudit.getStatus() == AfterServiceAuditService.STATUS_SUCCESS) {
                    refundTenantToMerchantCarriageMoney = DoubleUtils.add(refundTenantToMerchantCarriageMoney, afterServiceAudit.getRealRefundCarriageMoney());
                }
            }
            Double refundSaasToTenantCarriageMoney = 0D;
            for (AfterServiceAudit afterServiceAudit : afterServiceAudits) {
                if (afterServiceAudit.getType() == AfterServiceAuditService.TYPE_AFTER_SERVICE_REFUND && afterServiceAudit.getStatus() == AfterServiceAuditService.STATUS_SUCCESS) {
                    refundSaasToTenantCarriageMoney = DoubleUtils.add(refundSaasToTenantCarriageMoney, afterServiceAudit.getRealRefundTenantCarriageMoney());
                }
            }
            Double cancelCarriageMoney = 0D;
            for (AfterServiceAudit afterServiceAudit : afterServiceAudits) {
                if (afterServiceAudit.getType() == AfterServiceAuditService.TYPE_CANCEL_REFUND && afterServiceAudit.getStatus() == AfterServiceAuditService.STATUS_SUCCESS) {
                    cancelCarriageMoney = DoubleUtils.add(cancelCarriageMoney, afterServiceAudit.getRealRefundCarriageMoney());
                }
            }

            //因为productCost已经过滤掉了取消的子单，所以这里就不用算取消退款的情况。saas财务分析有算
            Double tenantRefundProductMoney = 0D;
            for (AfterServiceAudit afterServiceAudit : afterServiceAudits) {
                if (afterServiceAudit.getType() == AfterServiceAuditService.TYPE_AFTER_SERVICE_REFUND &&
                        afterServiceAudit.getStatus() == AfterServiceAuditService.STATUS_SUCCESS) {
                    RefundRecord refundRecord = refundRecordMap.get(afterServiceAudit.getId());
                    if (null == refundRecord) {
                        continue;
                    }
                    tenantRefundProductMoney = DoubleUtils.add(tenantRefundProductMoney, refundRecord.getTenantProductPrice());
                }
            }

            boolean isTenantLogistics = DistributionProductLogisticsSourceEnum.isTenantLogistics(order.getLogisticsSource());
            order.setProductIncome(order.getBeAfterServiceOrder() ? 0D : DoubleUtils.sub(order.getProductAmount(), order.getRefundProductAmount()));
            //order.getCurrentCarriageAmount()为退款后，订单剩下的运费金额
            order.setExpressIncome(order.getBeAfterServiceOrder() ? 0D : order.getCurrentCarriageAmount());

            // 物流成本
            BigDecimal realTenantCarriageAmount = NumberUtil.sub(order.getTenantCarriageAmount(), order.getTenantRefundCarriageAmount());
            order.setExpressCost(order.getBeAfterServiceOrder() && !isTenantLogistics ? 0D : realTenantCarriageAmount.doubleValue());

            //售后订单，服务费收入0
            order.setServiceAmount(order.getBeAfterServiceOrder() ? 0D : order.getServiceAmount());
            order.setProductCost(DoubleUtils.sub(productCost, tenantRefundProductMoney));
            order.setUsedFreeGold(order.getUsedFreeGold() - order.getRefundFreeGold());
            order.setTotalIncome(DoubleUtils.add(order.getExpressIncome(), order.getProductIncome()));
            if (EndProductType.isPrivate(order.getProductionType())) {
                //私有产品服务费收入为0 成本有值
                order.setServiceAmountCost(DoubleUtils.sub(order.getServiceAmount(), order.getRefundServiceAmount()));
                order.setServiceAmount(0d);
            } else {
                order.setServiceAmount(DoubleUtils.sub(order.getServiceAmount(), order.getRefundServiceAmount()));
                order.setServiceAmountCost(0D);
            }

            //租户物流,自提/线上物流/寄付,服务费成本和物流成本为0
            if (order.getLogisticsSource().equalsIgnoreCase(DistributionProductLogisticsSourceEnum.ORDER_TENANT.name())) {
                Logistics logistics = order.getLogistics();
                if (null != logistics) {
                    if (LogisticsConstant.isOnlineLogistics(logistics.getServiceProviderId()) || LogisticsCodeIdEnum.isZtOrConsignment(logistics.getCodeId())) {
                        order.setExpressCost(0D);
                        order.setServiceAmountCost(0D);
                    }
                }
            }

            order.setTotalCost(DoubleUtils.add(order.getExpressCost(), order.getProductCost(), order.getServiceAmountCost()));
            order.setTotalIncome(DoubleUtils.add(order.getTotalIncome(), order.getServiceAmount()));

            String orderTypeShow = OrderConstant.getOrderType(tenantId, order.getTenantId(), orderAmountRespDTO.getProductTenantId());
            order.setOrderType(orderTypeShow);
            TenantRespDto tenantRespDto = tenantMap.get(orderAmountRespDTO.getProductTenantId());
            order.setTenantName(null!=tenantRespDto?tenantRespDto.getName():"");
        }
        hideInfo(orders,tenantId);
        return searchBean;
    }

    private Map<Long, TenantRespDto> getTenantRespDtoMap(Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap, List<Order> orders) {
        List<Long> productTenantIds = orderAmountRespDTOMap.values().stream().map(i -> i.getProductTenantId()).filter(i -> NumberUtils.greaterZero(i)).distinct().collect(Collectors.toList());
        List<Long> tenantIds = orders.stream().map(i -> i.getTenantId()).filter(i -> NumberUtils.greaterZero(i)).distinct().collect(Collectors.toList());
        tenantIds.addAll(productTenantIds);
        tenantIds=tenantIds.stream().distinct().collect(Collectors.toList());
        List<TenantRespDto> tenantRespDtos = tenantFeign.getByIds(tenantIds);
        Map<Long, TenantRespDto> tenantMap = ListUtil.toMap(BaseDTO::getId, tenantRespDtos);
        return tenantMap;
    }

    private static void hideInfo(List<Order> orders,Long currentTenantId) {
        for (Order order : orders) {
            if(order.getOrderType().equals(OrderConstant.ORDER_TYPE_CROSS_TENANT)){
                List<OrderItem> itemList = order.getItems();
                for (OrderItem orderItem : itemList) {
                    orderItem.setFactory(null);
                }
            }
            if(order.getOrderType().equals(OrderConstant.DISTRIBUTION)){

                Merchant merchant = order.getMerchant();
                boolean sdsdiy = TenantCommonConstant.isSdsdiy(currentTenantId);
                boolean otherTenantMerchant = !merchant.getTenantId().equals(currentTenantId);
                if(!sdsdiy && otherTenantMerchant){
                    Merchant hideMerchant=new Merchant();
                    hideMerchant.setName(merchant.getMerchantNo());
                    order.setMerchant(hideMerchant);

                    User hideCustomer=new User();
                    hideCustomer.setUsername("****");
                    order.setCustomer(hideCustomer);
                }
            }
        }
    }

    /**
     * 订单对应的子订单
     *
     * @param orders
     */
    public void buildSpliceLogisticsCarriage(List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        List<List<Order>> orderList = ListUtils.spliceArrays(orders,1000);
        for (List<Order> list : orderList) {
            buildLogisticsCarriage(list);
        }
        Set<Long> logisticsIds = Sets.newHashSet();
        for (Order order : orders) {
            if(CollUtil.isNotEmpty(order.getLogisticsList())){
                for (OrderSettleLogistics orderSettleLogistics : order.getLogisticsList()) {
                    logisticsIds.add(orderSettleLogistics.getLogisticsId());
                }
            }
        }

        List<Logistics> logistics = this.logisticsService.findByIds(logisticsIds);
        Map<Long, Logistics> logisticsMap = Maps.newHashMap();
        for (Logistics logistic : logistics) {
            logisticsMap.put(logistic.getId(), logistic);
        }
        for (Order order : orders) {
            if(CollUtil.isNotEmpty(order.getLogisticsList())){
                for (OrderSettleLogistics orderSettleLogistics : order.getLogisticsList()) {
                    orderSettleLogistics.setName(orderSettleLogistics.getCarriageName());
                    Logistics logistic= logisticsMap.get(orderSettleLogistics.getLogisticsId());
                    if (logistic != null) {
                        orderSettleLogistics.setName(logistic.getName());
                        if(LogisticsCodeIdEnum.isZt(logistic.getCodeId())){
                            //自提没有运单号
                            orderSettleLogistics.setCarriageNo("");
                        }
                    }
                }
            }

        }


    }
    /**
     * 订单对应的子订单
     *
     * @param orders
     */
    public void buildSpliceItemNum(List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        List<List<Order>> orderList = ListUtils.spliceArrays(orders,1000);
        for (List<Order> list : orderList) {
            buildOrderItemNum(list);
        }
    }
    /**
     * 订单对应的子订单
     *
     * @param orders
     */
    public void buildLogisticsCarriage(List<Order> orders) {
        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        List<OrderCarriageRespDto> orderCarriageResp = orderService.orderCarriageList(orderIds);
        Map<Long, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getId, r -> r));

        for (OrderCarriageRespDto orderCarriageRespDto : orderCarriageResp) {
            Order order = orderMap.get(orderCarriageRespDto.getOrderId());
            if(order == null){
                continue;
            }
            if(order.getLogisticsList() == null){
                order.setLogisticsList(Lists.newArrayList());
            }
            OrderSettleLogistics orderSettleLogistics = new OrderSettleLogistics();
            orderSettleLogistics.setLogisticsId(orderCarriageRespDto.getLogisticsId());
            orderSettleLogistics.setCarriageNo(orderCarriageRespDto.getCarriageNo());
            orderSettleLogistics.setCarriageName(orderCarriageRespDto.getCarriageName());
            order.getLogisticsList().add(orderSettleLogistics);
        }

    }
    /**
     * 订单对应的子订单
     *
     * @param orders
     */
    public void buildOrderItemNum(List<Order> orders) {
        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        List<OrderItemCancelQtyDTO> cancelQtyList = this.orderItemChangeHistoryFeign.findCancelQtyDtoByOrderIds(BaseListDto.of(orderIds));
        Map<Long,OrderItemCancelQtyDTO> cancelQtyMap = cancelQtyList.stream().collect(Collectors.toMap(OrderItemCancelQtyDTO::getOrderItemId,Function.identity()));

        for (Order order : orders) {
            for (OrderItem item : order.getItems()) {
                OrderItemCancelQtyDTO orderItemCancelQtyDTO = cancelQtyMap.get(item.getId());
                if(orderItemCancelQtyDTO == null){
                    continue;
                }
                for (Map.Entry<String, Integer> stringIntegerEntry : orderItemCancelQtyDTO.getTypeQtyMap().entrySet()) {
                    String cancelType = stringIntegerEntry.getKey();
                    Integer cancelNum = stringIntegerEntry.getValue();
                    if(!cancelType.equalsIgnoreCase(OrderItemChangeTypeEnum.MERCHANT_CANCEL.code)){
                        continue;
                    }
                    item.setNum(cancelNum + item.getNum());
                    item.setMerchantCancelNum(cancelNum);

                }
            }
        }
    }


    public SearchBean<Order> saasSettleList(
            Integer payMethod
            , String productName
            , Long categoryId
            , Long merchantId
            , String orderNo
            , Long logisticsId
            , String status
            , String designStatus
            , Integer carriageStatus
            , Date createdDateBegin
            , Date createdDateEnd
            , Date finishedDateBegin
            , Date finishedDateEnd
            , String originType
            , Integer page
            , Integer size
            , String sort
            , Integer isMerchantTest
            , String orderType
    ) {
        Long tenantId=TenantCommonConstant.SDSDIY_TENANT_ID;
        SearchBean<Order> searchBean = null;
        Boolean isGroup = false;

        if (page == null || size == null) {
            searchBean = new SearchBean<>();
        } else {
            searchBean = new SearchBean<>(page, size);
        }
        searchBean.addInnerJoin(" sds_mc_order.order_amount as amount on main.id = amount.id");

        // searchBean.addWhere("amount", "product_tenant_id", TenantCommonConstant.SDSDIY_TENANT_ID);
        searchBean.addSql("main.tenant_id="+tenantId+" or amount.product_tenant_id="+tenantId);
        searchBean.addSql("main.`compensation_amount` > 0 OR main.factory_compensation_amount   OR main.`status` IN (2, 3, 4, 5) or main.total_amount > main.refund_amount");
        if (finishedDateBegin != null) {
            searchBean.addWhere("finish_time", SqlWhereCharater.GE, finishedDateBegin.getTime());
        }
        if (finishedDateEnd != null) {
            searchBean.addWhere("finish_time", SqlWhereCharater.LE, finishedDateEnd.getTime());
        }
        if (finishedDateBegin != null || finishedDateEnd != null) {
            //status,finish_time,option_user 索引增加性能
            searchBean.addWhere("status", OrderStatus.FINISH.getStatus());
        }
        if (isMerchantTest != null) {
            List<Long> testMerchantIds = merchantFeign.getTestMerchantIds(null);
            if (YES.equals(isMerchantTest) && CollectionUtil.isNotEmpty(testMerchantIds)) {
                searchBean.addIn("main.merchant_id", testMerchantIds);
            }
            if (NO.equals(isMerchantTest) && CollectionUtil.isNotEmpty(testMerchantIds)) {
                searchBean.addNotIn("main.merchant_id", testMerchantIds);
            }
        }
        if (createdDateBegin != null) {
            searchBean.addSql("main.pay_time > #{condition.createdDateBegin}");
            searchBean.addConditon("createdDateBegin", createdDateBegin.getTime());
        } else {
            searchBean.addWhere("pay_time", SqlWhereCharater.GT, NumberUtils.longZero());
        }

        if (createdDateEnd != null) {
            searchBean.addSql("main.pay_time < #{condition.createdDateEnd}");
            searchBean.addConditon("createdDateEnd", createdDateEnd.getTime());
        }

        if (StringUtils.isNotBlank(designStatus)) {
            searchBean.addWhere("design_status", designStatus);
        }

        if (StringUtils.isNotBlank(orderNo)) {
            searchBean.addSql(" (main.no like #{condition.orderNo} )");
            searchBean.addConditon("orderNo", orderNo + "%");
        }
        if (logisticsId != null && logisticsId > 0) {
            searchBean.addWhere("logistics_id", logisticsId);
        }

        if (merchantId != null && merchantId > 0) {
            searchBean.addWhere("merchant_id", merchantId);
        }
        if (StringUtils.isNotBlank(productName) || categoryId != null) {
            searchBean.addLeftJoin(" order_item as oi on oi.order_id = main.id");
            searchBean.addLeftJoin(" product as p on oi.product_id = p.id");
            searchBean.setGroup("main.id");
            isGroup = true;
        }

        if (StringUtils.isNotBlank(productName)) {
            if (StringUtils.isNotBlank(productName)) {
                searchBean.addSql(" (oi.product_name like #{condition.product_name} or p.sku like #{condition.product_name} or main.no like #{condition.product_name}) ");
                searchBean.addConditon("product_name", "%" + productName + "%");
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(originType)) {
            OrderOriginType orderOriginType = OrderOriginType.check(originType);
            if (orderOriginType.equals(OrderOriginType.NORMAL)) {
                searchBean.addNoWhere("origin_type", OrderOriginType.FBA.getValue());
            } else {
                searchBean.addWhere("origin_type", originType);
            }
        }
        if (status != null) {
            List<Long> longList = StringUtils.stringToLongList(status);
            searchBean.addIn("main.status", longList);
        }

        if (categoryId != null) {
            List<Long> categoryIds = categoryService.childrenIds(categoryId);
            categoryIds.add(-1L);
            searchBean.addIn("p.category_id", categoryIds);
        }

        if (payMethod != null) {
            String paymentMethodCode = orderService.getPayMethodCode(payMethod);
            searchBean.addWhere("amount", "payment_method", paymentMethodCode);
        }
        //自营订单：selfOwned，订单租户是自己，产品租户也是自己
        //跨租户供应订单：crossTenant，订单租户是自己，产品租户是别人
        //分销订单：distribution，订单租户是别人，产品租户是自己
        if (StrUtil.isNotBlank(orderType)) {
            if(OrderConstant.ORDER_TYPE_SELF_OWNED.equals(orderType)){
                searchBean.addWhere("tenant_id", tenantId);
                searchBean.addWhere("amount", "product_tenant_id", tenantId);
            }else if(OrderConstant.ORDER_TYPE_CROSS_TENANT.equals(orderType)){
                searchBean.addWhere("tenant_id", tenantId);
                searchBean.addNoWhere("amount", "product_tenant_id", tenantId);
            }else if(OrderConstant.DISTRIBUTION.equals(orderType)){
                searchBean.addNoWhere("tenant_id", tenantId);
                searchBean.addWhere("amount", "product_tenant_id", tenantId);
            }
        }

        if (StringUtils.isNotBlank(sort)) {
            searchBean.setSort(StringUtils.getOrderByToSort(sort));
        } else {
            searchBean.setSort("(main.id+0) desc");
        }
        searchBean = orderService.list(searchBean, isGroup);
        if (CollectionUtils.isEmpty(searchBean.getItems())) {
            return searchBean;
        }
        afterServiceEntranceShow(searchBean.getItems());

        if (CollectionUtils.isNotEmpty(searchBean.getItems())) {
            List<List<Order>> ordersList = ListUtil.spliceArrays(searchBean.getItems(),1000);
            for (List<Order> orders : ordersList) {
                orderService.formatOrderItems(orders);
            }
            orderService.formatMerchant(searchBean.getItems());
            orderService.formatFactory(searchBean.getItems());
        }
        orderService.formatCustomer(searchBean.getItems());
        this.buildSpliceLogisticsCarriage(searchBean.getItems());
        this.buildSpliceItemNum(searchBean.getItems());
        int index = 0;
        List<OrderItem> items = new ArrayList<>();
        List<Long> itemIds = Lists.newArrayList();
        List<Long> orderIds = Lists.newArrayList();
        for (Order order : searchBean.getItems()) {
            orderIds.add(order.getId());
            if (order.getLogisticsId() != null && order.getLogisticsId() == 0 && StrUtil.isNotEmpty(order.getCarriageName())) {
                order.setLogistics(new Logistics());
                order.getLogistics().setName(order.getCarriageName());
            }
            for (OrderItem orderItem : order.getItems()) {
                index++;
                items.add(orderItem);
                itemIds.add(orderItem.getId());
            }
        }

        if (!items.isEmpty()) {
            orderService.ORM(items);
        }

        Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap = orderAmountService.getAmountMap(orderIds);
        Map<Long, OrderItemPriceRespDto> orderItemPriceRespDtoMap = this.getItemPrice(itemIds);

        List<Order> orders = searchBean.getItems();
        List<Long> afterServiceIds = Lists.newArrayList();
        for (Order order : orders) {
            for (OrderItem orderItem : order.getItems()) {
                if (orderItem.getAfterServiceAuditItemDto() != null) {
                    afterServiceIds.add(orderItem.getAfterServiceAuditItemDto().getAfterServiceAuditId());
                }
            }
        }
        Map<Long, AfterServiceAudit> afterServiceAuditMap = Maps.newHashMap();
        Map<Long, RefundRecord> refundRecordMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(afterServiceIds)) {
            List<AfterServiceAudit> afterServiceAudits = afterServiceAuditService.findByIds(afterServiceIds);
            for (AfterServiceAudit afterServiceAudit : afterServiceAudits) {
                afterServiceAuditMap.put(afterServiceAudit.getId(), afterServiceAudit);
            }
            List<String> orderNos = afterServiceAudits.stream().map(a -> a.getOrderNo()).collect(Collectors.toList());
            List<RefundRecord> refundRecords = refundRecordDao.findByNos(orderNos);
            refundRecordMap = refundRecords.stream().collect(Collectors.toMap(r -> r.getAfterServiceAuditId(), r -> r, (a, b) -> b));
        }
        Map<Long, TenantRespDto> tenantMap = getTenantRespDtoMap(orderAmountRespDTOMap, orders);
        buildTenant(orders,tenantMap);
        Map<Long, OrderItemSupplyChainDTO> orderItemSupplyChainMap = orderItemSupplyChainFeign.getMapByIds(itemIds);
        Map<Long, OrderFbaCarriageDetailRespDto> orderIdFbaCarriageDetailMap = getOrderIdFbaCarriageDetailMap(orders);
        Map<Long, OrderAmountDetailDTO> orderAmountDetailDTOMap = orderAmountDetailFeign.findByOrderIds(orderIds);
        this.buildLogisticsCost(orders);

        for (Order order : orders) {
            if (EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.getValue().equals(order.getPaymentType())) {
                BigDecimal realTenantCarriageAmount = NumberUtil.sub(order.getTenantCarriageAmount(), order.getTenantRefundCarriageAmount());
                order.setCurrentCarriageAmount(realTenantCarriageAmount.doubleValue());
            }

            Double productCost = 0D;
            BigDecimal factorOrderCarriageAmount = BigDecimal.ZERO;
            OrderAmountRespDTO orderAmountRespDTO = orderAmountRespDTOMap.get(order.getId());
            setSaasOrderPayment(order, orderAmountRespDTO);
            order.setPaymentType(orderAmountRespDTO.getPaymentType());
            List<AfterServiceAudit> afterServiceAudits = Lists.newArrayList();

            for (OrderItem orderItem : order.getItems()) {
                OrderItemPriceRespDto orderItemPriceRespDto = orderItemPriceRespDtoMap.get(orderItem.getId());
                orderItem.setProductCost(0D);
                if (orderItem.getStatus() != OrderStatus.CANCEL.getStatus() && orderItem.getFactoryOrder() == null && order.getStatus() == OrderStatus.SHELVE.getStatus()) {
                    FactoryOrder factoryOrder = factoryOrderService.getByNoAll(orderItem.getNo());
                    if (factoryOrder != null) {
                        orderItem.setFactoryOrder(ConvertUtil.dtoConvert(factoryOrder, FactoryOrderDto.class));
                    }
                }

                if (orderItem.getProductSupply() != null) {
                    OrderItemSupplyChainDTO supplyChain = orderItemSupplyChainMap.get(orderItem.getId());
                    int mergeNum = supplyChain == null ? orderItem.getNum() : supplyChain.getMergeNum();
                    orderItem.setProductUnitCost(SupplyService.getSupplyPrice(orderItem.getProductSupply(), mergeNum));
                }
                if (orderItem.getFactoryOrder() != null) {
                    FactoryOrderDto factoryOrderDto = new FactoryOrderDto();
                    factoryOrderDto.setCompensationAmount(orderItem.getFactoryOrder().getCompensationAmount());
                    factoryOrderDto.setOutCycleDate(orderItem.getFactoryOrder().getOutCycleDate());
                    factoryOrderDto.setProductTime(orderItem.getFactoryOrder().getProductTime());
                    factoryOrderDto.setShipTime(orderItem.getFactoryOrder().getShipTime());
                    factoryOrderDto.setCancelTime(orderItem.getFactoryOrder().getCancelTime());
                    factoryOrderDto.setNum(orderItem.getFactoryOrder().getNum());
                    factoryOrderDto.setPrice(orderItem.getFactoryOrder().getPrice());
                    factoryOrderDto.setStatus(orderItem.getFactoryOrder().getStatus());
                    factoryOrderDto.setCarriageAmount(orderItem.getFactoryOrder().getCarriageAmount());

                    double subCompensationAmount = NumberUtil.sub(orderItem.getFactoryOrder().getTotalPrice(), orderItem.getFactoryOrder().getCompensationAmount());
                    factoryOrderDto.setSettlePrice(NumberUtil.add(subCompensationAmount, orderItem.getFactoryOrder().getCarriageAmount()).doubleValue());
                    factorOrderCarriageAmount = factorOrderCarriageAmount.add(orderItem.getFactoryOrder().getCarriageAmount());
                    if (orderItem.getFactoryOrder().getStatus() != OrderStatus.CANCEL.getStatus()) {
                        orderItem.setProductCost(orderItem.getFactoryOrder().getTotalPrice());
                        orderItem.setProductUnitCost(orderItem.getFactoryOrder().getPrice());
                    } else {
                        orderItem.setProductCost(0D);
                        orderItem.setProductUnitCost(0D);

                    }
                    orderItem.setShowFactoryOrder(factoryOrderDto);
                }
                Double price = orderItemPriceRespDto != null ? orderItemPriceRespDto.getTenantPrice().doubleValue() : 0d;
                orderItem.setPrice(price);

                Double income = orderItemPriceRespDto != null ? DoubleUtils.mul(orderItemPriceRespDto.getTenantPrice(),orderItem.getNum()) : 0d;
                if (orderItem.getAfterServiceAuditItemDto() != null) {
                    AfterServiceAudit afterServiceAudit = afterServiceAuditMap.get(orderItem.getAfterServiceAuditItemDto().getAfterServiceAuditId());
                    if (afterServiceAudit != null) {
                        if(afterServiceAudit.getType() == AfterServiceAuditService.TYPE_AFTER_SERVICE_REFUND) {
                            //售后退款-收入-0                           orderItem.setProductIncome(0D);
                            if (afterServiceAudit.getDutyAffiliation() == AfterServiceAuditService.DUTY_AFFILIATION_FACTORY) {
                                orderItem.setProductCost(0D);
                            }
                        }else if(afterServiceAudit.getType() == AfterServiceAuditService.TYPE_CANCEL_REFUND){
                            //取消
                            if(0 < afterServiceAudit.getRealRefundProductMoney()){
                                //退产品费，则认为没收入
                                income = 0d;
                            }
                        }
                    }
                    if (!afterServiceAudits.contains(afterServiceAudit)) {
                        afterServiceAudits.add(afterServiceAudit);
                    }
                }else {
                    if (orderItem.getStatus() == OrderStatus.CANCEL.getStatus() && orderItem.getRefundStatus() == RefundService.getRefundSuccess()) {
                        //没有售后记录，且状态为取消，则直接为0d
                        income = 0d;
                    }
                }
                orderItem.setProductIncome(income);

                productCost = DoubleUtils.add(orderItem.getProductCost(), productCost);
            }

            Double tenantRefundProductMoney = 0D;
            for (AfterServiceAudit afterServiceAudit : afterServiceAudits) {
                if ((afterServiceAudit.getType() == AfterServiceAuditService.TYPE_AFTER_SERVICE_REFUND ||
                        afterServiceAudit.getType() == AfterServiceAuditService.TYPE_CANCEL_REFUND) &&
                        afterServiceAudit.getStatus() == AfterServiceAuditService.STATUS_SUCCESS) {
                    RefundRecord refundRecord = refundRecordMap.get(afterServiceAudit.getId());
                    if (null == refundRecord) {
                        continue;
                    }
                    tenantRefundProductMoney = DoubleUtils.add(tenantRefundProductMoney, refundRecord.getTenantProductPrice());
                }
            }
            Double refundProductMoney = order.getRefundProductAmount();

            Double finalRefundProductMoney = tenantRefundProductMoney;
            // if (EnumOrderPayType.SAAS.getValue().equalsIgnoreCase(orderAmountRespDTO.getPaymentType())) {
            //     //未开启线上付款，购买saas产品，退款saas直接退商户，所以退款产品金额需要取refundProductMoney
            //     finalRefundProductMoney = refundProductMoney;
            // }

            if (order.getLogisticsSource().equalsIgnoreCase(DistributionProductLogisticsSourceEnum.ORDER_TENANT.name())) {
                order.setCurrentCarriageAmount(BigDecimal.ZERO.doubleValue());
                order.setServiceAmount(BigDecimal.ZERO.doubleValue());
                order.setRefundServiceAmount(BigDecimal.ZERO.doubleValue());
            }
            Double refundMaterialServiceAmount = order.getRefundMaterialServiceAmount();
            OrderFbaCarriageDetailRespDto orderFbaCarriageDetailRespDto = orderIdFbaCarriageDetailMap.get(order.getId());
            Double serviceAmount = getServiceAmount(order, orderFbaCarriageDetailRespDto,orderAmountDetailDTOMap);
            //租户责任售后重发的，有产品金额
            order.setProductIncome(DoubleUtils.sub(orderAmountRespDTO.getTenantProductAmount(), finalRefundProductMoney));

            order.setExpressIncome(order.getBeAfterServiceOrder() ? 0D : order.getCurrentCarriageAmount());

            order.setFactorOrderCarriageAmount(factorOrderCarriageAmount);
            order.setMaterialServiceAmount(DoubleUtils.sub(order.getMaterialServiceAmount(), refundMaterialServiceAmount));
            order.setProductCost(productCost);

            Double refundFreeGold =orderAmountRespDTO.getTenantRefundFreeGold();;
            // if (EnumOrderPayType.SAAS.getValue().equalsIgnoreCase(orderAmountRespDTO.getPaymentType())) {
            //     //未开启线上付款，购买saas产品，退款赠送金取不同字段
            //     refundFreeGold = order.getRefundFreeGold();
            // }
            order.setUsedFreeGold(DoubleUtils.sub(orderAmountRespDTO.getTenantUsedFreeGold(),refundFreeGold));

            order.setServiceAmount(DoubleUtils.sub(serviceAmount, orderAmountRespDTO.getTenantRefundServiceAmount()));
            order.setTotalCost(DoubleUtils.add(order.getExpressCost(), order.getProductCost(), order.getServiceAmountCost(), order.getFactorOrderCarriageAmount()));
            order.setTotalIncome(DoubleUtils.add(order.getExpressIncome(), order.getProductIncome(), order.getServiceAmount(), order.getMaterialServiceAmount()));
            TenantRespDto tenantRespDto = tenantMap.get(orderAmountRespDTO.getProductTenantId());
            order.setSupplierName(null!=tenantRespDto?tenantRespDto.getName():"");
            String orderTypeShow = OrderConstant.getOrderType(tenantId, order.getTenantId(), orderAmountRespDTO.getProductTenantId());
            order.setOrderType(orderTypeShow);
        }

        return searchBean;
    }

    @NotNull
    private Map<Long, OrderFbaCarriageDetailRespDto> getOrderIdFbaCarriageDetailMap(List<Order> orders) {
        Map<Long, OrderFbaCarriageDetailRespDto> orderIdFbaCarriageDetailMap = Maps.newHashMap();
        List<Order> fbaOrders = orders.stream().filter(i -> i.getOriginType().equals(OrderOriginType.FBA.getValue())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(fbaOrders)) {
            List<Long> fbaOrderIds = fbaOrders.stream().map(i -> i.getId()).collect(Collectors.toList());
            List<OrderFbaCarriageDetailRespDto> fbaCarriageDetails = orderFbaCarriageDetailFeign.getByIds(fbaOrderIds);
            orderIdFbaCarriageDetailMap = fbaCarriageDetails.stream().collect(Collectors.toMap(i -> i.getId(), i -> i, (a, b) -> b));
        }
        return orderIdFbaCarriageDetailMap;
    }

    private static Double getServiceAmount(Order order, OrderFbaCarriageDetailRespDto orderFbaCarriageDetailRespDto,Map<Long, OrderAmountDetailDTO> orderAmountDetailDTOMap) {
        Double serviceAmount = order.getServiceAmount();
        OrderAmountDetailDTO orderAmountDetailDTO = orderAmountDetailDTOMap.get(order.getId());
        Double fnskuChangeAmount = null != orderAmountDetailDTO && NumberUtils.greaterZero(orderAmountDetailDTO.getFnskuChangeAmount()) ? orderAmountDetailDTO.getFnskuChangeAmount() : 0D;
        if (null != orderFbaCarriageDetailRespDto) {
            //未支付运费，取原服务费
            if (!order.getCarriagePayStaus().equals(CarriagePayStatus.SUCCESS.getValue())) {
                BigDecimal fnskuDifference = NumberUtil.sub(fnskuChangeAmount, orderFbaCarriageDetailRespDto.getOriginalFnskuChangeAmount());
                Assert.validateTrue(NumberUtil.isLess(fnskuDifference,BigDecimal.ZERO),"fnskuDifference小于0");
                serviceAmount=NumberUtil.add(orderFbaCarriageDetailRespDto.getOriginalServiceAmount(),fnskuDifference).doubleValue();
            }
        }
        return serviceAmount;
    }

    public static void setOrderPayment(Order order, OrderAmountRespDTO orderAmountRespDTO) {
        Payment payment = new Payment();
        payment.setPayTime(order.getPayTime());
        if (orderAmountRespDTO != null) {
            payment.setMethod(orderAmountRespDTO.getPaymentMethod());
        }
        order.setPayment(payment);

    }

    public static void setSaasOrderPayment(Order order, OrderAmountRespDTO orderAmountRespDTO) {
        Payment payment = new Payment();
        payment.setPayTime(order.getPayTime());
        if (orderAmountRespDTO != null) {
            payment.setMethod(orderAmountRespDTO.getPaymentMethod());
            if (!TenantCommonConstant.isSdsdiy(order.getTenantId())) {
                //非sds 支付方式都是余额
//                payment.setMethod(PaymentMethod.BALANCEPAY.getValue());
            }
        }
        order.setPayment(payment);

    }


    /**
     * 售后入口显示
     *
     * @param orders
     */
    public void afterServiceEntranceShow(List<Order> orders) {
        List<AfterServiceAuditDto> asad = afterServiceAuditService.getDtoListByOrderIds(orders.stream().map(o -> o.getId()).collect(Collectors.toList()));
        for (Order order : orders) {
            order.setAbleAfterService(0);
            if (OrderService.ableAfterService(order)) {
                order.setAbleAfterService(1);
            }

            Double refundingMoney = 0D;
            List<Integer> afterServiceRefundType = Arrays.asList(AfterServiceAuditService.TYPE_CANCEL_REFUND, AfterServiceAuditService.TYPE_AFTER_SERVICE_REFUND);
            for (AfterServiceAuditDto one : asad) {
                if (one.getOrderId().equals(order.getId())) {
                    if (order.getAbleAfterService() == 0 && one.getType() != AfterServiceAuditService.TYPE_CANCEL_REFUND) {
                        order.setAbleAfterService(1);
                    }
                    if (afterServiceRefundType.contains(one.getType())
                            && (one.getStatus().equals(AfterServiceAuditService.STATUS_REMITTING) || one.getStatus().equals(AfterServiceAuditService.STATUS_DISPOSING))) {
                        refundingMoney += one.getRequestRefundAmount();
                    }
                }
            }

            order.setRefundingAmount(DoubleUtils.scale(refundingMoney));
        }
    }

    public Map<Long, OrderItemPriceRespDto> getItemPrice(List<Long> itemIds) {
        List<OrderItemPriceRespDto> orderItemPriceRespDtos = orderItemPriceFeign.findByItemIds(itemIds);
        return orderItemPriceRespDtos.stream().collect(Collectors.toMap(OrderItemPriceRespDto::getOrderItemId, Function.identity()));

    }

    public void buildTenant(List<Order> orders, Map<Long, TenantRespDto> tenantMap) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        for (Order order : orders) {
            TenantRespDto tenantRespDto = tenantMap.get(order.getTenantId());
            if (tenantRespDto != null) {
                order.setTenantName(tenantRespDto.getName());
            }
        }

    }

    public void buildLogisticsCost(List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        List<List<Order>> orderLists = ListUtils.spliceArrays(orders,1000);
        for (List<Order> orderList : orderLists) {
            this.spliceBuildLogisticsCost(orderList);
        }

    }
    public void spliceBuildLogisticsCost(List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        List<String> orderNos = orders.stream().map(Order::getNo).collect(Collectors.toList());
        Map<String, BigDecimal> logisticsCostMap = logisticsReconciliationImportFeign.logisticsCostByOrderNos(orderNos);
        for (Order order : orders) {
            order.setExpressCost(DoubleUtils.scale(logisticsCostMap.get(order.getNo())));
        }

    }

    public ExcelWriter saasFinanceSettlementStatisticsExport(Integer payMethod
            , Long categoryId
            , Long merchantId
            , String productName
            , String orderNo
            , Long factoryId
            , Long logisticsId
            , String status
            , String designStatus
            , Date createdDateBegin
            , Date createdDateEnd
            , Date finishDateBegin
            , Date finishDateEnd, String originType, Integer isMerchantTest
            ,String orderType) {
        List<Order> orders = saasSettleList(payMethod, productName, categoryId, merchantId, orderNo, logisticsId,
            status, designStatus, null, createdDateBegin, createdDateEnd, finishDateBegin,
            finishDateEnd, originType, null, null, null, isMerchantTest,orderType).getItems();

        return this.financeSettlementStatisticsExport(orders);
    }

    public ExcelWriter tenantFinanceSettlementStatisticsExport(
            Long tenantId,
            Integer payChannelMethod
            , Long categoryId
            , Long merchantId
            , String productName
            , String orderNo
            , Long factoryId
            , Long logisticsId
            , String status
            , String designStatus
            , Date createdDateBegin
            , Date createdDateEnd
            , Date finishDateBegin
            , Date finishDateEnd, String originType
            ,String orderType) {
        List<Order> orders = tenantSettleList(tenantId, payChannelMethod, productName, categoryId, merchantId, orderNo,
            logisticsId, status, designStatus, null, createdDateBegin, createdDateEnd, finishDateBegin,
            finishDateEnd, originType, null, null, null,orderType).getItems();
        return this.tenantFinanceSettlementStatisticsExport(orders);
    }

    public ExcelWriter financeSettlementStatisticsExport(List<Order> orders) {
        log.info("finance_settlement_statistics_export settleList end");
        //换行
        String enterChar = String.valueOf((char)10);
        ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getBigWriter(1000);;
        Integer count = 0;
//        Map<Long, OrderCarriageRespDto> orderCarriageMap = orderCarriageFeignService.getOrderCarriageMap(orders, null);
        for (Order o : orders) {
            count += o.getItems().size();
        }

        List<Integer> widths = Arrays.asList(25, 25, 20, 20, 35, 45, 12, 15, 10, 15, 15, 25, 15, 15, 12, 15, 15, 30, 15, 25, 25, 20, 20, 25, 25, 25, 25, 25);
        for (int i = 0; i < widths.size(); i++) {
            writer.setColumnWidth(i, widths.get(i));
        }

        List<String> head = Arrays.asList("客户订单号", "第三方订单号", "商户", "开单账号",
                "产品名称", "规格型号", "数量", "产品单价", "产品总价", "素材服务费", "产品供货单价", "产品供货总价", "产品赔付", "工厂物流", "结算价",
                "工厂名称", "订单状态", "订单总价", "赠送金", "客户物流收入", "客户物流成本", "工厂物流成本", "生产订单号", "子订单状态", "生产备注", "产品收入", "服务费收入", "物流渠道", "运单号", "支付渠道", "下单时间", "超期时间", "工厂赔付金额"
                , "平台赔付金额", "备注", "完成时间","财务备注","一级分类","二级分类","三级分类","租户","供应商","bd","平台","国家");
        writer.writeRow(head);
        /* 区块样式 */
        CellStyle cellStyle = writer.getCellStyle();
        /* 自动换行 */
        cellStyle.setWrapText(true);
        //合并订单列
//        List<Integer> columns = Arrays.asList(0, 1, 2, 3, 13, 14, 15, 16, 17, 21, 22, 23, 24, 25, 26, 29, 30);
        List<Integer> columns = Arrays.asList(0, 1, 2, 3, 16, 17, 18, 19, 20, 21, 25, 26, 27, 28, 29, 30, 33, 34,40,41);

        Integer index = 0;
        log.info("finance_settlement_statistics_export order_size:" + orders.size());
        List<Long> nvzhuangIds = categoryService.nvzhuangIds();
        Set<Long> merchantIds = Sets.newHashSet();
        for (Order order : orders) {
            merchantIds.add(order.getMerchantId());
        }
        Map<Long, CrmUserRespDTO> crmUserRespDTOMap = crmMerchantClueFeign.getCrmUserMapByMerchantIds(Lists.newArrayList(merchantIds));
        orderService.formatAddress(orders);
        for (Order one : orders) {
//            OrderCarriageRespDto orderCarriageResp = orderCarriageMap.get(one.getId());
            index++;
            log.info("finance_settlement_statistics_export:" + index);
            if (OrderStatus.DELETE.getStatus() == one.getStatus()) {
                continue;
            }
            boolean isFirst = true;
            int subRowNum = 0;

            List<String> logisticsNames = Lists.newArrayList();
            List<String> carriageNos = Lists.newArrayList();

            if(one.getLogisticsList() != null) {
                for (OrderSettleLogistics orderSettleLogistics : one.getLogisticsList()) {
                    logisticsNames.add(orderSettleLogistics.getName());
                    carriageNos.add(orderSettleLogistics.getCarriageNo());
                }
            }
            String logisticsName = String.join(enterChar, logisticsNames);
            String carriageNo = String.join(enterChar, carriageNos);
            CrmUserRespDTO crmUserRespDTO = crmUserRespDTOMap.get(one.getMerchantId());
            for (OrderItem innerOne : one.getItems()) {
                if (OrderStatus.DELETE.getStatus() == innerOne.getStatus()) {
                    continue;
                }
                String factoryOrderStatusDesc = "";
                if (innerOne.getFactoryOrder() != null) {
                    innerOne.setShipOverTime(innerOne.getFactoryOrder().shipOverTime());
                    innerOne.setCompensation(innerOne.getFactoryOrder().getCompensation());
                    FactoryOrderStatus factoryOrderStatus = FactoryOrderStatus.getByStatus(innerOne.getFactoryOrder().getStatus());
                    if (factoryOrderStatus != null) {
                        factoryOrderStatusDesc = factoryOrderStatus.getRemark();
                    }
                }


                List<Object> row = Arrays.asList(!isFirst ? "" : one.getNo(),
                        !isFirst ? "" : one.getOutOrderNo(),
                        !isFirst ? "" : one.getMerchant() == null ? "" : one.getMerchant().getName(),
                        !isFirst ? "" : one.getCustomer() == null ? "" : one.getCustomer().getUsername(),
                        innerOne.getProduct() != null ? innerOne.getProduct().getName() : innerOne.getProductName(),
                        "尺寸：" + innerOne.getProduct().getSize() + " 颜色：" + innerOne.getProduct().getColorName() + " 材质：" + (innerOne.getProduct().getTexture() == null ? "" : innerOne.getProduct().getTexture().getName()),
                        exportNum(innerOne.getNum(),innerOne.getMerchantCancelNum()),
                        innerOne.getPrice(),
                        innerOne.getAmount(),
                        innerOne.getMaterialServiceAmount(),
                        innerOne.getProductUnitCost(),
                        DoubleUtils.scale(innerOne.getProductCost()),
                        innerOne.getFactoryOrder() == null ? 0 : innerOne.getFactoryOrder().getCompensationAmount(),
                        innerOne.getFactoryOrder() == null ? 0 : innerOne.getFactoryOrder().getCarriageAmount(),
                        innerOne.getShowFactoryOrder() == null ? 0 : innerOne.getShowFactoryOrder().getSettlePrice(),
                        innerOne.getFactory() == null ? "" : innerOne.getFactory().getName(),
                        innerOne.getStatus().equals(OrderStatus.PAIN.getStatus()) && one.getDesignStatus().equalsIgnoreCase(OrderDesignStatus.WAIT.name()) ?
                                OrderDesignStatus.WAIT.getDesc() :
                                OrderStatus.getByStatus(one.getStatus()).getDesc(),

                        !isFirst ? "" : one.getTotalIncome(),
                        !isFirst ? "" : one.getUsedFreeGold(),
                        !isFirst ? "" : one.getExpressIncome(),
                        !isFirst ? "" : one.getExpressCost(),
                        !isFirst ? "" : one.getFactorOrderCarriageAmount(),
                        innerOne.getNo(),
                        OrderStatus.getByStatus(innerOne.getStatus()).getDesc(),
                        innerOne.getBeResendForLose() ? "补件" : factoryOrderStatusDesc,
                        !isFirst ? "" : one.getProductIncome(),
                        !isFirst ? "" : one.getServiceAmount(),
                        !isFirst ? "" : logisticsName,
                        !isFirst ? "" : carriageNo,
                        !isFirst ? "" : PaymentMethod.getByValud(one.getPayment().getMethod()).getDesc(),
                        !isFirst ? "" : DateUtil.formatDateTime(new Date(one.getPayment().getPayTime())),
                        com.ziguang.base.support.DateUtil.toDhmsStyle(innerOne.getShipOverTime()),
                        innerOne.getCompensation() == null ? "0.00" : innerOne.getCompensation().getAmount(),
                        !isFirst ? "" : one.getCompensationAmount(),
                        !isFirst ? "" : exportRemark(one),
                        one.getFinishTime() != null && one.getFinishTime() > 0 ? com.ziguang.base.support.DateUtil.longToString(one.getFinishTime()) : "",
                        exportCaiwuRemark(one, innerOne, nvzhuangIds),
                        innerOne.getProduct() != null &&  innerOne.getProduct().getCategories() != null && innerOne.getProduct().getCategories().size() == 3? innerOne.getProduct().getCategories().get(0).getName() :"",
                        innerOne.getProduct() != null &&  innerOne.getProduct().getCategories() != null && innerOne.getProduct().getCategories().size() == 3? innerOne.getProduct().getCategories().get(1).getName() :"",
                        innerOne.getProduct() != null &&  innerOne.getProduct().getCategories() != null && innerOne.getProduct().getCategories().size() == 3? innerOne.getProduct().getCategories().get(2).getName() :"",
                        one.getTenantName(),
                        one.getSupplierName(),
                        crmUserRespDTO != null ? crmUserRespDTO.getName() :"",
                        MerchantStorePlatformEnum.getDescByCode(one.getMerchantStorePlatformCode()),
                        one.getAddress() != null ? one.getAddress().getCountry() : ""

                );

                writer.writeRow(row);
                if (isFirst) {
                    isFirst = false;
                }
                subRowNum++;
            }
            if (subRowNum > 1) {
                List<Object> columnsOrderData = Arrays.asList(one.getNo(),
                        one.getOutOrderNo(),
                        one.getMerchant() == null ? "" : one.getMerchant().getName(),
                        one.getCustomer() == null ? "" : one.getCustomer().getUsername(),
                        one.getStatus().equals(OrderStatus.PAIN.getStatus()) && one.getDesignStatus().equalsIgnoreCase(OrderDesignStatus.WAIT.name()) ?
                                OrderDesignStatus.WAIT.getDesc() :
                                OrderStatus.getByStatus(one.getStatus()).getDesc(),
                        one.getTotalIncome(),
                        one.getUsedFreeGold(),
                        one.getExpressIncome(),
                        one.getExpressCost(),
                        one.getFactorOrderCarriageAmount(),
                        one.getProductIncome(),
                        one.getServiceAmount(),
                        logisticsName,
                        carriageNo,
//                        orderCarriageResp != null ? orderCarriageResp.getCarriageNo() : "",
                        one.getPayment() == null ? "" : PaymentMethod.getByValud(one.getPayment().getMethod()).getDesc(),
                        one.getPayment() == null ? "" : DateUtil.formatDateTime(new Date(one.getPayment().getPayTime())),
                        one.getCompensationAmount(),
                        exportRemark(one),
                        one.getTenantName(),
                        one.getSupplierName()
                );
                for (int i = 0; i < columns.size(); i++) {
                    Integer c = columns.get(i);
                    Sheet sheet =  writer.getSheet();
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(writer.getCurrentRow() - subRowNum, writer.getCurrentRow() - 1, c, c));
                    Cell cell = sheet.getRow(writer.getCurrentRow() - subRowNum).getCell(c);
                    cell.setCellStyle(cellStyle);
                    Object o = columnsOrderData.get(i);
                    if (o instanceof Number) {
                        cell.setCellValue(Double.valueOf(String.valueOf(columnsOrderData.get(i))));
                    } else {
                        cell.setCellValue(String.valueOf(columnsOrderData.get(i)));
                    }
                }
            }
        }

        return writer;
    }

    public static Integer exportNum(Integer originNum,Integer orderCancelNum){
        if(orderCancelNum == null){
            return originNum;
        }
        return originNum - orderCancelNum;

    }

    public ExcelWriter tenantFinanceSettlementStatisticsExport(List<Order> orders) {
        log.info("finance_settlement_statistics_export settleList end");
        ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getBigWriter(1000);;
        Integer count = 0;
        String enterChar = String.valueOf((char)10);
//        Map<Long, OrderCarriageRespDto> orderCarriageMap = orderCarriageFeignService.getOrderCarriageMap(orders, null);
        for (Order o : orders) {
            count += o.getItems().size();
        }


        List<Integer> widths = Arrays.asList(25, 25, 20, 20, 35, 45, 12, 15, 10, 15, 15, 25, 15, 15, 12, 15, 15, 30, 15, 25, 25, 20, 20, 25, 25, 25, 25, 25,25);
        for (int i = 0; i < widths.size(); i++) {
            writer.setColumnWidth(i, widths.get(i));
        }

        List<String> head = Arrays.asList("客户订单号", "第三方订单号", "商户", "开单账号",
                "产品名称", "规格型号", "数量", "产品单价", "产品总价", "产品供货单价", "产品供货总价",
                "工厂名称", "订单状态", "订单总价", "赠送金", "物流收入", "物流成本", "生产订单号", "子订单状态",
                "生产备注", "产品收入", "服务费收入", "物流渠道", "运单号", "支付渠道", "下单时间", "超期时间","租户", "备注");
        writer.writeRow(head);
        //生产备注  19

        //合并订单列
        List<Integer> columns = Arrays.asList(0, 1, 2, 3, 12, 13, 14, 15, 16, 20, 21, 22, 23, 24, 25, 27,28);
//        List<Integer> columns = Arrays.asList(0, 1, 2, 3, 13, 14, 15, 16, 17, 21, 22, 23, 24, 25, 26, 29, 30);
        /* 区块样式 */
        CellStyle cellStyle = writer.getCellStyle();
        /* 自动换行 */
        cellStyle.setWrapText(true);
        Integer index = 0;
        log.info("finance_settlement_statistics_export order_size:" + orders.size());

        for (Order one : orders) {
//            OrderCarriageRespDto orderCarriageResp = orderCarriageMap.get(one.getId());
            index++;
            log.info("finance_settlement_statistics_export:" + index);
            if (OrderStatus.DELETE.getStatus() == one.getStatus()) {
                continue;
            }
            boolean isFirst = true;
            int subRowNum = 0;
            List<String> logisticsNames = Lists.newArrayList();
            List<String> carriageNos = Lists.newArrayList();

            if(CollUtil.isNotEmpty(one.getLogisticsList())){
                for (OrderSettleLogistics orderSettleLogistics : one.getLogisticsList()) {
                    logisticsNames.add(orderSettleLogistics.getName());
                    carriageNos.add(orderSettleLogistics.getCarriageNo());
                }
            }

            String logisticsName = String.join(enterChar, logisticsNames);
            String carriageNo = String.join(enterChar, carriageNos);


            for (OrderItem innerOne : one.getItems()) {
                if (OrderStatus.DELETE.getStatus() == innerOne.getStatus()) {
                    continue;
                }
                String factoryOrderStatusDesc = "";
                if (innerOne.getFactoryOrder() != null) {
                    innerOne.setShipOverTime(innerOne.getFactoryOrder().shipOverTime());
                    innerOne.setCompensation(innerOne.getFactoryOrder().getCompensation());
                    FactoryOrderStatus factoryOrderStatus = FactoryOrderStatus.getByStatus(innerOne.getFactoryOrder().getStatus());
                    if (factoryOrderStatus != null) {
                        factoryOrderStatusDesc = factoryOrderStatus.getRemark();
                    }
                }


                List<Object> row = Arrays.asList(!isFirst ? "" : one.getNo(),
                        !isFirst ? "" : one.getOutOrderNo(),
                        !isFirst ? "" : one.getMerchant() == null ? "" : one.getMerchant().getName(),
                        !isFirst ? "" : one.getCustomer() == null ? "" : one.getCustomer().getUsername(),
                        innerOne.getProduct() != null ? innerOne.getProduct().getName() : innerOne.getProductName(),
                        "尺寸：" + innerOne.getProduct().getSize() + " 颜色：" + innerOne.getProduct().getColorName() + " 材质：" + (innerOne.getProduct().getTexture() == null ? "" : innerOne.getProduct().getTexture().getName()),
                        exportNum(innerOne.getNum(),innerOne.getMerchantCancelNum()),
                        innerOne.getPrice(),
                        innerOne.getAmount(),
                        innerOne.getProductUnitCost(),
                        DoubleUtils.scale(innerOne.getProductCost()),
                        innerOne.getFactory() == null ? "" : innerOne.getFactory().getName(),
                        innerOne.getStatus().equals(OrderStatus.PAIN.getStatus()) && one.getDesignStatus().equalsIgnoreCase(OrderDesignStatus.WAIT.name()) ?
                                OrderDesignStatus.WAIT.getDesc() :
                                OrderStatus.getByStatus(one.getStatus()).getDesc(),

                        !isFirst ? "" : one.getTotalIncome(),
                        !isFirst ? "" : one.getUsedFreeGold(),
                        !isFirst ? "" : one.getExpressIncome(),
                        !isFirst ? "" : one.getExpressCost(),
                        innerOne.getNo(),
                        OrderStatus.getByStatus(innerOne.getStatus()).getDesc(),
                        innerOne.getBeResendForLose() ? "补件" : factoryOrderStatusDesc,
                        !isFirst ? "" : one.getProductIncome(),
                        !isFirst ? "" : one.getServiceAmount(),
                        !isFirst ? "" : logisticsName,
                        !isFirst ? "" : carriageNo,
//                        !isFirst ? "" : PaymentChannelMethodEnum.isOffline(one.getPayment().getMethod()) ? PaymentChannelMethodEnum.OFFLINE.getDesc() : PaymentChannelMethodEnum.ONLINE.getDesc(),
                        !isFirst ? "" : PaymentMethod.getByValud(one.getPayment().getMethod()).getDesc(),
                        !isFirst ? "" : DateUtil.formatDateTime(new Date(one.getPayment().getPayTime())),
                        com.ziguang.base.support.DateUtil.toDhmsStyle(innerOne.getShipOverTime()),
                        one.getTenantName(),
                        !isFirst ? "" : exportRemark(one)


                );
                writer.writeRow(row);
                if (isFirst) {
                    isFirst = false;
                }
                subRowNum++;
            }
            if (subRowNum > 1) {
                List<Object> columnsOrderData = Arrays.asList(one.getNo(),
                        one.getOutOrderNo(),
                        one.getMerchant() == null ? "" : one.getMerchant().getName(),
                        one.getCustomer() == null ? "" : one.getCustomer().getUsername(),
                        one.getStatus().equals(OrderStatus.PAIN.getStatus()) && one.getDesignStatus().equalsIgnoreCase(OrderDesignStatus.WAIT.name()) ?
                                OrderDesignStatus.WAIT.getDesc() :
                                OrderStatus.getByStatus(one.getStatus()).getDesc(),
                        one.getTotalIncome(),
                        one.getUsedFreeGold(),
                        one.getExpressIncome(),
                        one.getExpressCost(),
                        one.getProductIncome(),
                        one.getServiceAmount(),
                        logisticsName,
                        carriageNo,
                        PaymentMethod.getByValud(one.getPayment().getMethod()).getDesc(),
                        one.getPayment() == null ? "" : DateUtil.formatDateTime(new Date(one.getPayment().getPayTime())),
                        one.getTenantName(),
                        exportRemark(one)
                );
                log.info("finance_settlement_statistics_export order item writer begin:" + index);
                for (int i = 0; i < columns.size(); i++) {
                    Integer c = columns.get(i);
                    Sheet sheet = writer.getSheet();
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(writer.getCurrentRow() - subRowNum, writer.getCurrentRow() - 1, c, c));
                    Object o = columnsOrderData.get(i);
                    Cell cell = sheet.getRow(writer.getCurrentRow() - subRowNum).getCell(c);
                    cell.setCellStyle(cellStyle);
                    if (o instanceof Number) {
                        cell.setCellValue(Double.valueOf(String.valueOf(columnsOrderData.get(i))));
                    } else {
                        cell.setCellValue(String.valueOf(columnsOrderData.get(i)));
                    }
                }
                log.info("finance_settlement_statistics_export order item writer end:" + index);
            }
        }

        return writer;
    }

    private static String exportRemark(Order one) {
        String remark = one.getBeAfterServiceOrder() ? "售后" : "";
        if (OrderOriginType.FBA.getValue().equals(one.getOriginType())) {
            remark += "FBA";
        }
        if (OrderConstant.ORDER_TYPE_CROSS_TENANT.equals(one.getOrderType())) {
            remark += "跨租户供应订单";
        }
        if (OrderConstant.DISTRIBUTION.equals(one.getOrderType())) {
            remark += "分销订单";
        }
        return remark;
    }

    /**
     * caiwu 想要的
     *
     * @param one
     * @return
     */
    private static String exportCaiwuRemark(Order one, OrderItem orderItem, List<Long> nzCategoryIds) {
        String remark = one.getMerchant() != null && !TenantCommonConstant.SDSDIY_TENANT_ID.equals(one.getMerchant().getTenantId()) ? "saas" : "";
        if (orderItem.getProduct() != null && nzCategoryIds.contains(orderItem.getProduct().getCategoryId())) {
            remark += "女装";
        }
        return remark;
    }

}