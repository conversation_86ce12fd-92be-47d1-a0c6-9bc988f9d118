package com.ps.ps.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ps.dto.MerchantBalanceDto;
import com.ps.dto.OrderPaymentRes;
import com.ps.exception.BusinessException;
import com.ps.ps.dao.OrderDao;
import com.ps.ps.feign.FactoryOrderCreateTaskFeign;
import com.ps.ps.feign.MerchantFeign;
import com.ps.ps.feign.OrderFeign;
import com.ps.ps.feign.application.ApplicationFeign;
import com.ps.ps.feign.logistics.TenantLogisticsFeign;
import com.ps.ps.feign.order.OrderAmountFeign;
import com.ps.ps.feign.order.OrderPaymentRelFeign;
import com.ps.ps.feign.order.TenantLogisticsOrderFeign;
import com.ps.ps.feign.payment.*;
import com.ps.ps.feign.tenant.TenantFeign;
import com.ps.ps.feign.user.MerchantUserAccountFeign;
import com.ps.ps.feign.wallet.TenantWalletFeign;
import com.ps.ps.service.cache.ProductCacheService;
import com.ps.ps.service.linstener.OrderEventService;
import com.ps.ps.service.payment.MerchantUserAccountService;
import com.ps.support.Assert;
import com.ps.support.IdGenerator;
import com.ps.support.utils.MathUtils;
import com.ps.support.utils.OrderCodeUtil;
import com.ps.util.EndProductUtil;
import com.ps.util.TransactionUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.EnumCommonStatus;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.enums.ApplicationCodeEnum;
import com.sdsdiy.common.base.enums.PlatformPermissionCodeEnum;
import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.logisticsapi.constant.LogisticsConstant;
import com.sdsdiy.logisticsapi.constant.TenantLogisticsConstant;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.LogisticsAndLogisticsSourceResp;
import com.sdsdiy.logisticsapi.enums.LogisticsCodeIdEnum;
import com.sdsdiy.orderapi.constant.EnumOrderPayType;
import com.sdsdiy.orderapi.constant.EnumOrderRefreshType;
import com.sdsdiy.orderapi.constant.OrderOriginType;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.constant.event.message.customer.CustomerPayDesignedOrderMessage;
import com.sdsdiy.orderapi.constant.event.message.customer.CustomerPayUndesignOrderMessage;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderapi.dto.order.*;
import com.sdsdiy.orderapi.dto.tenant.TenantLogisticsOrderReqDto;
import com.sdsdiy.orderapi.dto.tenant.TenantLogisticsOrderRespDto;
import com.sdsdiy.orderdata.dto.OrderItemPriceRespDto;
import com.sdsdiy.orderdata.dto.OrderItemRespDto;
import com.sdsdiy.orderdata.dto.OrderPaymentRelCreateDTO;
import com.sdsdiy.orderdata.dto.OrderPaymentRelRespDTO;
import com.sdsdiy.orderdata.dto.msg.BaseOrderMessageDTO;
import com.sdsdiy.orderdata.dto.order.update.OrderFreeGoldDTO;
import com.sdsdiy.orderdata.dto.order.update.OrderFreeGoldUpdateDTO;
import com.sdsdiy.orderdata.dto.order.update.OrderItemFreeGoldDTO;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import com.sdsdiy.paymentapi.dto.TenantWalletDto;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.TenantDistributionQueryParam;
import com.sdsdiy.paymentapi.param.TransactionEntryParam;
import com.sdsdiy.paymentapi.param.bill.MerchantBillCreateParam;
import com.sdsdiy.paymentapi.param.bill.MerchantBillsCreateParam;
import com.sdsdiy.paymentapi.util.PaymentParamUtil;
import com.sdsdiy.productdata.dto.auth.MerchantAuthProductParentRespDTO;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.constant.enums.DistributionProductLogisticsSourceEnum;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.UserAccountBalanceResp;
import com.sdsdiy.userapi.dto.application.TenantApplicationScenarioRelRespDto;
import com.sdsdiy.userapi.dto.tenant.*;
import com.sdsdiy.userapi.dto.tenant.req.TenantApplicationReqDto;
import com.sdsdiy.userapi.dto.tenant.req.TenantGroupConsumePermissionReqDto;
import com.sdsdiy.userapi.dto.tenant.req.TenantGroupReturnPermissionReqDto;
import com.ziguang.base.dto.GetBalanceBo;
import com.ziguang.base.dto.OrderAddDto;
import com.ziguang.base.model.*;
import com.ziguang.base.support.DoubleUtils;
import com.ziguang.base.support.contant.EndProductType;
import com.ziguang.base.support.contant.OrderDesignStatus;
import com.ziguang.base.support.contant.OrderStatus;
import com.ziguang.base.support.contant.PaymentMethod;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.userapi.constant.MerchantUserAccountConstant.MERCHANT_USER_BALANCE_TYPE;


@Log4j2
@Service
public class OrderPaymentService {
    public static final int TYPE_ORDER = 1;
    @Resource
    OrderPaymentRelFeign orderPaymentRelFeign;

    @Resource
    TenantFeign tenantFeign;

    @Resource
    OrderAmountFeign orderAmountFeign;
    @Resource
    MerchantFeign merchantFeign;
    @Resource
    FreePayMerchantFeign freePayMerchantFeign;

    @Resource
    TenantWalletFeign tenantWalletFeign;

    @Resource
    MerchantBillGenerateService merchantBillGenerateService;

    @Resource
    ProductCacheService productCacheService;

    @Resource
    FactoryOrderCreateTaskFeign factoryOrderCreateTaskFeign;

    @Resource
    MerchantUserAccountFeign merchantUserAccountFeign;
    @Resource
    TransactionFeign transactionFeign;

    @Resource
    EndProductService endProductService;

    @Resource
    OrderItemService orderItemService;

    @Resource
    OrderAmountRegenService orderAmountRegenService;

    @Resource
    @Lazy
    OrderService orderService;

    @Resource
    private OrderDao orderDao;

    @Resource
    OrderFeign orderFeign;


    @Resource
    PaymentAliFeign paymentServiceFeign;

    @Resource
    MerchantBillFeign merchantBillFeign;

    @Resource
    OrderEventService orderEventService;
    @Resource
    TenantLogisticsOrderFeign tenantLogisticsOrderFeign;
    @Resource
    TenantLogisticsFeign tenantLogisticsFeign;
    @Resource
    ApplicationFeign applicationFeign;
    
    @Resource
    private RocketMQTemplate rocketMQTemplate;
    @Resource
    private MerchantService merchantService;
    @Resource
    private FbaOrderTaskService fbaOrderTaskService;
    @Resource
    private TenantDistributionWalletFeign tenantDistributionWalletFeign;
    @Resource
    @Lazy
    private OrderPaymentService itself;

    public void payment(Long merchantId,User user,List<Long> orderIds){
        GetBalanceBo getBalanceBo = new GetBalanceBo(merchantId, user.getId(), orderIds, false);
        MerchantBalanceDto merchantBalanceDto = orderService.getBalance(getBalanceBo);
        OrderAddDto orderAddDto = new OrderAddDto();
        Merchant merchant = this.merchantService.findById(merchantId);

        UserAccountBalanceResp merchantBalance = this.merchantUserAccountFeign.getBalance(merchantId, user.getId(), MERCHANT_USER_BALANCE_TYPE);
        Assert.validateNull(merchantBalance, "账号信息异常！！");

        boolean tenantOpenOnlinePay = merchant.getTenantOpenOnlinePay().equals(YES);
        Boolean boolCheckIsFreePay = this.freePayMerchantFeign.checkIsFreePay(merchant.getTenantId(), merchant.getId(), user.getId());
        Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap = this.orderFeign.amountMap(new IdsSearchHelper(orderIds, null));
        boolean onlyOffLinePay = this.checkPaymentType(tenantOpenOnlinePay && !boolCheckIsFreePay, orderAmountRespDTOMap);
        if (onlyOffLinePay) {
            orderAddDto.setPaymentMethod(PaymentMethod.OFFLINE.getValue());
        } else {
            orderAddDto.setPaymentMethod(PaymentMethod.BALANCEPAY.getValue());
        }
        orderAddDto.setTotalAmount(merchantBalanceDto.getPaymentAmount());

        orderAddDto.setBalance(merchantBalance.getBalance().doubleValue());
        orderAddDto.setFreeGold(0D);
        orderAddDto.setUseBalance(merchantBalanceDto.getUseBalance());
        orderAddDto.setUseFreeGold(merchantBalanceDto.getUseFreeGold());
        orderAddDto.setCheckPassword(false);
        this.payment(merchantId, user.getId(), orderIds, orderAddDto);
    }

    /**
     * 根据订单id 发起支付
     * @param orderIds
     * @return
     */
    public OrderPaymentRes payment(Long merchantId, Long userId, List<Long> orderIds, OrderAddDto orderAddDto) {
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new BusinessException("订单id不能传空");
        }
        List<Order> comList = orderDao.findByIds(orderIds);
        Assert.validateBool(comList.size() == orderIds.size(), "订单数据异常");
        List<Order> orders = null;
        List<Long> paymentIds = Lists.newArrayList();
        Long currentPaymentId = null;
        Double totalAmount = 0D;
        MerchantRespDto merchant = merchantFeign.getMerchantById(merchantId);
        merchant.setId(merchantId);

        MerchantBalanceDto merchantBalanceDto = this.checkBalance(orderAddDto, userId, merchant, orderIds);

        String paymentMethod = orderAddDto.getPaymentMethod();

        Assert.validateTrue(merchantBalanceDto.isOnlyOffLinePay() != PaymentMethodEnum.isOffline(paymentMethod), "支付方式发生变动，请稍后刷新重试！");

        if (CollectionUtils.isNotEmpty(merchantBalanceDto.getOrders())) {
            orders = merchantBalanceDto.getOrders();
        }

        for (Order order : orders) {
            if (!order.getMerchantId().equals(merchantId)) {
                throw new BusinessException("订单信息不合法");
            }
        }
        if (orders.size() != orderIds.size()) {
            throw new BusinessException("订单id不合法");
        }
        List<Long> productIds = Lists.newArrayList();
        for (Order order : orders) {
            for (OrderItem orderItem : order.getItems()) {
                if (com.sdsdiy.orderdata.enums.OrderStatus.noCancel(orderItem.getStatus())) {
                    productIds.add(orderItem.getProductId());
                }
            }
            if (!OrderStatus.unpain(order.getStatus())) {
                throw new BusinessException("订单" + order.getNo() + "不能发起支付，请刷新重试");
            }
            if (!order.getPaymentId().equals(currentPaymentId)) {
                paymentIds.add(order.getPaymentId());
            }
            totalAmount = DoubleUtils.add(totalAmount, order.getTotalAmount());
        }
        List<Product> products = productCacheService.findByIds(productIds);
        Set<Long> parentIds = products.stream().map(Product::getParentId).collect(Collectors.toSet());
        Map<Long, MerchantAuthProductParentRespDTO> authParentMap = endProductService.getAuthParentMap(parentIds, merchantId);
        for (Product product : products) {
            EndProductType endProductType = EndProductUtil
                    .getEndProductType(authParentMap.get(product.getParentId()), product, merchant.getTenantId());
            if (!EndProductType.isValid(endProductType)) {
                throw new BusinessException("该订单中有无效产品，不能发起支付");
            }
        }
        for (Order o : orders) {
            if (o.getSettleAccountsStatus() != OrderService.SETTLE_ACCOUNTS_STATUS_NO) {
                throw new BusinessException("存在异常状态订单！请重新下单！");
            }
        }
        Map<Long, String> orderIdBillTitleMap = getOrderIdBillTitleMap(orders);

        List<PaymentParam> paymentParams = Lists.newArrayList();
        List<TransactionEntryParam> transactionEntryList = Lists.newArrayList();

        UserAccountBalanceResp userAccountBalanceResp = merchantUserAccountFeign.getUserBalance(merchantId, userId);
        List<TenantPayDto> tenantPayDtos = merchantBalanceDto.getPayDto().getTenantPayDtos();
        for (TenantPayDto tenantPayDto : tenantPayDtos) {
            if (NumberUtil.isGreater(tenantPayDto.getTenantTotalAmount(), BigDecimal.ZERO)) {
                // 使用了租户余额
                PaymentParam paymentParam = this.genPayTenantToTenantForOrder(merchant, userId, tenantPayDto);
                log.info("tenant paymentParam={}", JSON.toJSONString(paymentParam));
                paymentParams.add(paymentParam);
                for (OrderPaymentAmountDto orderPaymentAmountDto : tenantPayDto.getOrderPaymentAmountDtos()) {
                    TransactionEntryParam transactionEntryParam = new TransactionEntryParam();
                    transactionEntryParam.setPaymentBizNo(paymentParam.getBizNo());
                    transactionEntryParam.setBizNoForBill(orderPaymentAmountDto.getOrderNo());
                    transactionEntryParam.setBalance(orderPaymentAmountDto.getTenantUseBalance().abs());
                    transactionEntryParam.setBonus(orderPaymentAmountDto.getTenantUseFreeGold().abs());
                    transactionEntryParam.setTitle(orderIdBillTitleMap.get(orderPaymentAmountDto.getOrderId()));
                    transactionEntryParam.setType(NumberUtil.isGreater(orderPaymentAmountDto.getTenantUseBalance(),BigDecimal.ZERO)
                        ?TransactionEntryTypeEnum.PAY.getValue() : TransactionEntryTypeEnum.REFUND.getValue());
                    transactionEntryList.add(transactionEntryParam);
                }
            }
        }
        MerchantPayDto merchantPayDto = merchantBalanceDto.getPayDto().getMerchantPayDto();
        if (NumberUtil.isGreater(merchantPayDto.getTotalAmount(), BigDecimal.ZERO)) {
            PaymentParam paymentParam = this.genPayMerchantToTenantForOrder(merchant, userAccountBalanceResp, userId, paymentMethod, merchantPayDto);
            log.info("merchant paymentParam={}", JSON.toJSONString(paymentParam));
            paymentParams.add(paymentParam);
            for (OrderPaymentAmountDto orderPaymentAmountDto : merchantPayDto.getOrderPaymentAmountDtos()) {
                TransactionEntryParam transactionEntryParam = new TransactionEntryParam();
                transactionEntryParam.setPaymentBizNo(paymentParam.getBizNo());
                transactionEntryParam.setBizNoForBill(orderPaymentAmountDto.getOrderNo());
                transactionEntryParam.setBalance(orderPaymentAmountDto.getUseBalance().abs());
                transactionEntryParam.setBonus(orderPaymentAmountDto.getUseFreeGold().abs());
                transactionEntryParam.setTitle(orderIdBillTitleMap.get(orderPaymentAmountDto.getOrderId()));
                transactionEntryParam.setType(NumberUtil.isGreater(orderPaymentAmountDto.getUseBalance(),BigDecimal.ZERO)
                    ?TransactionEntryTypeEnum.PAY.getValue() : TransactionEntryTypeEnum.REFUND.getValue());
                transactionEntryList.add(transactionEntryParam);
            }
        }

        MultiTransactionCreateParam param=new MultiTransactionCreateParam();
        param.setPaymentList(paymentParams);
        param.setTransactionEntryList(transactionEntryList);
        PaymentDto payment = transactionFeign.createPayment(param);

        log.info("orderCreatePayment={}", JSON.toJSONString(payment));
        OrderPaymentRes resPayment = itself.payAndUpdateOrder(userId, orderIds, orders, merchant, merchantBalanceDto, paymentMethod,
            merchantPayDto, payment,paymentParams);
        return resPayment;


    }

    @GlobalTransactional
    public OrderPaymentRes payAndUpdateOrder(Long userId, List<Long> orderIds, List<Order> orders, MerchantRespDto merchant,
                                             MerchantBalanceDto merchantBalanceDto, String paymentMethod,
                                             MerchantPayDto merchantPayDto, PaymentDto payment,List<PaymentParam> paymentParams) {
        OrderPaymentRes resPayment = new OrderPaymentRes();
        if (CollectionUtils.isEmpty(paymentParams)) {
            //无需支付
            Assert.validateTrue(PaymentMethodEnum.needWaitCustomerPaid(paymentMethod), "此订单支付金额为0，不可使用支付宝支付");
            this.consumeOrReturnTenantOrderCount(merchantBalanceDto.getTenantLogisticsOrderReqDtos(), userId, merchant.getTenantId());
            this.paySuccess(userId, orders);
            resPayment.setStatus(PaymentStatusEnum.PAID.getStatus());
            return resPayment;
        }

        resPayment.setTotalAmount(DoubleUtils.number2BigDecimal(merchantPayDto.getTotalAmount()));
        resPayment.setId(payment.getId());

        this.addOrderPaymentRelNew(payment, orderIds);
        if (!PaymentMethodEnum.needWaitCustomerPaid(paymentMethod)) {
            //余额支付
            this.consumeOrReturnTenantOrderCount(merchantBalanceDto.getTenantLogisticsOrderReqDtos(), userId, merchant.getTenantId());
            PaymentDto paymentDto = transactionFeign.operateTransaction(payment.getId());

            log.info("orderOperateTransaction={}", JSON.toJSONString(paymentDto));
            this.updateBalanceFreeGold(paymentMethod, merchantBalanceDto.getOrderFreeGoldDTOS(), merchantBalanceDto.getOrderItemFreeGoldDTOS());
            this.paySuccess(userId, orders);
            resPayment.setStatus(PaymentStatusEnum.PAID.getStatus());
            resPayment.setOrderDesignStatus(orders.get(0).getDesignStatus());
        } else {
            //支付宝支付
            this.checkDecrementTenant(merchantBalanceDto.getTenantOrderNum(), merchant.getTenantId());
            this.onlyUpdateTenantBalanceFreeGold(paymentMethod, merchantBalanceDto.getOrderFreeGoldDTOS(), merchantBalanceDto.getOrderItemFreeGoldDTOS());
            resPayment.setStatus(PaymentStatusEnum.WAIT_PAY.getStatus());
            resPayment.setImgUrl(payment.getImgUrl());
        }
        return resPayment;
    }

    @NotNull
    private Map<Long, String> getOrderIdBillTitleMap(List<Order> orders) {
        Map<Long,String> orderIdBillTitleMap=Maps.newHashMap();
        for (Order order : orders) {
            Integer num = 0;
            Long productId = null;
            List<OrderItem> orderItems = order.getItems();
            for (OrderItem orderItem : orderItems) {
                if (com.sdsdiy.orderdata.enums.OrderStatus.noCancel(orderItem.getStatus()) &&
                    NumberUtils.greaterZero(orderItem.getProductId())) {
                    num += orderItem.getNum();
                    productId = orderItem.getProductId();
                }
            }
            String name = "";
            if (orderItems.size() > 0) {
                List<String> getName = productCacheService.getName(productId);
                name = getName.get(0);
            }
            String numString = num > 1 ? "...等 " + num : num.toString();
            String orderName = "购买" + name + numString + "件商品";
            orderIdBillTitleMap.put(order.getId(), orderName);
        }
        return orderIdBillTitleMap;
    }

    public OrderPaymentRes fbaCarriagePayAndBill(Long merchantId, Long userId, Long orderId, OrderAddDto dto) {
        MerchantRespDto merchant = merchantFeign.getMerchantById(merchantId);
        Order order = orderService.findById(orderId);

        String paymentMethod = dto.getPaymentMethod();
        Double amount = dto.getTotalAmount();
        MerchantBalanceDto merchantBalanceDto = this.checkBalanceByAmount(paymentMethod, orderId, userId, merchantId, amount);
        log.info("merchantBalanceDto={}", JSON.toJSONString(merchantBalanceDto));
        List<Order> orders = merchantBalanceDto.getOrders();
        List<PaymentParam> paymentParams = Lists.newArrayList();
        List<TransactionEntryParam> transactionEntryList = new ArrayList<>();

        UserAccountBalanceResp userAccountBalanceResp = merchantUserAccountFeign.getUserBalance(merchantId, userId);
        List<TenantPayDto> tenantPayDtos = merchantBalanceDto.getPayDto().getTenantPayDtos();
        for (TenantPayDto tenantPayDto : tenantPayDtos) {
            if (NumberUtil.isGreater(tenantPayDto.getTenantUseBalance(),BigDecimal.ZERO)) {
                PaymentParam paymentParam = this.genPayTenantToTenantForFbaCarriage(merchant, userId, tenantPayDto,tenantPayDto.getProductTenantId());
                log.info("tenant paymentParam={}", JSON.toJSONString(paymentParam));
                paymentParams.add(paymentParam);
                for (OrderPaymentAmountDto orderPaymentAmountDto : tenantPayDto.getOrderPaymentAmountDtos()) {
                    TransactionEntryParam transactionEntryParam = new TransactionEntryParam();
                    transactionEntryParam.setPaymentBizNo(paymentParam.getBizNo());
                    transactionEntryParam.setBizNoForBill(orderPaymentAmountDto.getOrderNo());
                    transactionEntryParam.setBalance(orderPaymentAmountDto.getTenantUseBalance());
                    transactionEntryParam.setBonus(orderPaymentAmountDto.getTenantUseFreeGold());
                    transactionEntryParam.setType(TransactionEntryTypeEnum.PAY.getValue());
                    transactionEntryParam.setTitle("支付fba运费");
                    transactionEntryList.add(transactionEntryParam);
                }
            }
        }
        if (merchantBalanceDto.getPaymentAmount()>0) {
            PaymentParam paymentParam = this.genPayMerchantToTenantForFbaCarriage(merchant, userAccountBalanceResp, userId,
                paymentMethod, order.getNo(), merchantBalanceDto);
            log.info("merchant paymentParam={}", JSON.toJSONString(paymentParam));
            paymentParams.add(paymentParam);
            TransactionEntryParam transactionEntryParam = new TransactionEntryParam();
            transactionEntryParam.setPaymentBizNo(paymentParam.getBizNo());
            transactionEntryParam.setBizNoForBill(order.getNo());
            transactionEntryParam.setBalance(paymentParam.getBalance());
            transactionEntryParam.setBonus(paymentParam.getBonus());
            transactionEntryParam.setTitle(paymentParam.getTitle());
            transactionEntryParam.setType(TransactionEntryTypeEnum.PAY.getValue());
            transactionEntryList.add(transactionEntryParam);
        }
        OrderPaymentRes resPayment = new OrderPaymentRes();

        if (CollectionUtils.isEmpty(paymentParams)) {
            //无需支付
            Assert.validateTrue(PaymentMethodEnum.needWaitCustomerPaid(paymentMethod), "此订单支付金额为0，不可使用支付宝支付");
            this.paySuccess(userId, orders);
            resPayment.setStatus(PaymentStatusEnum.PAID.getStatus());
            return resPayment;
        }

        MultiTransactionCreateParam param=new MultiTransactionCreateParam();
        param.setPaymentList(paymentParams);
        param.setTransactionEntryList(transactionEntryList);
        PaymentDto payment = transactionFeign.createPayment(param);
        if (!PaymentMethodEnum.needWaitCustomerPaid(paymentMethod)) {
            //余额支付
            PaymentDto paymentDto = transactionFeign.operateTransaction(payment.getId());
            log.info("payFbaCarriageresult={}", JSON.toJSONString(paymentDto));
            resPayment.setStatus(PaymentStatusEnum.PAID.getStatus());
            resPayment.setOrderDesignStatus(orders.get(0).getDesignStatus());
        } else {
            //支付宝支付
            resPayment.setStatus(PaymentStatusEnum.WAIT_PAY.getStatus());
            resPayment.setImgUrl(payment.getImgUrl());
        }
        resPayment.setTotalAmount(DoubleUtils.number2BigDecimal(merchantBalanceDto.getPaymentAmount()));
        resPayment.setId(payment.getId());
        return resPayment;


    }

    /**
     * 创建对应的
     *
     * @param payments
     */
    public void createMerchantBill(Long tenantId, Long merchantId, Long userId, List<PaymentDto> payments, List<Long> orderIds) {
        //todo: 消息
        log.info("payments={}", JSON.toJSONString(payments));
        List<Order> orders = orderService.findByIds(orderIds);
        orderService.formatOrderItems(orders);
        Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap = orderFeign.amountMap(new IdsSearchHelper(orderIds, null));
        UserAccountBalanceResp userAccountBalanceResp = merchantUserAccountFeign.getUserBalance(merchantId, userId);
        TenantWalletDto tenantWallet = tenantWalletFeign.get(tenantId);
        MerchantBillDisposeMoney merchantBillDisposeMoney = MerchantBillGenerateService.generateDisposeMonty(tenantWallet, userAccountBalanceResp);
        List<MerchantBillCreateParam> merchantBillCreateParams = Lists.newArrayList();
        for (PaymentDto payment : payments) {
            if (payment.getSourceRole().equalsIgnoreCase(PaymentRoleEnum.MERCHANT.getCode())) {
                //商户付款生成租户账单
                merchantBillCreateParams.addAll(merchantBillGenerateService.genMerchantByOrdersAndPayment(payment, orders, merchantBillDisposeMoney));
            } else if (payment.getSourceRole().equalsIgnoreCase(PaymentRoleEnum.TENANT.getCode())) {
                //租户付款生成租户账单
                merchantBillCreateParams.addAll(merchantBillGenerateService.genTenantByOrdersAndPayment(merchantId, payment, orders, merchantBillDisposeMoney, orderAmountRespDTOMap));

            }
        }
        merchantBillFeign.batchCreate(MerchantBillsCreateParam.builder().params(merchantBillCreateParams).build());

    }

    public PaymentDto orderAliPaySuccess(PaymentDto paymentDto) {
        List<OrderPaymentRelRespDTO> orderPaymentRelRespDTOS = orderPaymentRelFeign.findByPaymentId(paymentDto.getId());
        Assert.validateTrue(CollectionUtils.isEmpty(orderPaymentRelRespDTOS), "数据异常");
        List<Long> orderIds = Lists.newArrayList();
        for (OrderPaymentRelRespDTO orderPaymentRelRespDTO : orderPaymentRelRespDTOS) {
            orderIds.add(orderPaymentRelRespDTO.getOrderId());
        }
        List<Order> orders = orderDao.findByIds(orderIds);
        checkOrderTotalAmount(paymentDto, orders);
        orderService.formatOrderItems(orders);
        Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap = orderFeign.amountMap(new IdsSearchHelper(orderIds, null));
        MerchantRespDto merchant = merchantFeign.getMerchantById(paymentDto.getSourceMerchantId());
        merchant.setId(paymentDto.getSourceMerchantId());

        Map<Long, LogisticsAndLogisticsSourceResp> logisticsIdAndLogisticsSourceMap = getLogisticsIdAndLogisticsSourceMap(merchant.getTenantId(), orders);
        List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos = getTenantLogisticsOrderRespDtos(orders, orderAmountRespDTOMap, logisticsIdAndLogisticsSourceMap, merchant);
        this.consumeOrReturnTenantOrderCount(tenantLogisticsOrderReqDtos, paymentDto.getSourceUserId(), merchant.getTenantId());
        this.paySuccess(paymentDto.getOperateUserId(), orders);
        return paymentDto;
    }

    private static void checkOrderTotalAmount(PaymentDto paymentDto, List<Order> orders) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (Order order : orders) {
            totalAmount = totalAmount.add(BigDecimal.valueOf(order.getTotalAmount()));
        }

        log.info("orderTotalAmount{},paymentTotalAmount={}", totalAmount, paymentDto.getTotalAmount());
        if (!MathUtils.priceEqual(paymentDto.getTotalAmount(), totalAmount)) {
            log.info("支付宝支付失败，支付金额{},订单金额{}", paymentDto.getTotalAmount(), totalAmount);
            Assert.wrong("金额发生变动付款失败");
        }
    }

    public void addOrderPaymentRel(PaymentDto merchantPayment, PaymentDto tenantPayment, List<Long> orderIds) {
        Long merchantPaymentId = merchantPayment == null ? null : merchantPayment.getId();
        Long tenantPaymentId = tenantPayment == null ? null : tenantPayment.getId();
        if (!NumberUtils.greaterZero(merchantPaymentId) && !NumberUtils.greaterZero(tenantPaymentId)) {
            return;
        }
        List<OrderPaymentRelCreateDTO> params = Lists.newArrayList();

        for (Long orderId : orderIds) {
            OrderPaymentRelCreateDTO orderPaymentRelCreateDTO = new OrderPaymentRelCreateDTO();
            orderPaymentRelCreateDTO.setOrderId(orderId);
            orderPaymentRelCreateDTO.setPaymentId(merchantPaymentId);
            orderPaymentRelCreateDTO.setTenantPaymentId(tenantPaymentId);
            params.add(orderPaymentRelCreateDTO);
        }
        orderPaymentRelFeign.batchSave(params);

    }
    public void addOrderPaymentRelNew(PaymentDto merchantPayment,List<Long> orderIds) {
        Long merchantPaymentId = merchantPayment == null ? null : merchantPayment.getId();
        if (!NumberUtils.greaterZero(merchantPaymentId)) {
            return;
        }
        List<OrderPaymentRelCreateDTO> params = Lists.newArrayList();

        for (Long orderId : orderIds) {
            OrderPaymentRelCreateDTO orderPaymentRelCreateDTO = new OrderPaymentRelCreateDTO();
            orderPaymentRelCreateDTO.setOrderId(orderId);
            orderPaymentRelCreateDTO.setPaymentId(merchantPaymentId);
            params.add(orderPaymentRelCreateDTO);
        }
        orderPaymentRelFeign.batchSave(params);

    }

    /**
     * 创建租户下单信息
     *
     * @param userId
     * @param paymentMethod
     * @param merchantBalanceDto
     * @return
     */
    // private PaymentParam generatePaymentDtoForTenantPay(MerchantRespDto merchant, Long userId, String paymentMethod, MerchantBalanceDto merchantBalanceDto) {
    //     PaymentParam paymentParam = new PaymentParam();
    //     paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
    //     //租户一定是通过余额付给商户
    //     paymentParam.setMethod(PaymentMethodEnum.BALANCE.getCode());
    //     paymentParam.setBalance(DoubleUtils.number2BigDecimal(merchantBalanceDto.getTenantUseBalance(), BigDecimal.ROUND_CEILING));
    //     paymentParam.setGiftMoney(DoubleUtils.number2BigDecimal(merchantBalanceDto.getTenantUseFreeGold(), BigDecimal.ROUND_CEILING));
    //     paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
    //     paymentParam.setSubject("购买订单");
    //     paymentParam.setTotalAmount(DoubleUtils.number2BigDecimal(merchantBalanceDto.getTenantPaymentAmount(), BigDecimal.ROUND_CEILING));
    //     paymentParam.setSourceTenantId(merchant.getTenantId());
    //     paymentParam.setSourceMerchantId(0L);
    //     paymentParam.setOperateUserId(userId);
    //     paymentParam.setSourceUserId(userId);
    //     paymentParam.setSourceRole(PaymentRoleEnum.TENANT.getCode());
    //     paymentParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
    //
    //
    //     paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
    //     paymentParam.setOperateTargetRoleId(merchant.getId());
    //
    //     paymentParam.setTargetMerchantId(0L);
    //     paymentParam.setTargetTenantId(0L);
    //     paymentParam.setTargetRole(PaymentRoleEnum.SAAS.getCode());
    //     paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.NO_USE_BALANCE.getUsedType());
    //     paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_BUY_PRODUCT.getCode());
    //     paymentParam.setAlipaySupportPayeeType(AlipaySupportPayeeTypeEnum.ORDER.getPayeeType());
    //     return PaymentParamUtil.adjustParam(paymentParam);
    // }

    private PaymentParam genPayTenantToTenantForOrder(MerchantRespDto merchant, Long userId,
                                                      TenantPayDto tenantPayDto) {
        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        paymentParam.setPayType(TransactionPayTypeEnum.SUB.getValue());
        paymentParam.setMethod(PaymentMethodEnum.BALANCE.getCode());
        paymentParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_BUY_PRODUCT.getCode());
        paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
        paymentParam.setBalanceType(BalanceUsedType.TENANT_DISTRIBUTION_BALANCE.getUsedType());
        paymentParam.setBalance(DoubleUtils.number2BigDecimal(tenantPayDto.getTenantUseBalance(), BigDecimal.ROUND_CEILING));
        paymentParam.setBonus(DoubleUtils.number2BigDecimal(tenantPayDto.getTenantUseFreeGold(), BigDecimal.ROUND_CEILING));
        paymentParam.setTitle("购买订单");

        paymentParam.setSourceRole(PaymentRoleEnum.TENANT_DIS.getCode());
        paymentParam.setSourceTenantId(merchant.getTenantId());
        paymentParam.setSourceMerchantId(0L);
        paymentParam.setSourceUserId(0L);

        paymentParam.setTargetRole(PaymentRoleEnum.TENANT_SUP.getCode());
        paymentParam.setTargetMerchantId(0L);
        paymentParam.setTargetTenantId(tenantPayDto.getProductTenantId());

        paymentParam.setOperateUserId(0L);
        paymentParam.setOperateRole(PaymentRoleEnum.SYSTEM.getCode());
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(merchant.getId());
        return paymentParam;
    }

    // private PaymentParam generateFbaCarriagePaymentDtoForTenantPay(MerchantRespDto merchant, Long userId,
    //                                                                String orderNo, MerchantBalanceDto merchantBalanceDto) {
    //     PaymentParam paymentParam = new PaymentParam();
    //     paymentParam.setBizNo(orderNo);
    //     //租户一定是通过余额付给商户
    //     paymentParam.setMethod(PaymentMethodEnum.BALANCE.getCode());
    //     paymentParam.setBalance(DoubleUtils.number2BigDecimal(merchantBalanceDto.getTenantUseBalance(), BigDecimal.ROUND_CEILING));
    //     paymentParam.setGiftMoney(DoubleUtils.number2BigDecimal(merchantBalanceDto.getTenantUseFreeGold(), BigDecimal.ROUND_CEILING));
    //     paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
    //     paymentParam.setSubject("支付fba运费");
    //     paymentParam.setTotalAmount(DoubleUtils.number2BigDecimal(merchantBalanceDto.getTenantPaymentAmount(), BigDecimal.ROUND_CEILING));
    //     paymentParam.setSourceTenantId(merchant.getTenantId());
    //     paymentParam.setSourceMerchantId(0L);
    //     paymentParam.setOperateUserId(userId);
    //     paymentParam.setSourceUserId(userId);
    //     paymentParam.setSourceRole(PaymentRoleEnum.TENANT.getCode());
    //     paymentParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
    //
    //     paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
    //     paymentParam.setOperateTargetRoleId(merchant.getId());
    //
    //     paymentParam.setTargetMerchantId(0L);
    //     paymentParam.setTargetTenantId(0L);
    //     paymentParam.setTargetRole(PaymentRoleEnum.SAAS.getCode());
    //     paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.NO_USE_BALANCE.getUsedType());
    //     paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_PAY_FBA_ORDER_CARRIAGE.getCode());
    //     paymentParam.setAlipaySupportPayeeType(AlipaySupportPayeeTypeEnum.ORDER.getPayeeType());
    //     return PaymentParamUtil.adjustParam(paymentParam);
    // }

    private PaymentParam genPayTenantToTenantForFbaCarriage(MerchantRespDto merchant, Long userId,
                                                            TenantPayDto tenantPayDto,Long productTenantId) {
        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setPayType(TransactionPayTypeEnum.SUB.getValue());
        paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        paymentParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_PAY_FBA_ORDER_CARRIAGE.getCode());
        paymentParam.setBalanceType(BalanceUsedType.TENANT_DISTRIBUTION_BALANCE.getUsedType());
        paymentParam.setMethod(PaymentMethodEnum.BALANCE.getCode());
        paymentParam.setBalance(DoubleUtils.number2BigDecimal(tenantPayDto.getTenantUseBalance(), BigDecimal.ROUND_CEILING));
        paymentParam.setBonus(DoubleUtils.number2BigDecimal(tenantPayDto.getTenantUseFreeGold(), BigDecimal.ROUND_CEILING));
        paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
        paymentParam.setTitle("支付fba运费");

        paymentParam.setSourceRole(PaymentRoleEnum.TENANT_DIS.getCode());
        paymentParam.setSourceTenantId(merchant.getTenantId());
        paymentParam.setSourceMerchantId(0L);
        paymentParam.setSourceUserId(0L);

        paymentParam.setTargetRole(PaymentRoleEnum.TENANT_SUP.getCode());
        paymentParam.setTargetMerchantId(0L);
        paymentParam.setTargetTenantId(productTenantId);

        paymentParam.setOperateRole(PaymentRoleEnum.SYSTEM.getCode());
        paymentParam.setOperateUserId(userId);
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(merchant.getId());
        return PaymentParamUtil.adjustParam(paymentParam);
    }


    /**
     * 创建租户下单信息
     *
     * @param userId
     * @param paymentMethod
     * @param merchantBalanceDto
     * @return
     */
    // private PaymentParam generatePaymentDtoForMerchantPay(MerchantRespDto merchant,
    //                                                       UserAccountBalanceResp userAccountBalanceResp, Long userId,
    //                                                       String paymentMethod, MerchantBalanceDto merchantBalanceDto) {
    //     PaymentParam paymentParam = new PaymentParam();
    //     paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
    //     paymentParam.setMethod(paymentMethod);
    //     if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)) {
    //         Double balance = DoubleUtils.sub(merchantBalanceDto.getPaymentAmount(), merchantBalanceDto.getUseFreeGold());
    //         paymentParam.setBalance(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
    //         paymentParam.setGiftMoney(DoubleUtils.number2BigDecimal(merchantBalanceDto.getUseFreeGold(), BigDecimal.ROUND_CEILING));
    //     } else if (PaymentMethodEnum.OFFLINE.getCode().equalsIgnoreCase(paymentMethod)) {
    //         paymentParam.setBalance(DoubleUtils.number2BigDecimal(merchantBalanceDto.getPaymentAmount(), BigDecimal.ROUND_CEILING));
    //         paymentParam.setGiftMoney(DoubleUtils.number2BigDecimal(BigDecimal.ZERO));
    //     } else {
    //         paymentParam.setBalance(BigDecimal.ZERO);
    //         paymentParam.setGiftMoney(BigDecimal.ZERO);
    //     }
    //     paymentParam.setSourceUserId(0L);
    //     if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)) {
    //         if (MerchantUserAccountService.inCommonAccount(userAccountBalanceResp)) {
    //             paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.COMMON_BALANCE.getUsedType());
    //         } else {
    //             paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType());
    //             paymentParam.setSourceUserId(userId);
    //         }
    //     } else {
    //         paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.NO_USE_BALANCE.getUsedType());
    //     }
    //     paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
    //     paymentParam.setSubject("购买订单");
    //     paymentParam.setTotalAmount(DoubleUtils.number2BigDecimal(merchantBalanceDto.getPaymentAmount(), BigDecimal.ROUND_CEILING));
    //     paymentParam.setSourceTenantId(merchant.getTenantId());
    //     paymentParam.setSourceMerchantId(merchant.getId());
    //     paymentParam.setOperateUserId(userId);
    //     paymentParam.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
    //     paymentParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
    //
    //     paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
    //     paymentParam.setOperateTargetRoleId(merchant.getId());
    //
    //     if (0 < merchantBalanceDto.getMerchantPaySaasTotalAmount()) {
    //         //支付给saas 平台
    //         paymentParam.setTargetMerchantId(0L);
    //         paymentParam.setTargetTenantId(0L);
    //         paymentParam.setTargetRole(PaymentRoleEnum.SAAS.getCode());
    //     } else {
    //         // 支付给租户
    //         paymentParam.setTargetMerchantId(paymentParam.getSourceMerchantId());
    //         paymentParam.setTargetTenantId(paymentParam.getSourceTenantId());
    //         paymentParam.setTargetRole(PaymentRoleEnum.TENANT.getCode());
    //     }
    //     paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_BUY_PRODUCT.getCode());
    //     paymentParam.setAlipaySupportPayeeType(AlipaySupportPayeeTypeEnum.ORDER.getPayeeType());
    //     return PaymentParamUtil.adjustParam(paymentParam);
    // }

    private PaymentParam genPayMerchantToTenantForOrder(MerchantRespDto merchant,
                                                        UserAccountBalanceResp userAccountBalanceResp, Long userId,
                                                        String paymentMethod, MerchantPayDto merchantPayDto) {
        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        paymentParam.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        Integer balanceType = MerchantUserAccountConstant.getMerchantBalanceTypeForPaymentOrRefund(userAccountBalanceResp.getBalanceType(), paymentMethod);
        paymentParam.setBalanceType(balanceType);
        paymentParam.setMethod(paymentMethod);
        paymentParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_BUY_PRODUCT.getCode());
        paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
        paymentParam.setTitle("购买订单");
        paymentParam.setBalance(DoubleUtils.number2BigDecimal(merchantPayDto.getUseBalance(), BigDecimal.ROUND_CEILING));
        paymentParam.setBonus(DoubleUtils.number2BigDecimal(merchantPayDto.getUseFreeGold(), BigDecimal.ROUND_CEILING));

        Long sourceUserId = getSourceUserId(userAccountBalanceResp, userId, paymentMethod);
        paymentParam
            .setSourceRole(PaymentRoleEnum.MERCHANT.getCode())
            .setSourceTenantId(merchant.getTenantId())
            .setSourceMerchantId(merchant.getId())
            .setSourceUserId(sourceUserId);

        paymentParam
            .setTargetRole(PaymentRoleEnum.TENANT.getCode())
            .setTargetMerchantId(0L)
            .setTargetTenantId(merchant.getTenantId());

        // 商户操作，影响商户
        paymentParam
            .setOperateRole(PaymentRoleEnum.MERCHANT.getCode())
            .setOperateUserId(userId)
            .setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode())
            .setOperateTargetRoleId(merchant.getId());
        return paymentParam;
    }

    private static Integer getMerchantBalanceTypeForPay(UserAccountBalanceResp userAccountBalanceResp, String paymentMethod) {
        Integer balanceType;
        if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)) {
            if (MerchantUserAccountService.inCommonAccount(userAccountBalanceResp)) {
                balanceType=BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType();
            } else {
                balanceType=BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType();
            }
        } else {
            balanceType=BalanceUsedType.NO_USE_BALANCE.getUsedType();
        }
        return balanceType;
    }

    private static Long getSourceUserId(UserAccountBalanceResp userAccountBalanceResp, Long userId, String paymentMethod) {
        Long sourceUserId = 0L;
        if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)&&!MerchantUserAccountService.inCommonAccount(userAccountBalanceResp)){
            sourceUserId= userId;
        }
        return sourceUserId;
    }

    private PaymentParam genPayMerchantToTenantForFbaCarriage(MerchantRespDto merchant, UserAccountBalanceResp userAccountBalanceResp,
                                                              Long userId, String paymentMethod, String orderNo, MerchantBalanceDto merchantBalanceDto) {
        Long tenantId = merchant.getTenantId();
        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        paymentParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_PAY_FBA_ORDER_CARRIAGE.getCode());
        paymentParam.setMethod(paymentMethod);
        if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)) {
            Double balance = DoubleUtils.sub(merchantBalanceDto.getPaymentAmount(), merchantBalanceDto.getUseFreeGold());
            paymentParam.setBalance(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
            paymentParam.setBonus(DoubleUtils.number2BigDecimal(merchantBalanceDto.getUseFreeGold(), BigDecimal.ROUND_CEILING));
        } else if (PaymentMethodEnum.OFFLINE.getCode().equalsIgnoreCase(paymentMethod)) {
            paymentParam.setBalance(DoubleUtils.number2BigDecimal(merchantBalanceDto.getPaymentAmount(), BigDecimal.ROUND_CEILING));
            paymentParam.setBonus(DoubleUtils.number2BigDecimal(BigDecimal.ZERO));
        }
        Integer balanceType = MerchantUserAccountConstant.getMerchantBalanceTypeForPaymentOrRefund(userAccountBalanceResp.getBalanceType(), paymentMethod);
        paymentParam.setBalanceType(balanceType);
        Long sourceUserId = MerchantUserAccountConstant.getSourceUserId(userAccountBalanceResp.getBalanceType(), userId, paymentMethod);
        paymentParam.setSourceUserId(sourceUserId);
        paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
        paymentParam.setTitle("支付fba运费");

        paymentParam.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setSourceTenantId(tenantId);
        paymentParam.setSourceMerchantId(merchant.getId());

        paymentParam.setTargetRole(PaymentRoleEnum.TENANT.getCode());
        paymentParam.setTargetTenantId(tenantId);
        paymentParam.setTargetMerchantId(0L);

        paymentParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateUserId(userId);
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(merchant.getId());

        return paymentParam;
    }

    // private PaymentParam generateFbaCarriagePaymentDtoForMerchantPay(MerchantRespDto merchant, UserAccountBalanceResp userAccountBalanceResp,
    //                                                                  Long userId, String paymentMethod, String orderNo, MerchantBalanceDto merchantBalanceDto) {
    //     PaymentParam paymentParam = new PaymentParam();
    //     paymentParam.setPayType(TransactionPayTypeEnum.MAIN.getValue());
    //     paymentParam.setBizNo(orderNo);
    //     // 租户一定是通过余额付给商户
    //     paymentParam.setMethod(paymentMethod);
    //     if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)) {
    //         Double balance = DoubleUtils.sub(merchantBalanceDto.getPaymentAmount(), merchantBalanceDto.getUseFreeGold());
    //         paymentParam.setBalance(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
    //         paymentParam.setGiftMoney(DoubleUtils.number2BigDecimal(merchantBalanceDto.getUseFreeGold(), BigDecimal.ROUND_CEILING));
    //     } else if (PaymentMethodEnum.OFFLINE.getCode().equalsIgnoreCase(paymentMethod)) {
    //         paymentParam.setBalance(DoubleUtils.number2BigDecimal(merchantBalanceDto.getPaymentAmount(), BigDecimal.ROUND_CEILING));
    //         paymentParam.setGiftMoney(DoubleUtils.number2BigDecimal(BigDecimal.ZERO));
    //     } else {
    //         paymentParam.setBalance(BigDecimal.ZERO);
    //         paymentParam.setGiftMoney(BigDecimal.ZERO);
    //     }
    //     paymentParam.setSourceUserId(0L);
    //     if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)) {
    //         if (MerchantUserAccountService.inCommonAccount(userAccountBalanceResp)) {
    //             paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.COMMON_BALANCE.getUsedType());
    //         } else {
    //             paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType());
    //             paymentParam.setSourceUserId(userId);
    //         }
    //     } else {
    //         paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.NO_USE_BALANCE.getUsedType());
    //     }
    //     paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
    //     paymentParam.setSubject("支付fba运费");
    //     paymentParam.setTotalAmount(DoubleUtils.number2BigDecimal(merchantBalanceDto.getPaymentAmount(), BigDecimal.ROUND_CEILING));
    //     paymentParam.setSourceTenantId(merchant.getTenantId());
    //     paymentParam.setSourceMerchantId(merchant.getId());
    //     paymentParam.setOperateUserId(userId);
    //     paymentParam.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
    //     paymentParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
    //
    //     paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
    //     paymentParam.setOperateTargetRoleId(merchant.getId());
    //
    //     if (0 < merchantBalanceDto.getMerchantPaySaasTotalAmount()) {
    //         //支付给saas 平台
    //         paymentParam.setTargetMerchantId(0L);
    //         paymentParam.setTargetTenantId(0L);
    //         paymentParam.setTargetRole(PaymentRoleEnum.SAAS.getCode());
    //     } else {
    //         //支付给租户
    //         paymentParam.setTargetMerchantId(paymentParam.getSourceMerchantId());
    //         paymentParam.setTargetTenantId(paymentParam.getSourceTenantId());
    //         paymentParam.setTargetRole(PaymentRoleEnum.TENANT.getCode());
    //     }
    //     paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_PAY_FBA_ORDER_CARRIAGE.getCode());
    //     paymentParam.setAlipaySupportPayeeType(AlipaySupportPayeeTypeEnum.ORDER.getPayeeType());
    //     return PaymentParamUtil.adjustParam(paymentParam);
    // }


    /**
     * 这个只是校验
     */
    public MerchantBalanceDto checkBalance(OrderAddDto orderAddDto, Long userId, MerchantRespDto merchant, List<Long> orderIds) {
        MerchantBalanceDto merchantBalanceDto = new MerchantBalanceDto();
        GetBalanceBo getBalanceBo = new GetBalanceBo(merchant.getId(), userId, orderIds, false);
        merchantBalanceDto = this.getOrderBalance(getBalanceBo, null);
        checkBalance(orderAddDto.getPaymentMethod(), merchantBalanceDto);
        return merchantBalanceDto;

    }

    public MerchantBalanceDto checkBalanceByAmount(String paymentMethod, Long orderId, Long userId, Long merchantId, Double amount) {
        MerchantBalanceDto merchantBalanceDto = this.getBalanceByAmount(orderId, userId, merchantId, amount);
        checkBalance(paymentMethod, merchantBalanceDto);
        return merchantBalanceDto;

    }

    public void checkBalance(String paymentMethod, MerchantBalanceDto merchantBalanceDto) {
        if (PaymentMethodEnum.BALANCE.getCode().equals(paymentMethod)) {
            if (merchantBalanceDto.getStatus() == MerchantService.IS_NO_USE_BALANCE) {
                throw new BusinessException("账户余额不够！");
            }
        }else{
            checkTenantBalance(merchantBalanceDto);
        }
    }

    public void checkTenantBalance(MerchantBalanceDto merchantBalanceDto) {
        if (null == merchantBalanceDto.getPayDto() || CollUtil.isEmpty(merchantBalanceDto.getPayDto().getTenantPayDtos())) {
            return;
        }
        // 非余额支付只需要注意租户的余额
        List<Long> productTenantIds = merchantBalanceDto.getPayDto().getTenantPayDtos().stream()
            .filter(i -> i.getTenantBalanceStatus() == MerchantService.IS_NO_USE_BALANCE)
            .map(i -> i.getProductTenantId())
            .distinct()
            .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(productTenantIds)) {
            TenantRespDto tenantRespDto = tenantFeign.getTenantInfoById(productTenantIds.get(0));
            String tenantNo = null != tenantRespDto ? tenantRespDto.getTenantNo() : "";
            throw new BusinessException("租户（" + tenantNo + "）授信余额不足，请联系客服");
        }
    }

    public boolean isOnlyOffLinePay(Long merchantId, Long userId, Long orderId, Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap) {
        MerchantRespDto merchant = merchantFeign.getMerchantById(merchantId);
        //是否开启线上支付
        boolean tenantOpenOnlinePay = merchant.getTenantOpenOnlinePay().equals(BasePoConstant.YES);
        Boolean boolCheckIsFreePay = freePayMerchantFeign.checkIsFreePay(merchant.getTenantId(), merchant.getId(), userId);
        return checkPaymentType(tenantOpenOnlinePay && !boolCheckIsFreePay, orderAmountRespDTOMap);
    }


//     public MerchantBalanceDto getBalance(GetBalanceBo bo, MerchantRespDto merchant) {
//         MerchantBalanceDto merchantBalanceDto = new MerchantBalanceDto();
//         if (merchant == null) {
//             merchant = merchantFeign.getMerchantById(bo.getMerchantId());
//         }
//         Boolean boolCheckIsFreePay = freePayMerchantFeign.checkIsFreePay(merchant.getTenantId(), merchant.getId(), bo.getUserId());
//         //是否开启线上支付
//         boolean tenantOpenOnlinePay = merchant.getTenantOpenOnlinePay().equals(BasePoConstant.YES);
//
//         if (tenantOpenOnlinePay) {
//             TenantWalletDto tenantWallet = tenantWalletFeign.get(merchant.getTenantId());
//             merchantBalanceDto.setTenantBalance(tenantWallet.getBalance().doubleValue());
//             merchantBalanceDto.setTenantFreeGold(tenantWallet.getGiftMoney().doubleValue());
//         } else {
//             //线上支付未开启时，租户有余额也不能用，直接设置成0
//             merchantBalanceDto.setTenantBalance(BigDecimal.ZERO.doubleValue());
//             merchantBalanceDto.setTenantFreeGold(BigDecimal.ZERO.doubleValue());
//         }
//
//
//         //构建订单信息
//         List<Order> orders = orderService.findByIds(bo.getOrderIds());
//         Assert.validateBool(bo.getOrderIds().size() == orders.size(), "数据异常");
//         Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap = orderFeign.amountMap(new IdsSearchHelper(bo.getOrderIds(), null));
//         merchantBalanceDto.setOrderAmountRespDTOMap(orderAmountRespDTOMap);
//         boolean onlyOffLinePay = this.checkPaymentType(tenantOpenOnlinePay && !boolCheckIsFreePay, orderAmountRespDTOMap);
//         merchantBalanceDto.setOnlyOffLinePay(onlyOffLinePay);
//         orderService.formatOrderItems(orders);
//         orderService.formatAddress(orders);
//         for (Order order : orders) {
//             orderService.removeCancelItems(order);
//         }
//         //旧订单金额，用于比较订单是否变动
//         Double oldTotalAmount = 0D;
//         for (Order order : orders) {
//             oldTotalAmount = DoubleUtils.add(oldTotalAmount, order.getTotalAmount());
//         }
//         if (bo.getRegenMoney()) {
//             orders = orderAmountRegenService.reGenAmountByOrders(orders);
//         }
//
//         UserAccountBalanceResp merchantBalance = merchantUserAccountFeign.getBalance(bo.getMerchantId(), bo.getUserId(), MERCHANT_USER_BALANCE_TYPE);
//         merchantBalanceDto.setBalance(merchantBalance.getBalance().doubleValue());
//         merchantBalanceDto.setFreeGold(merchantBalance.getFreeGold().doubleValue());
//
//         Map<Long, OrderItemPriceRespDto> orderItemPriceRespDtoMap = orderService.getItemsPriceByOrders(orders);
//         Assert.validateBool(bo.getOrderIds().size() == orderAmountRespDTOMap.size(), "旧数据未处理，请联系开发");
//
//         Assert.validateNull(merchantBalance, "账号信息异常！！");
//         Double totalFreeGold = 0D;
//         Double totalTenantFreeGold = 0D;
//         //商户要付的总金额
//         double totalAmount = 0D;
//         //用户是否免付钱 todo 关闭免支付，后续查数据库
//         boolean avoidPayment = false;
//
//         PayTotalAmountDto payTotalAmountDto = new PayTotalAmountDto();
//         List<OrderItemFreeGoldDTO> orderItemFreeGoldDTOS = Lists.newArrayList();
//         List<OrderFreeGoldDTO> orderFreeGoldDTOS = Lists.newArrayList();
//
//         //获取物流类型和发货方式，是否租户物流，是的话，发货方式是什么
//         Map<Long, LogisticsAndLogisticsSourceResp> logisticsIdAndLogisticsSourceMap = getLogisticsIdAndLogisticsSourceMap(merchant.getTenantId(), orders);
//
//         for (Order order : orders) {
//             OrderAmountRespDTO orderAmountRespDTO = orderAmountRespDTOMap.get(order.getId());
//             Assert.validateNull(orderAmountRespDTO, "订单金额信息异常请联系开发");
//             String paymentType = null;
//             //计算订单的各项金额
//             double orderTotalAmount = order.getTotalAmount();
//             OrderFreeGoldDTO orderFreeGoldDTO = new OrderFreeGoldDTO();
//             orderFreeGoldDTO.setOrderId(order.getId());
//             orderFreeGoldDTO.setOrderAmount(orderTotalAmount);
//             orderFreeGoldDTO.setOrderTenantAmount(orderAmountRespDTO.getTenantTotalAmount());
//             orderFreeGoldDTOS.add(orderFreeGoldDTO);
//             totalAmount = DoubleUtils.add(totalAmount, orderTotalAmount);
//             //商户是否真实的支付了
//             boolean merchantPay = false;
//             boolean tenantPay = false;
//
//             //判断中有更简单的写法，但这样可以更清晰的描述程序
//             if (EnumOrderPayType.TENANT.getValue().equalsIgnoreCase(orderAmountRespDTO.getPaymentType())) {
//                 //购买租户的产品
//                 paymentType = EnumOrderPayType.TENANT.getValue();
//                 if (tenantOpenOnlinePay) {
//                     //开启线上支付 则商户付钱
//                     payTotalAmountDto.increaseMerchantPayTenant(orderTotalAmount);
//                     merchantPay = true;
//                 } else {
//                     //未开启线上付款或用户免支付  商户不必付钱直接过
//                 }
//             } else {
//                 //购买sds的产品
//                 if (tenantOpenOnlinePay) {
//                     //开启线上支付时 租户一定要付钱给saas
//                     paymentType = EnumOrderPayType.TENANT_SAAS.getValue();
//                     log.info("orderAmountRespDTO={}", JSON.toJSONString(orderAmountRespDTO));
//                     //租户物流，不收运费和服务费
//                     Double tenantToSaasTotalAmount = getTenantToSaasTotalAmount(logisticsIdAndLogisticsSourceMap, order, orderAmountRespDTO);
//                     log.info("tenantToSaasTotalAmount={}", tenantToSaasTotalAmount);
//                     payTotalAmountDto.increaseTenantPaySaas(tenantToSaasTotalAmount);
//                     //租户物流，不收运费和服务费，更新orderamount用
//                     orderFreeGoldDTO.setOrderTenantAmount(tenantToSaasTotalAmount);
//                     tenantPay = true;
//                     if (avoidPayment) {
//                         //如果开启免付则商户可以不用付钱 do nothing
//                     } else {
//                         //未开启免费则商户必须付对应的钱 给租户
//                         payTotalAmountDto.increaseMerchantPayTenant(orderTotalAmount);
//                         merchantPay = true;
//                     }
//                 } else {
//                     //未开启线上支付时 则是商户付钱 给saas
//                     paymentType = EnumOrderPayType.SAAS.getValue();
//                     payTotalAmountDto.increaseMerchantPaySaas(orderTotalAmount);
//                     merchantPay = true;
//                 }
//             }
//
//             orderFreeGoldDTO.setPaymentType(paymentType);
//             Double weight = 0d;
//             for (OrderItem orderItem : order.getItems()) {
//                 weight += orderItem.getWeight();
//             }
//             order.setWeight(weight);
//             Double orderMaxCanUsedFreeGold = 0D;
//             Double tenantOrderMaxCanUsedFreeGold = 0D;
//             for (OrderItem orderItem : order.getItems()) {
//                 double itemAmount = orderItem.getAmount();
//                 OrderItemFreeGoldDTO orderItemFreeGoldDTO = new OrderItemFreeGoldDTO();
//                 orderItemFreeGoldDTO.setOrderItemId(orderItem.getId());
//                 orderItemFreeGoldDTO.setPaymentType(paymentType);
//
//                 orderItemFreeGoldDTOS.add(orderItemFreeGoldDTO);
//
//                 OrderItemPriceRespDto orderItemPriceRespDto = orderItemPriceRespDtoMap.get(orderItem.getId());
//                 //租户要付的钱
//                 double itemTenantAmount = DoubleUtils.mul(orderItemPriceRespDto.getTenantPrice(), orderItem.getNum());
//                 orderItemFreeGoldDTO.setItemTenantAmount(itemTenantAmount);
//                 orderItemFreeGoldDTO.setItemAmount(itemAmount);
//
//                 if (!OrderStatus.isCancel(orderItem.getStatus()) && orderItem.getProduct().onSaleValidStatus() == ProductRecommendService.NO_STATUS) {
//                     //没有促销价时才能使用赠送金
//                     double maxCanUsedfreeGold = DoubleUtils.mul(itemAmount, OrderService.getFreeGoldRatio());
//                     double tenantItemMaxCanUsedFreeGold = DoubleUtils.mul(itemTenantAmount, OrderService.getFreeGoldRatio());
//
//                     /*******计算可用赠送金 begin*******/
//                     if (maxCanUsedfreeGold > itemAmount) {
//                         maxCanUsedfreeGold = itemAmount;
//                     }
//                     double leftFreeGold = DoubleUtils.sub(merchantBalance.getFreeGold().doubleValue(), totalFreeGold);
//                     if (leftFreeGold < maxCanUsedfreeGold) {
//                         maxCanUsedfreeGold = leftFreeGold;
//                     }
//                     /*****计算可用赠送金 end*******/
//
//                     /*******计算租户可用赠送金 begin*******/
//                     if (tenantItemMaxCanUsedFreeGold > itemTenantAmount) {
//                         tenantItemMaxCanUsedFreeGold = itemTenantAmount;
//                     }
//                     double leftTenantFreeGold = DoubleUtils.sub(merchantBalanceDto.getTenantFreeGold(), totalTenantFreeGold);
//                     if (leftTenantFreeGold < tenantItemMaxCanUsedFreeGold) {
//                         tenantItemMaxCanUsedFreeGold = leftTenantFreeGold;
//                     }
//                     /*******计算租户可用赠送金 end*******/
//
//                     if (merchantPay) {
//                         //商户有真实付费时才使用赠送金
//                         orderMaxCanUsedFreeGold = DoubleUtils.add(orderMaxCanUsedFreeGold, maxCanUsedfreeGold);
// //                        orderItem.setMaxCanUseGold(maxCanUsedfreeGold);
//                         totalFreeGold = DoubleUtils.add(maxCanUsedfreeGold, totalFreeGold);
//                         orderItemFreeGoldDTO.setItemFreeGold(maxCanUsedfreeGold);
//                     }
//                     if (tenantPay) {
//                         //租户有付费时才使用赠送金
//                         tenantOrderMaxCanUsedFreeGold = DoubleUtils.add(tenantOrderMaxCanUsedFreeGold, tenantItemMaxCanUsedFreeGold);
//                         totalTenantFreeGold = DoubleUtils.add(tenantItemMaxCanUsedFreeGold, totalTenantFreeGold);
//                         orderItemFreeGoldDTO.setItemTenantFreeGold(tenantItemMaxCanUsedFreeGold);
//                     }
//
//                 }
//             }
//             orderFreeGoldDTO.setOrderFreeGold(orderMaxCanUsedFreeGold);
//             orderFreeGoldDTO.setOrerTenantFreeGold(tenantOrderMaxCanUsedFreeGold);
//
//         }
//
//
//         List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos = getTenantLogisticsOrderRespDtos(orders, orderAmountRespDTOMap, logisticsIdAndLogisticsSourceMap, merchant);
//         //保存扣减额度，扣减额度
//         merchantBalanceDto.setTenantLogisticsOrderReqDtos(tenantLogisticsOrderReqDtos);
//         BigDecimal deductTotalValue = tenantLogisticsOrderReqDtos.stream().map(o -> o.getDeductTotalValue()).reduce(BigDecimal.ZERO, BigDecimal::add);
//         merchantBalanceDto.setTenantOrderNum(deductTotalValue);
//
//         Double needBalance = DoubleUtils.sub(totalAmount, totalFreeGold);
//         merchantBalanceDto.setNeedBalance(needBalance);
//
//         Double useFreeGold = Math.min(merchantBalance.getFreeGold().doubleValue(), totalFreeGold);
//         Double useTenantFreeGold = Math.min(merchantBalanceDto.getTenantFreeGold(), totalTenantFreeGold);
//
//         merchantBalanceDto.setUseBalance(DoubleUtils.sub(totalAmount, useFreeGold));
//         merchantBalanceDto.setMerchantUseBalance(DoubleUtils.sub(payTotalAmountDto.getMerchantTotalAmount(), useFreeGold));
//         merchantBalanceDto.setTenantUseBalance(DoubleUtils.sub(payTotalAmountDto.getTenantPaySaasTotalAmount(), useTenantFreeGold));
//
//         merchantBalanceDto.setUseFreeGold(useFreeGold);
//         merchantBalanceDto.setTenantUseFreeGold(useTenantFreeGold);
//
//         merchantBalanceDto.setPaymentAmount(totalAmount);
//         merchantBalanceDto.setTenantPaymentAmount(payTotalAmountDto.getTenantPaySaasTotalAmount());
//
//         merchantBalanceDto.setAmountChange(!MathUtils.priceEqual(oldTotalAmount, totalAmount));
//         merchantBalanceDto.setOrders(orders);
//         merchantBalanceDto.setTenantOpenOnlinePay(tenantOpenOnlinePay);
//
//         merchantBalanceDto.setMerchantPaySaasTotalAmount(payTotalAmountDto.getMerchantPaySaasTotalAmount());
//         merchantBalanceDto.setMerchantPayTenantTotalAmount(payTotalAmountDto.getMerchantPayTenantTotalAmount());
//         merchantBalanceDto.setOrderItemFreeGoldDTOS(orderItemFreeGoldDTOS);
//         merchantBalanceDto.setOrderFreeGoldDTOS(orderFreeGoldDTOS);
//         //merchantBalanceDto.setTenantOrderNum(tenantOrderNum);
//         this.checkBalance(merchantBalanceDto);
//         if (merchantBalanceDto.getTenantBalanceStatus() == MerchantService.IS_NO_USE_BALANCE) {
//             throw new BusinessException("您的服务商余额不足，请联系客服");
//         }
//         return merchantBalanceDto;
//     }
    public MerchantBalanceDto getOrderBalance(GetBalanceBo bo, MerchantRespDto merchant) {

        if (merchant == null) {
            merchant = merchantFeign.getMerchantById(bo.getMerchantId());
        }
        Long tenantId = merchant.getTenantId();
        List<Order> orders = getOrders(bo);
        Boolean boolCheckIsFreePay = freePayMerchantFeign.checkIsFreePay(merchant.getTenantId(), merchant.getId(), bo.getUserId());
        UserAccountBalanceResp merchantBalance = merchantUserAccountFeign.getBalance(bo.getMerchantId(), bo.getUserId(), MERCHANT_USER_BALANCE_TYPE);
        Assert.validateNull(merchantBalance, "账号信息异常！！");
        Map<Long, OrderItemPriceRespDto> orderItemPriceRespDtoMap = orderService.getItemsPriceByOrders(orders);

        //是否开启线上支付
        boolean tenantOpenOnlinePay = merchant.getTenantOpenOnlinePay().equals(BasePoConstant.YES);
        Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap = orderFeign.amountMap(new IdsSearchHelper(bo.getOrderIds(), null));
        List<Long> otherProductTenantIds = orderAmountRespDTOMap.values().stream().map(i -> i.getProductTenantId()).filter(i -> !i.equals(tenantId)).distinct().collect(Collectors.toList());
        Assert.validateTrue(CollUtil.isNotEmpty(otherProductTenantIds)&&!tenantOpenOnlinePay,"开启线上付款才能分销产品");
        Assert.validateBool(bo.getOrderIds().size() == orderAmountRespDTOMap.size(), "旧数据未处理，请联系开发");
        boolean onlyOffLinePay = this.checkPaymentType(tenantOpenOnlinePay && !boolCheckIsFreePay, orderAmountRespDTOMap);
        Map<Long, TenantDistributorWalletDto> suTenantIdWalletMap=Maps.newHashMap();
        if (tenantOpenOnlinePay) {
            List<Long> productTenantIds = orderAmountRespDTOMap.values().stream().filter(i -> NumberUtils.greaterZero(i.getProductTenantId())).map(i -> i.getProductTenantId()).distinct().collect(Collectors.toList());
            TenantDistributionQueryParam tenantDistributionQueryParam = new TenantDistributionQueryParam();
            tenantDistributionQueryParam.setSupTenantId(productTenantIds);
            tenantDistributionQueryParam.setDisTenantIds(Collections.singletonList(merchant.getTenantId()));
            List<TenantDistributorWalletDto> tenantDistributorWalletDtos = tenantDistributionWalletFeign.queryWallets(tenantDistributionQueryParam);
            suTenantIdWalletMap = ListUtil.toMap(TenantDistributorWalletDto::getSupTenantId, tenantDistributorWalletDtos);
        }

        Map<Long,Double> tenantIdTotalTenantFreeGoldMap = Maps.newHashMap();
        List<OrderPaymentAmountDto> orderPaymentAmountDtos = getOrderPaymentAmountDtos(orders, merchantBalance,
            orderItemPriceRespDtoMap, tenantOpenOnlinePay, orderAmountRespDTOMap, suTenantIdWalletMap, tenantIdTotalTenantFreeGoldMap);
        List<TenantPayDto> tenantPayDtos = getTenantPayDtos(orderPaymentAmountDtos);
        formatTenantBalance(suTenantIdWalletMap, tenantPayDtos);
        MerchantPayDto merchantPayDto = getMerchantPayDto(orderPaymentAmountDtos);

        PayDto payDto = new PayDto();
        payDto.setMerchantPayDto(merchantPayDto);
        payDto.setTenantPayDtos(tenantPayDtos);
        log.info("orderPayDto={}", JSON.toJSONString(payDto));

        //旧订单金额，用于比较订单是否变动
        Double oldTotalAmount = 0D;
        for (Order order : orders) {
            oldTotalAmount = DoubleUtils.add(oldTotalAmount, order.getTotalAmount());
        }

        MerchantBalanceDto merchantBalanceDto = new MerchantBalanceDto();
        merchantBalanceDto.setOrderAmountRespDTOMap(orderAmountRespDTOMap);
        merchantBalanceDto.setOnlyOffLinePay(onlyOffLinePay);
        merchantBalanceDto.setBalance(merchantBalance.getBalance().doubleValue());
        merchantBalanceDto.setFreeGold(merchantBalance.getFreeGold().doubleValue());

        //保存扣减额度，扣减额度
        Map<Long, LogisticsAndLogisticsSourceResp> logisticsIdAndLogisticsSourceMap = getLogisticsIdAndLogisticsSourceMap(merchant.getTenantId(), orders);
        List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos = getTenantLogisticsOrderRespDtos(orders, orderAmountRespDTOMap, logisticsIdAndLogisticsSourceMap, merchant);
        merchantBalanceDto.setTenantLogisticsOrderReqDtos(tenantLogisticsOrderReqDtos);
        BigDecimal deductTotalValue = tenantLogisticsOrderReqDtos.stream().map(o -> o.getDeductTotalValue()).reduce(BigDecimal.ZERO, BigDecimal::add);
        merchantBalanceDto.setTenantOrderNum(deductTotalValue);

        merchantBalanceDto.setMerchantUseBalance(merchantPayDto.getUseBalance().doubleValue());
        merchantBalanceDto.setUseFreeGold(merchantPayDto.getUseFreeGold().doubleValue());
        merchantBalanceDto.setPaymentAmount(merchantPayDto.getTotalAmount().doubleValue());
        merchantBalanceDto.setNeedBalance(merchantPayDto.getUseBalance().doubleValue());
        merchantBalanceDto.setUseBalance(merchantPayDto.getUseBalance().doubleValue());

        merchantBalanceDto.setOrders(orders);
        merchantBalanceDto.setTenantOpenOnlinePay(tenantOpenOnlinePay);
        merchantBalanceDto.setPayDto(payDto);
        merchantBalanceDto.setAmountChange(!MathUtils.priceEqual(oldTotalAmount, merchantPayDto.getTotalAmount()));

        formatOrderItemAmounts(orderAmountRespDTOMap, merchantPayDto, merchantBalanceDto);
        this.formatBalanceStatus(merchantBalanceDto);
        checkBalance("", merchantBalanceDto);
        return merchantBalanceDto;
    }

    private static void formatOrderItemAmounts(Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap,
                                               MerchantPayDto merchantPayDto, MerchantBalanceDto merchantBalanceDto) {
        List<OrderItemFreeGoldDTO> orderItemFreeGoldDTOS = Lists.newArrayList();
        List<OrderFreeGoldDTO> orderFreeGoldDTOS = Lists.newArrayList();
        for (OrderPaymentAmountDto orderPaymentAmountDto : merchantPayDto.getOrderPaymentAmountDtos()) {
            OrderAmountRespDTO orderAmountRespDTO = orderAmountRespDTOMap.get(orderPaymentAmountDto.getOrderId());
            Boolean isDistribution = orderAmountRespDTO.getIsDistribution();
            String payType = isDistribution ? EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.getValue() : EnumOrderPayType.MERCHANT_OWNTENANT.getValue();
            OrderFreeGoldDTO orderFreeGoldDTO = new OrderFreeGoldDTO();
            orderFreeGoldDTO.setOrderId(orderPaymentAmountDto.getOrderId());
            orderFreeGoldDTO.setPaymentType(payType);
            orderFreeGoldDTO.setOrderAmount(orderPaymentAmountDto.getTotalAmount().doubleValue());
            orderFreeGoldDTO.setOrderBalance(orderPaymentAmountDto.getUseBalance().doubleValue());
            orderFreeGoldDTO.setOrderFreeGold(orderPaymentAmountDto.getUseFreeGold().doubleValue());
            orderFreeGoldDTO.setOrderTenantAmount(orderPaymentAmountDto.getTenantTotalAmount().doubleValue());
            orderFreeGoldDTO.setOrderTenantBalance(orderPaymentAmountDto.getTenantUseBalance().doubleValue());
            orderFreeGoldDTO.setOrerTenantFreeGold(orderPaymentAmountDto.getTenantUseFreeGold().doubleValue());
            orderFreeGoldDTOS.add(orderFreeGoldDTO);

            for (OrderItemPaymentAmountDto itemPayDto : orderPaymentAmountDto.getItemPayDtos()) {
                OrderItemFreeGoldDTO orderItemFreeGoldDTO=new OrderItemFreeGoldDTO();
                orderItemFreeGoldDTO.setOrderItemId(itemPayDto.getOrderItemId());
                orderItemFreeGoldDTO.setPaymentType(payType);
                orderItemFreeGoldDTO.setItemAmount(itemPayDto.getTotalAmount().doubleValue());
                orderItemFreeGoldDTO.setItemBalance(itemPayDto.getUseBalance().doubleValue());
                orderItemFreeGoldDTO.setItemFreeGold(itemPayDto.getUseFreeGold().doubleValue());
                orderItemFreeGoldDTO.setItemTenantBalance(itemPayDto.getTenantUseBalance().doubleValue());
                orderItemFreeGoldDTO.setItemTenantFreeGold(itemPayDto.getTenantUseFreeGold().doubleValue());
                orderItemFreeGoldDTO.setItemTenantAmount(itemPayDto.getTenantTotalAmount().doubleValue());
                orderItemFreeGoldDTOS.add(orderItemFreeGoldDTO);
            }
        }
        merchantBalanceDto.setOrderItemFreeGoldDTOS(orderItemFreeGoldDTOS);
        merchantBalanceDto.setOrderFreeGoldDTOS(orderFreeGoldDTOS);
    }

    private List<Order> getOrders(GetBalanceBo bo) {
        List<Order> orders = orderService.findByIds(bo.getOrderIds());
        Assert.validateBool(bo.getOrderIds().size() == orders.size(), "数据异常");
        orderService.formatOrderItems(orders);
        orderService.formatAddress(orders);
        for (Order order : orders) {
            orderService.removeCancelItems(order);
        }
        if (bo.getRegenMoney()) {
            orders = orderAmountRegenService.reGenAmountByOrders(orders);
        }
        return orders;
    }

    @NotNull
    private static List<OrderPaymentAmountDto> getOrderPaymentAmountDtos(List<Order> orders,
                                                                         UserAccountBalanceResp merchantBalance,
                                                                         Map<Long, OrderItemPriceRespDto> orderItemPriceRespDtoMap,
                                                                         boolean tenantOpenOnlinePay,
                                                                         Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap,
                                                                         Map<Long, TenantDistributorWalletDto> suTenantIdWalletMap,
                                                                         Map<Long,Double> tenantIdTotalTenantFreeGoldMap) {
        double totalFreeGold = 0D;
        List<OrderPaymentAmountDto> orderPaymentAmountDtos=Lists.newArrayList();
        for (Order order : orders) {
            OrderAmountRespDTO orderAmountRespDTO = orderAmountRespDTOMap.get(order.getId());
            Assert.validateNull(orderAmountRespDTO, "订单金额记录不存在");
            Double totalAmount = order.getTotalAmount();

            Double totalTenantUseFreeGold = tenantIdTotalTenantFreeGoldMap.getOrDefault(orderAmountRespDTO.getProductTenantId(),0D);
            Double tenantTotalAmount = getTenantTotalAmount(tenantOpenOnlinePay, order, orderAmountRespDTO);
            OrderFreeGoldDto orderFreeGoldDto = getOrderUseFreeGold(
                merchantBalance,totalFreeGold,totalAmount,
                orderItemPriceRespDtoMap, suTenantIdWalletMap,
                totalTenantUseFreeGold, order, orderAmountRespDTO,
                tenantTotalAmount);
            tenantIdTotalTenantFreeGoldMap.put(orderAmountRespDTO.getProductTenantId(), orderFreeGoldDto.getTenantTotalUseFreeGold());
            Double tenantUseFreeGold = orderFreeGoldDto.getTenantOrderUsedFreeGold();
            double tenantUseBalance = DoubleUtils.sub(tenantTotalAmount, tenantUseFreeGold);

            totalFreeGold = orderFreeGoldDto.getTotalUseFreeGold();
            Double usedFreeGold = orderFreeGoldDto.getOrderUsedFreeGold();
            double useBalance = DoubleUtils.sub(totalAmount, usedFreeGold);

            OrderPaymentAmountDto orderPaymentAmountDto = new OrderPaymentAmountDto();
            orderPaymentAmountDto.setOrderId(order.getId());
            orderPaymentAmountDto.setOrderNo(order.getNo());
            orderPaymentAmountDto.setProductTenantId(orderAmountRespDTO.getProductTenantId());
            orderPaymentAmountDto.setTotalAmount(BigDecimal.valueOf(totalAmount));
            orderPaymentAmountDto.setUseBalance(BigDecimal.valueOf(useBalance));
            orderPaymentAmountDto.setUseFreeGold(BigDecimal.valueOf(usedFreeGold));
            orderPaymentAmountDto.setTenantTotalAmount(BigDecimal.valueOf(tenantTotalAmount));
            orderPaymentAmountDto.setTenantUseBalance(BigDecimal.valueOf(tenantUseBalance));
            orderPaymentAmountDto.setTenantUseFreeGold(BigDecimal.valueOf(tenantUseFreeGold));
            orderPaymentAmountDto.setItemPayDtos(orderFreeGoldDto.getItemPayDtos());
            orderPaymentAmountDtos.add(orderPaymentAmountDto);
        }
        return orderPaymentAmountDtos;
    }

    private static OrderFreeGoldDto getOrderUseFreeGold(UserAccountBalanceResp merchantBalance,
                                                        Double totalUseFreeGold, Double totalAmount,
                                                        Map<Long, OrderItemPriceRespDto> orderItemPriceRespDtoMap,
                                                        Map<Long, TenantDistributorWalletDto> suTenantIdWalletMap,
                                                        Double tenantTotalUseFreeGold, Order order,
                                                        OrderAmountRespDTO orderAmountRespDTO,
                                                        Double tenantTotalAmount) {
        boolean tenantPay = tenantTotalAmount > 0D;
        boolean merchantPay = totalAmount > 0D;
        double merchantFreeGold = merchantBalance.getFreeGold().doubleValue();
        TenantDistributorWalletDto tenantDistributorWalletDto = suTenantIdWalletMap.get(orderAmountRespDTO.getProductTenantId());
        Assert.validateTrue(tenantPay&&null==tenantDistributorWalletDto, "租户授信钱包不存在");
        double tenantOrderUsedFreeGold = 0D;
        double orderUsedFreeGold = 0D;
        List<OrderItemPaymentAmountDto> itemPayDtos=Lists.newArrayList();
        for (OrderItem orderItem : order.getItems()) {
            double itemAmount = 0D;
            double itemMaxCanUsedFreeGold=0D;
            double tenantItemAmount=0D;
            double tenantItemMaxCanUsedFreeGold=0D;
            if (!OrderStatus.isCancel(orderItem.getStatus()) && orderItem.getProduct().onSaleValidStatus() == ProductRecommendService.NO_STATUS) {
                if(tenantPay){
                    OrderItemPriceRespDto orderItemPriceRespDto = orderItemPriceRespDtoMap.get(orderItem.getId());
                    tenantItemAmount = DoubleUtils.mul(orderItemPriceRespDto.getTenantPrice(), orderItem.getNum());
                    tenantItemMaxCanUsedFreeGold = DoubleUtils.mul(tenantItemAmount, OrderService.getFreeGoldRatio());
                    // 子单要使用的赠送金
                    if (tenantItemMaxCanUsedFreeGold > tenantItemAmount) {
                        tenantItemMaxCanUsedFreeGold = tenantItemAmount;
                    }
                    // 子单要使用的赠送金不能超过剩余赠送金
                    double leftTenantFreeGold = DoubleUtils.sub(tenantDistributorWalletDto.getGiftMoney(), tenantTotalUseFreeGold);
                    if (leftTenantFreeGold < tenantItemMaxCanUsedFreeGold) {
                        tenantItemMaxCanUsedFreeGold = leftTenantFreeGold;
                    }
                    // 租户有付费时才使用赠送金
                    tenantOrderUsedFreeGold = DoubleUtils.add(tenantOrderUsedFreeGold, tenantItemMaxCanUsedFreeGold);
                    tenantTotalUseFreeGold = DoubleUtils.add(tenantItemMaxCanUsedFreeGold, tenantTotalUseFreeGold);
                }
                // 以下商户
                if(merchantPay){
                    itemAmount = orderItem.getAmount();
                    // 子单要使用的赠送金
                    itemMaxCanUsedFreeGold = DoubleUtils.mul(itemAmount, OrderService.getFreeGoldRatio());
                    if (itemMaxCanUsedFreeGold > itemAmount) {
                        itemMaxCanUsedFreeGold = itemAmount;
                    }
                    // 子单要使用的赠送金不能超过剩余赠送金
                    double leftFreeGold = DoubleUtils.sub(merchantFreeGold, totalUseFreeGold);
                    if (leftFreeGold < itemMaxCanUsedFreeGold) {
                        itemMaxCanUsedFreeGold = leftFreeGold;
                    }
                    // 商户有真实付费时才使用赠送金
                    orderUsedFreeGold = DoubleUtils.add(orderUsedFreeGold, itemMaxCanUsedFreeGold);
                    totalUseFreeGold = DoubleUtils.add(itemMaxCanUsedFreeGold, totalUseFreeGold);
                }
            }
            if (merchantPay || tenantPay) {
                OrderItemPaymentAmountDto itemPayDto = new OrderItemPaymentAmountDto();
                itemPayDto.setOrderItemId(orderItem.getId());
                itemPayDto.setUseFreeGold(BigDecimal.valueOf(itemMaxCanUsedFreeGold));
                itemPayDto.setTotalAmount(BigDecimal.valueOf(itemAmount));
                itemPayDto.setUseBalance(BigDecimal.valueOf(DoubleUtils.sub(itemAmount, itemMaxCanUsedFreeGold)));
                itemPayDto.setTenantUseFreeGold(BigDecimal.valueOf(tenantItemMaxCanUsedFreeGold));
                itemPayDto.setTenantTotalAmount(BigDecimal.valueOf(tenantItemAmount));
                itemPayDto.setUseBalance(BigDecimal.valueOf(DoubleUtils.sub(tenantItemAmount, tenantItemMaxCanUsedFreeGold)));
                itemPayDtos.add(itemPayDto);
            }
        }

        OrderFreeGoldDto orderFreeGoldDto = new OrderFreeGoldDto();
        orderFreeGoldDto.setTenantTotalUseFreeGold(tenantTotalUseFreeGold);
        orderFreeGoldDto.setTenantOrderUsedFreeGold(tenantOrderUsedFreeGold);
        orderFreeGoldDto.setTotalUseFreeGold(totalUseFreeGold);
        orderFreeGoldDto.setOrderUsedFreeGold(orderUsedFreeGold);
        orderFreeGoldDto.setItemPayDtos(itemPayDtos);
        return orderFreeGoldDto;
    }

    private static OrderFreeGoldDto getOrderUseFreeGold1(UserAccountBalanceResp merchantBalance, Double totalFreeGold,
                                                         Order order,
                                                         Double totalAmount,
                                                         Map<Long,OrderItemPaymentAmountDto> itemPayDtoMap) {
        if (totalAmount <= 0D) {
            OrderFreeGoldDto orderFreeGoldDto = new OrderFreeGoldDto();
            orderFreeGoldDto.setTotalUseFreeGold(totalFreeGold);
            orderFreeGoldDto.setOrderUsedFreeGold(0D);
            return orderFreeGoldDto;
        }
        double merchantFreeGold = merchantBalance.getFreeGold().doubleValue();
        Double orderUsedFreeGold = 0D;
        for (OrderItem orderItem : order.getItems()) {
            double itemAmount = orderItem.getAmount();
            double itemMaxCanUsedFreeGold=0D;
            if (!OrderStatus.isCancel(orderItem.getStatus()) && orderItem.getProduct().onSaleValidStatus() == ProductRecommendService.NO_STATUS) {
                // 子单要使用的赠送金
                itemMaxCanUsedFreeGold = DoubleUtils.mul(itemAmount, OrderService.getFreeGoldRatio());
                if (itemMaxCanUsedFreeGold > itemAmount) {
                    itemMaxCanUsedFreeGold = itemAmount;
                }
                // 子单要使用的赠送金不能超过剩余赠送金
                double leftFreeGold = DoubleUtils.sub(merchantFreeGold, totalFreeGold);
                if (leftFreeGold < itemMaxCanUsedFreeGold) {
                    itemMaxCanUsedFreeGold = leftFreeGold;
                }
                // 商户有真实付费时才使用赠送金
                orderUsedFreeGold = DoubleUtils.add(orderUsedFreeGold, itemMaxCanUsedFreeGold);
                totalFreeGold = DoubleUtils.add(itemMaxCanUsedFreeGold, totalFreeGold);

            }
            OrderItemPaymentAmountDto itemPayDto = itemPayDtoMap.get(orderItem.getId());
            if(null==itemPayDto){
                itemPayDto=new OrderItemPaymentAmountDto();
                itemPayDto.setOrderItemId(orderItem.getId());
                itemPayDtoMap.put(orderItem.getId(),itemPayDto);
            }
            itemPayDto.setUseFreeGold(BigDecimal.valueOf(itemMaxCanUsedFreeGold));
            itemPayDto.setTotalAmount(BigDecimal.valueOf(itemAmount));
            itemPayDto.setUseBalance(BigDecimal.valueOf(DoubleUtils.sub(itemAmount,itemMaxCanUsedFreeGold)));
        }
        OrderFreeGoldDto orderFreeGoldDto = new OrderFreeGoldDto();
        orderFreeGoldDto.setTotalUseFreeGold(totalFreeGold);
        orderFreeGoldDto.setOrderUsedFreeGold(orderUsedFreeGold);
        return orderFreeGoldDto;
    }

    private static Double getTenantTotalAmount(boolean tenantOpenOnlinePay, Order order, OrderAmountRespDTO orderAmountRespDTO) {
        Double tenantTotalAmount=0D;
        if (orderAmountRespDTO.getIsDistribution()) {
            //购买其他租户的产品
            if(tenantOpenOnlinePay){
                tenantTotalAmount = getTenantToTenantTotalAmount(order, orderAmountRespDTO);
            }else{
                Assert.wrong("开启线上付款才能分销产品");
            }
        }
        return tenantTotalAmount;
    }

    @NotNull
    private static Double getTotalAmount(boolean tenantOpenOnlinePay, Order order, OrderAmountRespDTO orderAmountRespDTO) {
        Double totalAmount=0D;
        if (orderAmountRespDTO.getIsDistribution()) {
            //购买其他租户的产品
            totalAmount=order.getTotalAmount();
        } else {
            //购买自己租户的产品
            if (tenantOpenOnlinePay) {
                //开启线上支付 则商户付钱
                totalAmount=order.getTotalAmount();
            } else {
                //未开启线上付款，线下支付，商户不必付钱直接过
            }
        }
        return totalAmount;
    }

    private static Double getTenantToTenantTotalAmount(Order order, OrderAmountRespDTO orderAmountRespDTO) {
        boolean isFba = OrderOriginType.isFba(order.getOriginType());
        boolean notHaveLogistics = !NumberUtils.greaterZero(order.getLogisticsId());
        boolean isDistribution = !orderAmountRespDTO.getProductTenantId().equals(orderAmountRespDTO.getTenantId());
        boolean isOwnTenantLogistics=false;
        if (isFba && notHaveLogistics) {
            //租户fba，无地址，无物流情况下，根据产品来判断，自发产品-租户物流；分销产品由于只能用sds物流，所以分销产品-sds物流
            isOwnTenantLogistics = !isDistribution;
        }

        log.info("getTenantToSaasTotalAmount,isOwnTenantLogistics={}", isOwnTenantLogistics);
        Double tenantToTenantTotalAmount = orderAmountRespDTO.getTenantTotalAmount();
        if (isOwnTenantLogistics) {
            tenantToTenantTotalAmount = DoubleUtils.add(orderAmountRespDTO.getTenantProductAmount(), order.getMaterialServiceAmount());
        }
        return tenantToTenantTotalAmount;
    }

    public MerchantBalanceDto getBalanceByAmount(Long orderId, Long userId, Long merchantId, Double amount) {
        MerchantRespDto merchant = merchantFeign.getMerchantById(merchantId);
        Boolean boolCheckIsFreePay = freePayMerchantFeign.checkIsFreePay(merchant.getTenantId(), merchant.getId(), userId);
        Order order = orderService.checkById(orderId);
        Assert.validateNull(order, "数据异常");
        Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap = orderFeign.amountMap(new IdsSearchHelper(Lists.newArrayList(orderId), null));
        OrderAmountRespDTO orderAmountRespDTO = orderAmountRespDTOMap.get(orderId);
        Assert.validateNull(orderAmountRespDTO, "订单金额信息异常请联系开发");
        UserAccountBalanceResp merchantBalance = merchantUserAccountFeign.getBalance(merchantId, userId, MERCHANT_USER_BALANCE_TYPE);
        Assert.validateNull(merchantBalance, "账号信息异常！！");
        orderService.formatOrderItems(Lists.newArrayList(order));
        orderService.formatAddress(Lists.newArrayList(order));
        orderService.removeCancelItems(order);
        //是否开启线上支付
        boolean tenantOpenOnlinePay = merchant.getTenantOpenOnlinePay().equals(BasePoConstant.YES);
        boolean onlyOffLinePay = this.checkPaymentType(tenantOpenOnlinePay && !boolCheckIsFreePay, orderAmountRespDTOMap);
        PayDto payDto = getPayTotalAmountDtoNew(order,BigDecimal.valueOf(amount), tenantOpenOnlinePay, orderAmountRespDTO);
        MerchantPayDto merchantPayDto = payDto.getMerchantPayDto();

        MerchantBalanceDto merchantBalanceDto = new MerchantBalanceDto();
        merchantBalanceDto.setOrderAmountRespDTOMap(orderAmountRespDTOMap);
        merchantBalanceDto.setOnlyOffLinePay(onlyOffLinePay);
        merchantBalanceDto.setBalance(merchantBalance.getBalance().doubleValue());
        merchantBalanceDto.setFreeGold(merchantBalance.getFreeGold().doubleValue());
        merchantBalanceDto.setNeedBalance(merchantPayDto.getUseBalance().doubleValue());
        merchantBalanceDto.setUseBalance(merchantPayDto.getUseBalance().doubleValue());
        merchantBalanceDto.setMerchantUseBalance(merchantPayDto.getUseBalance().doubleValue());
        merchantBalanceDto.setUseFreeGold(merchantPayDto.getUseFreeGold().doubleValue());
        merchantBalanceDto.setPaymentAmount(merchantPayDto.getTotalAmount().doubleValue());
        merchantBalanceDto.setOrders(Lists.newArrayList(order));
        merchantBalanceDto.setTenantOpenOnlinePay(tenantOpenOnlinePay);
        merchantBalanceDto.setPayDto(payDto);
        this.formatBalanceStatus(merchantBalanceDto);

        return merchantBalanceDto;
    }

    private PayTotalAmountDto getPayTotalAmountDto(Double amount, boolean tenantOpenOnlinePay,
                                                   boolean avoidPayment, OrderAmountRespDTO orderAmountRespDTO) {
        PayTotalAmountDto payTotalAmountDto = new PayTotalAmountDto();
        //判断中有更简单的写法，但这样可以更清晰的描述程序
        if (EnumOrderPayType.TENANT.getValue().equalsIgnoreCase(orderAmountRespDTO.getPaymentType())) {
            //购买租户的产品
            if (tenantOpenOnlinePay) {
                //开启线上支付 则商户付钱
                payTotalAmountDto.increaseMerchantPayTenant(amount);
            } else {
                //未开启线上付款或用户免支付  商户不必付钱直接过
            }
        } else {
            //购买sds的产品
            if (tenantOpenOnlinePay) {
                //开启线上支付时 租户一定要付钱给saas
                log.info("orderAmountRespDTO={}", JSON.toJSONString(orderAmountRespDTO));
                payTotalAmountDto.increaseTenantPaySaas(amount);
                if (avoidPayment) {
                    //如果开启免付则商户可以不用付钱 do nothing
                } else {
                    //未开启免费则商户必须付对应的钱 给租户
                    payTotalAmountDto.increaseMerchantPayTenant(amount);
                }
            } else {
                //未开启线上支付时 则是商户付钱 给saas
                payTotalAmountDto.increaseMerchantPaySaas(amount);
            }
        }
        log.info("payTotalAmountDto={}", JSON.toJSONString(payTotalAmountDto));
        return payTotalAmountDto;
    }

    private PayDto getPayTotalAmountDtoNew(Order order, BigDecimal amount,
                                           boolean tenantOpenOnlinePay, OrderAmountRespDTO orderAmountRespDTO) {
        Map<Long, TenantDistributorWalletDto> suTenantIdWalletMap=Maps.newHashMap();
        if (tenantOpenOnlinePay) {
            TenantDistributionQueryParam tenantDistributionQueryParam = new TenantDistributionQueryParam();
            tenantDistributionQueryParam.setSupTenantId(Collections.singletonList(orderAmountRespDTO.getProductTenantId()));
            tenantDistributionQueryParam.setDisTenantIds(Collections.singletonList(orderAmountRespDTO.getTenantId()));
            List<TenantDistributorWalletDto> tenantDistributorWalletDtos = tenantDistributionWalletFeign.queryWallets(tenantDistributionQueryParam);
            suTenantIdWalletMap = ListUtil.toMap(TenantDistributorWalletDto::getSupTenantId, tenantDistributorWalletDtos);
        }

        List<OrderPaymentAmountDto> orderPaymentAmountDtos = getOrderPaymentAmountDtos(order, amount, tenantOpenOnlinePay, orderAmountRespDTO);
        List<TenantPayDto> tenantPayDtos = getTenantPayDtos(orderPaymentAmountDtos);
        formatTenantBalance(suTenantIdWalletMap, tenantPayDtos);
        MerchantPayDto merchantPayDto = getMerchantPayDto(orderPaymentAmountDtos);

        PayDto payDto = new PayDto();
        payDto.setMerchantPayDto(merchantPayDto);
        payDto.setTenantPayDtos(tenantPayDtos);

        log.info("payDto={}", JSON.toJSONString(payDto));
        return payDto;
    }

    private void formatTenantBalance(Map<Long, TenantDistributorWalletDto> suTenantIdWalletMap, List<TenantPayDto> tenantPayDtos) {
        for (TenantPayDto tenantPayDto : tenantPayDtos) {
            TenantDistributorWalletDto walletDto = suTenantIdWalletMap.get(tenantPayDto.getProductTenantId());
            tenantPayDto.setTenantBalance(null != walletDto ? walletDto.getBalance() : BigDecimal.ZERO);
            tenantPayDto.setTenantFreeGold(null != walletDto ? walletDto.getGiftMoney() : BigDecimal.ZERO);
        }
    }

    @NotNull
    private static List<OrderPaymentAmountDto> getOrderPaymentAmountDtos(Order order, BigDecimal amount, boolean tenantOpenOnlinePay, OrderAmountRespDTO orderAmountRespDTO) {
        List<OrderPaymentAmountDto> orderPaymentAmountDtos = Lists.newArrayList();
        OrderPaymentAmountDto orderPaymentAmountDto = new OrderPaymentAmountDto();
        orderPaymentAmountDto.setOrderId(order.getId());
        orderPaymentAmountDto.setOrderNo(order.getNo());
        orderPaymentAmountDto.setProductTenantId(orderAmountRespDTO.getProductTenantId());
        orderPaymentAmountDtos.add(orderPaymentAmountDto);

        if (EnumOrderPayType.MERCHANT_OWNTENANT.getValue().equalsIgnoreCase(orderAmountRespDTO.getPaymentType())) {
            // 购买自己租户的产品
            if (tenantOpenOnlinePay) {
                // 自己租户产品，开启线上支付 商户付给自己租户
                orderPaymentAmountDto.setUseBalance(amount);
                orderPaymentAmountDto.setTotalAmount(amount);
            } else {
                // 自己租户产品，未开启线上支付 线下支付
            }
        } else {
            //购买其他租户产品
            if (tenantOpenOnlinePay) {
                log.info("orderAmountRespDTO={}", JSON.toJSONString(orderAmountRespDTO));
                // 其他租户产品，开启线上支付，商户付给自己租户
                orderPaymentAmountDto.setUseBalance(amount);
                orderPaymentAmountDto.setTotalAmount(amount);
                // 其他租户产品，开启线上支付时 自己租户一定要付钱给其他租户
                orderPaymentAmountDto.setTenantUseBalance(amount);
                orderPaymentAmountDto.setTenantTotalAmount(amount);
            } else {
                Assert.wrong("未开启线上付款，不可下单分销产品");
            }
        }
        return orderPaymentAmountDtos;
    }

    @NotNull
    private static List<TenantPayDto> getTenantPayDtos(List<OrderPaymentAmountDto> orderPaymentAmountDtos) {
        List<TenantPayDto> tenantPayDtos = Lists.newArrayList();
        Map<Long, List<OrderPaymentAmountDto>> productTenantIdAmountsMap =
            ListUtil.toMapValueList(OrderPaymentAmountDto::getProductTenantId, orderPaymentAmountDtos);

        for (Map.Entry<Long, List<OrderPaymentAmountDto>> entry : productTenantIdAmountsMap.entrySet()) {
            Long productTenantId = entry.getKey();
            List<OrderPaymentAmountDto> amountDtos = entry.getValue();

            TenantPayDto tenantPayDto = new TenantPayDto();
            tenantPayDto.setProductTenantId(productTenantId);

            // 求和
            BigDecimal tenantUseBalance = amountDtos.stream()
                .map(OrderPaymentAmountDto::getTenantUseBalance)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal tenantUseFreeGold = amountDtos.stream()
                .map(OrderPaymentAmountDto::getTenantUseFreeGold)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal tenantTotalAmount = amountDtos.stream()
                .map(OrderPaymentAmountDto::getTenantTotalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            tenantPayDto.setTenantUseBalance(tenantUseBalance);
            tenantPayDto.setTenantUseFreeGold(tenantUseFreeGold);
            tenantPayDto.setTenantTotalAmount(tenantTotalAmount);

            tenantPayDto.setOrderPaymentAmountDtos(amountDtos);

            tenantPayDtos.add(tenantPayDto);
        }
        return tenantPayDtos;
    }

    @NotNull
    private static MerchantPayDto getMerchantPayDto(List<OrderPaymentAmountDto> orderPaymentAmountDtos) {
        MerchantPayDto merchantPayDto = new MerchantPayDto();
        // 求和
        BigDecimal useBalance = orderPaymentAmountDtos.stream()
            .map(OrderPaymentAmountDto::getUseBalance)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal useFreeGold = orderPaymentAmountDtos.stream()
            .map(OrderPaymentAmountDto::getUseFreeGold)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalAmount = orderPaymentAmountDtos.stream()
            .map(OrderPaymentAmountDto::getTotalAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        merchantPayDto.setUseBalance(useBalance);
        merchantPayDto.setUseFreeGold(useFreeGold);
        merchantPayDto.setTotalAmount(totalAmount);

        merchantPayDto.setOrderPaymentAmountDtos(orderPaymentAmountDtos);
        return merchantPayDto;
    }

    public Map<Long, LogisticsAndLogisticsSourceResp> getLogisticsIdAndLogisticsSourceMap(Long tenantId, List<Order> orders) {
        Set<Long> logisticsIds = orders.stream().map(o -> o.getLogisticsId()).collect(Collectors.toSet());
        BaseListReqDto baseListReqDto = new BaseListReqDto();
        baseListReqDto.setIdList(Lists.newArrayList(logisticsIds));
        Map<Long, LogisticsAndLogisticsSourceResp> logisticsIdAndLogisticsSourceMap = tenantLogisticsFeign.getLogisticsAndLogisticsSource(tenantId, baseListReqDto);
        return logisticsIdAndLogisticsSourceMap;
    }

    private Double getTenantToSaasTotalAmount(Map<Long, LogisticsAndLogisticsSourceResp> logisticsIdAndLogisticsSourceMap, Order order, OrderAmountRespDTO orderAmountRespDTO) {
        boolean isFba = OrderOriginType.isFba(order.getOriginType());
        boolean notHaveLogistics = !NumberUtils.greaterZero(order.getLogisticsId());
        boolean isDistribution = !orderAmountRespDTO.getProductTenantId().equals(orderAmountRespDTO.getTenantId());
        boolean isTenantLogistics;
        if (isFba && notHaveLogistics) {
            //租户fba，无地址，无物流情况下，根据产品来判断，自发产品-租户物流；分销产品由于只能用sds物流，所以分销产品-sds物流
            isTenantLogistics = !isDistribution;
        } else {
            isTenantLogistics = isTenantLogistics(order.getLogisticsId(), logisticsIdAndLogisticsSourceMap);
        }

        log.info("getTenantToSaasTotalAmount,isTenantLogistics={}", isTenantLogistics);
        Double tenantToSaasTotalAmount = orderAmountRespDTO.getTenantTotalAmount();
        if (isTenantLogistics) {
            tenantToSaasTotalAmount = DoubleUtils.add(orderAmountRespDTO.getTenantProductAmount(), order.getMaterialServiceAmount());
        }
        return tenantToSaasTotalAmount;
    }

    private static String getSelfQuotaType(Boolean isFba) {
        return isFba ? ApplicationCodeEnum.SELF_SEND_FBA_ORDER.getCode() : ApplicationCodeEnum.SELF_ORDER.getCode();
    }

    @NotNull
    private List<TenantLogisticsOrderReqDto> getTenantLogisticsOrderRespDtos(List<Order> orders, Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap,
                                                                             Map<Long, LogisticsAndLogisticsSourceResp> logisticsIdAndLogisticsSourceMap,
                                                                             MerchantRespDto merchant) {
        if (CollUtil.isEmpty(orders)) {
            return Lists.newArrayList();
        }
        // sds产品，分销订单，订单额度应用取sds的应用额度，其他租户，取分销其他租户应用
        TenantApplicationReqDto tenantApplicationReqDto = new TenantApplicationReqDto();
        tenantApplicationReqDto.setTenantId(merchant.getTenantId());
        tenantApplicationReqDto.setApplicationCodeList(ApplicationCodeEnum.orderCountApplicationCodeList);
        List<TenantApplicationScenarioRelRespDto> tenantApplicationScenarioRelRespDtos = applicationFeign.getByCodeList(merchant.getTenantId(),tenantApplicationReqDto);
        Map<String, BigDecimal> appCodeValueMap = tenantApplicationScenarioRelRespDtos.stream()
            .filter(t -> null != t.getValue()).collect(Collectors.toMap(t -> t.getApplicationCode(), t -> t.getValue(), (a, b) -> b));

        List<TenantLogisticsOrderReqDto> tenantLogisticsOrderDtos = Lists.newArrayList();
        boolean tenantOpenOnlinePay = merchant.getTenantOpenOnlinePay().equals(BasePoConstant.YES);
        BigDecimal oneBigDecimal = new BigDecimal("1");
        for (Order order : orders) {
            OrderAmountRespDTO orderAmountRespDTO = orderAmountRespDTOMap.get(order.getId());
            Boolean isFba = OrderOriginType.isFba(order.getOriginType());
            TenantLogisticsOrderReqDto tenantLogisticsOrderDto = null;
            boolean isSdsProduct = orderAmountRespDTO.getIsSdsProduct();
            Boolean isDistribution = orderAmountRespDTO.getIsDistribution();
            //自发订单
            if (!isDistribution) {
                tenantLogisticsOrderDto = new TenantLogisticsOrderReqDto();
                tenantLogisticsOrderDto.setId(order.getId());
                tenantLogisticsOrderDto.setQuotaType(getSelfQuotaType(isFba));
                BigDecimal quotaValue = appCodeValueMap.get(tenantLogisticsOrderDto.getQuotaType());
                if (null == quotaValue) {
                    throw new BusinessException("自发订单额度值异常");
                }
                tenantLogisticsOrderDto.setQuotaValue(quotaValue);
                tenantLogisticsOrderDto.setNum(oneBigDecimal);
                tenantLogisticsOrderDto.setTotalValue(NumberUtil.roundDown(NumberUtil.mul(tenantLogisticsOrderDto.getQuotaValue(), tenantLogisticsOrderDto.getNum()), 2));
                log.info("self order ,tenantLogisticsOrderDto={}", JSON.toJSONString(tenantLogisticsOrderDto));
            } else if (tenantOpenOnlinePay) {
                if (!NumberUtils.greaterZero(order.getLogisticsId())) {
                    continue;
                }
                //扣减的订单额度类型
                LogisticsAndLogisticsSourceResp logisticsSourceResp = logisticsIdAndLogisticsSourceMap.get(order.getLogisticsId());
                String orderQuotaType = isSdsProduct ? getSdsProductQuotaType(logisticsSourceResp) : ApplicationCodeEnum.DISTRIBUTE_OTHER_TENANT_ORDER.getCode();
                if (StrUtil.isNotEmpty(orderQuotaType)) {
                    tenantLogisticsOrderDto = new TenantLogisticsOrderReqDto();
                    tenantLogisticsOrderDto.setId(order.getId());
                    tenantLogisticsOrderDto.setQuotaType(orderQuotaType);
                    BigDecimal quotaValue = appCodeValueMap.get(tenantLogisticsOrderDto.getQuotaType());
                    if (null == quotaValue) {
                        throw new BusinessException("订单额度值异常");
                    }
                    tenantLogisticsOrderDto.setQuotaValue(quotaValue);
                    BigDecimal quotaNum = getQuotaNum(order, orderQuotaType);
                    tenantLogisticsOrderDto.setNum(quotaNum);
                    tenantLogisticsOrderDto.setTotalValue(NumberUtil.roundDown(NumberUtil.mul(tenantLogisticsOrderDto.getQuotaValue(), tenantLogisticsOrderDto.getNum()), 2));
                    tenantLogisticsOrderDto.setShipmentPlaceType(logisticsSourceResp.getShipmentPlaceType());
                }
            }
            if (null != tenantLogisticsOrderDto) {
                tenantLogisticsOrderDtos.add(tenantLogisticsOrderDto);
            }
        }

        //要扣减的额度
        Set<Long> ids = tenantLogisticsOrderDtos.stream().map(TenantLogisticsOrderReqDto::getId).collect(Collectors.toSet());
        formatDeductTotalValue(ids, tenantLogisticsOrderDtos);
        return tenantLogisticsOrderDtos;
    }

    @NotNull
    private static String getOrderCountApplicationKey(Long tenantId,String applicationCode) {
        return tenantId + "#" + applicationCode;
    }

    private void formatDeductTotalValue(Set<Long> orderIds, List<TenantLogisticsOrderReqDto> tenantLogisticsOrderDtos) {
        Map<Long, TenantLogisticsOrderRespDto> orderIdExistTenantLogisticsOrderDtoMap = getOrderIdExistTenantLogisticsOrderDtoMap(orderIds);
        for (TenantLogisticsOrderReqDto tenantLogisticsOrderDto : tenantLogisticsOrderDtos) {
            Long orderId = tenantLogisticsOrderDto.getId();
            TenantLogisticsOrderRespDto existDto = orderIdExistTenantLogisticsOrderDtoMap.get(orderId);
            if (null == existDto) {
                tenantLogisticsOrderDto.setDeductTotalValue(tenantLogisticsOrderDto.getTotalValue());
                continue;
            }
            BigDecimal deductTotalValue = NumberUtil.roundDown(NumberUtil.sub(tenantLogisticsOrderDto.getTotalValue(), existDto.getTotalValue()), 2);
            tenantLogisticsOrderDto.setDeductTotalValue(deductTotalValue);
        }
    }

    @NotNull
    private Map<Long, TenantLogisticsOrderRespDto> getOrderIdExistTenantLogisticsOrderDtoMap(Set<Long> orderIds) {
        Map<Long, TenantLogisticsOrderRespDto> orderIdExistTenantLogisticsOrderDtoMap = Maps.newHashMap();
        List<TenantLogisticsOrderRespDto> existTenantLogisticsOrderRespDtos = tenantLogisticsOrderFeign.getByIds(Lists.newArrayList(orderIds));
        if (CollUtil.isNotEmpty(existTenantLogisticsOrderRespDtos)) {
            orderIdExistTenantLogisticsOrderDtoMap = existTenantLogisticsOrderRespDtos.stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (a, b) -> b));
        }
        return orderIdExistTenantLogisticsOrderDtoMap;
    }

    private BigDecimal getQuotaNum(Order order, String orderQuotaType) {
        BigDecimal quotaNum = BigDecimal.ZERO;
        if (orderQuotaType.equalsIgnoreCase(ApplicationCodeEnum.SDS_ORDER_PREPAID_OR_ONLINE_LOGISTICS_OR_SELF_PACKING.name())) {
            quotaNum = BigDecimal.valueOf(order.getServiceAmount());
        } else if (orderQuotaType.equalsIgnoreCase(ApplicationCodeEnum.SDS_ORDER_TENANT_ACCOUNT_TENANT_SHIPMENT.name()) ||
                orderQuotaType.equalsIgnoreCase(ApplicationCodeEnum.SDS_ORDER_TENANT_ACCOUNT_SDS_SHIPMENT.name())) {
            BigDecimal orderItemNum = order.getItems().stream().map(oi -> BigDecimal.valueOf(oi.getNum())).reduce(BigDecimal.ZERO, BigDecimal::add);
            quotaNum = orderItemNum;
        }else if (orderQuotaType.equalsIgnoreCase(ApplicationCodeEnum.DISTRIBUTE_OTHER_TENANT_ORDER.name())) {
            quotaNum = BigDecimal.valueOf(1L);
        }
        return quotaNum;
    }

    /**
     * 获取订单额度类型
     */
    public String getSdsProductQuotaType(LogisticsAndLogisticsSourceResp logisticsSourceResp) {
        if (null == logisticsSourceResp) {
            throw new BusinessException("物流不存在");
        }
        //sds物流TenantLogisticsOrderConstant.QUOTA_TYPE_NONE
        if (logisticsSourceResp.getLogisticsSource().equalsIgnoreCase(DistributionProductLogisticsSourceEnum.PRODUCT_TENANT.name())) {
            return "";
        }

        //自提、寄付、线上物流
        if (LogisticsCodeIdEnum.isZtOrConsignment(logisticsSourceResp.getCodeId()) || LogisticsConstant.isOnlineLogistics(logisticsSourceResp.getServiceProviderId())) {
            return ApplicationCodeEnum.SDS_ORDER_PREPAID_OR_ONLINE_LOGISTICS_OR_SELF_PACKING.getCode();
        }

        //租户物流
        if (logisticsSourceResp.getShipmentPlaceType().equalsIgnoreCase(TenantLogisticsConstant.ShipmentPlaceTypeEnum.TENANT.name())) {
            return ApplicationCodeEnum.SDS_ORDER_TENANT_ACCOUNT_TENANT_SHIPMENT.getCode();
        }
        if (logisticsSourceResp.getShipmentPlaceType().equalsIgnoreCase(TenantLogisticsConstant.ShipmentPlaceTypeEnum.ISSUING_BAY.name())) {
            return ApplicationCodeEnum.SDS_ORDER_TENANT_ACCOUNT_SDS_SHIPMENT.getCode();
        }
        return "";
    }


    public boolean isTenantLogistics(Long logisticsId, Map<Long, LogisticsAndLogisticsSourceResp> logisticsIdAndLogisticsSourceMap) {
        LogisticsAndLogisticsSourceResp logisticsSourceResp = logisticsIdAndLogisticsSourceMap.get(logisticsId);
        if (null == logisticsSourceResp) {
            throw new BusinessException("物流不存在");
        }
        return logisticsSourceResp.getLogisticsSource().equals(DistributionProductLogisticsSourceEnum.ORDER_TENANT.name());
    }

    @Data
    class PayTotalAmountDto {
        //商户付给saas的钱
        double merchantPaySaasTotalAmount;
        //商户付给租户的钱
        double merchantPayTenantTotalAmount;
        //商户总共要付的钱
        double merchantTotalAmount;
        //租户要付给saas的总金额
        double tenantPaySaasTotalAmount;

        public void increaseMerchantPaySaas(Double amount) {
            merchantPaySaasTotalAmount = DoubleUtils.add(merchantPaySaasTotalAmount, amount);
            merchantTotalAmount = DoubleUtils.add(merchantTotalAmount, amount);
        }

        public void increaseMerchantPayTenant(Double amount) {
            merchantPayTenantTotalAmount = DoubleUtils.add(merchantPayTenantTotalAmount, amount);
            merchantTotalAmount = DoubleUtils.add(merchantTotalAmount, amount);
        }

        public void increaseTenantPaySaas(Double amount) {
            tenantPaySaasTotalAmount = DoubleUtils.add(tenantPaySaasTotalAmount, amount);
        }

    }

    // public void checkBalance(MerchantBalanceDto merchantBalanceDto) {
    //     merchantBalanceDto.setStatus(MerchantService.IS_YES_USE_BALANCE);
    //     merchantBalanceDto.setTenantBalanceStatus(MerchantService.IS_YES_USE_BALANCE);
    //     merchantBalanceDto.setMerchantBalanceStatus(MerchantService.IS_YES_USE_BALANCE);
    //     if (merchantBalanceDto.getBalance() < merchantBalanceDto.getMerchantUseBalance()) {
    //         // 商户的余额不足
    //         merchantBalanceDto.setStatus(MerchantService.IS_NO_USE_BALANCE);
    //         merchantBalanceDto.setMerchantBalanceStatus(MerchantService.IS_NO_USE_BALANCE);
    //     }
    //     if (merchantBalanceDto.getTenantBalance() < merchantBalanceDto.getTenantUseBalance()) {
    //         // 租户余额不足
    //         merchantBalanceDto.setStatus(MerchantService.IS_NO_USE_BALANCE);
    //         merchantBalanceDto.setTenantBalanceStatus(MerchantService.IS_NO_USE_BALANCE);
    //
    //     }
    //
    // }
    public void formatBalanceStatus(MerchantBalanceDto merchantBalanceDto) {
        merchantBalanceDto.setStatus(MerchantService.IS_YES_USE_BALANCE);
        merchantBalanceDto.setMerchantBalanceStatus(MerchantService.IS_YES_USE_BALANCE);
        if (merchantBalanceDto.getBalance() < merchantBalanceDto.getMerchantUseBalance()) {
            // 商户的余额不足
            merchantBalanceDto.setStatus(MerchantService.IS_NO_USE_BALANCE);
            merchantBalanceDto.setMerchantBalanceStatus(MerchantService.IS_NO_USE_BALANCE);
        }
        List<TenantPayDto> tenantPayDtos = merchantBalanceDto.getPayDto().getTenantPayDtos();
        for (TenantPayDto tenantPayDto : tenantPayDtos) {
            if (NumberUtil.isLess(tenantPayDto.getTenantBalance(),tenantPayDto.getTenantUseBalance())) {
                // 租户余额不足
                merchantBalanceDto.setStatus(MerchantService.IS_NO_USE_BALANCE);
                tenantPayDto.setTenantBalanceStatus(MerchantService.IS_NO_USE_BALANCE);
            }else{
                tenantPayDto.setTenantBalanceStatus(MerchantService.IS_YES_USE_BALANCE);
            }
        }

    }


    public List<Order> paySuccess(Long userId, List<Order> orders) {
        log.info("paysucess-zmy,userId{},orders:{}", userId, JSON.toJSONString(orders));
        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        double totalAmount = 0d;
        List<OrderItem> orderItems = Lists.newArrayList();
        for (Order order : orders) {
            if (order.getStatus() != OrderStatus.NONE.getStatus() && order.getStatus() != OrderStatus.UNPAIN.getStatus()) {
                // 支付宝特殊处理 todo
                Assert.wrong("支付失败，部分订单已支付或取消");

            }
            orderItems.addAll(order.getItems());
            totalAmount = DoubleUtils.add(order.getTotalAmount(), totalAmount);
        }

        Map<Long, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        orderDao.paySuccessByIds(IdGenerator.nextStringId(), System.currentTimeMillis(), orderIds, OrderStatus.PAIN.getStatus());

        orderItemService.updatePaymentStatusByOrderIds(orderIds);
//        orderDao.updateCarriageStatusOfPrivateOrderAndZTByIds(orderIds, LogisticsNumber.ZT.getNumber());

        List<OrderItemRespDto> orderItemRespDtos = Lists.newArrayList();
        for (OrderItem orderItem : orderItems) {
            Order order = orderMap.get(orderItem.getOrderId());
            if (order != null && order.getDesignStatus().equals(OrderDesignStatus.FINISH.name())) {
                //取消 删除的子单就不展示了
                if (orderItem.getStatus() != OrderStatus.CANCEL.getStatus() && orderItem.getStatus() != OrderStatus.DELETE.getStatus()) {
                    OrderItemRespDto orderItemRespDto = new OrderItemRespDto();
                    orderItemRespDto.setOrderId(orderItem.getOrderId());
                    orderItemRespDto.setId(orderItem.getId());
                    orderItemRespDtos.add(orderItemRespDto);
                }
            }
        }
        if (CollUtil.isNotEmpty(orderItemRespDtos)) {
            factoryOrderCreateTaskFeign.batchSave(orderItemRespDtos);
        }
        // 支付，重算工厂，异步有时间先后问题，改为同步
        resetFbaOrderTask(orderIds);
        paymentSuccessNotify(userId, orders);

        return orders;
    }
    public void resetFbaOrderTask(List<Long> orderIds){
        if(CollUtil.isEmpty(orderIds)){
            return;
        }
        //批量获取，非空的，批量删除，批量更新状态
        List<FbaOrderTask> fbaOrderTasks = fbaOrderTaskService.findByOrderIds(orderIds);
        if(CollUtil.isEmpty(fbaOrderTasks)){
            log.info("paysucess-zmy-resetFbaOrderTask-fbaOrderTasksEmpty-orderIds={}", JSON.toJSONString(orderIds));
            return;
        }
        List<Long> notUpdateFbaOrderTaskOrderIds = fbaOrderTasks.stream()
            .filter(i -> i.getUpdateStatus().equals(EnumCommonStatus.NO.getCode()))
            .map(FbaOrderTask::getOrderId)
            .distinct()
            .collect(Collectors.toList());
        log.info("paysucess-zmy-resetFbaOrderTask-notUpdateFbaOrderTaskOrderIds:{}", JSON.toJSONString(notUpdateFbaOrderTaskOrderIds));
        fbaOrderTaskService.batchReset(notUpdateFbaOrderTaskOrderIds);
    }

    public static boolean valid(Payment payment) {
        return false;
    }

    public void paymentSuccessNotify(Long userId, List<Order> updateOrders) {
        log.info("paysucess-zmy,userId{},updateOrders:{}", userId, JSON.toJSONString(updateOrders));
        TransactionUtil.afterCommit(()->{
            log.info("paysucess-zmy-registerHook");
            if (updateOrders != null) {
                final List<Long> ids = updateOrders.stream().map(Order::getId).collect(Collectors.toList());
                for (Order updateOrder : updateOrders) {
                    Long id = updateOrder.getId();
                    log.info("paysucess-zmy-if");
                    if (updateOrder.getDesignStatus().equals(OrderDesignStatus.WAIT.name())) {
                        log.info("paysucess-zmy-WAIT");

                        //待设计
                        CustomerPayUndesignOrderMessage customerPayUndesignOrderMessage = new CustomerPayUndesignOrderMessage();
                        customerPayUndesignOrderMessage.setEid(id);
                        customerPayUndesignOrderMessage.setOrderId(id);
                        customerPayUndesignOrderMessage.setSendingTime(new Date());
                        customerPayUndesignOrderMessage.setOperatorUid(userId);
                        orderEventService.sendProcessMsg(customerPayUndesignOrderMessage, OrderProgressConstant.CUSTOMER_PAY_UNDESIGN_ORDER);
                    } else {
                        log.info("paysucess-zmy-not-WAIT");
                        CustomerPayDesignedOrderMessage customerPayDesignedOrderMessage = new CustomerPayDesignedOrderMessage();
                        customerPayDesignedOrderMessage.setEid(id);
                        customerPayDesignedOrderMessage.setOrderId(id);
                        customerPayDesignedOrderMessage.setSendingTime(new Date());
                        customerPayDesignedOrderMessage.setOperatorUid(userId);
                        orderEventService.sendProcessMsg(customerPayDesignedOrderMessage, OrderProgressConstant.CUSTOMER_PAY_DESIGNED_ORDER);
                    }
//                    mqTemplate.sendMessage(OrderMqConstant.ORDER_PAY_SUCCESS_AFTER_TOPIC, new BaseOrderMessageDTO(id).setUserId(userId));
                    rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.ORDER_PAY_SUCCESS_AFTER_TOPIC, new BaseOrderMessageDTO(id).setUserId(userId));
                }
                orderEventService.sendOrderSyncMqBatch(ids, EnumOrderRefreshType.ORDER_STATUS.getValue());
            }

        });
    }


    /**
     * 更新租户与商户的赠送金
     *
     * @param orderItemFreeGoldDTOS
     */
    public void updateBalanceFreeGold(String paymentMethod, List<OrderFreeGoldDTO> orderFreeGoldDTOS, List<OrderItemFreeGoldDTO> orderItemFreeGoldDTOS) {
        for (OrderFreeGoldDTO orderFreeGoldDTO : orderFreeGoldDTOS) {
            orderFreeGoldDTO.setPaymentMethod(paymentMethod);
        }
        OrderFreeGoldUpdateDTO orderFreeGoldUpdateDTO = new OrderFreeGoldUpdateDTO();
        orderFreeGoldUpdateDTO.setOrderFreeGoldDTOList(orderFreeGoldDTOS);
        orderFreeGoldUpdateDTO.setOrderItemFreeGoldDTOS(orderItemFreeGoldDTOS);
        log.info("orderFreeGoldUpdateDTO={}", JSON.toJSONString(orderFreeGoldUpdateDTO));
        orderAmountFeign.updateBalanceFreeGold(orderFreeGoldUpdateDTO);
    }

    /**
     * 支付宝支付时，只更新租户使用的赠送金
     *
     * @param orderItemFreeGoldDTOS
     */
    public void onlyUpdateTenantBalanceFreeGold(String paymentMethod, List<OrderFreeGoldDTO> orderFreeGoldDTOS, List<OrderItemFreeGoldDTO> orderItemFreeGoldDTOS) {
        for (OrderFreeGoldDTO orderFreeGoldDTO : orderFreeGoldDTOS) {
            orderFreeGoldDTO.setPaymentMethod(paymentMethod);
            orderFreeGoldDTO.setOrderFreeGold(0d);
            orderFreeGoldDTO.setOrderBalance(0d);
        }
        for (OrderItemFreeGoldDTO orderItemFreeGoldDTO : orderItemFreeGoldDTOS) {
            //支付宝支付不实用余额与赠送金
            orderItemFreeGoldDTO.setItemBalance(0d);
            orderItemFreeGoldDTO.setItemFreeGold(0d);
        }
        OrderFreeGoldUpdateDTO orderFreeGoldUpdateDTO = new OrderFreeGoldUpdateDTO();
        orderFreeGoldUpdateDTO.setOrderFreeGoldDTOList(orderFreeGoldDTOS);
        orderFreeGoldUpdateDTO.setOrderItemFreeGoldDTOS(orderItemFreeGoldDTOS);
        orderAmountFeign.updateBalanceFreeGold(orderFreeGoldUpdateDTO);
    }

    /**
     * 线上支付未开启或者用户开启免支付 则不可同时购买 租户产品与平台产品
     *
     * @param onlinePayTenant
     * @param orderAmountRespDTOMap
     */
    public boolean checkPaymentType(boolean onlinePayTenant, Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap) {
        boolean offlinePayTenant = !onlinePayTenant;
        boolean payDistribution = false;
        boolean payOwnTenant = false;
        for (OrderAmountRespDTO value : orderAmountRespDTOMap.values()) {
            if (EnumOrderPayType.onlyPayTenant(value.getPaymentType())) {
                payOwnTenant = true;
            } else {
                payDistribution = true;
            }
        }
        Assert.validateTrue(offlinePayTenant && payDistribution && payOwnTenant, "线上付款和线下付款的产品不能批量支付");
        if (offlinePayTenant && payOwnTenant) {
            //只能线下支付
            return true;
        } else {
            return false;
        }
    }

    public void saveOrUpdateTenantLogisticsOrders(List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos, Long userId) {
        if (CollUtil.isEmpty(tenantLogisticsOrderReqDtos)) {
            return;
        }
        if (CollUtil.isEmpty(tenantLogisticsOrderReqDtos)) {
            return;
        }
        for (TenantLogisticsOrderReqDto tenantLogisticsOrderReqDto : tenantLogisticsOrderReqDtos) {
            tenantLogisticsOrderReqDto.setUpdateUid(userId);
            tenantLogisticsOrderReqDto.setCreateUid(userId);
        }
        tenantLogisticsOrderFeign.saveOrUpdate(tenantLogisticsOrderReqDtos);

    }

    public void decrementTenant(Long offsetNum, Long userId, Long tenantId) {
        if (!NumberUtils.greaterZero(offsetNum)) {
            return;
        }
        TenantConsumePermissionReqDto consumePermissionReqDto = new TenantConsumePermissionReqDto();
        consumePermissionReqDto.setConsumeValue(BigDecimal.valueOf(offsetNum));
        consumePermissionReqDto.setCode(PlatformPermissionCodeEnum.order_amount.getCode());
        consumePermissionReqDto.setUserId(userId);
        consumePermissionReqDto.setSourceCode(ApplicationCodeEnum.SELF_ORDER.getCode());
        TenantConsumePermissionRespDto tenantConsumePermissionRespDto = tenantFeign.consumePermission(tenantId, consumePermissionReqDto);
        if (tenantConsumePermissionRespDto != null && !tenantConsumePermissionRespDto.getStatus()) {
            throw new BusinessException("订单额度不足，请联系客服");
        }
    }

    public void consumeOrReturnTenantOrderCount(List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos, Long userId, Long tenantId) {
        if (CollUtil.isEmpty(tenantLogisticsOrderReqDtos)) {
            return;
        }
        returnOrderCount(tenantLogisticsOrderReqDtos, userId, tenantId);
        consumeOrderCount(tenantLogisticsOrderReqDtos, userId, tenantId);
        saveOrUpdateTenantLogisticsOrders(tenantLogisticsOrderReqDtos, userId);
    }

    private void returnOrderCount(List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos, Long userId, Long tenantId) {
        List<TenantLogisticsOrderReqDto> returnOrderCounts = tenantLogisticsOrderReqDtos.stream().filter(t -> t.getDeductTotalValue().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(returnOrderCounts)) {
            List<TenantReturnPermissionReqDto> tenantReturnPermissionReqDtoList = Lists.newArrayList();
            for (TenantLogisticsOrderReqDto logisticsOrderReqDto : returnOrderCounts) {
                TenantReturnPermissionReqDto tenantReturnPermissionReqDto = new TenantReturnPermissionReqDto();
                tenantReturnPermissionReqDto.setSourceCode(logisticsOrderReqDto.getQuotaType());
                tenantReturnPermissionReqDto.setReturnValue(logisticsOrderReqDto.getDeductTotalValue().abs());
                tenantReturnPermissionReqDtoList.add(tenantReturnPermissionReqDto);
            }
            tenantReturnPermissionReqDtoList = tenantReturnPermissionReqDtoList.stream().collect(Collectors.toMap(item -> item.getSourceCode(), item -> item, (a, b) -> {
                a.setReturnValue(NumberUtil.roundDown(NumberUtil.add(a.getReturnValue(), b.getReturnValue()), 2));
                return a;
            })).values().stream().collect(Collectors.toList());
            TenantGroupReturnPermissionReqDto reqDto = new TenantGroupReturnPermissionReqDto();
            reqDto.setTenantId(tenantId);
            reqDto.setCode(PlatformPermissionCodeEnum.order_amount.getCode());
            reqDto.setUserId(userId);
            reqDto.setTenantReturnPermissionReqDtoList(tenantReturnPermissionReqDtoList);
            tenantFeign.returnPermissionByGourp(tenantId, reqDto);
        }
    }

    private void consumeOrderCount(List<TenantLogisticsOrderReqDto> tenantLogisticsOrderReqDtos, Long userId, Long tenantId) {
        List<TenantLogisticsOrderReqDto> consumeOrderCounts = tenantLogisticsOrderReqDtos.stream().filter(t -> t.getDeductTotalValue().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(consumeOrderCounts)) {
            List<TenantConsumePermissionReqDto> tenantConsumePermissionReqDtoList = Lists.newArrayList();
            for (TenantLogisticsOrderReqDto logisticsOrderReqDto : consumeOrderCounts) {
                TenantConsumePermissionReqDto tenantConsumePermissionReqDto = new TenantConsumePermissionReqDto();
                tenantConsumePermissionReqDto.setSourceCode(logisticsOrderReqDto.getQuotaType());
                tenantConsumePermissionReqDto.setConsumeValue(logisticsOrderReqDto.getDeductTotalValue());
                tenantConsumePermissionReqDtoList.add(tenantConsumePermissionReqDto);
            }
            TenantGroupConsumePermissionReqDto reqDto = new TenantGroupConsumePermissionReqDto();
            reqDto.setTenantId(tenantId);
            reqDto.setCode(PlatformPermissionCodeEnum.order_amount.getCode());
            reqDto.setUserId(userId);
            reqDto.setTenantConsumePermissionReqDtoList(tenantConsumePermissionReqDtoList);
            tenantFeign.consumePermissionByGourp(tenantId, reqDto);
        }
    }

    public void increaseTenant(Long offsetNum, Long userId, Long tenantId) {
        if (!NumberUtils.greaterZero(offsetNum)) {
            return;
        }
        TenantReturnPermissionReqDto dto = new TenantReturnPermissionReqDto();
        dto.setReturnValue(BigDecimal.valueOf(offsetNum));
        dto.setCode(PlatformPermissionCodeEnum.order_amount.getCode());
        dto.setUserId(userId);
        dto.setSourceCode(ApplicationCodeEnum.SELF_ORDER.getCode());
        tenantFeign.returnPermission(tenantId, dto);
    }

    public void checkDecrementTenant(BigDecimal offsetNum, Long tenantId) {
        //大于0，则扣减订单额度，小于等于0，返还，无需校验
        if (null == offsetNum || offsetNum.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        TenantRespDto tenantRespDto = tenantFeign.getTenantPermissionById(tenantId);
        TenantPlatformPermissionRespDto tenantPlatformPermissionRespDto = tenantRespDto.getOrderPermission();
        Assert.validateNull(tenantPlatformPermissionRespDto, "订单额度不足，请联系客服");
        BigDecimal leftValue = tenantPlatformPermissionRespDto.getTotalValue().subtract(tenantPlatformPermissionRespDto.getUsedValue());
        Assert.validateTrue(leftValue.compareTo(offsetNum) < 0, "订单额度不足，请联系客服");

    }


}

