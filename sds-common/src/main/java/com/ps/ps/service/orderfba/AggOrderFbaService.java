package com.ps.ps.service.orderfba;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.ps.base.entity.SearchBean;
import com.ps.ps.feign.ImportedPlatformOrderFbaFeign;
import com.ps.ps.feign.ImportedPlatformOrderFbaItemFeign;
import com.ps.ps.feign.OrderFeign;
import com.ps.ps.feign.OrderItemFeign;
import com.ps.ps.feign.logistics.LogisticsServiceAmountFeign;
import com.ps.ps.feign.logistics.TenantLogisticsFeign;
import com.ps.ps.feign.order.FbaItemProductLabelPrintedFeign;
import com.ps.ps.feign.order.OrderFbaCarriageDetailFeign;
import com.ps.ps.feign.order.OrderFbaFeign;
import com.ps.ps.feign.order.OrderRefundFeign;
import com.ps.ps.feign.payment.MerchantBillFeign;
import com.ps.ps.service.*;
import com.ps.ps.service.linstener.OrderEventService;
import com.ps.ps.service.payment.MerchantUserAccountService;
import com.ps.support.Assert;
import com.ps.support.utils.OrderCodeUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.EnumCommonStatus;
import com.sdsdiy.common.base.helper.DoubleUtils;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsdata.dto.base.LogisticServiceAmountDTO;
import com.sdsdiy.orderapi.constant.EnumCarriagePayStatus;
import com.sdsdiy.orderapi.constant.EnumOrderAmountCode;
import com.sdsdiy.orderapi.constant.EnumOrderItemTransferType;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.constant.event.message.admin.AdminFinishUploadLabelMessage;
import com.sdsdiy.orderapi.constant.event.message.admin.AdminUploadPackingLabelMessage;
import com.sdsdiy.orderapi.dto.ImportedPlatformOrderFbaItemReqDto;
import com.sdsdiy.orderapi.dto.ImportedPlatformOrderFbaItemRespDto;
import com.sdsdiy.orderapi.dto.ImportedPlatformOrderFbaRespDto;
import com.sdsdiy.orderapi.dto.OrderDTO;
import com.sdsdiy.orderapi.dto.order.*;
import com.sdsdiy.orderapi.dto.orderfba.FbaItemProductLabelPrintedReqDto;
import com.sdsdiy.orderapi.dto.orderfba.FbaItemProductLabelPrintedRespDto;
import com.sdsdiy.orderapi.dto.orderfba.OrderFbaCarriageDetailRespDto;
import com.sdsdiy.orderapi.enumeration.EnumOrderFbaStatus;
import com.sdsdiy.orderdata.dto.OrderAmountDetailDTO;
import com.sdsdiy.orderdata.dto.OrderItemRespDto;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentapi.dto.RefundDto;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentapi.param.bill.MerchantBillCreateParam;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.UserAccountBalanceResp;
import com.ziguang.base.dto.*;
import com.ziguang.base.dto.logistics.CountryExpressInfoNewVO;
import com.ziguang.base.dto.order.OrderFbaCarriageAmountDto;
import com.ziguang.base.model.*;
import com.ziguang.base.support.contant.LogisticsCodeId;
import com.ziguang.base.support.contant.OrderStatus;
import com.ziguang.base.support.contant.Platform;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/02/27 14:09
 **/
@Service
@Slf4j
public class AggOrderFbaService {

    @Resource
    private OrderFbaFeign orderFbaFeign;
    @Resource
    private OrderEventService orderEventService;
    @Resource
    private OrderItemFeign orderItemFeign;
    @Resource
    private OrderFeign orderFeign;
    @Resource
    private ImportedPlatformOrderFbaItemFeign importedPlatformOrderFbaItemFeign;
    @Resource
    private OrderFbaService orderFbaService;
    @Resource
    private LogisticsServiceAmountFeign logisticsServiceAmountFeign;
    @Resource
    private TenantLogisticsFeign tenantLogisticsFeign;
    @Resource
    private ImportedPlatformOrderFbaFeign importedPlatformOrderFbaFeign;
    @Resource
    private FbaOrderTaskService fbaOrderTaskService;
    @Resource
    private OrderUpdateLogisticsService orderUpdateLogisticsService;
    @Resource
    private MerchantUserAccountService merchantUserAccountService;
    @Resource
    private MerchantBillFeign merchantBillFeign;
    @Resource
    private OrderFbaCarriageDetailFeign orderFbaCarriageDetailFeign;
    @Resource
    private CountryExpressInfoService countryExpressInfoService;
    @Resource
    private OrderService orderService;
    @Resource
    private FbaItemProductLabelPrintedFeign fbaItemProductLabelPrintedFeign;
    @Resource
    private OrderItemTransferHistoryService orderItemTransferHistoryService;
    @Resource
    private FactoryOrderService factoryOrderService;
    @Resource
    private OrderRefundFeign orderRefundFeign;

    public void uploadBoxLabels(Long orderId, Long userId, OrderFbaBoxLabelDto boxLabelDto) {
        List<String> boxLabels = boxLabelDto.getBoxLabels();
        checkBoxLabels(boxLabels);
        OrderFbaRespDto orderFbaRespDto = orderFbaFeign.findDtoByOrderId(orderId);
        Assert.validateTrue(null == orderFbaRespDto, "fba装箱信息不存在");
        OrderFbaReqDto orderFbaReqDto = getOrderFbaReqDto(orderFbaRespDto, boxLabels);
        orderFbaFeign.updateByOrderFba(orderFbaReqDto);
        if (!EnumOrderFbaStatus.FINISH.getValue().equals(orderFbaRespDto.getStatus())) {
            sendAdminUploadPackingLabelMessage(orderId, userId);
        }
    }

    @NotNull
    private static OrderFbaReqDto getOrderFbaReqDto(OrderFbaRespDto orderFbaRespDto, List<String> boxLabels) {
        OrderFbaReqDto orderFbaReqDto = new OrderFbaReqDto();
        orderFbaReqDto.setLabelsJson(JSONUtil.toJsonStr(boxLabels));
        orderFbaReqDto.setUploadLabelTime(System.currentTimeMillis());
        if(orderFbaRespDto.getBoxLabelPrinted().equals(BasePoConstant.YES)){
            orderFbaReqDto.setBoxLabelChanged(BasePoConstant.YES);
        }
        orderFbaReqDto.setBoxLabelPrinted(BasePoConstant.NO);
        orderFbaReqDto.setId(orderFbaRespDto.getId());
        return orderFbaReqDto;
    }

    private static void checkBoxLabels(List<String> boxLabels) {
        boxLabels.forEach(l -> {
            if (!l.toLowerCase().endsWith(".pdf")) {
                Assert.wrong("请上传pdf文件");
            }
        });
    }

    private void sendAdminUploadPackingLabelMessage(Long orderId, Long userId) {
        AdminUploadPackingLabelMessage message = new AdminUploadPackingLabelMessage();
        message.setOrderId(orderId);
        message.setEid(orderId);
        message.setSendingTime(new Date());
        message.setOperatorUid(userId);
        orderEventService.sendProcessMsg(message, OrderProgressConstant.ADMIN_UPLOAD_PACKING_LABEL);
    }

    public static void main(String[] args) {
        OrderProductLabelDto dto=new OrderProductLabelDto();
        List<Long> orderItemIds=Lists.newArrayList(1L,2L);
        dto.setOrderItemIds(orderItemIds);

        List<Long> orderItemIds1 = Lists.newArrayList();
        orderItemIds1.addAll(dto.getOrderItemIds());
        orderItemIds1.add(4L);

        System.out.println(JSON.toJSONString(dto.getOrderItemIds()));
        System.out.println(JSON.toJSONString(orderItemIds1));
    }

    @GlobalTransactional
    public SearchBean<OrderFbaPrintProductDto> printProductLabel(Long userId,Long orderId, OrderProductLabelDto dto) {
        orderFbaFeign.updateItemProductLabelPrintedByOrderId(orderId, BasePoConstant.YES);
        saveFbaItemProductLabelPrinted(userId, orderId, dto);
        updateProductLabelPrinted(orderId);
        List<OrderFbaPrintProductDto> result = getOrderFbaPrintProductDtos(dto, dto.getOrderItemIds());
        return formatSearchBeanDto(dto, result);
    }

    @NotNull
    private List<OrderFbaPrintProductDto> getOrderFbaPrintProductDtos(OrderProductLabelDto dto, List<Long> paramOrderItemIds) {
        Map<Long, ImportedPlatformOrderFbaItemRespDto> orderItemIdFbaItemMap = getOrderItemIdFbaItemMap(paramOrderItemIds);
        List<FactoryOrder> factoryOrders = factoryOrderService.findByOrderItemIdsNotCancel(dto.getOrderItemIds());
        List<OrderFbaPrintProductDto> result=Lists.newArrayList();
        for (FactoryOrder factoryOrder : factoryOrders) {
            for (int i = 0; i < factoryOrder.getNum(); i++) {
                OrderFbaPrintProductDto printProductDto = new OrderFbaPrintProductDto();
                ImportedPlatformOrderFbaItemRespDto fbaItemRespDto = orderItemIdFbaItemMap.get(factoryOrder.getOrderItemId());
                printProductDto.setLabelFnsku(fbaItemRespDto.getLabelFnsku());
                printProductDto.setProductName(fbaItemRespDto.getImportProductName());
                printProductDto.setLabelStatus(fbaItemRespDto.getLabelStatus());
                printProductDto.setLabelFnskuUrl(fbaItemRespDto.getLabelFnskuUrl());
                result.add(printProductDto);
            }
        }
        return result;
    }

    @NotNull
    private Map<Long, ImportedPlatformOrderFbaItemRespDto> getOrderItemIdFbaItemMap(List<Long> paramOrderItemIds) {
        List<Long> orderItemIds = Lists.newArrayList();
        orderItemIds.addAll(paramOrderItemIds);
        //驳回或补件的子单，需要找到原子单，从而用原子单获取ImportedPlatformOrderFbaItemRespDto信息
        List<OrderItemTransferHistoryDto> orderItemTransferHistoryDtos = orderItemTransferHistoryService.getByNewItemIds(orderItemIds, EnumOrderItemTransferType.REJECT.getValue(),"newOrderItemId,orderItemId,transferType");
        List<String> transferTypes = Lists.newArrayList(EnumOrderItemTransferType.REJECT.getValue(), EnumOrderItemTransferType.LOSE_RESEND.getValue(), EnumOrderItemTransferType.TRANSFER.getValue());
        List<Long> sourceOrderItemIds = orderItemTransferHistoryDtos.stream().filter(i->transferTypes.contains(i.getTransferType())).map(OrderItemTransferHistoryDto::getOrderItemId).distinct().collect(Collectors.toList());
        if(CollUtil.isNotEmpty(sourceOrderItemIds)){
            orderItemIds.addAll(sourceOrderItemIds);
        }
        //获取ImportedPlatformOrderFbaItemRespDto
        String orderItemIdsStr =orderItemIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        List<ImportedPlatformOrderFbaItemRespDto> fbaItemRespDtos = importedPlatformOrderFbaItemFeign.getByOrderItemIds(orderItemIdsStr, "");
        genAndSaveItemFnskuUrl(fbaItemRespDtos);

        Map<Long, ImportedPlatformOrderFbaItemRespDto> importFbaItemMap = fbaItemRespDtos.stream().collect(Collectors.toMap(i -> i.getOrderItemId(), Function.identity(), (a, b) -> b));

        Map<Long,ImportedPlatformOrderFbaItemRespDto> orderItemIdFbaItemMap= Maps.newHashMap();
        Map<Long, Long> newOrderItemIdOrderItemIdMap = orderItemTransferHistoryDtos.stream().collect(Collectors.toMap(i -> i.getNewOrderItemId(), i -> i.getOrderItemId(), (a, b) -> b));
        for (Long orderItemId : paramOrderItemIds) {
            Long sourceOrderItemId = newOrderItemIdOrderItemIdMap.getOrDefault(orderItemId, orderItemId);
            ImportedPlatformOrderFbaItemRespDto importedPlatformOrderFbaItemRespDto = importFbaItemMap.get(sourceOrderItemId);
            if(null!=importedPlatformOrderFbaItemRespDto){
                orderItemIdFbaItemMap.put(orderItemId,importedPlatformOrderFbaItemRespDto);
            }
        }
        return orderItemIdFbaItemMap;
    }

    /**
     * 生产完成之后，如果打印产品标签，则orderFba的产品标签状态要变为已打印
     * @param orderId
     */
    private void updateProductLabelPrinted(Long orderId) {
        OrderFbaRespDto orderFbaRespDto = orderFbaFeign.findDtoByOrderId(orderId);
        OrderFbaReqDto updateOrderFba = new OrderFbaReqDto();
        updateOrderFba.setId(orderFbaRespDto.getId());
        updateOrderFba.setProductLabelPrinted(BasePoConstant.YES);
        updateOrderFba.setProductLabelChanged(BasePoConstant.NO);
        orderFbaFeign.updateByOrderFba(updateOrderFba);
    }

    private static boolean productedAndLabelNoChange(OrderFbaRespDto orderFbaRespDto) {
        return orderFbaRespDto.getProductedStatus().equals(OrderFbaService.PRODUCTED_STATUS_FINSH) &&
            orderFbaRespDto.getProductLabelChanged().equals(BasePoConstant.NO);
    }

    private void saveFbaItemProductLabelPrinted(Long userId, Long orderId, OrderProductLabelDto dto) {
        List<Long> orderItemIds = dto.getOrderItemIds();
        List<FbaItemProductLabelPrintedRespDto> existFbaItemProductLabelPrinteds = fbaItemProductLabelPrintedFeign.getByItemIds(orderItemIds);
        List<Long> existOrderItemIds = existFbaItemProductLabelPrinteds.stream().map(i -> i.getOrderItemId()).collect(Collectors.toList());
        List<Long> saveOrderItemIds = orderItemIds.stream().filter(i -> !existOrderItemIds.contains(i)).collect(Collectors.toList());
        fbaItemProductLabelPrintedFeign.updateFnskuChangeNoByItemIds(orderItemIds);
        if (CollUtil.isEmpty(saveOrderItemIds)) {
            return;
        }
        List<FbaItemProductLabelPrintedReqDto> fbaItemProductLabelPrintedReqDtos=Lists.newArrayList();
        for (Long orderItemId : saveOrderItemIds) {
            FbaItemProductLabelPrintedReqDto fbaItemProductLabelPrintedReqDto = FbaItemProductLabelPrintedReqDto.builder()
                .orderItemId(orderItemId)
                .orderId(orderId)
                .createUid(userId)
                .build();
            fbaItemProductLabelPrintedReqDtos.add(fbaItemProductLabelPrintedReqDto);
        }
        fbaItemProductLabelPrintedFeign.batchSave(fbaItemProductLabelPrintedReqDtos);
    }

    @NotNull
    private SearchBean<OrderFbaPrintProductDto> formatSearchBeanDto(OrderProductLabelDto dto, List<OrderFbaPrintProductDto> result) {
        Integer page = dto.getPage();
        Integer size = dto.getSize();
        int totalCount = result.size();
        //手动分页
        if (null != page && null != size) {
            result = orderFbaService.paging(result, page, size);
        }
        SearchBean<OrderFbaPrintProductDto> sb = new SearchBean<>(page, size);
        sb.setItems(result);
        sb.setTotalCount(totalCount);
        return sb;
    }

    private void genAndSaveItemFnskuUrl(List<ImportedPlatformOrderFbaItemRespDto> fbaItemRespDtos) {
        if (CollUtil.isEmpty(fbaItemRespDtos)) {
            return;
        }
        List<ImportedPlatformOrderFbaItemReqDto> reqDtoList = Lists.newArrayList();
        for (ImportedPlatformOrderFbaItemRespDto item : fbaItemRespDtos) {
            ImportedPlatformOrderFbaItemReqDto update = new ImportedPlatformOrderFbaItemReqDto();
            update.setId(item.getId());
            update.setLabelFnskuUrl(orderFbaService.genShapeCodeProductLabel(item.getLabelFnsku().trim()));
            reqDtoList.add(update);
            item.setLabelFnskuUrl(update.getLabelFnskuUrl());
        }
        importedPlatformOrderFbaItemFeign.updateBatch(reqDtoList);
    }

    @NotNull
    private static List<OrderFbaPrintProductDto> getOrderFbaPrintProductDtos(List<ImportedPlatformOrderFbaItemRespDto> fbaItemRespDtos) {
        if (CollUtil.isEmpty(fbaItemRespDtos)) {
            return Collections.emptyList();
        }
        List<OrderFbaPrintProductDto> result = new ArrayList<>();
        for (ImportedPlatformOrderFbaItemRespDto item : fbaItemRespDtos) {
            for (int i = 0; i < item.getNum(); i++) {
                OrderFbaPrintProductDto printProductDto = new OrderFbaPrintProductDto();
                printProductDto.setLabelFnsku(item.getLabelFnsku());
                printProductDto.setProductName(item.getImportProductName());
                printProductDto.setLabelStatus(item.getLabelStatus());
                printProductDto.setLabelFnskuUrl(item.getLabelFnskuUrl());
                result.add(printProductDto);
            }
        }

        return result;
    }

    public void forceNext(Long orderId, Integer status, Long userId) {
        OrderFbaRespDto orderFbaRespDto = orderFbaFeign.findDtoByOrderId(orderId);
        Assert.validateTrue(null == orderFbaRespDto, "fba装箱信息不存在");
        Assert.validateTrue(!orderFbaRespDto.getStatus().equals(status), "fba装箱状态已更新，请刷新重试");
        EnumOrderFbaStatus nextStatusEnum = EnumOrderFbaStatus.getForceNextStatus(status);
        Assert.validateTrue(null == nextStatusEnum, "无效的fba状态：" + status);
        OrderFbaReqDto update = new OrderFbaReqDto();
        update.setId(orderFbaRespDto.getId());
        update.setStatus(nextStatusEnum.getValue());
        orderFbaFeign.updateByOrderFba(update);
        SendAdminFinishUploadLabelMessage(orderId, userId);
        orderFbaService.updatePackingStepToEs(orderId,nextStatusEnum.getValue());

    }

    private void SendAdminFinishUploadLabelMessage(Long orderId, Long userId) {
        AdminFinishUploadLabelMessage message = new AdminFinishUploadLabelMessage();
        message.setOrderId(orderId);
        message.setEid(orderId);
        message.setSendingTime(new Date());
        message.setOperatorUid(userId);
        orderEventService.sendProcessMsg(message, OrderProgressConstant.ADMIN_FINISH_UPLOAD_LABEL);
    }

    public OrderFbaCarriageDifferencePriceDto carriageDifferencePrice(Long orderId, OrderFbaCarriageAmountDto dto) {
        Double oldFbaBoxAmount;
        Double oldFbaLabelAmount;
        Double oldTakeSelfAmount;
        Double oldServiceAmount;
        Double dbOldServiceAmount;
        Double oldLogisticsAmount;
        Double oldFnskuChangeAmount;
        Double lastFnskuChangeAmount=0D;
        Integer oldPasteLabel;
        String fields = "id,no,originId,serviceAmount,refundServiceAmount,currentCarriageAmount,transactionCode,carriagePayStaus,issuingBayAreaId,tenantId,logisticsId";
        OrderDTO orderDTO = orderFeign.findById(orderId, fields);
        OrderFbaCarriageDetailRespDto orderFbaCarriageDetailRespDto = orderFbaCarriageDetailFeign.getById(orderId);
        boolean oldAmountFromCarriageDetail = isOldAmountFromCarriageDetail(orderDTO, orderFbaCarriageDetailRespDto);
        OrderAmountDetailDTO orderAmountDetail = orderFbaService.getOrderAmountDetail(orderId);

        //待付运费，取订单原费用信息
        if (oldAmountFromCarriageDetail) {
            oldFbaBoxAmount = orderFbaCarriageDetailRespDto.getOriginalBoxAmount().doubleValue();
            oldFbaLabelAmount = orderFbaCarriageDetailRespDto.getOriginalLabelAmount().doubleValue();
            oldTakeSelfAmount = orderFbaCarriageDetailRespDto.getOriginalTakeSelfAmount().doubleValue();
            oldServiceAmount = orderFbaCarriageDetailRespDto.getOriginalServiceAmount().doubleValue();
            oldLogisticsAmount = orderFbaCarriageDetailRespDto.getOriginalCarriageAmount().doubleValue();
            oldPasteLabel = orderFbaCarriageDetailRespDto.getOriginalPasteLabel();
            oldFnskuChangeAmount = orderFbaCarriageDetailRespDto.getOriginalFnskuChangeAmount().doubleValue();
            lastFnskuChangeAmount=orderFbaCarriageDetailRespDto.getCurrentFnskuChangeAmount().doubleValue();
        } else {
            oldFbaBoxAmount = null != orderAmountDetail && NumberUtils.greaterZero(orderAmountDetail.getFbaBoxAmount()) ? orderAmountDetail.getFbaBoxAmount() : Double.valueOf(0D);
            oldFbaLabelAmount = null != orderAmountDetail && NumberUtils.greaterZero(orderAmountDetail.getFbaLabelAmount()) ? orderAmountDetail.getFbaLabelAmount() : Double.valueOf(0D);
            oldTakeSelfAmount = null != orderAmountDetail && NumberUtils.greaterZero(orderAmountDetail.getTakeSelfAmount()) ? orderAmountDetail.getTakeSelfAmount() : Double.valueOf(0D);
            oldFnskuChangeAmount = null != orderAmountDetail && NumberUtils.greaterZero(orderAmountDetail.getFnskuChangeAmount()) ? orderAmountDetail.getFnskuChangeAmount() : Double.valueOf(0D);
            oldServiceAmount = DoubleUtils.sub(orderDTO.getServiceAmount(), orderDTO.getRefundServiceAmount());
            oldLogisticsAmount = orderDTO.getCurrentCarriageAmount();
            ImportedPlatformOrderFbaRespDto importedPlatformOrderFbaRespDto = importedPlatformOrderFbaFeign.getById(orderDTO.getOriginId(), "");
            oldPasteLabel = null != importedPlatformOrderFbaRespDto ? importedPlatformOrderFbaRespDto.getPasteLabel() : BasePoConstant.NO;
        }
        dbOldServiceAmount = oldServiceAmount;

        List<OrderItemRespDto> orderItemRespDtos = orderItemFeign.findByOrderId(orderId);
        List<OrderItemRespDto> noCancelItems = orderItemRespDtos.stream().filter(i -> !OrderStatus.isCancel(i.getStatus())).collect(Collectors.toList());

        //物流不变，运营修改贴标费，取运营的
        Double podUnitFbaLabelAmount = getPodUnitFbaLabelAmount(dto, orderDTO);
        TenantLogisticsRespDto logisticsRespDto = tenantLogisticsFeign.getDtoById(dto.getLogisticsId());

        CountryExpressInfoNewVO countryExpressInfoNew = getCountryExpressInfoNew(orderDTO, dto.getAddress(), noCancelItems, logisticsRespDto.getId());
        Double unitFbaLabelAmount = null != podUnitFbaLabelAmount ? podUnitFbaLabelAmount : countryExpressInfoNew.getLogistics().getFbaLabelAmount();
        Double newServiceAmount = countryExpressInfoNew.getLogistics().getServiceAmount();
        Double newTakeSelfAmount = countryExpressInfoNew.getLogistics().getPickServiceAmount();
        newTakeSelfAmount = null != newTakeSelfAmount ? newTakeSelfAmount : 0D;

        Double newFnskuChangeAmount = null != orderAmountDetail && NumberUtils.greaterZero(orderAmountDetail.getFnskuChangeAmount()) ? orderAmountDetail.getFnskuChangeAmount() : Double.valueOf(0D);

        BigDecimal newFbaLabelAmount = BigDecimal.ZERO;
        if (dto.getPasteLabel().equals(BasePoConstant.YES)) {
            for (OrderItemRespDto item : noCancelItems) {
                Double itemFbaLabelAmount = DoubleUtils.mul(item.getNum(), unitFbaLabelAmount);
                newFbaLabelAmount = NumberUtil.add(newFbaLabelAmount, itemFbaLabelAmount);
            }
            //需要算上现在fnsku修改费用
            newFbaLabelAmount = NumberUtil.add(newFbaLabelAmount, newFnskuChangeAmount);
            newServiceAmount = NumberUtil.add(newServiceAmount.doubleValue(), newFbaLabelAmount.doubleValue());

            double fnskuChangeDifference=NumberUtil.sub(newFnskuChangeAmount, oldFnskuChangeAmount);
            Assert.validateTrue(fnskuChangeDifference<0D,"fnskuChangeDifference负数");
            if (fnskuChangeDifference > 0D) {
                oldServiceAmount = NumberUtil.add(oldServiceAmount.doubleValue(), fnskuChangeDifference);
            }
        }

        boolean isFbaZt = LogisticsCodeId.FBA_ZT.getCodeId().equalsIgnoreCase(logisticsRespDto.getCodeId());
        BigDecimal newLogisticsAmount = isFbaZt ? BigDecimal.ZERO : dto.getLogisticsAmount();
        Double newFbaBoxAmount = getNewFbaBoxAmount(dto.getLogisticsId());
        BigDecimal newAmount = NumberUtil.add(newServiceAmount, newLogisticsAmount);
        Double oldAmount = DoubleUtils.add(oldServiceAmount, oldLogisticsAmount);
        BigDecimal currentDifferentAmount = NumberUtil.sub(newAmount, oldAmount);

        return OrderFbaCarriageDifferencePriceDto.builder()
            .oldServiceAmount(BigDecimal.valueOf(oldServiceAmount))
            .dbOldServiceAmount(BigDecimal.valueOf(dbOldServiceAmount))
            .oldLogisticsAmount(BigDecimal.valueOf(oldLogisticsAmount))
            .oldLabelPrice(BigDecimal.valueOf(oldFbaLabelAmount))
            .oldBoxPrice(BigDecimal.valueOf(oldFbaBoxAmount))
            .oldPasteLabel(oldPasteLabel)
            .oldTakeSelfAmount(BigDecimal.valueOf(oldTakeSelfAmount))
            .newServiceAmount(BigDecimal.valueOf(newServiceAmount))
            .newLogisticsAmount(newLogisticsAmount)
            .newLabelPrice(newFbaLabelAmount)
            .unitFbaLabelAmount(BigDecimal.valueOf(unitFbaLabelAmount))
            .newBoxPrice(BigDecimal.valueOf(newFbaBoxAmount))
            .pasteLabel(dto.getPasteLabel())
            .newTakeSelfAmount(BigDecimal.valueOf(newTakeSelfAmount))
            .oldFnskuChangeAmount(BigDecimal.valueOf(oldFnskuChangeAmount))
            .newFnskuChangeAmount(BigDecimal.valueOf(newFnskuChangeAmount))
            .differencePrice(currentDifferentAmount)
            .build();
    }

    @Nullable
    private Double getPodUnitFbaLabelAmount(OrderFbaCarriageAmountDto dto, OrderDTO orderDTO) {
        Double podUnitFbaLabelAmount = null;
        if (orderDTO.getLogisticsId().equals(dto.getLogisticsId())) {
            FbaOrderTask fbaOrderTask = fbaOrderTaskService.findByOrderId(orderDTO.getId());
            if (fbaOrderTask != null && fbaOrderTask.getUpdateStatus().equals(EnumCommonStatus.YES.getCode())) {
                podUnitFbaLabelAmount = fbaOrderTask.getFbaLabelAmount();
            }
        }
        return podUnitFbaLabelAmount;
    }

    private ReferDto formatFbaReferDto(OrderDTO order, Address address, List<OrderItemRespDto> orderItems, Long logisticsId) {
        ReferDto referDto = new ReferDto();
        referDto.setLogisticsType(LogisticsService.TYPE_FBA);
        referDto.setCountry(null != address ? address.getCountry() : "");
        referDto.setProvinceCode(null != address ? address.getProvinceCode() : "");
        referDto.setCityCode(null != address ? address.getCityCode() : "");
        referDto.setIssuingBayAreaId(order.getIssuingBayAreaId());
        referDto.setTenantId(order.getTenantId());
        referDto.setPriorLogisticsId(logisticsId);
        referDto.setType(BasePoConstant.YES);
        List<ReferProductInfoDto> productInfos = com.google.common.collect.Lists.newArrayList();
        referDto.setProductInfo(productInfos);

        if (CollUtil.isNotEmpty(orderItems)) {
            orderItems.stream().forEach(oi -> {
                ReferProductInfoDto productInfoDto = new ReferProductInfoDto();
                productInfoDto.setProductId(oi.getProductId());
                productInfoDto.setNum(oi.getNum());
                productInfoDto.setCurrentPrice(oi.getPrice());
                productInfos.add(productInfoDto);
            });
        }
        return referDto;
    }

    private CountryExpressInfoNewVO getCountryExpressInfoNew(OrderDTO order, Address address, List<OrderItemRespDto> orderItems, Long logisticsId) {
        ReferDto referDto = formatFbaReferDto(order, address, orderItems, logisticsId);
        List<CountryExpressInfoNewVO> countryExpressInfoNewList = countryExpressInfoService.findReferToOrderId(referDto, order.getId(), order.getStatus());
        if (CollUtil.isEmpty(countryExpressInfoNewList)) {
            Assert.wrong("物流为空");
        }
        return countryExpressInfoNewList.get(0);
    }

    private boolean isOldAmountFromCarriageDetail(OrderDTO orderDTO, OrderFbaCarriageDetailRespDto orderFbaCarriageDetailRespDto) {
        boolean carriagePaid = orderDTO.getCarriagePayStaus().equals(EnumCarriagePayStatus.SUCCESS.getValue());
        Assert.validateTrue(carriagePaid, "用户已支付fba运费，请刷新页面");
        OrderFbaRespDto orderFbaRespDto = orderFbaFeign.findDtoByOrderId(orderDTO.getId());
        Assert.validateTrue(null == orderFbaRespDto, "order fba不存在");
        Assert.validateTrue(!orderFbaRespDto.getStatus().equals(EnumOrderFbaStatus.WAIT_CALCULATE_SHIPPING.getValue()) && !orderFbaRespDto.getStatus().equals(EnumOrderFbaStatus.UNPAY_SHIPPING.getValue()), "fba装箱状态不正确");
        boolean oldAmountFromCarriageDetail = orderFbaRespDto.getStatus().equals(EnumOrderFbaStatus.UNPAY_SHIPPING.getValue());
        if (oldAmountFromCarriageDetail && null == orderFbaCarriageDetailRespDto) {
            oldAmountFromCarriageDetail = false;
        }
        return oldAmountFromCarriageDetail;
    }

    @NotNull
    private Double getNewFbaBoxAmount(Long logisticsId) {
        Double newFbaBoxAmount = 0D;
        List<LogisticServiceAmountDTO> logisticServiceAmountDTOs = logisticsServiceAmountFeign.listByLogisticsId(logisticsId);
        if (CollUtil.isNotEmpty(logisticServiceAmountDTOs)) {
            List<LogisticServiceAmountDTO> logisticServiceAmountDTOList = logisticServiceAmountDTOs.stream().filter(i -> i.getAmountCode().equals(EnumOrderAmountCode.FBA_BOX.getValue())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(logisticServiceAmountDTOList)) {
                newFbaBoxAmount = logisticServiceAmountDTOList.get(0).getAmount();
            }
        }
        return newFbaBoxAmount;
    }

    public String remarkFormat = "运费差价:%s，服务费差价:%s";

    private String fbaCarriagePayRemarkFormatPay = "平台已核算完运费%s元，补差价%s元";
    private String fbaCarriagePayRemarkFormatRefund = "平台已核算完运费%s元，退差价%s元";


    private RefundParam generateFbaRefundDtoForMerchantPay(Long orderId, MerchantRespDto merchant, Long userId, Double totalAmount) {
        UserAccountBalanceResp userAccountBalanceResp = merchantUserAccountService.getUserAccountBalance(merchant.getId(), orderId);
        RefundParam refundParam = new RefundParam();
        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        refundParam.setBalance(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(totalAmount, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(BigDecimal.ZERO);
        refundParam.setPayMethod(PaymentMethodEnum.BALANCE.getCode());
        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        refundParam.setSubject("FBA订单退款");

        refundParam.setTotalAmount(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(totalAmount, BigDecimal.ROUND_CEILING));
        refundParam.setTargetTenantId(merchant.getTenantId());
        refundParam.setTargetMerchantId(merchant.getId());
        refundParam.setTargetRole(PaymentRoleEnum.MERCHANT.getCode());

        refundParam.setSourceMerchantId(0L);
        refundParam.setSourceTenantId(0L);
        refundParam.setSourceRole(PaymentRoleEnum.SAAS.getCode());

        if (MerchantUserAccountConstant.ALLOCATION.equalsIgnoreCase(userAccountBalanceResp.getBalanceType())) {
            refundParam.setTargetUserId(userAccountBalanceResp.getId());
            refundParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType());
        } else {
            refundParam.setTargetUserId(0L);
            refundParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.COMMON_BALANCE.getUsedType());
        }
        refundParam.setDetailPurpose(DetailPurpose.ADMIN_EDIT_MERCHANT_FBA_CARRIAGE_REFUND.getCode());
        return refundParam;
    }
    private String fbaCarriagePayRemarkFormat = "平台已核算完运费%s元";

    public void refundForMerchantPay(Order order, MerchantRespDto merchant, Long userId, OrderFbaCarriageDifferencePriceDto differencePriceDto) {
        //退款
        BigDecimal differencePrice = BigDecimal.valueOf(Math.abs(differencePriceDto.getDifferencePrice().doubleValue()));
        String title = "FBA订单退款,订单号:" + order.getNo();
        BigDecimal differenceLogisticsAmount = NumberUtil.sub(differencePriceDto.getNewLogisticsAmount(), differencePriceDto.getOldLogisticsAmount());
        BigDecimal differenceServiceAmount = NumberUtil.sub(differencePriceDto.getNewServiceAmount(), differencePriceDto.getOldServiceAmount());
        String remark = String.format(remarkFormat, differenceLogisticsAmount, differenceServiceAmount);
        //orderService.podOrderRefundAndCreateBill(order, merchant, userId, differencePrice, "FBA订单退款", DetailPurpose.ADMIN_EDIT_MERCHANT_FBA_CARRIAGE_REFUND.getCode(), orderName, remark);
        OrderRefundHandleParam handleParam = new OrderRefundHandleParam();
        handleParam.setOrderId(order.getId())
                .setBalance(differencePrice)
                .setTenantBalance(BigDecimal.ZERO)
                .setBonus(BigDecimal.ZERO)
                .setTenantBonus(BigDecimal.ZERO)
                .setSubject("FBA订单退款")
                .setTitle(title)
                .setRemark(remark)
                .setPurposeType(PurposeType.REFUND.getCode())
                .setDetailPurpose(DetailPurpose.ADMIN_EDIT_MERCHANT_FBA_CARRIAGE_REFUND.getCode())
                .setOperateRole(PaymentRoleEnum.TENANT.getCode())
                .setOperateRoleId(userId);
        orderRefundFeign.refundHandle(handleParam);
    }

    public MerchantBillCreateParam genMerchantRefundEditFbaBill(RefundDto refundDto,
                                                                Long operateUserId,
                                                                MerchantBillDisposeMoney disposeMoney,
                                                                String orderName,
                                                                String remark,
                                                                Double money,
                                                                String no) {

        String sourceRole = refundDto.getSourceRole();

        String targetRole = refundDto.getTargetRole();
        Long targetTenantId = refundDto.getTargetTenantId();
        Long targetMerchantId = refundDto.getTargetMerchantId();

        targetTenantId = targetTenantId == null ? 0L : targetTenantId;
        targetMerchantId = targetMerchantId == null ? 0L : targetMerchantId;

        return MerchantBillCreateParam.builder()
            .bizNo(no)
            .tradeNo(refundDto.getTradeNo())
            .orderName(orderName)
            .sourceRole(refundDto.getSourceRole())
            .sourceTenantId(refundDto.getSourceTenantId())
            .sourceMerchantId(refundDto.getSourceMerchantId())
            .sourceUserId(0L)
            .operateUserId(operateUserId)
            .operateRole(PaymentRoleEnum.TENANT.getCode())
            .operateTargetRole(refundDto.getTargetRole())
            .remarks(remark)
            .targetRole(targetRole)
            .targetTenantId(targetTenantId)
            .targetMerchantId(targetMerchantId)
            .targetUserId(0L)
            .paymentMethod(refundDto.getPayMethod())
            .totalMoney(disposeMoney.getDisposeMerchantBalance())
            .freeGold(disposeMoney.getDisposeMerchantGift())
            .changedBalance(BigDecimal.valueOf(money))
            .changedGift(com.ziguang.base.support.DoubleUtils.number2BigDecimal(0d))
            .purposeType(PurposeType.REFUND.getCode())
            .detailPurpose(DetailPurpose.ADMIN_EDIT_MERCHANT_FBA_CARRIAGE_REFUND.getCode())
            .disposeTenantBalance(BigDecimal.ZERO)
            .disposeTenantGift(BigDecimal.ZERO)
            .disposeMerchantBalance(disposeMoney.getDisposeMerchantBalance())
            .disposeMerchantGift(disposeMoney.getDisposeMerchantGift())
            .build();
    }

    public void addOrderRemark(Long orderId, User user) {
        String remark = getFbaCarriagePayRemark(orderId);
        orderService.addPayFbaCarriageRemark(user, orderId, remark, Platform.ADMIN);
    }

    public String getFbaCarriagePayRemark(Long orderId) {
        String remark = "";
        OrderFbaCarriageDetailRespDto orderFbaCarriageDetailRespDto = orderFbaCarriageDetailFeign.getById(orderId);
        if (null != orderFbaCarriageDetailRespDto) {
            BigDecimal currentCarriageAmount = orderFbaCarriageDetailRespDto.getCurrentCarriageAmount();
            BigDecimal totalAmount = orderFbaCarriageDetailRespDto.getTotalAmount();
            if (NumberUtil.isGreater(totalAmount, BigDecimal.ZERO)) {
                remark = String.format(fbaCarriagePayRemarkFormatPay, currentCarriageAmount, totalAmount);
            } else if (NumberUtil.isLess(totalAmount, BigDecimal.ZERO)) {
                remark = String.format(fbaCarriagePayRemarkFormatRefund, currentCarriageAmount, totalAmount);
            } else {
                remark = String.format(fbaCarriagePayRemarkFormat, currentCarriageAmount);
            }
        } else {
            OrderFba orderFba = orderFbaService.findByOrderId(orderId);
            Double carriageMoney = orderFba.getCarriageMoney();
            remark = String.format(fbaCarriagePayRemarkFormat, carriageMoney);
        }
        return remark;
    }

    public void resetBox(Long orderId, Long userId) {
        orderFbaFeign.resetBox(orderId, userId);
    }

    public List<FbaBoxSimpleDto> boxOutline(FbaBoxSimpleParam dto) {
        return orderFbaFeign.boxOutline(dto);
    }
}
