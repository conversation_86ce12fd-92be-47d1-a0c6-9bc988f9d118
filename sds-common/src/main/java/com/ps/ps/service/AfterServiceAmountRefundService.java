package com.ps.ps.service;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.ps.dto.OrderRefundParam;
import com.ps.ps.feign.payment.TransactionFeign;
import com.ps.support.utils.OrderCodeUtil;
import com.sdsdiy.common.base.helper.DoubleUtils;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.RefundDto;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentapi.param.TransactionEntryParam;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.UserAccountBalanceResp;
import com.ziguang.base.model.OrderItem;
import com.ziguang.base.model.RefundRecord;
import com.ziguang.base.model.RefundRecordItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AfterServiceAmountRefundService {

    private final ProductService productService;
    private final OrderItemService orderItemService;
    private final TransactionFeign transactionFeign;

    public RefundDto refundToAccount(OrderRefundParam orderRefundParam,
                                     RefundRecord refundRecord,
                                     List<RefundRecordItem> refundRecordItems,
                                     UserAccountBalanceResp userAccountBalanceResp) {

        List<RefundParam> refundParams = Lists.newArrayList();
        List<TransactionEntryParam> transactionEntryList = new ArrayList<>();
        String refundTitle = this.buildRefundTitle(refundRecordItems);
        //租户退给商户
        if (NumberUtils.greaterZero(refundRecord.getBalance()) || NumberUtils.greaterZero(refundRecord.getFreeGold())) {
            //商户退款
            RefundParam refundParam = this.genRefundTenantToMerchant(orderRefundParam, userAccountBalanceResp, refundRecord.getBalance(), refundRecord.getFreeGold());
            refundParams.add(refundParam);
            transactionEntryList.add(genTenantTransactionEntryParams(refundParam.getBizNo(), refundTitle, refundRecord.getBalance(), refundRecord.getFreeGold()));
        }
        //租户之间退款
        if (NumberUtils.greaterZero(refundRecord.getTenantBalance()) || NumberUtils.greaterZero(refundRecord.getTenantFreeGold())) {
            //租户退款
            RefundParam refundParam = this.genRefundTenantToTenant(orderRefundParam, refundRecord.getTenantBalance(), refundRecord.getTenantBalance());
            refundParams.add(refundParam);
            transactionEntryList.add(genTenantTransactionEntryParams(refundParam.getBizNo(), refundTitle, refundRecord.getTenantBalance(), refundRecord.getTenantBalance()));
        }
        if (refundParams.isEmpty()) {
            log.warn("无有效退款金额，跳过退款流程，退款记录: {}", refundRecord.getId());
        }
        MultiTransactionCreateParam param = new MultiTransactionCreateParam();
        param.setTransactionEntryList(transactionEntryList);
        param.setRefundList(refundParams);
        RefundDto refund = transactionFeign.createRefund(param);
        transactionFeign.operateRefund(refund.getId());
        return refund;
    }

    /**
     * 租户退给商户钱
     */
    private RefundParam genRefundTenantToMerchant(OrderRefundParam orderRefundParam,
                                                  UserAccountBalanceResp userAccountBalanceResp,
                                                  Double balance, Double freeGold) {

        MerchantRespDto merchant = orderRefundParam.getMerchant();
        RefundParam refundParam = new RefundParam();
        refundParam.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        refundParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());

        refundParam.setOperateRole(orderRefundParam.getRefundOperatorRole());
        refundParam.setOperateUserId(orderRefundParam.getUserId());
        refundParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        refundParam.setOperateTargetRoleId(merchant.getId());

        refundParam.setBalance(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(DoubleUtils.number2BigDecimal(freeGold, BigDecimal.ROUND_CEILING));
        refundParam.setPayMethod(orderRefundParam.getPaymentMethod());
        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        refundParam.setSubject("退款");

        refundParam.setTargetTenantId(orderRefundParam.getMerchant().getTenantId());
        refundParam.setTargetMerchantId(orderRefundParam.getMerchant().getId());
        refundParam.setTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        refundParam.setTargetUserId(orderRefundParam.getUserId());

        refundParam.setSourceMerchantId(0L);
        refundParam.setSourceTenantId(refundParam.getSourceTenantId());
        refundParam.setSourceRole(PaymentRoleEnum.TENANT.getCode());

        if (MerchantUserAccountConstant.ALLOCATION.equalsIgnoreCase(userAccountBalanceResp.getBalanceType())) {
            refundParam.setTargetUserId(userAccountBalanceResp.getId());
            refundParam.setBalanceType(BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType());
        } else {
            refundParam.setBalanceType(BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType());
        }
        refundParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_ORDER.getCode());
        return refundParam;
    }


    private RefundParam genRefundTenantToTenant(OrderRefundParam orderRefundParam, Double balance, Double freeGold) {
        MerchantRespDto merchant = orderRefundParam.getMerchant();

        RefundParam refundParam = new RefundParam();
        refundParam.setPayType(TransactionPayTypeEnum.SUB.getValue())
                .setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus())
                .setOperateRole(PaymentRoleEnum.TENANT_SUP.getCode())
                .setOperateUserId(orderRefundParam.getRefundUserId())
                .setOperateTargetRole(PaymentRoleEnum.TENANT_DIS.getCode())// 影响了谁
                .setOperateTargetRoleId(0L);

        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        refundParam.setBalance(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(DoubleUtils.number2BigDecimal(freeGold, BigDecimal.ROUND_CEILING));
        refundParam.setPayMethod(PaymentMethodEnum.BALANCE.getCode());
        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        refundParam.setSubject("退款");

        refundParam.setSourceTenantId(orderRefundParam.getProductTenantId());
        refundParam.setSourceMerchantId(0L);
        refundParam.setSourceRole(PaymentRoleEnum.TENANT_SUP.getCode());

        refundParam.setTargetTenantId(merchant.getTenantId());
        refundParam.setTargetMerchantId(0L);
        refundParam.setTargetRole(PaymentRoleEnum.TENANT_DIS.getCode());
        refundParam.setTargetUserId(0L);

        refundParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_ORDER.getCode());
        return refundParam;
    }

    private static TransactionEntryParam genTenantTransactionEntryParams(String refundBizNo,
                                                                         String refundTitle,
                                                                         Double balance,
                                                                         Double freeGold) {

        TransactionEntryParam transactionEntryParam = new TransactionEntryParam();
        transactionEntryParam.setRefundBizNo(refundBizNo);
        transactionEntryParam.setBalance(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        transactionEntryParam.setBonus(DoubleUtils.number2BigDecimal(freeGold, BigDecimal.ROUND_CEILING));
        transactionEntryParam.setType(TransactionEntryTypeEnum.REFUND.getValue());
        transactionEntryParam.setTitle(refundTitle);
        return transactionEntryParam;
    }


    private String buildRefundTitle(List<RefundRecordItem> refundRecordItems) {
        if (CollectionUtil.isEmpty(refundRecordItems)) {
            log.warn("退款记录项为空，使用默认退款标题");
            return "退款：无商品信息";
        }
        // 提取所有订单项ID
        List<Long> orderItemIds = refundRecordItems.stream()
                .map(RefundRecordItem::getOrderItemId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (orderItemIds.isEmpty()) {
            return "退款：无有效订单项";
        }
        // 查询订单项并计算总数量、提取商品ID
        List<OrderItem> orderItems = orderItemService.findByIds(orderItemIds);
        if (CollectionUtil.isEmpty(orderItems)) {
            log.warn("未查询到订单项，orderItemIds: {}", orderItemIds);
            return "退款：订单项不存在";
        }
        int totalNum = orderItems.stream().mapToInt(OrderItem::getNum).sum();
        Long productId = orderItems.stream()
                .map(OrderItem::getProductId)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);

        // 获取商品名称
        String productName = "未知商品";
        if (productId != null) {
            List<String> productNames = productService.getName(productId);
            if (!CollectionUtil.isEmpty(productNames)) {
                productName = productNames.get(0);
            }
        }
        // 拼接标题
        return totalNum > 1
                ? String.format("退款：%s...等%d件商品", productName, totalNum)
                : String.format("退款：%s 1件商品", productName);
    }

}