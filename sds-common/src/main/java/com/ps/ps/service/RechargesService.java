package com.ps.ps.service;

import cn.hutool.json.JSONUtil;
import com.ps.base.service.TKBaseService;
import com.ps.exception.BusinessException;
import com.ps.ps.dao.*;
import com.ps.ps.feign.payment.TransactionFeign;
import com.ps.ps.feign.tenant.TenantPodFeign;
import com.ps.ps.feign.wallet.TenantWalletFeign;
import com.ps.support.utils.OrderCodeUtil;
import com.ps.tool.cbt.common.util.StringUtil;
import com.sdsdiy.common.base.constant.AmountConstant;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.dto.TenantWalletDto;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.userapi.dto.payment.PaymentReqDto;
import com.ziguang.base.dto.ActivityContentDto;
import com.ziguang.base.dto.ActivityDto;
import com.ziguang.base.dto.ActivityFreeeGoldDto;
import com.ziguang.base.dto.RechargesDto;
import com.ziguang.base.model.AdminConfigs;
import com.ziguang.base.model.MerchantActivity;
import com.ziguang.base.model.PlatformGoodOrder;
import com.ziguang.base.model.RechargesRecode;
import com.ziguang.base.support.contant.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
public class RechargesService extends TKBaseService<PlatformGoodOrderDao, PlatformGoodOrder> {

    @Autowired
    @Lazy
    private PlatformGoodOrderService platformGoodOrderService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private AdminConfigService adminConfigService;
    @Autowired
    private MerchantActivityDao merchantActivityDao;
    @Autowired
    private RechargesRecodeDao rechargesRecodeDao;
    @Autowired
    private MerchantDao merchantDao;
    @Autowired
    private ActivityContentDao activityContentDao;
    @Autowired
    private WebPushService webPushService;
    @Resource
    TenantWalletFeign tenantWalletFeign;
    @Resource
    TenantPodFeign tenantPodFeign;
    @Resource
    private TransactionFeign transactionFeign;


    @Transactional(rollbackFor = Exception.class)
    public PaymentDto recharge(RechargesDto rechargesDto, Long merchantId, Long userId,Long tenantId){
        if(rechargesDto.getTotalAmount()==null){
            throw new BusinessException("输入金额不能为空");
        }
        if (rechargesDto.getTotalAmount() < 20) {
            throw new BusinessException("请输入正确的金额");
        }
        if (!PaymentMethodEnum.needWaitCustomerPaid(rechargesDto.getPaymentMethod())) {
            throw new BusinessException("支付参数不正确");
        }
        return rechargesThirdPartyPay(merchantId, userId, tenantId, rechargesDto);
    }


    /**
     * 第三方支付,支付宝，拉卡拉
     */
    public PaymentDto rechargesThirdPartyPay(Long merchantId, Long userId, Long tenantId, RechargesDto rechargesDto) {
        RechargesRecode rechargesRecode = newRechargesRecode(rechargesDto, merchantId, userId, new ActivityFreeeGoldDto());
        rechargesRecodeDao.insertSelective(rechargesRecode);
        rechargesRecode.setId(rechargesRecodeDao.getLastInsertId());

        BigDecimal balance = BigDecimal.valueOf(rechargesRecode.getBalance());
        if (balance.compareTo(AmountConstant.MIN_ALI_PAY_AMOUNT) < 0) {
            throw new com.sdsdiy.common.base.exception.BusinessException("支付金额不可小于最小金额" + AmountConstant.MIN_ALI_PAY_AMOUNT + "元");
        }
        if (balance.compareTo(AmountConstant.MAX_ALI_PAY_AMOUNT) > 0) {
            throw new com.sdsdiy.common.base.exception.BusinessException("支付金额不可超出最大金额" + AmountConstant.MAX_ALI_PAY_AMOUNT + "元");
        }
        TenantWalletDto tenantWalletDto = tenantWalletFeign.get(tenantId);
        if (null == tenantWalletDto) {
            logger.info("租户钱包不存在，租户id：" + tenantId);
            throw new BusinessException("租户钱包不存在");
        }
        List<PaymentParam> paymentList = new ArrayList<>();
        if(tenantWalletDto.getOpenOnlinePay().equals(BasePoConstant.YES)){
            PaymentParam paymentParam = genPayMerchantToTenantForRecharge(merchantId, userId, tenantId, rechargesRecode, rechargesDto.getPaymentMethod());
            PaymentParam tenantPaymentParam = genPayTenantToSaasForRecharge(merchantId, userId, tenantId, rechargesRecode, rechargesDto.getPaymentMethod());
            paymentList.add(paymentParam);
            paymentList.add(tenantPaymentParam);
        }else{
            PaymentParam paymentParam = genPayMerchantToSaasForRecharge(merchantId, userId, tenantId, rechargesRecode, rechargesDto.getPaymentMethod());
            paymentList.add(paymentParam);
        }
        MultiTransactionCreateParam param=new MultiTransactionCreateParam();
        param.setPaymentList(paymentList);
        PaymentDto paymentDto = transactionFeign.createPayment(param);
        rechargesRecodeDao.updatePayment(rechargesRecode.getId(), paymentDto.getId());
        return paymentDto;
    }
    private PaymentReqDto getMerchantRechargePaymentParam(Long merchantId, Long userId, Long tenantId,
                                                          RechargesRecode rechargesRecode, Double totalAmount,
                                                          TenantWalletDto tenantWalletDto, String paymentMethod) {
        PaymentReqDto paymentReqDto = new PaymentReqDto();
        paymentReqDto.setMethod(paymentMethod);
        paymentReqDto.setBalance(BigDecimal.valueOf(rechargesRecode.getBalance()));
        paymentReqDto.setFreeGold(BigDecimal.ZERO);
        paymentReqDto.setSubject("商户充值");
        paymentReqDto.setBizNo(rechargesRecode.getNo());
        paymentReqDto.setTotalAmount(BigDecimal.valueOf(totalAmount));
        paymentReqDto.setSourceTenantId(tenantId);
        paymentReqDto.setSourceMerchantId(merchantId);
        paymentReqDto.setSourceUserId(userId);
        paymentReqDto.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setTargetMerchantId(0L);
        paymentReqDto.setTargetTenantId(getTargetTenantId(tenantWalletDto.getOpenOnlinePay(), tenantId));
        paymentReqDto.setTargetRole(getTargetRole(tenantWalletDto.getOpenOnlinePay()));
        paymentReqDto.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.NO_USE_BALANCE.getUsedType());
        paymentReqDto.setDetailPurpose(DetailPurpose.MERCHANT_ALIPAY_RECHARGE.getCode());
        paymentReqDto.setPurposeType(PurposeType.RECHARGE.getCode());
        paymentReqDto.setOperateUserId(userId);
        paymentReqDto.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setOperateTargetRoleId(merchantId);
        paymentReqDto.setAlipaySupportPayeeType(AlipaySupportPayeeTypeEnum.RECHARGE.getPayeeType());
        return paymentReqDto;
    }
    private PaymentParam genPayMerchantToTenantForRecharge(Long merchantId, Long userId, Long tenantId,
                                                           RechargesRecode rechargesRecode, String paymentMethod) {
        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setBizNo(rechargesRecode.getNo());
        paymentParam.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        paymentParam.setBillType(PaymentBillTypeEnum.DIRECT.getStatus());
        paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_ALIPAY_RECHARGE.getCode());
        paymentParam.setPurposeType(PurposeType.RECHARGE.getCode());
        paymentParam.setMethod(paymentMethod);
        paymentParam.setBalance(BigDecimal.valueOf(rechargesRecode.getBalance()));
        paymentParam.setBonus(BigDecimal.ZERO);
        paymentParam.setTitle("商户充值");

        paymentParam.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setSourceTenantId(tenantId);
        paymentParam.setSourceMerchantId(merchantId);
        paymentParam.setSourceUserId(userId);

        paymentParam.setTargetRole(PaymentRoleEnum.TENANT.getCode());
        paymentParam.setTargetMerchantId(0L);
        paymentParam.setTargetTenantId(tenantId);

        paymentParam.setBalanceType(BalanceUsedType.NO_USE_BALANCE.getUsedType());

        paymentParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateUserId(userId);
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(merchantId);
        return paymentParam;
    }
    private PaymentParam genPayMerchantToSaasForRecharge(Long merchantId, Long userId, Long tenantId,
                                                         RechargesRecode rechargesRecode, String paymentMethod) {
        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        paymentParam.setBizNo(rechargesRecode.getNo());
        paymentParam.setBillType(PaymentBillTypeEnum.DIRECT.getStatus());
        paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_ALIPAY_RECHARGE.getCode());
        paymentParam.setPurposeType(PurposeType.RECHARGE.getCode());
        paymentParam.setMethod(paymentMethod);
        paymentParam.setBalanceType(BalanceUsedType.NO_USE_BALANCE.getUsedType());
        paymentParam.setBalance(BigDecimal.valueOf(rechargesRecode.getBalance()));
        paymentParam.setBonus(BigDecimal.ZERO);
        paymentParam.setTitle("商户充值");

        paymentParam.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setSourceTenantId(tenantId);
        paymentParam.setSourceMerchantId(merchantId);
        paymentParam.setSourceUserId(userId);

        paymentParam.setTargetRole(PaymentRoleEnum.SAAS.getCode());
        paymentParam.setTargetMerchantId(0L);
        paymentParam.setTargetTenantId(TenantCommonConstant.SAAS_TENANT_ID);

        paymentParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateUserId(userId);
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(merchantId);
        return paymentParam;
    }

    private PaymentParam genPayTenantToSaasForRecharge(Long merchantId,Long userId,Long tenantId,RechargesRecode rechargesRecode,
                                                       String paymentMethod) {
        PaymentParam paymentReqDto = new PaymentParam();
        paymentReqDto.setPayType(TransactionPayTypeEnum.SUB.getValue());
        paymentReqDto.setBillType(PaymentBillTypeEnum.DIRECT.getStatus());
        paymentReqDto.setDetailPurpose(DetailPurpose.MERCHANT_ALIPAY_RECHARGE.getCode());
        paymentReqDto.setPurposeType(PurposeType.RECHARGE.getCode());
        paymentReqDto.setBalanceType(BalanceUsedType.TENANT_BALANCE.getUsedType());
        paymentReqDto.setMethod(PaymentMethodEnum.BALANCE.getCode());
        paymentReqDto.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        paymentReqDto.setBalance(BigDecimal.valueOf(rechargesRecode.getBalance()));
        paymentReqDto.setBonus(BigDecimal.ZERO);
        paymentReqDto.setTitle("商户充值");

        paymentReqDto.setSourceRole(PaymentRoleEnum.TENANT.getCode());
        paymentReqDto.setSourceTenantId(tenantId);
        paymentReqDto.setSourceMerchantId(0L);
        paymentReqDto.setSourceUserId(0L);

        paymentReqDto.setTargetRole(PaymentRoleEnum.SAAS.getCode());
        paymentReqDto.setTargetMerchantId(0L);
        paymentReqDto.setTargetTenantId(TenantCommonConstant.SAAS_TENANT_ID);

        paymentReqDto.setOperateRole(PaymentRoleEnum.SYSTEM.getCode());
        paymentReqDto.setOperateUserId(userId);
        paymentReqDto.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentReqDto.setOperateTargetRoleId(merchantId);
        return paymentReqDto;
    }

    public Long getTargetTenantId(Integer openOnlinePay, Long tenantId) {
        if (isOpenOnlinePay(openOnlinePay)) {
            return tenantId;
        }
        return TenantCommonConstant.SAAS_TENANT_ID;
    }

    public String getTargetRole(Integer openOnlinePay) {
        if (isOpenOnlinePay(openOnlinePay)) {
            return PaymentRoleEnum.TENANT.getCode();
        }
        return PaymentRoleEnum.SAAS.getCode();
    }

    private boolean isOpenOnlinePay(Integer openOnlinePay) {
        return null != openOnlinePay && openOnlinePay.equals(BasePoConstant.YES);
    }


    public RechargesRecode newRechargesRecode(RechargesDto rechargesDto,Long merchantId,Long userId,ActivityFreeeGoldDto activityFreeeGoldDto){
        RechargesRecode rechargesRecode = new RechargesRecode();
        rechargesRecode.setNo(OrderCodeUtil.getOrderIdByUUId());
        rechargesRecode.setActivityId(activityFreeeGoldDto.getActivityId());
        rechargesRecode.setBalance(rechargesDto.getTotalAmount());
        rechargesRecode.setFreeGold(activityFreeeGoldDto.getFreeGold());
        rechargesRecode.setUserId(userId);
        rechargesRecode.setMerchantId(merchantId);
        rechargesRecode.setCreateTime(System.currentTimeMillis());
        rechargesRecode.setStatus(OrderStatus.UNPAIN.getStatus());
        rechargesRecode.setRemark(rechargesDto.getRemark());
        rechargesRecode.setType(rechargesDto.getType());
        return rechargesRecode;
    }

    public ActivityFreeeGoldDto getFreeGold(Double totalAmount, Long merchantId) {
        if (totalAmount<20){
            throw new BusinessException("支付金额大于20");
        }
        Double freeGold = 0D;
        ActivityFreeeGoldDto activityFreeeGoldDto = new ActivityFreeeGoldDto();
        AdminConfigs adminConfigs = adminConfigService.getCodeValue("activity_code");
        if(adminConfigs!=null){
            ActivityDto activityDto = JSONUtil.toBean(adminConfigs.getValue(),ActivityDto.class);
            if(!StringUtil.isEmpty(activityDto.getCode())){
                ActivityDto activity = activityService.getActivity(activityDto.getCode(),System.currentTimeMillis());
                if(activity.getId()!=null){
                    activityFreeeGoldDto.setActivityId(activity.getId());
                    List<ActivityContentDto> contentList = activityContentDao.getActivityContent(activity.getId(),1);
                    if(contentList.size()<0){
                        activityFreeeGoldDto.setFreeGold(freeGold);
                        return activityFreeeGoldDto;
                    }
//                    if(totalAmount<activity.getAppointAmount()){
//                        activityFreeeGoldDto.setFreeGold(freeGold);
//                        return activityFreeeGoldDto;
//                    }
                    //活动存在
                    List<MerchantActivity> list = merchantActivityDao.findByMerchantId(merchantId,activity.getId(), PaymentType.RECHARGE.getStatus());
                    if(activity.getFreeTime()!=0){
                        if (list.size()>=activity.getFreeTime()){
                            activityFreeeGoldDto.setFreeGold(freeGold);
                            return activityFreeeGoldDto;
                        }
                    }
                    List<ActivityContentDto> contentItems = contentList;
                    if(contentItems.size()>0){
                        for (ActivityContentDto activityContentDto:contentItems){
                            Double freeGoldCount = Math.floor(totalAmount/activityContentDto.getAppointAmount());
                            if(freeGoldCount<=0){
                                break;
                            }else{
                                freeGold = activityContentDto.getFreeAmount();
                            }
                        }
                    }
                    activityFreeeGoldDto.setFreeGold(freeGold);
                }
            }
        }
        return activityFreeeGoldDto;
    }

    public void paySuccess(Long paymentId) {
        rechargesRecodeDao.updateStatus(paymentId, OrderStatus.PAIN.getStatus());
        // 更新merchant的值
        RechargesRecode rechargesRecode = rechargesRecodeDao.selectOneByPaymentId(paymentId);
        merchantDao.updateRecharge(rechargesRecode.getMerchantId(),rechargesRecode.getBalance(),rechargesRecode.getFreeGold());
        webPushService.pushMsgToWebByMerchantId(MsgModule.MERCHANT_PROFILE, rechargesRecode.getMerchantId());
        //更新我的账单
        String orderType = PurposeNameStatus.ALI_RECHARGE.getSpecificPurpose();
        String orderName = PurposeNameStatus.ALI_RECHARGE.getOrderName();
        PlatformGoodOrder platformGoodOrder = new PlatformGoodOrder();
        platformGoodOrder.setTotalAmount(rechargesRecode.getBalance());
        platformGoodOrder.setUserId(rechargesRecode.getUserId());
        platformGoodOrder.setMerchantId(rechargesRecode.getMerchantId());
        platformGoodOrder.setUsableFreeGold(rechargesRecode.getFreeGold());
        platformGoodOrder.setNo(rechargesRecode.getNo());
        platformGoodOrder.setPaymentMethod(PaymentMethod.ALIPAY.getValue());
        platformGoodOrderService.insertMyBill(platformGoodOrder,orderName,orderType, purposeTypeStatus.RECHARGE.getStatus(),platformGoodOrder.getMerchantId());
        if(!(rechargesRecode.getActivityId()==null || rechargesRecode.getActivityId()==0 || rechargesRecode.getFreeGold()==0)){
            addActivity(rechargesRecode.getMerchantId(),rechargesRecode.getUserId(),rechargesRecode.getActivityId());
        }
    }

    public void addActivity(Long merchantId,Long userId,Long activityId){
        MerchantActivity merchantActivity = new MerchantActivity();
        merchantActivity.setActivityId(activityId);
        merchantActivity.setMerchantId(merchantId);
        merchantActivity.setUserId(userId);
        merchantActivity.setStatus(1);
        merchantActivity.setActivityPayType(PaymentType.RECHARGE.getStatus());
        merchantActivityDao.insertSelective(merchantActivity);
    }
}
