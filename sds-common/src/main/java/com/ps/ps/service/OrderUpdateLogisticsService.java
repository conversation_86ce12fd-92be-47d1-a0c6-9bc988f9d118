package com.ps.ps.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ps.bo.order.OrderPutLogisticsBO;
import com.ps.dto.BatchPutLogisticsReqDto;
import com.ps.dto.PutLogisticsRespDto;
import com.ps.exception.BatchUpdateLogisticsStatusException;
import com.ps.exception.BusinessException;
import com.ps.ps.dao.OrderImportExtraInfoDao;
import com.ps.ps.dao.PickUpLogisticsInfoDao;
import com.ps.ps.feign.AdminPrepaidFeign;
import com.ps.ps.feign.MerchantFeign;
import com.ps.ps.feign.OrderFeign;
import com.ps.ps.feign.issuingbay.IssuingBayAreaFeign;
import com.ps.ps.feign.logistics.LogisticsChannelFeign;
import com.ps.ps.feign.logistics.LogisticsExpensesFeign;
import com.ps.ps.feign.logistics.TenantLogisticsFeign;
import com.ps.ps.feign.order.*;
import com.ps.ps.feign.payment.MerchantBillFeign;
import com.ps.ps.feign.payment.PaymentAliFeign;
import com.ps.ps.feign.payment.RefundFeign;
import com.ps.ps.feign.payment.TransactionFeign;
import com.ps.ps.feign.user.MerchantUserAccountFeign;
import com.ps.ps.service.linstener.OrderEventService;
import com.ps.ps.service.payment.MerchantUserAccountService;
import com.ps.support.Assert;
import com.ps.support.utils.MathUtils;
import com.ps.support.utils.OrderCodeUtil;
import com.ps.support.utils.StringUtils;
import com.ps.tool.cbt.common.util.StringUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.entity.dto.BaseValueChangeDTO;
import com.sdsdiy.common.base.enums.EuropeUnionEnum;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayAreaRespDto;
import com.sdsdiy.logisticsapi.constant.CountryExpressInfoNewConstant;
import com.sdsdiy.logisticsapi.constant.LogisticsConstant;
import com.sdsdiy.logisticsapi.dto.BatchLogisticsFreightReqDto;
import com.sdsdiy.logisticsapi.dto.BatchLogisticsFreightRespDto;
import com.sdsdiy.logisticsapi.dto.LogisticsAddressReqDto;
import com.sdsdiy.logisticsapi.dto.LogisticsFreightProductReqDto;
import com.sdsdiy.logisticsapi.dto.base.LogisticsChannelRespDto;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.LogisticsAndLogisticsSourceResp;
import com.sdsdiy.logisticsapi.enums.LogisticsCodeIdEnum;
import com.sdsdiy.logisticsdata.dto.base.LogisticsRespDto;
import com.sdsdiy.logisticsdata.util.CarriageAmountUtil;
import com.sdsdiy.orderapi.constant.*;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.constant.event.message.FbaOrderTaskLogisticsServiceAmountResetEventMessage;
import com.sdsdiy.orderapi.constant.event.message.carriage.OrderDeclarationApplyCarriageNoMessage;
import com.sdsdiy.orderapi.constant.event.message.customer.CustomerModifyPaidOrderMessage;
import com.sdsdiy.orderapi.constant.event.message.customer.CustomerModifyUnpaidOrderMessage;
import com.sdsdiy.orderapi.dto.AdminPrepaidRespDto;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderapi.dto.declaration.OrderProductDeclarationResp;
import com.sdsdiy.orderapi.dto.order.OrderEsSyncSqsMsg;
import com.sdsdiy.orderapi.dto.order.OrderTailLogisticsReqDto;
import com.sdsdiy.orderapi.dto.order.OrderTailLogisticsRespDto;
import com.sdsdiy.orderapi.dto.tenant.TenantQuotaDto;
import com.sdsdiy.orderapi.util.DeclarationServiceAmountUtil;
import com.sdsdiy.orderdata.bo.OrderPutLogisticsUpdateParcelBO;
import com.sdsdiy.orderdata.constant.order.OrderExtendKeyEnum;
import com.sdsdiy.orderdata.constant.order.PlatformOrderExtendValueEnum;
import com.sdsdiy.orderdata.dto.*;
import com.sdsdiy.orderdata.dto.logistics.OrderLogisticsHistoryAddDTO;
import com.sdsdiy.orderdata.dto.msg.OrderRefreshMsg;
import com.sdsdiy.orderdata.dto.msg.OrderResetFactoryOrderMessageDTO;
import com.sdsdiy.orderdata.dto.order.update.OrderLogisticsUpdateBatchParam;
import com.sdsdiy.orderdata.dto.order.update.OrderLogisticsUpdateItemParam;
import com.sdsdiy.orderdata.dto.order.update.OrderLogisticsUpdateParam;
import com.sdsdiy.orderdata.dto.order.update.TemuOnlineLogisticsCheckDto;
import com.sdsdiy.orderdata.enums.DeliveryTypeEnum;
import com.sdsdiy.orderdata.enums.OrderPutLogisticsUpdateParcelOperateTypeEnum;
import com.sdsdiy.orderdata.msg.OrderPutLogisticsUpdateParcelMsg;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.dto.RefundDto;
import com.sdsdiy.paymentapi.param.*;
import com.sdsdiy.paymentapi.param.bill.MerchantBillCreateParam;
import com.sdsdiy.paymentapi.param.bill.MerchantBillsCreateParam;
import com.sdsdiy.paymentapi.util.PaymentParamUtil;
import com.sdsdiy.productapi.dto.customsDeclaration.OrderDeclarationDto;
import com.sdsdiy.productapi.dto.product.ProductPriceResp;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.constant.enums.DistributionProductLogisticsSourceEnum;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.UserAccountBalanceResp;
import com.ziguang.base.dto.BatchPutLogisticsOrderItemDto;
import com.ziguang.base.dto.BatchPutLogisticsOrderOrderDto;
import com.ziguang.base.dto.PutLogisticsInfoDto;
import com.ziguang.base.model.*;
import com.ziguang.base.support.DoubleUtils;
import com.ziguang.base.support.contant.OrderOriginType;
import com.ziguang.base.support.contant.*;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.orderdata.constant.order.PlatformOrderExtendValueEnum.TIKTOK_ONLINE_LOGISTICS;


@Service
@EnableAsync
@Log4j2
@RequiredArgsConstructor
public class OrderUpdateLogisticsService {
    private final OrderUpdateCheckFeign orderUpdateCheckFeign;
    private final PlatformOrderItemExtendFeign platformOrderItemExtendFeign;
    private final PlatformOrderExtendFeign platformOrderExtendFeign;
    private final OrderTailLogisticsFeign orderTailLogisticsFeign;
    private final LogisticsChannelFeign logisticsChannelFeign;


    @Resource
    @Lazy
    OrderService orderService;
    @Resource
    AfterCommitExecutor afterCommitExecutor;

    @Resource
    MerchantUserAccountFeign merchantUserAccountFeign;
    @Resource
    TenantLogisticsFeign tenantLogisticsFeign;
    @Resource
    @Lazy
    private MerchantService merchantService;

    @Resource
    OrderAmountFeign orderAmountFeign;

    @Autowired
    private OrderEventService orderEventService;

    @Resource
    MerchantBillFeign merchantBillFeign;
    @Resource
    LogisticsService logisticsService;

    @Resource
    LogisticsExpensesFeign logisticsExpensesFeign;

    @Resource
    MerchantFeign merchantFeign;

    @Autowired
    BatchUpdateLogisticsService batchUpdateLogisticsService;
    @Autowired
    BatchUpdateLogisticsOrderService batchUpdateLogisticsOrderService;
    @Autowired
    BatchUpdateLogisticsOrderItemService batchUpdateLogisticsOrderItemService;

    @Resource
    MerchantUserAccountService merchantUserAccountService;

    @Resource
    PaymentAliFeign paymentFeign;
    @Resource
    RefundFeign refundFeign;

    @Resource
    OrderImportExtraInfoDao orderImportExtraInfoDao;


    @Resource
    PickUpLogisticsInfoDao pickUpLogisticsInfoDao;

    @Resource
    OrderFeign orderFeign;
    @Resource
    AdminPrepaidFeign adminPrepaidFeign;
    @Resource
    TenantLogisticsOrderFeign tenantLogisticsOrderFeign;
    @Resource
    IssuingBayAreaFeign issuingBayAreaFeign;
    @Resource
    private OrderDeclarationFeign orderDeclarationFeign;

    @Resource
    private OrderProductDeclarationFeign orderProductDeclarationFeign;
    @Resource
    private OrderLogisticsHistoryFeign orderLogisticsHistoryFeign;
    @Resource
    private OrderImportExtraInfoFeign orderImportExtraInfoFeign;
    @Resource
    private OrderExtendInfoFeign orderExtendInfoFeign;
    @Autowired
    private AddressService addressService;
    @Autowired
    private TransactionFeign transactionFeign;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    public PaymentDto batchUpdatLogisticsPayment(Long merchantId, Long userId, BatchPutLogisticsReqDto param, Boolean noPayPermission) {
        List<Long> ids = Lists.newArrayList();
        for (Order order : param.getOrders()) {
            Assert.validateNull(order.getId(), "订单id必传");
            Assert.validateNull(order.getTransactionCode(), "订单transactionCode必传");
            ids.add(order.getId());
        }
        Long logisticsId = param.getLogisticsId();
        Assert.validateNull(logisticsId, "物流id必传");
        Assert.validateNull(param.getPaymentMethod(), "支付方式必传");

        Address address = null;
        List<Long> addressAreaChangeOrderIds = Lists.newArrayList();
        if (param.getAddress() != null) {
            address = new Address();
            BeanUtil.copyProperties(param.getAddress(), address);
            address.setMerchantId(merchantId);
            address = orderService.getOrSaveAddress(address);

            orderService.formatAddress(param.getOrders());
            for (Order order : param.getOrders()) {
                boolean addressAreaChange = addressService.isAddressAreaChange(order.getAddress(), address);
                if (addressAreaChange) {
                    addressAreaChangeOrderIds.add(order.getId());
                }
            }
        }

        PutLogisticsRespDto putLogisticsRespDto = this.getPutLogisticsInfos(merchantId, userId, address, logisticsId, param.getTailLogisticsChannelId(), ids);
        Assert.validateBool(putLogisticsRespDto.getValid(), "没有可修改的订单，请确认订单信息是否发生变动，并刷新页面");
        //已支付
        if (putLogisticsRespDto.getBoolPain()) {
            //判断差额是否大于0，且没有支付权限
            orderService.checkPayPermission(putLogisticsRespDto, noPayPermission);
        }
        // 批量修改地址，地址国家或省份改变，则取消子单。用于取消子单进度显示
        formatDelItemIds(addressAreaChangeOrderIds, putLogisticsRespDto);

        PaymentDto paymentDto = this.batchUpdatLogisticsPaymentNew(merchantId, userId, param, putLogisticsRespDto);
        if (!addressAreaChangeOrderIds.isEmpty()) {
            rocketMQTemplate.sendNormalAfterCommit(
                    RocketMqTopicConst.EVENT_ORDER,
                    OrderTagConst.ORDER_RESET_FACTORY_ORDER_TOPIC,
                    new OrderResetFactoryOrderMessageDTO().setOrderIds(addressAreaChangeOrderIds));
        }
        return paymentDto;
    }

    private static void formatDelItemIds(List<Long> addressAreaChangeOrderIds, PutLogisticsRespDto putLogisticsRespDto) {
        if (CollUtil.isEmpty(addressAreaChangeOrderIds)) {
            return;
        }
        List<PutLogisticsInfoDto> putLogisticsInfoDtos = putLogisticsRespDto.getPutLogisticsInfoDtos();
        for (PutLogisticsInfoDto putLogisticsInfoDto : putLogisticsInfoDtos) {
            if (!addressAreaChangeOrderIds.contains(putLogisticsInfoDto.getOrderId())) {
                continue;
            }
            Order order = putLogisticsInfoDto.getOrder();
            // 只有 待确认/搁置中 且设计完成的订单能改
            boolean canReset = com.sdsdiy.orderdata.enums.OrderStatus.canResetFactoryOrder(order.getStatus())
                    && EnumOrderDesignStatus.FINISH.equalsCode(order.getDesignStatus());
            if (canReset) {
                List<Long> itemIds = order.getItems().stream()
                        .filter(i -> !com.sdsdiy.orderdata.enums.OrderStatus.CANCEL_STATUS_LIST.contains(i.getStatus()))
                        .filter(i -> !EnumOrderItemTransferType.TRANSFER_TYPE_LIST.contains(i.getTransferType()))
                        .filter(i -> !i.getBeResendForLose())
                        .map(i -> i.getId()).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(itemIds)) {
                    putLogisticsInfoDto.setDelFactoryOrderItemIds(itemIds);
                }
            }
        }
    }

    @NotNull
    private static List<Long> getLogisticsChangeOrderIds(Long logisticsId, List<Long> orderIds, Map<Long, Order> orderMap) {
        List<Long> logisticsChangeOrderIds = Lists.newArrayList();
        for (Long orderId : orderIds) {
            Order order = orderMap.get(orderId);
            if (!logisticsId.equals(order.getLogisticsId())) {
                logisticsChangeOrderIds.add(logisticsId);
            }
        }
        return logisticsChangeOrderIds;
    }

    private OrderPutLogisticsBO buildOrderPutLogisticsBO(List<PutLogisticsInfoDto> putLogisticsInfoDtos
            , BatchUpdateLogistics batchUpdateLogistics) {
        List<Long> orderIds = ListUtil.toValueList(putLogisticsInfoDtos, PutLogisticsInfoDto::getOrderId);
        List<Order> orderList = orderService.findByIds(orderIds);
        orderList = orderService.format(orderList);
        List<OrderAmountRespDTO> oldOrderAmounts = orderAmountFeign.findByIds(IdsSearchHelper.of(orderIds));

        Map<Long, Order> orderMap = Maps.newHashMap();
        Map<Long, Integer> orderOldDeliveryTypeMap = new HashMap<>();
        Map<Long, Long> orderServiceProviderIdMap = new HashMap<>();
        Map<Long, OrderAmountRespDTO> oldOrderAmountMap = oldOrderAmounts.stream().collect(Collectors.toMap(OrderAmountRespDTO::getId, Function.identity()));
        for (Order order : orderList) {
            orderMap.put(order.getId(), order);
            DeliveryTypeEnum deliveryTypeEnum = DeliveryTypeEnum.matchOrder(order.getOriginType(), order.getLogisticsCodeId());
            orderOldDeliveryTypeMap.put(order.getId(), deliveryTypeEnum.getCode());
            if (Objects.nonNull(order.getLogistics())) {
                orderServiceProviderIdMap.put(order.getId(), order.getLogistics().getServiceProviderId());
            }
        }
        List<OrderTailLogisticsRespDto> tailLogisticsList = orderTailLogisticsFeign.findDtoByOrderIds(BaseListDto.of(orderIds));
        Map<Long, OrderTailLogisticsRespDto> tailLogisticsMap = ListUtil.toMap(OrderTailLogisticsRespDto::getOrderId, tailLogisticsList);

        List<Order> noPaidOrders = orderList.stream().filter(o -> OrderStatus.noPain(o.getStatus())).collect(Collectors.toList());
        LogisticsAndLogisticsSourceResp logisticsSourceResp = null;
        if (CollUtil.isNotEmpty(noPaidOrders)) {
            Long tenantId = orderList.get(0).getTenantId();
            //租户物流
            BaseListReqDto baseListReqDto = new BaseListReqDto();
            baseListReqDto.setIdList(Lists.newArrayList(batchUpdateLogistics.getLogitsicsId()));
            Map<Long, LogisticsAndLogisticsSourceResp> logisticsIdAndLogisticsSourceMap = tenantLogisticsFeign.getLogisticsAndLogisticsSource(tenantId, baseListReqDto);
            logisticsSourceResp = logisticsIdAndLogisticsSourceMap.get(batchUpdateLogistics.getLogitsicsId());
        }

        OrderPutLogisticsBO bo = new OrderPutLogisticsBO();
        bo.setOrderIds(orderIds).setOrderMap(orderMap)
                .setOldOrderDeliveryTypeMap(orderOldDeliveryTypeMap)
                .setOldOrderServiceProviderIdMap(orderServiceProviderIdMap)
                .setOldOrderAmountMap(oldOrderAmountMap)
                .setOldOrderTailLogisticsMap(tailLogisticsMap)
                .setLogisticsSourceResp(logisticsSourceResp);
        return bo;
    }

    /**
     * 支付成功 或不需支付修改地址 ，应该使用事务，但外部都有事务所以暂时不用了
     *
     * @param paymentMethod 支付方式，未支付传null
     */
    public List<Order> batchUpdateLogistics(PaymentPayRefund paymentPayRefund, MerchantBillDisposeMoney merchantBillDisposeMoney,
                                            BatchUpdateLogistics batchUpdateLogistics,
                                            List<PutLogisticsInfoDto> putLogisticsInfoDtos, String paymentMethod,
                                            Long userId, Map<Long, Boolean> updateCarriageNoMap, List<OrderPutLogisticsUpdateParcelBO> orderPutLogisticsUpdateParcelBOs) {
        if (CollectionUtils.isEmpty(putLogisticsInfoDtos)) {
            throw new BatchUpdateLogisticsStatusException("没有可支持的物流");
        }
        Long merchantId = batchUpdateLogistics.getMerchantId();
        Long logisticsId = batchUpdateLogistics.getLogitsicsId();
        Long batchUpdateLogisticsId = batchUpdateLogistics.getId();
        boolean isPayment = paymentMethod != null;
        List<String> noPainStatus = Lists.newArrayList(com.sdsdiy.orderdata.enums.OrderStatus.UNPAIN.name(), com.sdsdiy.orderdata.enums.OrderStatus.NONE.name());
        OrderPutLogisticsBO putLogisticsBO = buildOrderPutLogisticsBO(putLogisticsInfoDtos, batchUpdateLogistics);

        Map<Long, Order> orderMap = putLogisticsBO.getOrderMap();

        //logistics最好由外部传入
        Logistics logistics = logisticsService.findLogisticById(logisticsId);
        boolean newLogisticsIsOnlineLogistics = LogisticsConstant.isOnlineLogistics(logistics.getServiceProviderId());

        Boolean updateSuccess = batchUpdateLogisticsService.updateStatus(batchUpdateLogisticsId, CommonStatus.ONLINE.getStatus());
        if (!updateSuccess) {
            throw new BatchUpdateLogisticsStatusException("已修改成功");
        }
        Long bossId = merchantService.getBoss(merchantId);
        userId = userId == null ? bossId : userId;
        Double leftFreeGold = merchantBillDisposeMoney.getDisposeMerchantGift().doubleValue();
        Double leftBalance = merchantBillDisposeMoney.getDisposeMerchantBalance().doubleValue();
        Double tenantLeftFreeGold = merchantBillDisposeMoney.getDisposeTenantGift().doubleValue();
        Double tenantLeftBalance = merchantBillDisposeMoney.getDisposeTenantBalance().doubleValue();

        List<MerchantBillCreateParam> merchantBillCreateParams = Lists.newArrayList();
        List<Order> returnOrders = Lists.newArrayList();
        List<OrderLogisticsUpdateParam> orderLogisticsUpdateParams = Lists.newArrayList();
        List<OrderLogisticsUpdateItemParam> orderLogisticsUpdateItemParams = Lists.newArrayList();
        List<Order> updateOrders = Lists.newArrayList();
        List<OrderLogisticsHistoryAddDTO> orderLogisticsHistoryAdds = Lists.newArrayList();

        for (PutLogisticsInfoDto putLogisticsInfoDto : putLogisticsInfoDtos) {
            Boolean updateCodeSuccess = orderService.updateTransactionCode(putLogisticsInfoDto.getOrderId(), putLogisticsInfoDto.getTransactionCode());
            if (!updateCodeSuccess) {
                Assert.wrong("订单信息已变动更新失败");
            }
            Order order = orderMap.get(putLogisticsInfoDto.getOrderId());
            Long orderId = order.getId();
            boolean isTenantLogistics = DistributionProductLogisticsSourceEnum.isTenantLogistics(order.getLogisticsSource());
            log.info("newLogisticsAmount:{}", JSON.toJSON(putLogisticsInfoDto));
            OrderAmountRespDTO oldOrderAmount = putLogisticsBO.getOldOrderAmountMap().get(putLogisticsInfoDto.getOrderId());
            log.info("oldAmount:{}", JSON.toJSON(oldOrderAmount));
            Double subCarriageAmount = DoubleUtils.sub(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewCarriageAmount(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOldCarriageAmount());
            Double subServiceAmount = DoubleUtils.sub(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewServiceAmount(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOldServiceAmount());
            double subTotalAmount = DoubleUtils.add(subCarriageAmount, subServiceAmount);
            //租户的差值
            double subTotalTenantAmount = subTotalAmount;
            double subTenantCarriageAmount = subCarriageAmount;
            //租户自己的物流则费用为0
            if (isTenantLogistics) {
                subTotalTenantAmount = 0d;
                subTenantCarriageAmount = 0d;
            }
            OrderImportExtraInfoDto orderImportInfo = putLogisticsInfoDto.getUpdateOrderImportInfo();
            if (orderImportInfo != null && !isTenantLogistics && NumberUtils.neZero(orderImportInfo.getTenantCarriageCommissionRate())) {
                BigDecimal tenantCarriageAmount = CarriageAmountUtil.getTenantCarriageAmount(BigDecimal.valueOf(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewCarriageAmount()), orderImportInfo.getTenantCarriageCommissionRate());
                double originTenantCarriageAmount = NumberUtil.sub(oldOrderAmount.getTenantCarriageAmount(), oldOrderAmount.getTenantRefundCarriageAmount());
                subTenantCarriageAmount = NumberUtil.sub(tenantCarriageAmount, originTenantCarriageAmount).doubleValue();
                subTotalTenantAmount = DoubleUtils.add(subTenantCarriageAmount, subServiceAmount);
                log.info("租户物流费：orderId {},物流差价{}，租户给SAAS的物流费用{}，原物流费用{} ", putLogisticsInfoDto.getOrderId(), subTenantCarriageAmount, tenantCarriageAmount, originTenantCarriageAmount);
            }

            if (0 != subCarriageAmount || 0 != subServiceAmount || 0 != putLogisticsInfoDto.getCommissionCharge() || 0 != subTotalTenantAmount || 0 != subTenantCarriageAmount) {
                OrderLogisticsUpdateParam orderLogisticsUpdateParam = OrderLogisticsUpdateParam.builder().orderId(putLogisticsInfoDto.getOrderId())
                        .subTotalAmount(subTotalAmount).subCarriageAmount(subCarriageAmount).subServiceAmount(subServiceAmount)
                        .subTenantTotalAmount(subTotalTenantAmount).subTenantCarriageAmount(subTenantCarriageAmount)
                        .build();
                orderLogisticsUpdateParams.add(orderLogisticsUpdateParam);
            }
            if (!noPainStatus.contains(putLogisticsInfoDto.getOrderStatus()) && paymentMethod != null && (!subCarriageAmount.equals(0D) || !subServiceAmount.equals(0D))) {
                //paymentMethod null 表示未支付 不需要账单
                String orderName = !subCarriageAmount.equals(0D) && !subServiceAmount.equals(0D) ? "运费" : (!subCarriageAmount.equals(0D) ? "运费" : "服务费");
                for (PaymentDto payment : paymentPayRefund.getPayments()) {
                    if (payment.getSourceRole().equalsIgnoreCase(PaymentRoleEnum.MERCHANT.getCode())) {
                        //商户付款生成租户账单
                        merchantBillCreateParams.add(MerchantBillGenerateService.genMerchantLogisticsUpdateBill(payment,
                            merchantBillDisposeMoney, orderName, subTotalAmount, putLogisticsInfoDto.getOrderNo(), leftBalance, leftFreeGold));
                    } else if (payment.getSourceRole().equalsIgnoreCase(PaymentRoleEnum.TENANT.getCode())) {
                        //租户物流，没有收取租户的运费和服务费，所以无需账单
                        if (isTenantLogistics) {
                            continue;
                        }
                        //租户付款生成租户账单
                        merchantBillCreateParams.add(MerchantBillGenerateService.genTenantLogisticsUpdateBill(merchantId,
                            payment, merchantBillDisposeMoney, orderName, subTotalTenantAmount, putLogisticsInfoDto.getOrderNo(), tenantLeftBalance, tenantLeftFreeGold));
                    }
                }
                for (RefundDto refundDto : paymentPayRefund.getRefundDtos()) {
                    if (refundDto.getTargetRole().equalsIgnoreCase(PaymentRoleEnum.MERCHANT.getCode())) {
                        //商户退款生成账单
                        merchantBillCreateParams.add(MerchantBillGenerateService.genMerchantRefundLogisticsUpdateBill(refundDto,
                            userId, merchantBillDisposeMoney, orderName, subTotalAmount, putLogisticsInfoDto.getOrderNo(), leftBalance, leftFreeGold));
                    } else if (refundDto.getTargetRole().equalsIgnoreCase(PaymentRoleEnum.TENANT.getCode())) {
                        //租户物流，没有收取租户的运费和服务费，所以无需账单
                        if (isTenantLogistics) {
                            continue;
                        }
                        //租户退款生成租户账单
                        merchantBillCreateParams.add(MerchantBillGenerateService.genTenantLogisticsUpdateRefundBill(merchantId,
                            userId, refundDto, merchantBillDisposeMoney, orderName, subTotalTenantAmount, putLogisticsInfoDto.getOrderNo(), tenantLeftBalance, tenantLeftFreeGold));
                    }
                }
                if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)) {
                    leftBalance = DoubleUtils.add(leftBalance, subTotalAmount);
                    tenantLeftBalance = DoubleUtils.add(tenantLeftBalance, subTotalTenantAmount);
                }
            }
            //是否需要更新运单号
            Boolean isUpdateCarriageNo = this.checkIsUpdateCarriageNo(batchUpdateLogistics, putLogisticsInfoDto, putLogisticsBO);
            updateCarriageNoMap.put(putLogisticsInfoDto.getOrderId(), isUpdateCarriageNo);
            //需要更新的订单信息
            Order updateOrder = new Order();
            updateOrder.setId(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOrderId());
            updateOrder.setAddressId(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getAddressId());
            updateOrder.setCountryExpressInfoId(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getCountryExpressInfoNewId());
            updateOrder.setLogisticsId(logisticsId);
            updateOrder.setLogisticsCodeId(logistics.getCodeId());
            if (logistics.getCodeId().equalsIgnoreCase(LogisticsCodeId.FBA.getCodeId())) {
                updateOrder.setCarriagePayStaus(CarriagePayStatus.AFTER_PACK.getValue());
            } else {
                updateOrder.setCarriagePayStaus(CarriagePayStatus.SUCCESS.getValue());
            }
            if (!isPayment) {
                updateOrder.setPaymentId(0L);
            }
            if (OrderStatus.noPain(order.getStatus()) && putLogisticsBO.getLogisticsSourceResp() != null) {
                updateOrder.setLogisticsSource(putLogisticsBO.getLogisticsSourceResp().getLogisticsSource());
            }
            updateOrders.add(updateOrder);

            //是否需要更新运单号
            DeliveryTypeEnum deliveryTypeEnum = DeliveryTypeEnum.matchOrder(order.getOriginType(), logistics.getCodeId());
            boolean deliveryTypeChange = deliveryTypeEnum.equalsCode(putLogisticsBO.getOldOrderDeliveryTypeMap().get(orderId));
            Long oldOrderServiceProviderId = putLogisticsBO.getOldOrderServiceProviderIdMap().get(orderId);
            log.info("orderId = {}, deliveryTypeChange = {}, oldServiceProviderId = {}, newLogisticsIsOnlineLogistics = {}, orderStatus() = {}",
                    orderId, deliveryTypeChange, oldOrderServiceProviderId, newLogisticsIsOnlineLogistics, order.getStatus());

            if (!order.getStatus().equals(OrderStatus.UNPAIN.getStatus())) {
                if (deliveryTypeChange
                        || !Objects.equals(LogisticsConstant.isOnlineLogistics(oldOrderServiceProviderId), newLogisticsIsOnlineLogistics)) {
                    OrderPutLogisticsUpdateParcelBO bo = new OrderPutLogisticsUpdateParcelBO();
                    bo.setOrderId(orderId);
                    bo.setOperateType(OrderPutLogisticsUpdateParcelOperateTypeEnum.INIT_PARCEL.getType());
                    orderPutLogisticsUpdateParcelBOs.add(bo);
                } else if (isUpdateCarriageNo) {
                    OrderPutLogisticsUpdateParcelBO bo = new OrderPutLogisticsUpdateParcelBO();
                    bo.setOrderId(orderId);
                    bo.setOperateType(OrderPutLogisticsUpdateParcelOperateTypeEnum.UPDATE_CARRIAGE_NO.getType());
                    bo.setCountryExpressInfoId(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getCountryExpressInfoNewId());
                    bo.setLogisticsId(logisticsId);
                    bo.setTailLogisticsChannelId(batchUpdateLogistics.getTailLogisticsChannelId());
                    orderPutLogisticsUpdateParcelBOs.add(bo);
                }
            } else {
                OrderPutLogisticsUpdateParcelBO bo = new OrderPutLogisticsUpdateParcelBO();
                bo.setOrderId(orderId);
                bo.setOperateType(OrderPutLogisticsUpdateParcelOperateTypeEnum.NONE.getType());
                bo.setCountryExpressInfoId(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getCountryExpressInfoNewId());
                bo.setLogisticsId(logisticsId);
                bo.setTailLogisticsChannelId(batchUpdateLogistics.getTailLogisticsChannelId());
                orderPutLogisticsUpdateParcelBOs.add(bo);
            }

            if (Boolean.TRUE.equals(isUpdateCarriageNo)) {
                OrderLogisticsHistoryAddDTO addDTO = new OrderLogisticsHistoryAddDTO();
                addDTO.setLogisticsId(logisticsId);
                addDTO.setOrderId(orderId);
                orderLogisticsHistoryAdds.add(addDTO);
            }
            returnOrders.add(updateOrder);
            if (CollectionUtils.isNotEmpty(putLogisticsInfoDto.getBatchPutLogisticsOrderItemDtos())) {
                for (BatchPutLogisticsOrderItemDto batchPutLogisticsOrderItemDto : putLogisticsInfoDto.getBatchPutLogisticsOrderItemDtos()) {
                    Double subItemServiceAmount = DoubleUtils.sub(batchPutLogisticsOrderItemDto.getNewServiceAmount(), batchPutLogisticsOrderItemDto.getOldServiceAmount());
                    if (!MathUtils.priceEqual(subItemServiceAmount, 0d)) {
                        OrderLogisticsUpdateItemParam orderLogisticsUpdateItemParam = OrderLogisticsUpdateItemParam.builder().orderItemId(batchPutLogisticsOrderItemDto.getId()).subServiceAmount(subItemServiceAmount).build();
                        orderLogisticsUpdateItemParams.add(orderLogisticsUpdateItemParam);
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(updateOrders)) {
            List<OrderSaveDto> orderUpdateDtoList = BeanUtils.toList(updateOrders, OrderSaveDto.class);
            orderFeign.updateBatchByUpdateLogistics(orderUpdateDtoList);
        }
        if (CollectionUtil.isNotEmpty(orderLogisticsHistoryAdds)) {
            this.orderLogisticsHistoryFeign.addBatch(orderLogisticsHistoryAdds);
        }
        Collections.reverse(merchantBillCreateParams);
        if (CollectionUtils.isNotEmpty(orderLogisticsUpdateItemParams) || CollectionUtils.isNotEmpty(orderLogisticsUpdateParams)) {
            orderAmountFeign.batchUpdateLogisticsRefreshAmount(OrderLogisticsUpdateBatchParam.builder().orderLogisticsUpdateItemParams(orderLogisticsUpdateItemParams).orderLogisticsUpdateParams(orderLogisticsUpdateParams).build());
        }
        if (CollectionUtils.isNotEmpty(merchantBillCreateParams)) {
            merchantBillFeign.batchCreate(MerchantBillsCreateParam.builder().params(merchantBillCreateParams).build());
        }
        //订单额度更新
        if (isPayment) {
            TenantQuotaDto dto = new TenantQuotaDto();
            dto.setOrderIds(putLogisticsBO.getOrderIds());
            dto.setUserId(userId);
            tenantLogisticsOrderFeign.batchConsumeOrReturnTenantOrderCount(dto);
        }
        //orderAmountDetail重算
        orderFeign.updateOrderAmountDetailByNewLogisticsId(putLogisticsBO.getOrderIds(), logisticsId);
        // 订单申报信息重置
        orderDeclarationFeign.remove(putLogisticsBO.getOrderIds());
        // 保存尾程物流渠道id
        List<BaseValueChangeDTO<Long, Long>> tailLogisticsChannelChangeList = batchSaveTailLogisticsChannelId(batchUpdateLogistics.getTailLogisticsChannelId(), userId, putLogisticsBO.getOrderIds());
        // 移除地址已更新标志
        removeOrderExtendAddressUpdated(putLogisticsBO, putLogisticsInfoDtos);
        // 物流改变，运营的寄付服务费重置
        sendResetPodPrepaidLogisticsServiceAmount(logisticsId, putLogisticsBO.getOrderIds(), orderMap);
        //消息发送
        sendMessage(putLogisticsInfoDtos, userId, isPayment, orderMap, logistics, tailLogisticsChannelChangeList);
        return returnOrders;
    }

    public List<Order> batchUpdateLogisticsNew(BatchUpdateLogistics batchUpdateLogistics,
                                               List<PutLogisticsInfoDto> putLogisticsInfoDtos, String paymentMethod,
                                               Long userId, Map<Long, Boolean> updateCarriageNoMap,
                                               List<OrderPutLogisticsUpdateParcelBO> orderPutLogisticsUpdateParcelBOs) {
        if (CollectionUtils.isEmpty(putLogisticsInfoDtos)) {
            throw new BatchUpdateLogisticsStatusException("没有可支持的物流");
        }
        Long merchantId = batchUpdateLogistics.getMerchantId();
        Long logisticsId = batchUpdateLogistics.getLogitsicsId();
        Long batchUpdateLogisticsId = batchUpdateLogistics.getId();
        boolean isPayment = paymentMethod != null;
        OrderPutLogisticsBO putLogisticsBO = buildOrderPutLogisticsBO(putLogisticsInfoDtos, batchUpdateLogistics);

        Map<Long, Order> orderMap = putLogisticsBO.getOrderMap();

        //logistics最好由外部传入
        Logistics logistics = logisticsService.findLogisticById(logisticsId);
        boolean newLogisticsIsOnlineLogistics = LogisticsConstant.isOnlineLogistics(logistics.getServiceProviderId());

        Boolean updateSuccess = batchUpdateLogisticsService.updateStatus(batchUpdateLogisticsId, CommonStatus.ONLINE.getStatus());
        if (!updateSuccess) {
            throw new BatchUpdateLogisticsStatusException("已修改成功");
        }
        Long bossId = merchantService.getBoss(merchantId);
        userId = userId == null ? bossId : userId;

        List<Order> returnOrders = Lists.newArrayList();
        List<OrderLogisticsUpdateParam> orderLogisticsUpdateParams = Lists.newArrayList();
        List<OrderLogisticsUpdateItemParam> orderLogisticsUpdateItemParams = Lists.newArrayList();
        List<Order> updateOrders = Lists.newArrayList();
        List<OrderLogisticsHistoryAddDTO> orderLogisticsHistoryAdds = Lists.newArrayList();

        for (PutLogisticsInfoDto putLogisticsInfoDto : putLogisticsInfoDtos) {
            Boolean updateCodeSuccess = orderService.updateTransactionCode(putLogisticsInfoDto.getOrderId(), putLogisticsInfoDto.getTransactionCode());
            if (!updateCodeSuccess) {
                Assert.wrong("订单信息已变动更新失败");
            }
            Order order = orderMap.get(putLogisticsInfoDto.getOrderId());
            Long orderId = order.getId();
            boolean isTenantLogistics = DistributionProductLogisticsSourceEnum.isTenantLogistics(order.getLogisticsSource());
            log.info("newLogisticsAmount:{}", JSON.toJSON(putLogisticsInfoDto));
            OrderAmountRespDTO oldOrderAmount = putLogisticsBO.getOldOrderAmountMap().get(putLogisticsInfoDto.getOrderId());
            log.info("oldAmount:{}", JSON.toJSON(oldOrderAmount));
            Double subCarriageAmount = DoubleUtils.sub(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewCarriageAmount(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOldCarriageAmount());
            Double subServiceAmount = DoubleUtils.sub(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewServiceAmount(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOldServiceAmount());
            double subTotalAmount = DoubleUtils.add(subCarriageAmount, subServiceAmount);
            //租户的差值
            double subTotalTenantAmount = subTotalAmount;
            double subTenantCarriageAmount = subCarriageAmount;
            //租户自己的物流则费用为0
            if (isTenantLogistics) {
                subTotalTenantAmount = 0d;
                subTenantCarriageAmount = 0d;
            }
            OrderImportExtraInfoDto orderImportInfo = putLogisticsInfoDto.getUpdateOrderImportInfo();
            if (orderImportInfo != null && !isTenantLogistics && NumberUtils.neZero(orderImportInfo.getTenantCarriageCommissionRate())) {
                BigDecimal tenantCarriageAmount = CarriageAmountUtil.getTenantCarriageAmount(BigDecimal.valueOf(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewCarriageAmount()), orderImportInfo.getTenantCarriageCommissionRate());
                double originTenantCarriageAmount = NumberUtil.sub(oldOrderAmount.getTenantCarriageAmount(), oldOrderAmount.getTenantRefundCarriageAmount());
                subTenantCarriageAmount = NumberUtil.sub(tenantCarriageAmount, originTenantCarriageAmount).doubleValue();
                subTotalTenantAmount = DoubleUtils.add(subTenantCarriageAmount, subServiceAmount);
                log.info("租户物流费：orderId {},物流差价{}，租户给SAAS的物流费用{}，原物流费用{} ", putLogisticsInfoDto.getOrderId(), subTenantCarriageAmount, tenantCarriageAmount, originTenantCarriageAmount);
            }

            if (0 != subCarriageAmount || 0 != subServiceAmount || 0 != putLogisticsInfoDto.getCommissionCharge() || 0 != subTotalTenantAmount || 0 != subTenantCarriageAmount) {
                OrderLogisticsUpdateParam orderLogisticsUpdateParam = OrderLogisticsUpdateParam.builder().orderId(putLogisticsInfoDto.getOrderId())
                        .subTotalAmount(subTotalAmount).subCarriageAmount(subCarriageAmount).subServiceAmount(subServiceAmount)
                        .subTenantTotalAmount(subTotalTenantAmount).subTenantCarriageAmount(subTenantCarriageAmount)
                        .build();
                orderLogisticsUpdateParams.add(orderLogisticsUpdateParam);
            }
            //是否需要更新运单号
            Boolean isUpdateCarriageNo = this.checkIsUpdateCarriageNo(batchUpdateLogistics, putLogisticsInfoDto, putLogisticsBO);
            updateCarriageNoMap.put(putLogisticsInfoDto.getOrderId(), isUpdateCarriageNo);
            //需要更新的订单信息
            Order updateOrder = new Order();
            updateOrder.setId(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOrderId());
            updateOrder.setAddressId(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getAddressId());
            updateOrder.setCountryExpressInfoId(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getCountryExpressInfoNewId());
            updateOrder.setLogisticsId(logisticsId);
            updateOrder.setLogisticsCodeId(logistics.getCodeId());
            if (logistics.getCodeId().equalsIgnoreCase(LogisticsCodeId.FBA.getCodeId())) {
                updateOrder.setCarriagePayStaus(CarriagePayStatus.AFTER_PACK.getValue());
            } else {
                updateOrder.setCarriagePayStaus(CarriagePayStatus.SUCCESS.getValue());
            }
            if (!isPayment) {
                updateOrder.setPaymentId(0L);
            }
            if (OrderStatus.noPain(order.getStatus()) && putLogisticsBO.getLogisticsSourceResp() != null) {
                updateOrder.setLogisticsSource(putLogisticsBO.getLogisticsSourceResp().getLogisticsSource());
            }
            updateOrders.add(updateOrder);

            //是否需要更新运单号
            DeliveryTypeEnum deliveryTypeEnum = DeliveryTypeEnum.matchOrder(order.getOriginType(), logistics.getCodeId());
            boolean deliveryTypeChange = deliveryTypeEnum.equalsCode(putLogisticsBO.getOldOrderDeliveryTypeMap().get(orderId));
            Long oldOrderServiceProviderId = putLogisticsBO.getOldOrderServiceProviderIdMap().get(orderId);
            log.info("orderId = {}, deliveryTypeChange = {}, oldServiceProviderId = {}, newLogisticsIsOnlineLogistics = {}, orderStatus() = {}",
                    orderId, deliveryTypeChange, oldOrderServiceProviderId, newLogisticsIsOnlineLogistics, order.getStatus());

            if (!order.getStatus().equals(OrderStatus.UNPAIN.getStatus())) {
                if (deliveryTypeChange
                        || !Objects.equals(LogisticsConstant.isOnlineLogistics(oldOrderServiceProviderId), newLogisticsIsOnlineLogistics)) {
                    OrderPutLogisticsUpdateParcelBO bo = new OrderPutLogisticsUpdateParcelBO();
                    bo.setOrderId(orderId);
                    bo.setOperateType(OrderPutLogisticsUpdateParcelOperateTypeEnum.INIT_PARCEL.getType());
                    orderPutLogisticsUpdateParcelBOs.add(bo);
                } else if (isUpdateCarriageNo) {
                    OrderPutLogisticsUpdateParcelBO bo = new OrderPutLogisticsUpdateParcelBO();
                    bo.setOrderId(orderId);
                    bo.setOperateType(OrderPutLogisticsUpdateParcelOperateTypeEnum.UPDATE_CARRIAGE_NO.getType());
                    bo.setCountryExpressInfoId(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getCountryExpressInfoNewId());
                    bo.setLogisticsId(logisticsId);
                    bo.setTailLogisticsChannelId(batchUpdateLogistics.getTailLogisticsChannelId());
                    orderPutLogisticsUpdateParcelBOs.add(bo);
                }
            } else {
                OrderPutLogisticsUpdateParcelBO bo = new OrderPutLogisticsUpdateParcelBO();
                bo.setOrderId(orderId);
                bo.setOperateType(OrderPutLogisticsUpdateParcelOperateTypeEnum.NONE.getType());
                bo.setCountryExpressInfoId(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getCountryExpressInfoNewId());
                bo.setLogisticsId(logisticsId);
                bo.setTailLogisticsChannelId(batchUpdateLogistics.getTailLogisticsChannelId());
                orderPutLogisticsUpdateParcelBOs.add(bo);
            }

            if (Boolean.TRUE.equals(isUpdateCarriageNo)) {
                OrderLogisticsHistoryAddDTO addDTO = new OrderLogisticsHistoryAddDTO();
                addDTO.setLogisticsId(logisticsId);
                addDTO.setOrderId(orderId);
                orderLogisticsHistoryAdds.add(addDTO);
            }
            returnOrders.add(updateOrder);
            if (CollectionUtils.isNotEmpty(putLogisticsInfoDto.getBatchPutLogisticsOrderItemDtos())) {
                for (BatchPutLogisticsOrderItemDto batchPutLogisticsOrderItemDto : putLogisticsInfoDto.getBatchPutLogisticsOrderItemDtos()) {
                    Double subItemServiceAmount = DoubleUtils.sub(batchPutLogisticsOrderItemDto.getNewServiceAmount(), batchPutLogisticsOrderItemDto.getOldServiceAmount());
                    if (!MathUtils.priceEqual(subItemServiceAmount, 0d)) {
                        OrderLogisticsUpdateItemParam orderLogisticsUpdateItemParam = OrderLogisticsUpdateItemParam.builder().orderItemId(batchPutLogisticsOrderItemDto.getId()).subServiceAmount(subItemServiceAmount).build();
                        orderLogisticsUpdateItemParams.add(orderLogisticsUpdateItemParam);
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(updateOrders)) {
            List<OrderSaveDto> orderUpdateDtoList = BeanUtils.toList(updateOrders, OrderSaveDto.class);
            orderFeign.updateBatchByUpdateLogistics(orderUpdateDtoList);
        }
        if (CollectionUtil.isNotEmpty(orderLogisticsHistoryAdds)) {
            this.orderLogisticsHistoryFeign.addBatch(orderLogisticsHistoryAdds);
        }
        if (CollectionUtils.isNotEmpty(orderLogisticsUpdateItemParams) || CollectionUtils.isNotEmpty(orderLogisticsUpdateParams)) {
            orderAmountFeign.batchUpdateLogisticsRefreshAmount(OrderLogisticsUpdateBatchParam.builder().orderLogisticsUpdateItemParams(orderLogisticsUpdateItemParams).orderLogisticsUpdateParams(orderLogisticsUpdateParams).build());
        }
        //订单额度更新
        if (isPayment) {
            TenantQuotaDto dto = new TenantQuotaDto();
            dto.setOrderIds(putLogisticsBO.getOrderIds());
            dto.setUserId(userId);
            tenantLogisticsOrderFeign.batchConsumeOrReturnTenantOrderCount(dto);
        }
        //orderAmountDetail重算
        orderFeign.updateOrderAmountDetailByNewLogisticsId(putLogisticsBO.getOrderIds(), logisticsId);
        // 订单申报信息重置
        orderDeclarationFeign.remove(putLogisticsBO.getOrderIds());
        // 保存尾程物流渠道id
        List<BaseValueChangeDTO<Long, Long>> tailLogisticsChannelChangeList = batchSaveTailLogisticsChannelId(batchUpdateLogistics.getTailLogisticsChannelId(), userId, putLogisticsBO.getOrderIds());
        // 移除地址已更新标志
        removeOrderExtendAddressUpdated(putLogisticsBO, putLogisticsInfoDtos);
        // 物流改变，运营的寄付服务费重置
        sendResetPodPrepaidLogisticsServiceAmount(logisticsId, putLogisticsBO.getOrderIds(), orderMap);
        //消息发送
        sendMessage(putLogisticsInfoDtos, userId, isPayment, orderMap, logistics, tailLogisticsChannelChangeList);
        return returnOrders;
    }

    public void removeOrderExtendAddressUpdated(OrderPutLogisticsBO putLogisticsBO, List<PutLogisticsInfoDto> putLogisticsInfoDtos) {
        if (CollUtil.isEmpty(putLogisticsInfoDtos)) {
            return;
        }
        List<Long> orderIds = putLogisticsInfoDtos.stream().filter(i -> {
            if (i.getBatchPutLogisticsOrderOrderDto() == null) {
                return false;
            }
            Order order = putLogisticsBO.getOrderMap().get(i.getOrderId());
            if (order == null) {
                return false;
            }
            return !Objects.equals(order.getAddressId(), i.getBatchPutLogisticsOrderOrderDto().getAddressId());
        }).map(PutLogisticsInfoDto::getOrderId).collect(Collectors.toList());
        if (CollUtil.isEmpty(orderIds)) {
            return;
        }
        orderExtendInfoFeign.delValueByCode(orderIds, OrderExtendKeyEnum.ADDRESS.getCode());
    }

    private List<BaseValueChangeDTO<Long, Long>> batchSaveTailLogisticsChannelId(Long tailLogisticsChannelId, Long userId, List<Long> orderIds) {
        if (!NumberUtils.greaterZero(tailLogisticsChannelId)) {
            return orderTailLogisticsFeign.batchDeleteByOrderIds(orderIds);
        }
        List<OrderTailLogisticsReqDto> orderTailLogisticsReqDtos = Lists.newArrayList();
        for (Long orderId : orderIds) {
            OrderTailLogisticsReqDto orderTailLogisticsReqDto = new OrderTailLogisticsReqDto();
            orderTailLogisticsReqDto.setOrderId(orderId);
            orderTailLogisticsReqDto.setLogisticsChannelId(tailLogisticsChannelId);
            orderTailLogisticsReqDto.setCreateId(userId);
            orderTailLogisticsReqDto.setUpdateId(userId);
            orderTailLogisticsReqDtos.add(orderTailLogisticsReqDto);
        }
        if (CollUtil.isNotEmpty(orderTailLogisticsReqDtos)) {
            return orderTailLogisticsFeign.batchSaveOrUpdateDto(orderTailLogisticsReqDtos);
        }
        return Collections.emptyList();
    }

    private void sendResetPodPrepaidLogisticsServiceAmount(Long logisticsId, List<Long> orderIds, Map<Long, Order> orderMap) {
        List<Long> logisticsChangeOrderIds = getLogisticsChangeOrderIds(logisticsId, orderIds, orderMap);
        for (Long orderId : logisticsChangeOrderIds) {
            FbaOrderTaskLogisticsServiceAmountResetEventMessage msg = new FbaOrderTaskLogisticsServiceAmountResetEventMessage(orderId);
            //orderEventService.sendMessage(msg, FbaOrderTaskConstant.TOPIC_FBA_ORDER_TASK_LOGISTICS_SERVICE_AMOUNT_RESET);
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.TOPIC_FBA_ORDER_TASK_LOGISTICS_SERVICE_AMOUNT_RESET, msg);
        }
    }

    /**
     * 校验订单是否需要更新运单号
     *
     * @return 是否更新运单号
     */
    private Boolean checkIsUpdateCarriageNo(BatchUpdateLogistics batchUpdateLogistics
            , PutLogisticsInfoDto putLogisticsInfoDto, OrderPutLogisticsBO bo) {
        Order order = bo.getOrderMap().get(putLogisticsInfoDto.getOrderId());
        //物流变更
        if (!Objects.equals(batchUpdateLogistics.getLogitsicsId(), order.getLogisticsId())) {
            return Boolean.TRUE;
        }
        // 尾程变了
        OrderTailLogisticsRespDto tailLogisticsRespDto = bo.getOldOrderTailLogisticsMap().get(putLogisticsInfoDto.getOrderId());
        Long oldTailChannelId = tailLogisticsRespDto == null ? null : tailLogisticsRespDto.getLogisticsChannelId();
        if (!Objects.equals(batchUpdateLogistics.getTailLogisticsChannelId(), oldTailChannelId)) {
            return Boolean.TRUE;
        }
        //地址变更
        if (!order.getAddressId().equals(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getAddressId())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /***
     *修改地址物流消息发送
     * <AUTHOR>
     * @date 2020/7/24 17:19
     */
    private void sendMessage(List<PutLogisticsInfoDto> putLogisticsInfoDtos, Long userId, Boolean isPayment
            , Map<Long, Order> orderMap, Logistics logistics
            , List<BaseValueChangeDTO<Long, Long>> tailLogisticsChannelChangeList) {
        Map<Long, BaseValueChangeDTO<Long, Long>> tailChangeMap = ListUtil.toMap(BaseValueChangeDTO::getKey, tailLogisticsChannelChangeList);
        for (PutLogisticsInfoDto putLogisticsInfoDto : putLogisticsInfoDtos) {
            Order order = orderMap.get(putLogisticsInfoDto.getOrderId());
            Address oldAddress = order.getAddress();
            String spacer = " ";
            String oldAddressName = oldAddress.getConsignee() + spacer + oldAddress.getMobilePhone() + spacer + oldAddress.getCountry() + spacer
                    + oldAddress.getProvince() + spacer + oldAddress.getCity() + spacer + oldAddress.getDetail() + spacer + oldAddress.getPostcode();
            if (isPayment) {
                //已付款修改
                CustomerModifyPaidOrderMessage message = new CustomerModifyPaidOrderMessage();
                message.setEid(putLogisticsInfoDto.getOrderId());
                message.setOrderId(putLogisticsInfoDto.getOrderId());
                message.setSendingTime(new Date());
                message.setOperatorUid(userId);
                Long addressId = putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getAddressId();
                message.setAddressId(addressId);
                message.setOldLogisticsId(order.getLogisticsId());
                message.setLogisticsId(logistics.getId());
                message.setNewLogistics(logistics.getName());
                message.setOldLogistics(order.getLogistics() == null ? "" : order.getLogistics().getName());
                message.setOldAddress(oldAddressName);
                message.setOldAddressDto(BeanUtil.copyProperties(oldAddress, AddressRespDto.class));
                message.setOldAddressId(order.getAddressId());
                message.setOldAmount(order.getCarriageAmount());
                message.setNewAmount(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewCarriageAmount());
                message.setNewServiceAmount(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewServiceAmount());
                message.setOldServiceAmount(putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOldServiceAmount());
                message.setDelFactoryOrderItemIds(putLogisticsInfoDto.getDelFactoryOrderItemIds());
                message.setTailChannelChange(tailChangeMap.get(putLogisticsInfoDto.getOrderId()));
                orderEventService.sendProcessMsg(message, OrderProgressConstant.CUSTOMER_MODIFY_PAID_ORDER);
            } else {
                //未付款修改
                CustomerModifyUnpaidOrderMessage message = new CustomerModifyUnpaidOrderMessage();
                message.setEid(putLogisticsInfoDto.getOrderId());
                message.setOrderId(putLogisticsInfoDto.getOrderId());
                message.setSendingTime(new Date());
                message.setOperatorUid(userId);
                Long addressId = putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getAddressId();
                message.setAddressId(addressId);
                message.setNewLogistics(logistics.getName());
                message.setOldLogistics(order.getLogistics() == null ? "" : order.getLogistics().getName());
                message.setOldAddress(oldAddressName);
                message.setOldAddressId(order.getAddressId());
                message.setLogisticsId(logistics.getId());
                message.setOldLogisticsId(order.getLogisticsId());
                message.setTailChannelChange(tailChangeMap.get(putLogisticsInfoDto.getOrderId()));
                orderEventService.sendProcessMsg(message, OrderProgressConstant.CUSTOMER_MODIFY_UNPAID_ORDER);
            }
        }
    }


//    @GlobalTransactional
//    public PaymentDto batchUpdatLogisticsPayment(Long merchantId, Long userId, BatchPutLogisticsReqDto params, PutLogisticsRespDto putLogisticsRespDto) {
//        Long logisticsId = params.getLogisticsId();
//        List<PutLogisticsInfoDto> putLogisticsInfoDtos = Lists.newArrayList();
//        Map<Long, String> transactionCodeMaps = Maps.newHashMap();
//        //订单id-是否更新运单号 Map
//        Map<Long, Boolean> updateCarriageNoMap = Maps.newHashMap();
//
//        List<Long> orderIds = Lists.newArrayList();
//        for (Order order : params.getOrders()) {
//            Assert.validateNull(order.getId(), "订单id必传");
//            Assert.validateNull(order.getTransactionCode(), "订单transactionCode必传");
//            transactionCodeMaps.put(order.getId(), order.getTransactionCode());
//            orderIds.add(order.getId());
//        }
//
//        List<Order> orders = orderService.findByIds(orderIds);
//
//        for (Order order : orders) {
//            boolean partShipped = Objects.equals(order.getStatus(), OrderStatus.PART_SHIPPED.getStatus());
//            if (Boolean.TRUE.equals(partShipped)) {
//                if (orders.size() > 1) {
//                    throw new BusinessException("存在已部分发货订单，不可自行修改物流/地址，请移除后重试");
//                } else {
//                    throw new BusinessException("该订单已部分发货，不可修改物流，如需调整后续包裹的物流，请联系客服");
//                }
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(orders)) {
//            // 查询旧地址
//            orderService.formatAddress(orders.stream().filter(i -> i.getAddress() == null).collect(Collectors.toList()));
//        }
//
//        for (PutLogisticsInfoDto putLogisticsInfoDto : putLogisticsRespDto.getPutLogisticsInfoDtos()) {
//            if (putLogisticsInfoDto.getStatus().equalsIgnoreCase(OrderService.getLogisticsAvailable())) {
//                putLogisticsInfoDtos.add(putLogisticsInfoDto);
//            }
//        }
//
//        BatchUpdateLogistics batchUpdateLogistics = batchUpdateLogisticsService.save(logisticsId, merchantId, params.getTailLogisticsChannelId());
//        ArrayList<OrderImportExtraInfoDto> updateOrderImportExtraInfos = Lists.newArrayList();
//        for (PutLogisticsInfoDto putLogisticsInfoDto : putLogisticsInfoDtos) {
//            Long orderId = putLogisticsInfoDto.getOrderId();
//            if (transactionCodeMaps.get(orderId) == null || !transactionCodeMaps.get(orderId).equalsIgnoreCase(putLogisticsInfoDto.getTransactionCode())) {
//                Assert.wrong("订单信息发生变动，请重新进入");
//            }
//            if (!OrderService.getLogisticsAvailable().equalsIgnoreCase(putLogisticsInfoDto.getStatus())) {
//                continue;
//            }
//            updateOrderImportExtraInfos.add(putLogisticsInfoDto.getUpdateOrderImportInfo());
//            batchUpdateLogisticsOrderService.save(batchUpdateLogistics.getId(), putLogisticsInfoDto.getOrderId(), putLogisticsInfoDto.getOrderNo(), putLogisticsInfoDto.getCommissionCharge(), putLogisticsInfoDto.getAddress().getId(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getCountryExpressInfoNewId(), putLogisticsInfoDto.getTransactionCode(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewCarriageAmount(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOldCarriageAmount(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewServiceAmount(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOldServiceAmount());
//            if (CollectionUtils.isNotEmpty(putLogisticsInfoDto.getBatchPutLogisticsOrderItemDtos())) {
//                for (BatchPutLogisticsOrderItemDto batchPutLogisticsOrderItemDto : putLogisticsInfoDto.getBatchPutLogisticsOrderItemDtos()) {
//                    batchUpdateLogisticsOrderItemService.save(batchUpdateLogistics.getId(), putLogisticsInfoDto.getOrderId(), batchPutLogisticsOrderItemDto.getId(), batchPutLogisticsOrderItemDto.getNewServiceAmount(), batchPutLogisticsOrderItemDto.getOldServiceAmount());
//                }
//            }
//        }
//        //保存更新的费率
//        if (CollectionUtils.isNotEmpty(updateOrderImportExtraInfos)) {
//            orderImportExtraInfoFeign.updateBatchById(updateOrderImportExtraInfos);
//        }
//
//        PaymentDto payment = new PaymentDto();
//        payment.setStatus(PaymentService.STATUS_PAIN);
//
//        List<Order> updateOrders = null;
//        //退回到订单用户无需通知类型
//        boolean orderTypeUpdateNotify = !(putLogisticsRespDto.getOrderPain() && putLogisticsRespDto.getDifferentAmount() <= 0d && putLogisticsRespDto.getRefundOrderUserId() != null);
//        Long paymentUseId = NumberUtils.greaterZero(putLogisticsRespDto.getRefundOrderUserId()) ? putLogisticsRespDto.getRefundOrderUserId() : userId;
//        Double tenantAmount = Math.abs(putLogisticsRespDto.getTenantDifferentAmount());
//        List<RefundParam> refundParams = Lists.newArrayList();
//        List<PaymentParam> paymentParams = Lists.newArrayList();
//        if (putLogisticsRespDto.getTenantDifferentAmount() < 0d) {
//            //租户退款
//            refundParams.add(this.generateRefundDtoForTenantPay(putLogisticsRespDto.getMerchant(), paymentUseId, tenantAmount));
//        } else if (0 < putLogisticsRespDto.getTenantDifferentAmount()) {
//            //租户补款
//            paymentParams.add(this.generatePaymentDtoForTenantPay(putLogisticsRespDto.getMerchant(), paymentUseId, tenantAmount));
//        }
//
//        try {
//            List<OrderPutLogisticsUpdateParcelBO> orderPutLogisticsUpdateParcelBOs = new ArrayList<>();
//            if (!putLogisticsRespDto.getOrderPain()) {
//                //未支付直接修改
//                MerchantBillDisposeMoney merchantBillDisposeMoney = merchantUserAccountService.getDisposeMoney(putLogisticsRespDto.getMerchant().getTenantId(), putLogisticsRespDto.getMerchant().getId(), paymentUseId);
//
//                PaymentPayRefund paymentPayRefund = new PaymentPayRefund(Lists.newArrayList(), Lists.newArrayList(), putLogisticsRespDto.getMerchant());
//                updateOrders = this.batchUpdateLogistics(paymentPayRefund, merchantBillDisposeMoney, batchUpdateLogistics, putLogisticsInfoDtos, null, userId, updateCarriageNoMap, orderPutLogisticsUpdateParcelBOs);
//
//            } else if (putLogisticsRespDto.getDifferentAmount() <= 0d) {
//                //退款
//                UserAccountBalanceResp userAccountBalanceResp = merchantUserAccountFeign.getUserBalance(merchantId, userId);
//
//                Double amount = Math.abs(putLogisticsRespDto.getDifferentAmount());
//                if (NumberUtils.greaterZero(amount)) {
//                    //用户退款
//                    refundParams.add(this.generateRefundDtoForMerchantPay(putLogisticsRespDto.getOnlyOffLinePay(), putLogisticsRespDto.getMerchant(), paymentUseId, amount, userAccountBalanceResp));
//                }
//
//                PaymentPayRefund paymentPayRefund = paySuccessThen(refundParams, paymentParams);
//                paymentPayRefund.setMerchantRespDto(putLogisticsRespDto.getMerchant());
//                MerchantBillDisposeMoney merchantBillDisposeMoney = merchantUserAccountService.getDisposeMoney(putLogisticsRespDto.getMerchant().getTenantId(), putLogisticsRespDto.getMerchant().getId(), paymentUseId);
//
//                updateOrders = this.batchUpdateLogistics(paymentPayRefund, merchantBillDisposeMoney, batchUpdateLogistics, putLogisticsInfoDtos, params.getPaymentMethod(), userId, updateCarriageNoMap, orderPutLogisticsUpdateParcelBOs);
//            } else {
//                //补款
//                Double amount = putLogisticsRespDto.getDifferentAmount();
//                if (PaymentMethod.BALANCEPAY.getValue().equalsIgnoreCase(params.getPaymentMethod()) || PaymentMethodEnum.OFFLINE.getCode().equalsIgnoreCase(params.getPaymentMethod())) {
//                    //免密
//                    MerchantBillDisposeMoney merchantBillDisposeMoney = merchantUserAccountService.getMerchantDisposeMoney(putLogisticsRespDto.getMerchant().getId(), paymentUseId);
//                    paymentParams.add(this.generatePaymentDtoForMerchantPay(putLogisticsRespDto.getOnlyOffLinePay(), merchantBillDisposeMoney, putLogisticsRespDto.getMerchant(), params.getPaymentMethod(), paymentUseId, amount));
//                    PaymentPayRefund paymentPayRefund = paySuccessThen(refundParams, paymentParams);
//                    paymentPayRefund.setMerchantRespDto(putLogisticsRespDto.getMerchant());
//                    //修改后最新的信息所以还要查一遍
//                    merchantBillDisposeMoney = merchantUserAccountService.getDisposeMoney(putLogisticsRespDto.getMerchant().getTenantId(), putLogisticsRespDto.getMerchant().getId(), paymentUseId);
//
//                    updateOrders = this.batchUpdateLogistics(paymentPayRefund, merchantBillDisposeMoney, batchUpdateLogistics, putLogisticsInfoDtos, params.getPaymentMethod(), userId, updateCarriageNoMap, orderPutLogisticsUpdateParcelBOs);
//                } else if (PaymentMethodEnum.needWaitCustomerPaid(params.getPaymentMethod())) {
//                    MerchantBillDisposeMoney merchantBillDisposeMoney = merchantUserAccountService.getDisposeMoney(putLogisticsRespDto.getMerchant().getTenantId(), putLogisticsRespDto.getMerchant().getId(), paymentUseId);
//                    payment = this.createAliPayment(paymentParams, refundParams, merchantBillDisposeMoney, batchUpdateLogistics.getId(), putLogisticsRespDto.getOnlyOffLinePay(), putLogisticsRespDto.getMerchant(), params.getPaymentMethod(), paymentUseId, amount);
//                    payment.setStatus(PaymentService.STATUS_NO_PAIN);
//
//                }
//            }
//            if (updateOrders != null) {
//                List<OrderDeclarationApplyCarriageNoMessage> paramList = Lists.newArrayList();
//                for (Order updateOrder : updateOrders) {
//                    OrderDeclarationApplyCarriageNoMessage message = new OrderDeclarationApplyCarriageNoMessage();
//                    message.setOrderId(updateOrder.getId());
//                    message.setLogisticsId(updateOrder.getLogisticsId());
//                    message.setIsApplyCarriage(true);
//                    Boolean isUpdateCarriageNo = updateCarriageNoMap.get(updateOrder.getId());
//                    if (isUpdateCarriageNo != null && !isUpdateCarriageNo) {
//                        message.setIsApplyCarriage(false);
//                    }
//                    paramList.add(message);
//
//                }
//                log.info("更新订单消息发送详情：{}", JSON.toJSON(paramList));
//                afterCommitExecutor.execute(() -> {
//                    log.info("更新订单消息发送详情：{}", JSON.toJSON(paramList));
//                    Map<Long, OrderPutLogisticsUpdateParcelBO> map = orderPutLogisticsUpdateParcelBOs.stream().collect(Collectors.toMap(OrderPutLogisticsUpdateParcelBO::getOrderId, Function.identity()));
//                    log.info("更新订单 更新包裹map：{}", JSON.toJSON(map));
//
//                    for (OrderDeclarationApplyCarriageNoMessage param : paramList) {
//                        OrderEsSyncSqsMsg msg = new OrderEsSyncSqsMsg();
//                        msg.setOrderId(param.getOrderId());
//                        if (param.getIsApplyCarriage()) {
//                            msg.setUpdateCarriageNo(BasePoConstant.YES_STRING);
//                        } else {
//                            msg.setUpdateCarriageNo(BasePoConstant.NO_STRING);
//                        }
//                        rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.EVENT_ORDER_UPDATE, msg);
//
//                        if (map.containsKey(param.getOrderId())) {
//                            OrderPutLogisticsUpdateParcelBO bo = map.get(param.getOrderId());
//                            OrderPutLogisticsUpdateParcelMsg updateParcelMsg = new OrderPutLogisticsUpdateParcelMsg();
//                            updateParcelMsg.setOrderId(bo.getOrderId());
//                            updateParcelMsg.setOperateType(bo.getOperateType());
//                            updateParcelMsg.setCountryExpressInfoId(bo.getCountryExpressInfoId());
//                            updateParcelMsg.setLogisticsId(bo.getLogisticsId());
//                            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.ORDER_PUT_LOGISTICS_UPDATE_PARCEL, updateParcelMsg);
//                        }
//
//                        if (orderTypeUpdateNotify) {
//                            orderEventService.sendOrderUpdateTypeMsg(param.getOrderId(), userId);
//                        }
//                        orderEventService.orderPriceRefresh(userId, param.getOrderId());
//                    }
//                });
//            }
//        } catch (Exception e) {
//            ExceptionPrintUtil.printErrOneLine(e);
//            throw e;
//        }
//
//        return payment;
//
//    }
@GlobalTransactional
public PaymentDto batchUpdatLogisticsPaymentNew(Long merchantId, Long userId, BatchPutLogisticsReqDto params, PutLogisticsRespDto putLogisticsRespDto) {
    Long logisticsId = params.getLogisticsId();
    List<PutLogisticsInfoDto> putLogisticsInfoDtos = Lists.newArrayList();
    Map<Long, String> transactionCodeMaps = Maps.newHashMap();
    //订单id-是否更新运单号 Map
    Map<Long, Boolean> updateCarriageNoMap = Maps.newHashMap();

    List<Long> orderIds = Lists.newArrayList();
    for (Order order : params.getOrders()) {
            Assert.validateNull(order.getId(), "订单id必传");
            Assert.validateNull(order.getTransactionCode(), "订单transactionCode必传");
            transactionCodeMaps.put(order.getId(), order.getTransactionCode());
            orderIds.add(order.getId());
        }

        List<Order> orders = orderService.findByIds(orderIds);

        for (Order order : orders) {
            boolean partShipped = Objects.equals(order.getStatus(), OrderStatus.PART_SHIPPED.getStatus());
            if (Boolean.TRUE.equals(partShipped)) {
                if (orders.size() > 1) {
                    throw new BusinessException("存在已部分发货订单，不可自行修改物流/地址，请移除后重试");
                } else {
                    throw new BusinessException("该订单已部分发货，不可修改物流，如需调整后续包裹的物流，请联系客服");
                }
            }
        }

        if (CollectionUtils.isNotEmpty(orders)) {
            // 查询旧地址
            orderService.formatAddress(orders.stream().filter(i -> i.getAddress() == null).collect(Collectors.toList()));
        }

        for (PutLogisticsInfoDto putLogisticsInfoDto : putLogisticsRespDto.getPutLogisticsInfoDtos()) {
            if (putLogisticsInfoDto.getStatus().equalsIgnoreCase(OrderService.getLogisticsAvailable())) {
                putLogisticsInfoDtos.add(putLogisticsInfoDto);
            }
        }

        BatchUpdateLogistics batchUpdateLogistics = batchUpdateLogisticsService.save(logisticsId, merchantId, params.getTailLogisticsChannelId());
        ArrayList<OrderImportExtraInfoDto> updateOrderImportExtraInfos = Lists.newArrayList();
        for (PutLogisticsInfoDto putLogisticsInfoDto : putLogisticsInfoDtos) {
            Long orderId = putLogisticsInfoDto.getOrderId();
            if (transactionCodeMaps.get(orderId) == null || !transactionCodeMaps.get(orderId).equalsIgnoreCase(putLogisticsInfoDto.getTransactionCode())) {
                Assert.wrong("订单信息发生变动，请重新进入");
            }
            if (!OrderService.getLogisticsAvailable().equalsIgnoreCase(putLogisticsInfoDto.getStatus())) {
                continue;
            }
            updateOrderImportExtraInfos.add(putLogisticsInfoDto.getUpdateOrderImportInfo());
            batchUpdateLogisticsOrderService.save(batchUpdateLogistics.getId(), putLogisticsInfoDto.getOrderId(), putLogisticsInfoDto.getOrderNo(), putLogisticsInfoDto.getCommissionCharge(), putLogisticsInfoDto.getAddress().getId(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getCountryExpressInfoNewId(), putLogisticsInfoDto.getTransactionCode(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewCarriageAmount(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOldCarriageAmount(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getNewServiceAmount(), putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().getOldServiceAmount());
            if (CollectionUtils.isNotEmpty(putLogisticsInfoDto.getBatchPutLogisticsOrderItemDtos())) {
                for (BatchPutLogisticsOrderItemDto batchPutLogisticsOrderItemDto : putLogisticsInfoDto.getBatchPutLogisticsOrderItemDtos()) {
                    batchUpdateLogisticsOrderItemService.save(batchUpdateLogistics.getId(), putLogisticsInfoDto.getOrderId(), batchPutLogisticsOrderItemDto.getId(), batchPutLogisticsOrderItemDto.getNewServiceAmount(), batchPutLogisticsOrderItemDto.getOldServiceAmount());
                }
            }
        }
        //保存更新的费率
        if (CollectionUtils.isNotEmpty(updateOrderImportExtraInfos)) {
            orderImportExtraInfoFeign.updateBatchById(updateOrderImportExtraInfos);
        }

        PaymentDto payment = new PaymentDto();
        payment.setStatus(PaymentService.STATUS_PAIN);

        List<Order> updateOrders = null;
        //退回到订单用户无需通知类型
        boolean orderTypeUpdateNotify = !(putLogisticsRespDto.getOrderPain() && putLogisticsRespDto.getDifferentAmount() <= 0d && putLogisticsRespDto.getRefundOrderUserId() != null);
        Long paymentUseId = NumberUtils.greaterZero(putLogisticsRespDto.getRefundOrderUserId()) ? putLogisticsRespDto.getRefundOrderUserId() : userId;
        List<RefundParam> refundParams = Lists.newArrayList();
        List<PaymentParam> paymentParams = Lists.newArrayList();
        List<TransactionEntryParam> transactionEntryList = new ArrayList<>();

        try {
            formatTenantPayAndRefundParam(putLogisticsRespDto, refundParams, paymentParams, transactionEntryList);
            boolean merchantPay = putLogisticsRespDto.getOrderPain() && putLogisticsRespDto.getDifferentAmount() > 0D;
            boolean merchantRefund = putLogisticsRespDto.getOrderPain() && putLogisticsRespDto.getDifferentAmount() < 0D;
            if (putLogisticsRespDto.getOrderPain()) {
                formatMerchantPayAndRefundParam(merchantId, userId, params, putLogisticsRespDto, paymentUseId, refundParams, paymentParams, transactionEntryList);
                MultiTransactionCreateParam param = new MultiTransactionCreateParam();
                param.setPaymentList(paymentParams);
                param.setRefundList(refundParams);
                param.setTransactionEntryList(transactionEntryList);
                boolean balanceOrOfflinePay = PaymentMethod.BALANCEPAY.getValue().equalsIgnoreCase(params.getPaymentMethod()) || PaymentMethodEnum.OFFLINE.getCode().equalsIgnoreCase(params.getPaymentMethod());
                if(merchantPay){
                    PaymentDto paymentCreateResult = transactionFeign.createPayment(param);
                    if(balanceOrOfflinePay){
                        PaymentDto paymentResult = transactionFeign.operateTransaction(paymentCreateResult.getId());
                        log.info("merchantPay={}", JSON.toJSONString(paymentResult));
                    }else if (PaymentMethodEnum.needWaitCustomerPaid(params.getPaymentMethod())){
                        batchUpdateLogisticsService.updatePayment(batchUpdateLogistics.getId(), paymentCreateResult.getId(), null, null);
                        payment.setStatus(PaymentService.STATUS_NO_PAIN);
                    }
                }else if(merchantRefund){
                    RefundDto refundCreateResult = transactionFeign.createRefund(param);
                    RefundDto refundDto = transactionFeign.operateRefund(refundCreateResult.getId());
                    log.info("merchantRefund={}", JSON.toJSONString(refundDto));
                }
            }
            List<OrderPutLogisticsUpdateParcelBO> orderPutLogisticsUpdateParcelBOs = new ArrayList<>();
            if (!merchantPay || !PaymentMethodEnum.needWaitCustomerPaid(params.getPaymentMethod())) {
                updateOrders = this.batchUpdateLogisticsNew(batchUpdateLogistics, putLogisticsInfoDtos, null, userId, updateCarriageNoMap, orderPutLogisticsUpdateParcelBOs);
            }

            if (updateOrders != null) {
                List<OrderDeclarationApplyCarriageNoMessage> paramList = Lists.newArrayList();
                for (Order updateOrder : updateOrders) {
                    OrderDeclarationApplyCarriageNoMessage message = new OrderDeclarationApplyCarriageNoMessage();
                    message.setOrderId(updateOrder.getId());
                    message.setLogisticsId(updateOrder.getLogisticsId());
                    message.setIsApplyCarriage(true);
                    Boolean isUpdateCarriageNo = updateCarriageNoMap.get(updateOrder.getId());
                    if (isUpdateCarriageNo != null && !isUpdateCarriageNo) {
                        message.setIsApplyCarriage(false);
                    }
                    paramList.add(message);

                }
                log.info("更新订单消息发送详情：{}", JSON.toJSON(paramList));
                afterCommitExecutor.execute(() -> {
                    log.info("更新订单消息发送详情：{}", JSON.toJSON(paramList));
                    Map<Long, OrderPutLogisticsUpdateParcelBO> map = orderPutLogisticsUpdateParcelBOs.stream().collect(Collectors.toMap(OrderPutLogisticsUpdateParcelBO::getOrderId, Function.identity()));
                    log.info("更新订单 更新包裹map：{}", JSON.toJSON(map));

                    for (OrderDeclarationApplyCarriageNoMessage param : paramList) {
                        OrderEsSyncSqsMsg msg = new OrderEsSyncSqsMsg();
                        msg.setOrderId(param.getOrderId());
                        if (param.getIsApplyCarriage()) {
                            msg.setUpdateCarriageNo(BasePoConstant.YES_STRING);
                        } else {
                            msg.setUpdateCarriageNo(BasePoConstant.NO_STRING);
                        }
                        rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.EVENT_ORDER_UPDATE, msg);

                        if (map.containsKey(param.getOrderId())) {
                            OrderPutLogisticsUpdateParcelBO bo = map.get(param.getOrderId());
                            OrderPutLogisticsUpdateParcelMsg updateParcelMsg = new OrderPutLogisticsUpdateParcelMsg();
                            updateParcelMsg.setOrderId(bo.getOrderId());
                            updateParcelMsg.setOperateType(bo.getOperateType());
                            updateParcelMsg.setCountryExpressInfoId(bo.getCountryExpressInfoId());
                            updateParcelMsg.setLogisticsId(bo.getLogisticsId());
                            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.ORDER_PUT_LOGISTICS_UPDATE_PARCEL, updateParcelMsg);
                        }

                        if (orderTypeUpdateNotify) {
                            orderEventService.sendOrderUpdateTypeMsg(param.getOrderId(), userId);
                        }
                        orderEventService.orderPriceRefresh(userId, param.getOrderId());
                    }
                });
            }
        } catch (Exception e) {
            ExceptionPrintUtil.printErrOneLine(e);
            throw e;
        }

        return payment;

    }

    private void formatMerchantPayAndRefundParam(Long merchantId, Long userId, BatchPutLogisticsReqDto params, PutLogisticsRespDto putLogisticsRespDto, Long paymentUseId, List<RefundParam> refundParams, List<PaymentParam> paymentParams, List<TransactionEntryParam> transactionEntryList) {
        if (putLogisticsRespDto.getDifferentAmount() <= 0d) {
            // 租户退商户
            UserAccountBalanceResp userAccountBalanceResp = merchantUserAccountFeign.getUserBalance(merchantId, userId);
            Double amount = Math.abs(putLogisticsRespDto.getDifferentAmount());
            if (NumberUtils.greaterZero(amount)) {
                RefundParam merchantRefundParam = this.genRefundTenantToMerchantForLogistics(putLogisticsRespDto.getOnlyOffLinePay(), putLogisticsRespDto.getMerchant(), paymentUseId, amount, userAccountBalanceResp);
                refundParams.add(merchantRefundParam);
                List<PutLogisticsInfoDto> putLogisticsInfos = putLogisticsRespDto.getPutLogisticsInfoDtos();
                genTransactionEntryParams(putLogisticsInfos, null,merchantRefundParam.getBizNo(), transactionEntryList);
            }
        }else {
            //商户付给租户
            Double amount = putLogisticsRespDto.getDifferentAmount();
            MerchantBillDisposeMoney merchantBillDisposeMoney = merchantUserAccountService.getMerchantDisposeMoney(putLogisticsRespDto.getMerchant().getId(), paymentUseId);
            PaymentParam merchantPaymentParam = this.genPayMerchantToTenantForLogistics(merchantBillDisposeMoney.getBalanceType(), putLogisticsRespDto.getMerchant(), params.getPaymentMethod(), paymentUseId, amount);
            paymentParams.add(merchantPaymentParam);
            List<PutLogisticsInfoDto> putLogisticsInfos = putLogisticsRespDto.getPutLogisticsInfoDtos();
            genTransactionEntryParams(putLogisticsInfos, merchantPaymentParam.getBizNo(),null, transactionEntryList);
        }
    }

    private void formatTenantPayAndRefundParam(PutLogisticsRespDto putLogisticsRespDto, List<RefundParam> refundParams, List<PaymentParam> paymentParams, List<TransactionEntryParam> transactionEntryList) {
        List<PutLogisticsInfoDto> putLogisticsInfoList = putLogisticsRespDto.getPutLogisticsInfoDtos();
        Map<Long, List<PutLogisticsInfoDto>> productTenantIdPutInfoDtosMap = putLogisticsInfoList.stream()
            .filter(i -> null != i.getOrder() && NumberUtils.greaterZero(i.getOrder().getProductTenantId()))
            .collect(Collectors.groupingBy(i -> i.getOrder().getProductTenantId()));
        for (Map.Entry<Long, List<PutLogisticsInfoDto>> entry : productTenantIdPutInfoDtosMap.entrySet()) {
            Long productTenantId = entry.getKey();
            List<PutLogisticsInfoDto> putLogisticsInfoDtoList = entry.getValue();
            double totalTenantDiffAmount = putLogisticsInfoDtoList.stream().filter(i -> null != i.getBatchPutLogisticsOrderOrderDto()).mapToDouble(i -> i.getBatchPutLogisticsOrderOrderDto().getDifferenceTenantAmount()).sum();
            double absTenantDiffAmount = Math.abs(totalTenantDiffAmount);
            if (totalTenantDiffAmount < 0d) {
                //租户退款
                RefundParam tenantRefundParam = this.genRefundTenantToTenantForLogistics(putLogisticsRespDto.getMerchant(), productTenantId, absTenantDiffAmount);
                refundParams.add(tenantRefundParam);
                genTenantTransactionEntryParams(putLogisticsInfoDtoList, null,tenantRefundParam.getBizNo(), transactionEntryList);
            } else if (totalTenantDiffAmount>0D) {
                //租户付款
                PaymentParam tenantPaymentParam = this.genPayTenantToTenantForLogistics(putLogisticsRespDto.getMerchant(), productTenantId, absTenantDiffAmount);
                paymentParams.add(tenantPaymentParam);
                genTenantTransactionEntryParams(putLogisticsInfoDtoList, null,tenantPaymentParam.getBizNo(), transactionEntryList);
            }
        }
    }

    private static void genTransactionEntryParams(List<PutLogisticsInfoDto> putLogisticsInfos, String payBizNo,
                                                  String refundBizNo,
                                                  List<TransactionEntryParam> transactionEntryList) {
        for (PutLogisticsInfoDto putLogisticsInfo : putLogisticsInfos) {
            BatchPutLogisticsOrderOrderDto amountDto = putLogisticsInfo.getBatchPutLogisticsOrderOrderDto();
            double absDiffAmount = Math.abs(amountDto.getDifferenceMerchantAmount());
            if(!NumberUtils.greaterZero(absDiffAmount)){
                continue;
            }
            Double subCarriageAmount = DoubleUtils.sub(amountDto.getNewCarriageAmount(), amountDto.getOldCarriageAmount());
            Double subServiceAmount = DoubleUtils.sub(amountDto.getNewServiceAmount(), amountDto.getOldServiceAmount());

            boolean boolPay = NumberUtils.greaterZero(amountDto.getDifferenceMerchantAmount());
            String title = getTitle(subCarriageAmount, subServiceAmount, boolPay);
            Integer type = boolPay ? TransactionEntryTypeEnum.PAY.getValue() : TransactionEntryTypeEnum.REFUND.getValue();

            TransactionEntryParam transactionEntryParam = new TransactionEntryParam();
            transactionEntryParam.setRefundBizNo(refundBizNo);
            transactionEntryParam.setPaymentBizNo(payBizNo);
            transactionEntryParam.setBizNoForBill(putLogisticsInfo.getOrderNo());
            transactionEntryParam.setBalance(BigDecimal.valueOf(absDiffAmount));
            transactionEntryParam.setBonus(new BigDecimal(0));
            transactionEntryParam.setType(type);
            transactionEntryParam.setTitle(title);
            transactionEntryList.add(transactionEntryParam);
        }
    }

    private static void genTenantTransactionEntryParams(List<PutLogisticsInfoDto> putLogisticsInfos, String payBizNo,
                                                        String refundBizNo,
                                                        List<TransactionEntryParam> transactionEntryList) {
        for (PutLogisticsInfoDto putLogisticsInfo : putLogisticsInfos) {
            BatchPutLogisticsOrderOrderDto amountDto = putLogisticsInfo.getBatchPutLogisticsOrderOrderDto();
            double absDiffAmount = Math.abs(amountDto.getDifferenceTenantAmount());
            if (!NumberUtils.greaterZero(absDiffAmount)) {
                continue;
            }
            Double subCarriageAmount = DoubleUtils.sub(amountDto.getNewTenantCarriageAmount(), amountDto.getOldTenantCarriageAmount());
            Double subServiceAmount = DoubleUtils.sub(amountDto.getNewTenantServiceAmount(), amountDto.getOldTenantServiceAmount());

            boolean boolPay = NumberUtils.greaterZero(amountDto.getDifferenceTenantAmount());
            String title = getTitle(subCarriageAmount, subServiceAmount, boolPay);
            Integer type = boolPay ? TransactionEntryTypeEnum.PAY.getValue() : TransactionEntryTypeEnum.REFUND.getValue();

            TransactionEntryParam transactionEntryParam = new TransactionEntryParam();
            transactionEntryParam.setRefundBizNo(refundBizNo);
            transactionEntryParam.setPaymentBizNo(payBizNo);
            transactionEntryParam.setBizNoForBill(putLogisticsInfo.getOrderNo());
            transactionEntryParam.setBalance(BigDecimal.valueOf(absDiffAmount));
            transactionEntryParam.setBonus(new BigDecimal(0));
            transactionEntryParam.setType(type);
            transactionEntryParam.setTitle(title);
            transactionEntryList.add(transactionEntryParam);
        }
    }

    @NotNull
    private static String getTitle(Double subCarriageAmount, Double subServiceAmount, boolean boolPay) {
        String title = !subCarriageAmount.equals(0D) && !subServiceAmount.equals(0D) ? "运费" : (!subCarriageAmount.equals(0D) ? "运费" : "服务费");
        title = title + "变动" + (boolPay ? "补差价" : "退款");
        return title;
    }

    public class PaymentPayRefund {
        List<RefundDto> refundDtos;
        List<PaymentDto> payments;
        MerchantRespDto merchantRespDto;

        public PaymentPayRefund() {
        }

        public PaymentPayRefund(List<RefundDto> refundDtos, List<PaymentDto> payments, MerchantRespDto merchantRespDto) {
            this.refundDtos = refundDtos;
            this.payments = payments;
            this.merchantRespDto = merchantRespDto;
        }

        public List<RefundDto> getRefundDtos() {
            return refundDtos;
        }

        public PaymentPayRefund setRefundDtos(List<RefundDto> refundDtos) {
            this.refundDtos = refundDtos;
            return this;
        }

        public List<PaymentDto> getPayments() {
            return payments;
        }

        public PaymentPayRefund setPayments(List<PaymentDto> payments) {
            this.payments = payments;
            return this;
        }

        public MerchantRespDto getMerchantRespDto() {
            return merchantRespDto;
        }

        public PaymentPayRefund setMerchantRespDto(MerchantRespDto merchantRespDto) {
            this.merchantRespDto = merchantRespDto;
            return this;
        }
    }

//    public PaymentPayRefund paySuccessThen(List<RefundParam> refundParams, List<PaymentParam> paymentParams) {
//        List<RefundDto> refundDtos;
//        if (CollectionUtils.isNotEmpty(refundParams)) {
//            refundDtos = refundFeign.batchCreate(new RefundsCreateParam(refundParams));
//        } else {
//            refundDtos = Lists.newArrayList();
//        }
//        List<PaymentDto> payments;
//        if (CollectionUtils.isNotEmpty(paymentParams)) {
//            List<PaymentDto> paymentDtos = paymentFeign.batchCreate(new PaymentsCreateParam(paymentParams));
//            payments = paymentFeign.batchPay(new PaymentsPayParam(paymentDtos.stream().map(PaymentDto::getId).collect(Collectors.toList())));
//        } else {
//            payments = Lists.newArrayList();
//        }
//        return new PaymentPayRefund().setRefundDtos(refundDtos).setPayments(payments);
//    }


    public PaymentDto createAliPayment(List<PaymentParam> paymentParams, List<RefundParam> refundParams, MerchantBillDisposeMoney merchantBillDisposeMoney, Long id, boolean onlyOffLinePay, MerchantRespDto merchant, String paymentMethod, Long userId, Double totalAmount) {
        PaymentParam paymentParam = this.generatePaymentDtoForMerchantPay(onlyOffLinePay, merchantBillDisposeMoney, merchant, paymentMethod, userId, totalAmount);
        String bizNo = paymentParam.getBizNo();
        paymentParams.add(paymentParam);
        Long paymentId = 0L;
        Long tenantPaymentId = 0L;
        Long tenantRefundId = 0L;
        PaymentDto requestPaymentDto = null;
        RefundParam refundParam = null;
        for (RefundParam param : refundParams) {
            refundParam = param;
        }

        List<PaymentDto> paymentDtos = paymentFeign.batchCreate(new PaymentsCreateParam(paymentParams));
        for (PaymentDto paymentDto : paymentDtos) {
            if (paymentDto.getBizNo().equalsIgnoreCase(bizNo)) {
                requestPaymentDto = paymentDto;
                paymentId = paymentDto.getId();
            } else {
                tenantPaymentId = paymentDto.getId();
            }
        }

        if (refundParam != null) {
            RefundParam refundPrepare = refundFeign.prepare(refundParam);
            tenantRefundId = refundPrepare.getId();
        }

        batchUpdateLogisticsService.updatePayment(id, paymentId, tenantPaymentId, tenantRefundId);
        return requestPaymentDto;


    }


    /**
     * 租户付款
     *
     * @param merchant
     * @param userId
     * @param balance
     * @return
     */
    private PaymentParam generatePaymentDtoForTenantPay(MerchantRespDto merchant, Long userId, Double balance) {
        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        //租户一定是通过余额付给商户
        paymentParam.setMethod(PaymentMethodEnum.BALANCE.getCode());
        paymentParam.setBalance(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        paymentParam.setBonus(DoubleUtils.number2BigDecimal(0d, BigDecimal.ROUND_CEILING));
        paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
        paymentParam.setTitle("修改物流付款");
        paymentParam.setTotalAmount(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        paymentParam.setSourceTenantId(merchant.getTenantId());
        paymentParam.setSourceMerchantId(0L);
        paymentParam.setOperateUserId(userId);
        paymentParam.setSourceUserId(userId);
        paymentParam.setSourceRole(PaymentRoleEnum.TENANT.getCode());
        paymentParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());

        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(merchant.getId());

        paymentParam.setTargetMerchantId(0L);
        paymentParam.setTargetTenantId(0L);
        paymentParam.setTargetRole(PaymentRoleEnum.SAAS.getCode());
        paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.NO_USE_BALANCE.getUsedType());
        paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_LOGISTICS.getCode());
        paymentParam.setAlipaySupportPayeeType(AlipaySupportPayeeTypeEnum.ORDER.getPayeeType());
        return PaymentParamUtil.adjustParam(paymentParam);
    }
    private PaymentParam genPayTenantToTenantForLogistics(MerchantRespDto merchant, Long productTenantId, Double balance) {
        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        paymentParam.setPayType(TransactionPayTypeEnum.SUB.getValue());
        paymentParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_LOGISTICS.getCode());
        paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
        paymentParam.setMethod(PaymentMethodEnum.BALANCE.getCode());
        paymentParam.setBalanceType(BalanceUsedType.TENANT_DISTRIBUTION_BALANCE.getUsedType());
        paymentParam.setBalance(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        paymentParam.setBonus(BigDecimal.ZERO);
        paymentParam.setTitle("修改物流付款");

        paymentParam.setSourceRole(PaymentRoleEnum.TENANT_DIS.getCode());
        paymentParam.setSourceTenantId(merchant.getTenantId());
        paymentParam.setSourceMerchantId(0L);
        paymentParam.setSourceUserId(0L);

        paymentParam.setTargetRole(PaymentRoleEnum.TENANT_SUP.getCode());
        paymentParam.setTargetMerchantId(0L);
        paymentParam.setTargetTenantId(productTenantId);

        paymentParam.setOperateRole(PaymentRoleEnum.SYSTEM.getCode());
        paymentParam.setOperateUserId(0L);
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(merchant.getId());
        return paymentParam;
    }

    private RefundParam generateRefundDtoForTenantPay(MerchantRespDto merchant, Long userId, Double balance) {
        RefundParam refundParam = new RefundParam();
        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        //租户一定是通过余额付给商户
        refundParam.setBalance(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(BigDecimal.ZERO);
        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        refundParam.setPayMethod(PaymentMethodEnum.BALANCE.getCode());
        refundParam.setSubject("修改订单退款");
        refundParam.setTotalAmount(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setTargetTenantId(merchant.getTenantId());
        refundParam.setTargetMerchantId(merchant.getId());
        refundParam.setTargetUserId(userId);
        refundParam.setTargetRole(PaymentRoleEnum.TENANT.getCode());
        refundParam.setSourceMerchantId(0L);
        refundParam.setSourceTenantId(0L);
        refundParam.setSourceUserId(0L);
        refundParam.setSourceRole(PaymentRoleEnum.SAAS.getCode());
        refundParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_LOGISTICS.getCode());
        return refundParam;
    }
    private RefundParam genRefundTenantToTenantForLogistics(MerchantRespDto merchant, Long productTenantId, Double balance) {
        RefundParam refundParam = new RefundParam();
        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        refundParam.setPayType(TransactionPayTypeEnum.SUB.getValue());
        refundParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        refundParam.setPayMethod(PaymentMethodEnum.BALANCE.getCode());
        refundParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_LOGISTICS.getCode());
        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        refundParam.setBalanceType(BalanceUsedType.TENANT_DISTRIBUTION_BALANCE.getUsedType());
        refundParam.setBalance(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(BigDecimal.ZERO);
        refundParam.setSubject("修改订单退款");

        refundParam.setSourceRole(PaymentRoleEnum.TENANT_SUP.getCode());
        refundParam.setSourceMerchantId(0L);
        refundParam.setSourceTenantId(productTenantId);
        refundParam.setSourceUserId(0L);

        refundParam.setTargetRole(PaymentRoleEnum.TENANT_DIS.getCode());
        refundParam.setTargetTenantId(merchant.getTenantId());
        refundParam.setTargetMerchantId(0L);
        refundParam.setTargetUserId(0L);

        refundParam.setOperateRole(PaymentRoleEnum.SYSTEM.getCode());
        refundParam.setOperateUserId(0L);
        refundParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        refundParam.setOperateTargetRoleId(merchant.getId());
        return refundParam;
    }

    /**
     * @param onlyOffLinePay
     * @param merchant
     * @param userId
     * @param paymentMethod
     * @param totalAmount
     * @return
     */
    private PaymentParam generatePaymentDtoForMerchantPay(boolean onlyOffLinePay, MerchantBillDisposeMoney merchantBillDisposeMoney, MerchantRespDto merchant, String paymentMethod, Long userId, Double totalAmount) {
        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        //租户一定是通过余额付给商户
        paymentParam.setMethod(paymentMethod);
        if (PaymentMethodEnum.balancePay(paymentParam.getMethod())) {
            paymentParam.setBalance(DoubleUtils.number2BigDecimal(totalAmount, BigDecimal.ROUND_CEILING));
            paymentParam.setBonus(BigDecimal.ZERO);
        } else {
            paymentParam.setBalance(BigDecimal.ZERO);
            paymentParam.setBonus(BigDecimal.ZERO);
        }
        paymentParam.setSourceUserId(0L);

        if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)) {
            if (MerchantUserAccountService.inCommonAccount(merchantBillDisposeMoney.getBalanceType())) {
                paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.COMMON_BALANCE.getUsedType());
            } else {
                paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType());
                paymentParam.setSourceUserId(userId);
            }
        } else {
            paymentParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.NO_USE_BALANCE.getUsedType());
        }
        paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
        paymentParam.setTitle("修改物流支付");
        paymentParam.setTotalAmount(DoubleUtils.number2BigDecimal(totalAmount, BigDecimal.ROUND_CEILING));
        paymentParam.setSourceTenantId(merchant.getTenantId());
        paymentParam.setSourceMerchantId(merchant.getId());
        paymentParam.setOperateUserId(userId);
        paymentParam.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());

        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(merchant.getId());

        boolean openOnlinePay = MerchantService.isOpenOnlinePay(merchant);
        if (openOnlinePay || onlyOffLinePay) {
            //开启线上支付 一定是支付给租户
            //未开启时 如果都是线下支付的，那也一定是支付给租户
            paymentParam.setTargetMerchantId(paymentParam.getSourceMerchantId());
            paymentParam.setTargetTenantId(paymentParam.getSourceTenantId());
            paymentParam.setTargetRole(PaymentRoleEnum.TENANT.getCode());
        } else {
            //支付给saas 平台
            paymentParam.setTargetMerchantId(0L);
            paymentParam.setTargetTenantId(0L);
            paymentParam.setTargetRole(PaymentRoleEnum.SAAS.getCode());
        }
        paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_LOGISTICS.getCode());
        paymentParam.setAlipaySupportPayeeType(AlipaySupportPayeeTypeEnum.ORDER.getPayeeType());
        return PaymentParamUtil.adjustParam(paymentParam);
    }

    private PaymentParam genPayMerchantToTenantForLogistics(String userAccountBalanceType,MerchantRespDto merchant,
                                                            String paymentMethod,Long userId, Double totalAmount) {
        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        paymentParam.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        paymentParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        paymentParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_LOGISTICS.getCode());
        paymentParam.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
        paymentParam.setMethod(paymentMethod);
        Integer balanceType = MerchantUserAccountConstant.getMerchantBalanceTypeForPaymentOrRefund(userAccountBalanceType, paymentMethod);
        paymentParam.setBalanceType(balanceType);
        paymentParam.setBalance(DoubleUtils.number2BigDecimal(totalAmount, BigDecimal.ROUND_CEILING));
        paymentParam.setBonus(BigDecimal.ZERO);
        paymentParam.setTitle("修改物流支付");

        paymentParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateUserId(userId);
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());// 影响了谁
        paymentParam.setOperateTargetRoleId(merchant.getId());

        paymentParam.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setSourceTenantId(merchant.getTenantId());
        paymentParam.setSourceMerchantId(merchant.getId());
        Long sourceUserId = MerchantUserAccountConstant.getSourceUserId(userAccountBalanceType, userId, paymentMethod);
        paymentParam.setSourceUserId(sourceUserId);

        paymentParam.setTargetRole(PaymentRoleEnum.TENANT.getCode());
        paymentParam.setTargetMerchantId(0L);
        paymentParam.setTargetTenantId(merchant.getTenantId());

        return paymentParam;
    }

    private RefundParam generateRefundDtoForMerchantPay(boolean onlyOffLinePay, MerchantRespDto merchant, Long userId, Double totalAmount, UserAccountBalanceResp userAccountBalanceResp) {
        RefundParam refundParam = new RefundParam();
        boolean openOnlinePay = MerchantService.isOpenOnlinePay(merchant);
        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        //租户一定是通过余额付给商户
        refundParam.setBalance(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(totalAmount, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(BigDecimal.ZERO);
        if (onlyOffLinePay) {
            refundParam.setPayMethod(PaymentMethodEnum.OFFLINE.getCode());
        } else {
            refundParam.setPayMethod(PaymentMethodEnum.BALANCE.getCode());
        }
        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        refundParam.setSubject("修改物流退款");

        refundParam.setTotalAmount(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(totalAmount, BigDecimal.ROUND_CEILING));
        refundParam.setTargetTenantId(merchant.getTenantId());
        refundParam.setTargetMerchantId(merchant.getId());
        refundParam.setTargetUserId(userId);
        refundParam.setTargetRole(PaymentRoleEnum.MERCHANT.getCode());


        if (openOnlinePay || onlyOffLinePay) {
            //开启线上支付 一定是租户退款商户
            //未开启时 如果都是线下支付的，那也一定是租户退款给商户
            refundParam.setSourceMerchantId(0L);
            refundParam.setSourceTenantId(merchant.getTenantId());
            refundParam.setSourceRole(PaymentRoleEnum.TENANT.getCode());
        } else {
            //平台 退款给商户
            refundParam.setSourceMerchantId(0L);
            refundParam.setSourceTenantId(0L);
            refundParam.setSourceRole(PaymentRoleEnum.SAAS.getCode());
        }

        if (MerchantUserAccountConstant.ALLOCATION.equalsIgnoreCase(userAccountBalanceResp.getBalanceType())) {
            refundParam.setTargetUserId(userId);
            refundParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType());
        } else {
            refundParam.setTargetUserId(0L);
            refundParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.COMMON_BALANCE.getUsedType());
        }

        refundParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_LOGISTICS.getCode());
        return refundParam;
    }
    private RefundParam genRefundTenantToMerchantForLogistics(boolean onlyOffLinePay, MerchantRespDto merchant, Long userId,
                                                              Double totalAmount, UserAccountBalanceResp userAccountBalanceResp) {
        String payMethod = onlyOffLinePay ? PaymentMethodEnum.OFFLINE.getCode() : PaymentMethodEnum.BALANCE.getCode();
        RefundParam refundParam = new RefundParam();
        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        refundParam.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        refundParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        refundParam.setDetailPurpose(DetailPurpose.MERCHANT_UPDATE_LOGISTICS.getCode());
        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        Integer balanceType = MerchantUserAccountConstant.getMerchantBalanceTypeForPaymentOrRefund(userAccountBalanceResp.getBalanceType(), payMethod);
        refundParam.setBalanceType(balanceType);
        refundParam.setBalance(DoubleUtils.number2BigDecimal(totalAmount, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(BigDecimal.ZERO);
        refundParam.setPayMethod(payMethod);
        refundParam.setSubject("修改物流退款");

        refundParam.setOperateRole(PaymentRoleEnum.MERCHANT.getCode());
        refundParam.setOperateUserId(userId);
        refundParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        refundParam.setOperateTargetRoleId(merchant.getId());

        refundParam.setSourceRole(PaymentRoleEnum.TENANT.getCode());
        refundParam.setSourceMerchantId(0L);
        refundParam.setSourceTenantId(merchant.getTenantId());

        refundParam.setTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        refundParam.setTargetTenantId(0L);
        refundParam.setTargetMerchantId(merchant.getId());
        Long targetUserId = MerchantUserAccountConstant.getTargetUserId(userAccountBalanceResp.getBalanceType(), userId, payMethod);
        refundParam.setTargetUserId(targetUserId);
        return refundParam;
    }


    public PutLogisticsRespDto getPutLogisticsInfosByOrders(Long merchantId, Long userId, Long logisticsId
            , List<Order> orders, boolean changeAddress) {

        List<String> tiktokAutoImportOrderNos = ListUtil.toValueDistinctList(orders, Order::getOutOrderNo
                , i -> MerchantStorePlatformEnum.TIKTOK.equalsCode(i.getMerchantStorePlatformCode())
                        && StrUtil.isNotBlank(i.getOutOrderNo()) && com.sdsdiy.orderapi.constant.OrderOriginType.isAutoImport(i.getOriginType()));

        List<String> tiktokOnLineLogisticsOutNoList = platformOrderExtendFeign.matchValueByOutIds(TIKTOK_ONLINE_LOGISTICS,
                BaseListDto.of(tiktokAutoImportOrderNos), com.sdsdiy.orderapi.constant.OrderOriginType.AUTO_IMPORT.getCode());

        MerchantRespDto merchant = merchantFeign.getMerchantById(merchantId);
        boolean openPay = MerchantService.isOpenOnlinePay(merchant);
        PutLogisticsRespDto putLogisticsRespDto = new PutLogisticsRespDto();
        putLogisticsRespDto.setMerchant(merchant);
        orderService.formatMerchantStore(orders);
        List<PutLogisticsInfoDto> putLogisticsInfoDtos = Lists.newArrayList();
        List<BatchLogisticsFreightReqDto> batchLogisticsFreightReqDtos = Lists.newArrayList();
        List<PutLogisticsInfoDto> requestLogisticsInfoDtos = Lists.newArrayList();

        LogisticsRespDto logisticsRespDto = tenantLogisticsFeign.getOneLogisticsResp(logisticsId);
        Assert.validateNull(logisticsRespDto, "物流不存在");
        Long bayAreaId = null;
        boolean orderPain = false;
        List<Long> orderIds = Lists.newArrayList();
        for (Order order : orders) {
            orderIds.add(order.getId());
        }
        List<OrderImportExtraInfo> orderImportExtraInfos = orderImportExtraInfoDao.findByIds(orderIds);
        Map<Long, OrderImportExtraInfo> orderImportExtraInfoMap = orderImportExtraInfos.stream().collect(Collectors.toMap(OrderImportExtraInfo::getId, Function.identity()));

        boolean jitLogistics = DeliveryTypeEnum.isJit(logisticsRespDto.getCodeId());
        List<Long> jitOrderIds = orderExtendInfoFeign.getJitOrderIds(orderIds);
        Assert.validateTrue(changeAddress && CollUtil.isNotEmpty(jitOrderIds), "JIT订单修改地址请去速卖通后台操作");

        Map<Long, PickUpLogisticsInfo> pickUpLogisticsInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orderIds)) {
            List<PickUpLogisticsInfo> pickUpLogisticsInfos = pickUpLogisticsInfoDao.listByOrderId(orderIds);
            for (PickUpLogisticsInfo pickUpLogisticsInfo : pickUpLogisticsInfos) {
                pickUpLogisticsInfoMap.put(pickUpLogisticsInfo.getOrderId(), pickUpLogisticsInfo);
            }
        }

        List<Long> orderItemIds = Lists.newArrayList();
        Map<Long, OrderUserUpdateCheckDTO> orderUserUpdateCheckDTOMap = orderService.getOrderPaymentCheck(merchantId, userId, orderIds);
        for (Order order : orders) {
            for (OrderItem item : order.getItems()) {
                if (OrderStatus.statusRepeat(item.getStatus())) {
                    orderItemIds.add(item.getId());
                }
            }
        }
        Map<Long, OrderItemPriceRespDto> orderItemPriceRespDtoMap = orderService.getItemPriceMap(orderItemIds);
        Map<String, ProductPriceResp> productPriceRespMap = orderService.getPriceMap(merchantId, logisticsId, orders);

//        Map<String, Set<String>> temuUsToCaMap = getTemuUsToCaMap(orders);

        IdsSearchHelper idsSearchHelper = new IdsSearchHelper();
        idsSearchHelper.setIds(orderIds);
        Map<Long, OrderAmountRespDTO> orderAmountRespDTOMap = orderFeign.amountMap(idsSearchHelper);
        Set<Boolean> payTypes = Sets.newHashSet();
        for (Order order : orders) {
            Assert.validateBool(order.getMerchantId().equals(merchantId), "订单id不合法");
            if (orders.size() > 1 && order.getOrigin().equalsIgnoreCase(OrderOriginType.FBA.getValue())) {
                com.ps.support.Assert.wrong("fba订单不支持批量修改物流");
            }
            if (bayAreaId == null) {
                bayAreaId = order.getIssuingBayAreaId();
            }
            Boolean currentOrderPain = !OrderStatus.noPain(order.getStatus());
            //无值 未支付 则取当前值
            if (!bayAreaId.equals(order.getIssuingBayAreaId())) {
                com.ps.support.Assert.wrong("批量修改物流时仓区域位必须相同");
            }
            OrderAmountRespDTO orderAmountRespDTO = orderAmountRespDTOMap.get(order.getId());
            order.setProductTenantId(null!=orderAmountRespDTO?orderAmountRespDTO.getProductTenantId():null);
            PutLogisticsInfoDto putLogisticsInfoDto = new PutLogisticsInfoDto();
            putLogisticsInfoDto.setAddress(order.getAddress());
            putLogisticsInfoDto.setOrderPain(currentOrderPain);
            putLogisticsInfoDto.setOrderNo(order.getNo());
            putLogisticsInfoDto.setOrderId(order.getId());
            Double serviceAmount = DoubleUtils.sub(order.getServiceAmount(), order.getRefundServiceAmount());
            putLogisticsInfoDto.setNewAmount(0D);
            putLogisticsInfoDto.setOldLogistics(order.getLogistics());
            if (order.getLogistics() == null && StringUtils.isNotBlank(order.getCarriageName())) {
                Logistics logistics = new Logistics();
                logistics.setId(0L);
                logistics.setName(order.getCarriageName());
                putLogisticsInfoDto.setOldLogistics(logistics);
            }
            double commissionCharge = 0D;
            Double leftLogisticsAmount = order.getCurrentCarriageAmount();
            Double oldAmount = DoubleUtils.add(leftLogisticsAmount, serviceAmount);

            Logistics logistics = order.getLogistics();
            if (logistics != null && LogisticsService.resetNum(logistics) &&
                    (StringUtils.isNotBlank(order.getCarriageNo()) ||
                            LogisticsNumber.XUN_FENG.getNumber().equals(logistics.getServiceProviderId()) && Arrays.asList(OrderStatus.STOCK_UP.getStatus(), OrderStatus.FINISH.getStatus()).contains(order.getStatus()))) {
                commissionCharge = LogisticsService.commissionCharge(leftLogisticsAmount, logistics);
            }
            oldAmount = DoubleUtils.add(oldAmount, commissionCharge);

            putLogisticsInfoDto.setOldAmount(oldAmount);
            putLogisticsInfoDto.setCommissionCharge(commissionCharge);
            putLogisticsInfoDto.setOrder(order);
            putLogisticsInfoDto.setOrderStatus(com.sdsdiy.orderdata.enums.OrderStatus.orderStatus(order.getStatus(), order.getDesignStatus()));
            putLogisticsInfoDto.setTransactionCode(order.getTransactionCode());
            putLogisticsInfoDto.setStatus(OrderService.getLogisticsAvailable());

            BatchPutLogisticsOrderOrderDto batchPutLogisticsOrderOrderDto = new BatchPutLogisticsOrderOrderDto();
            batchPutLogisticsOrderOrderDto.setOldCarriageAmount(order.getCurrentCarriageAmount());
            batchPutLogisticsOrderOrderDto.setOldServiceAmount(DoubleUtils.sub(order.getServiceAmount(), order.getRefundServiceAmount()));

            batchPutLogisticsOrderOrderDto.setOrderId(order.getId());
            batchPutLogisticsOrderOrderDto.setAddressId(order.getAddressId());
            batchPutLogisticsOrderOrderDto.setOrderNo(order.getNo());
            putLogisticsInfoDto.setBatchPutLogisticsOrderOrderDto(batchPutLogisticsOrderOrderDto);
            PickUpLogisticsInfo pickUpLogisticsInfo = pickUpLogisticsInfoMap.get(order.getId());
            boolean payActivityChange = false;
            if (currentOrderPain) {
                //已支付
                payTypes.add(PaymentMethodEnum.isOffline(orderAmountRespDTOMap.get(order.getId()).getPaymentMethod()));
                for (OrderItem item : order.getItems()) {
                    if (OrderStatus.isCancel(item.getStatus())) {
                        continue;
                    }
                    String key = ProductPriceResp.priceKey(order.getNo(), item.getProductId());
//                    Map<Long, OrderItemPriceRespDto> orderItemPriceRespDtoMap = this.getItemPriceMap(orderItemIds);
//                    Map<String, ProductPriceResp> productPriceRespMap
                    OrderItemPriceRespDto orderItemPriceRespDto = orderItemPriceRespDtoMap.get(item.getId());
                    Long oldActivityId = orderItemPriceRespDto != null && NumberUtils.greaterZero(orderItemPriceRespDto.getActivityId()) ? orderItemPriceRespDto.getActivityId() : 0L;
                    ProductPriceResp productPriceResp = productPriceRespMap.get(key);
                    Long newActivityId = productPriceResp != null && productPriceResp.getActivity() != null ? productPriceResp.getActivity().getId() : 0L;
                    if (!payActivityChange) {
                        payActivityChange = !oldActivityId.equals(newActivityId);
                    }
                }
            }
            if (payActivityChange) {
                putLogisticsInfoDto.setMsg("涉及活动变更，请使用编辑产品编辑物流");
                putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
            } else if (pickUpLogisticsInfo != null) {
                putLogisticsInfoDto.setMsg("已完成质检");
                putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
            } else if (OrderStatus.isCancel(order.getStatus())) {
                putLogisticsInfoDto.setMsg("订单已取消");
                putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
            } else if (OrderStatus.FINISH.getStatus() == order.getStatus()) {
                putLogisticsInfoDto.setMsg("订单已完成");
                putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
            } else if (OrderStatus.NONE.getStatus() == order.getStatus()) {
                putLogisticsInfoDto.setMsg("订单待编辑");
                putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
            } else if (jitLogistics && !jitOrderIds.contains(order.getId())) {
                putLogisticsInfoDto.setMsg("物流不可用");
                putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
            } else if (!jitLogistics && jitOrderIds.contains(order.getId())) {
                putLogisticsInfoDto.setMsg("物流不可用");
                putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
            } else if (MerchantStorePlatformEnum.TEMU.equalsCode(order.getMerchantStorePlatformCode())) {
//                Set<String> values = temuUsToCaMap.getOrDefault(order.getOutOrderNo(), Collections.emptySet());
//                if (values.size() > 1) {
//                    putLogisticsInfoDto.setMsg(EnumPaymentCheck.TEMU_US2CA_BC_BBC.getMsg());
//                    putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
//                } else if (!LogisticsServiceProviderEnum.TEMU_ONLINE.equalsNumber(logisticsRespDto.getServiceProviderId())
//                        && values.contains(PlatformOrderItemExtendValueEnum.TEMU_US_TO_CA_BC.getCode())) {
//                    putLogisticsInfoDto.setMsg("物流不可用");
//                    putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
//                } else if (LogisticsServiceProviderEnum.TEMU_ONLINE.equalsNumber(logisticsRespDto.getServiceProviderId())
//                        && values.contains(PlatformOrderItemExtendValueEnum.TEMU_US_TO_CA_BBC.getCode())) {
//                    putLogisticsInfoDto.setMsg("物流不可用");
//                    putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
//                }
            }
            if (OrderService.getLogisticsAvailable().equals(putLogisticsInfoDto.getStatus())) {
                // 物流可用
                BatchLogisticsFreightReqDto batchLogisticsFreightReqDto = new BatchLogisticsFreightReqDto();
                LogisticsAddressReqDto logisticsAddressReqDto = new LogisticsAddressReqDto();
                logisticsAddressReqDto.setCountryCode(order.getAddress().getCountry());

                logisticsAddressReqDto.setPostcode(CompareUtils.aElseB(order.getAddress().getPostcode(), null));
                logisticsAddressReqDto.setProvinceCode(CompareUtils.aElseB(order.getAddress().getProvinceCode(), null));
                logisticsAddressReqDto.setProvince(CompareUtils.aElseB(order.getAddress().getProvince(), null));
                logisticsAddressReqDto.setCityCode(CompareUtils.aElseB(order.getAddress().getCityCode(), null));
                logisticsAddressReqDto.setAddressType(CompareUtils.aElseB(order.getAddress().getType(), null));
                batchLogisticsFreightReqDto.setAddress(logisticsAddressReqDto);

                batchLogisticsFreightReqDto.setMerchantStorePlatformCode(order.getMerchantStorePlatformCode());
                batchLogisticsFreightReqDto.setOutOrderNo(order.getOutOrderNo());
                batchLogisticsFreightReqDto.setOrderOriginType(order.getOriginType());
                List<LogisticsFreightProductReqDto> productList = Lists.newArrayList();
                for (OrderItem item : order.getItems()) {
                    if (OrderStatus.statusRepeat(item.getStatus())) {
                        LogisticsFreightProductReqDto logisticsFreightProductReqDto = new LogisticsFreightProductReqDto();
                        Product product = item.getProduct();
                        logisticsFreightProductReqDto.setId(product.getId());
                        logisticsFreightProductReqDto.setWeight(product.getWeight());
                        logisticsFreightProductReqDto.setNum(item.getNum());
                        logisticsFreightProductReqDto.setBoxLength(product.getBoxLength());
                        logisticsFreightProductReqDto.setBoxWidth(product.getBoxWidth());
                        logisticsFreightProductReqDto.setBoxHeight(product.getBoxHeight());
                        logisticsFreightProductReqDto.setCurrentPrice(BigDecimal.valueOf(item.getPrice()));
                        productList.add(logisticsFreightProductReqDto);
                    }
                }
                OrderImportExtraInfo orderImportExtraInfo = orderImportExtraInfoMap.get(order.getId());
                batchLogisticsFreightReqDto.setGoodsTotalPrice(BigDecimal.valueOf(order.getProductAmount()));
                batchLogisticsFreightReqDto.setProductList(productList);
                batchLogisticsFreightReqDto.setTenantId(order.getTenantId());
                if (currentOrderPain) {
                    batchLogisticsFreightReqDto.setTenantCarriageCommissionRate(orderImportExtraInfo.getTenantCarriageCommissionRate());
                }
                if (tiktokOnLineLogisticsOutNoList.contains(order.getOutOrderNo())) {
                    batchLogisticsFreightReqDto.setOrderMode(TIKTOK_ONLINE_LOGISTICS.getCode());
                }
                batchLogisticsFreightReqDtos.add(batchLogisticsFreightReqDto);
                requestLogisticsInfoDtos.add(putLogisticsInfoDto);
            }
            putLogisticsInfoDtos.add(putLogisticsInfoDto);
        }
        Double differentAmount = 0d;
        Double tenantDifferentAmount = 0d;
        Double differentTotalAmount = 0d;
        Assert.validateTrue(ListUtil.moreOneSize(payTypes), "不能同时修改线上与线下的订单");
        boolean onlyOffLinePay = false;
        if (CollectionUtils.isNotEmpty(payTypes)) {
            for (Boolean payType : payTypes) {
                onlyOffLinePay = payType;
            }
        }
        if (CollectionUtils.isNotEmpty(batchLogisticsFreightReqDtos)) {
            List<BatchLogisticsFreightRespDto> batchLogisticsFreightRespDtos = logisticsExpensesFeign.batchFreightList(batchLogisticsFreightReqDtos, logisticsId);
            //先批量获取需要计算费报告申报价
            List<Order> calTariffRateOrders = this.getCalTariffRateOrders(requestLogisticsInfoDtos, batchLogisticsFreightRespDtos);
            //获取产品的申报信息
            Map<String, OrderDeclarationDto> countryCodeProductIdKeyOrderDeclarationDtoMap = this.getCountryCodeProductIdKeyOrderDeclarationMap(logisticsId, calTariffRateOrders);
            //获取订单的申报信息
            Map<Long, List<OrderProductDeclarationResp>> orderIdKeyOrderDeclaraMap = this.getOrderIdKeyOrderDeclaraMap(calTariffRateOrders);

            for (int index = 0; index < batchLogisticsFreightRespDtos.size(); index++) {
                BatchLogisticsFreightRespDto batchLogisticsFreightRespDto = batchLogisticsFreightRespDtos.get(index);
                PutLogisticsInfoDto putLogisticsInfoDto = requestLogisticsInfoDtos.get(index);
                if (0 < batchLogisticsFreightRespDto.getIsUpdated()) {
                    Order order = putLogisticsInfoDto.getOrder();
                    OrderAmountRespDTO orderAmountRespDTO = orderAmountRespDTOMap.get(order.getId());
                    //付款时存该值
                    OrderImportExtraInfoDto updateOrderImportExtraInfo = new OrderImportExtraInfoDto();
                    updateOrderImportExtraInfo.setId(order.getId());
                    updateOrderImportExtraInfo.setTenantCarriageCommissionRate(batchLogisticsFreightRespDto.getTenantCarriageCommissionRate());
                    updateOrderImportExtraInfo.setCarriageFarawayType(batchLogisticsFreightRespDto.getCarriageFarawayType());
                    updateOrderImportExtraInfo.setCarriageFarawayAmount(batchLogisticsFreightRespDto.getCarriageFarawayAmount());
                    putLogisticsInfoDto.setUpdateOrderImportInfo(updateOrderImportExtraInfo);

                    Double serviceAmout = batchLogisticsFreightRespDto.getServiceAmount();
                    Map<Long, BigDecimal> productTariffRateMap = Collections.emptyMap();
                    //欧盟的逻辑
                    if (EuropeUnionEnum.isEuropeUnion(order.getAddress().getCountry())) {
                        boolean isCollectVatAbleMoney = orderService.isCollectVatAbleMoney(order, logisticsRespDto, orderImportExtraInfoMap.get(order.getId()));
                        if (isCollectVatAbleMoney) {
                            productTariffRateMap = batchLogisticsFreightRespDto.getProductIdKeyAndTariffRateMap();
                        }
                    } else {
                        productTariffRateMap = batchLogisticsFreightRespDto.getProductIdKeyAndTariffRateMap();
                        serviceAmout = DoubleUtils.add(serviceAmout, batchLogisticsFreightRespDto.getClearanceFee());
                    }
                    if (putLogisticsInfoDto.getOrderPain()) {
                        orderPain = true;
                    }
                    if (orderPain && EndProductType.isPrivate(order.getProductionType())) {
                        serviceAmout = DoubleUtils.sub(order.getServiceAmount(), order.getRefundServiceAmount());
                    }
                    List<BatchPutLogisticsOrderItemDto> itemDtos = Lists.newArrayList();
                    for (OrderItem item : order.getItems()) {
                        if (OrderStatus.statusRepeat(item.getStatus())) {
                            //排除取消的
                            Double itemServiceAmount = 0d;
                            Double itemRealityServiceAmount = 0d;
                            if (CountryExpressInfoNewConstant.CHARGE_TYPE_PRODUCT_NUM.equalsIgnoreCase(batchLogisticsFreightRespDto.getCountryExpressInfoNewRespDto().getChargeType())) {
                                itemRealityServiceAmount = com.sdsdiy.common.base.helper.DoubleUtils.mul(item.getNum(), batchLogisticsFreightRespDto.getCountryExpressInfoNewRespDto().getPerUnitCost());
                            } else if (LogisticsCodeId.isFba(batchLogisticsFreightRespDto.getCodeId())) {
                                itemServiceAmount = DoubleUtils.mul(item.getNum(), OrderService.getServiceUnitAmount());
                                itemRealityServiceAmount = itemServiceAmount;
                            } else {
                                //订单有申报价按照订单的走
                                itemServiceAmount = this.getTariffAmount(countryCodeProductIdKeyOrderDeclarationDtoMap
                                        , orderIdKeyOrderDeclaraMap, order, productTariffRateMap, item, itemServiceAmount);
                                itemRealityServiceAmount = itemServiceAmount;
                            }
                            serviceAmout = DoubleUtils.add(serviceAmout, itemServiceAmount);
                            BatchPutLogisticsOrderItemDto batchPutLogisticsOrderItemDto = new BatchPutLogisticsOrderItemDto();
                            batchPutLogisticsOrderItemDto.setId(item.getId());
                            batchPutLogisticsOrderItemDto.setOldServiceAmount(item.getServiceAmount());
                            batchPutLogisticsOrderItemDto.setNewServiceAmount(itemRealityServiceAmount);
                            itemDtos.add(batchPutLogisticsOrderItemDto);
                        }
                    }

                    Double newAmount = DoubleUtils.add(serviceAmout, batchLogisticsFreightRespDto.getFreight());
                    Double currentDifferentAmount = DoubleUtils.sub(newAmount, putLogisticsInfoDto.getOldAmount());
                    if (putLogisticsInfoDto.getOrderPain()) {
                        OrderUserUpdateCheckDTO orderUserUpdateCheckDTO = orderUserUpdateCheckDTOMap.get(order.getId());
                        if (orderUserUpdateCheckDTO != null && StringUtil.isNotBlank(orderUserUpdateCheckDTO.getMsg())) {
                            //私有用户操作公有订单 或公有用户操作私有订单 只有单个退款的可以过，其他不可以过
                            if (NumberUtils.greaterZero(currentDifferentAmount) || !ListUtil.oneSize(orders)) {
                                putLogisticsInfoDto.setMsg(orderUserUpdateCheckDTO.getMsg());
                                putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
                                continue;
                            } else {
                                //如果 是公有的直接退回公有 0代表退回公有占虎
                                Long refundUserId = orderUserUpdateCheckDTO.getPaymentUid() == null ? 0L : orderUserUpdateCheckDTO.getPaymentUid();
                                log.info("修改物流退回的用户ID {}", orderUserUpdateCheckDTO.getPaymentUid());
                                putLogisticsRespDto.setRefundOrderUserId(refundUserId);
                            }
                        }
                    }
                    BatchPutLogisticsOrderOrderDto batchPutLogisticsOrderOrderDto = putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto();
                    batchPutLogisticsOrderOrderDto.setNewCarriageAmount(batchLogisticsFreightRespDto.getFreight());
                    batchPutLogisticsOrderOrderDto.setNewServiceAmount(serviceAmout);
                    batchPutLogisticsOrderOrderDto.setDifferenceMerchantAmount(currentDifferentAmount);
                    batchPutLogisticsOrderOrderDto.setCountryExpressInfoNewId(batchLogisticsFreightRespDto.getCountryExpressInfoNew().getId());
                    putLogisticsInfoDto.setNewAmount(newAmount);
                    putLogisticsInfoDto.setBatchPutLogisticsOrderItemDtos(itemDtos);
//                   putLogisticsInfoDto.getBatchPutLogisticsOrderOrderDto().setOldTenantCarriageAmount(orderAmountRespDTO == null ? 0D : orderAmountRespDTO.getTenantCarriageAmount());
                    differentTotalAmount = DoubleUtils.add(differentTotalAmount, currentDifferentAmount);

                    if (putLogisticsInfoDto.getOrderPain()) {
                        differentAmount = DoubleUtils.add(differentAmount, currentDifferentAmount);
                        //排除租户物流来源（租户物流无需支付运费和服务费）
                        if (openPay && !EnumOrderPayType.onlyPayTenant(orderAmountRespDTO.getPaymentType()) &&
                            !DistributionProductLogisticsSourceEnum.isTenantLogistics(order.getLogisticsSource())) {
                            Double tenantCarriageAmount;
                            //租户给saas的钱若有佣金的扣处佣金的钱
                            if (batchLogisticsFreightRespDto.getTenantCarriageCommissionRate() != null && batchLogisticsFreightRespDto.getTenantCarriageCommissionRate().compareTo(BigDecimal.ZERO) > 0) {
                                tenantCarriageAmount = batchLogisticsFreightRespDto.getOriginalFreight();
                            } else {
                                //表示租户也要付相同价格的钱
                                tenantCarriageAmount = batchLogisticsFreightRespDto.getFreight();
                            }
                            Double newTenantAmount = NumberUtil.add(serviceAmout, tenantCarriageAmount);
                            Double originTenantCarriageAmount = NumberUtil.sub(orderAmountRespDTO.getTenantCarriageAmount(), orderAmountRespDTO.getTenantRefundCarriageAmount());
                            Double originTenantServiceAmount = NumberUtil.sub(orderAmountRespDTO.getTenantServiceAmount(), orderAmountRespDTO.getTenantRefundServiceAmount());
                            BigDecimal currentTenantDifferentAmount = NumberUtil.sub(newTenantAmount, originTenantCarriageAmount, originTenantServiceAmount);

                            batchPutLogisticsOrderOrderDto.setOldTenantCarriageAmount(originTenantCarriageAmount);
                            batchPutLogisticsOrderOrderDto.setOldTenantServiceAmount(originTenantServiceAmount);
                            batchPutLogisticsOrderOrderDto.setNewTenantCarriageAmount(tenantCarriageAmount);
                            batchPutLogisticsOrderOrderDto.setNewTenantServiceAmount(serviceAmout);
                            batchPutLogisticsOrderOrderDto.setDifferenceTenantAmount(currentTenantDifferentAmount.doubleValue());

                            tenantDifferentAmount = DoubleUtils.add(tenantDifferentAmount, currentTenantDifferentAmount);
                            log.info("租户物流费用计算：租户物流费{}，服务费{},旧的租户物流费{}，旧的服务费：{} ", tenantCarriageAmount, serviceAmout, originTenantCarriageAmount, originTenantServiceAmount);
                            log.info("租户物流费差值：{},变更费用：{}", currentTenantDifferentAmount, tenantDifferentAmount);
                            // tenantDifferentAmount = DoubleUtils.add(tenantDifferentAmount, currentDifferentAmount);
                        }
                    }
                } else {
                    // 不可修改
                    putLogisticsInfoDto.setMsg(batchLogisticsFreightRespDto.getErrorMessage());
                    putLogisticsInfoDto.setStatus(OrderService.getLogisticsNoAvailable());
                }
            }
            putLogisticsRespDto.setValid(true);
        } else {
            putLogisticsRespDto.setValid(false);
        }

        putLogisticsRespDto.setPutLogisticsInfoDtos(putLogisticsInfoDtos);
        putLogisticsRespDto.setTenantDifferentAmount(tenantDifferentAmount);
        putLogisticsRespDto.setOnlyOffLinePay(onlyOffLinePay);
        putLogisticsRespDto.setDifferentTotalAmount(differentTotalAmount);
        putLogisticsRespDto.setDifferentAmount(differentAmount);
        putLogisticsRespDto.setBoolPain(0 < differentAmount);
        putLogisticsRespDto.setOrderPain(orderPain);
        return putLogisticsRespDto;

    }

    @NotNull
    private List<Order> getCalTariffRateOrders(List<PutLogisticsInfoDto> requestLogisticsInfoDtos, List<BatchLogisticsFreightRespDto> batchLogisticsFreightRespDtos) {
        List<Order> calTariffRateOrders = Lists.newArrayList();
        for (int index = 0; index < batchLogisticsFreightRespDtos.size(); index++) {
            BatchLogisticsFreightRespDto batchLogisticsFreightRespDto = batchLogisticsFreightRespDtos.get(index);
            PutLogisticsInfoDto putLogisticsInfoDto = requestLogisticsInfoDtos.get(index);
            //可更新的订单且有申报价的才有费用
            if (0 < batchLogisticsFreightRespDto.getIsUpdated() && CollectionUtil.isNotEmpty(batchLogisticsFreightRespDto.getProductIdKeyAndTariffRateMap())) {
                calTariffRateOrders.add(putLogisticsInfoDto.getOrder());
            }
        }
        return calTariffRateOrders;
    }

    private Double getTariffAmount(Map<String, OrderDeclarationDto> countryCodeProductIdKeyOrderDeclarationDtoMap
            , Map<Long, List<OrderProductDeclarationResp>> orderIdKeyOrderDeclaraMap, Order order
            , Map<Long, BigDecimal> productTariffRateMap, OrderItem item, Double itemServiceAmount) {
        List<OrderProductDeclarationResp> orderProductDeclarations = orderIdKeyOrderDeclaraMap.get(order.getId());
        if (CollectionUtil.isNotEmpty(orderProductDeclarations)) {
            Map<Long, OrderProductDeclarationResp> orderItemIdKeyDeclare = orderProductDeclarations.stream().collect(Collectors.toMap(OrderProductDeclarationResp::getOrderItemId, Function.identity(), (a, b) -> a));
            OrderProductDeclarationResp orderProductDeclaration = orderItemIdKeyDeclare.get(item.getId());
            if (orderProductDeclaration != null) {
                itemServiceAmount = DeclarationServiceAmountUtil.getAmount(orderProductDeclaration, item.getNum(), productTariffRateMap);
            }
        } else {
            //产品的申报价
            OrderDeclarationDto orderDeclarationDto = countryCodeProductIdKeyOrderDeclarationDtoMap.get(order.getAddress().getCountry() + "_" + item.getProductId());
            if (orderDeclarationDto != null) {
                itemServiceAmount = DeclarationServiceAmountUtil
                        .getAmount(orderDeclarationDto, item.getNum(), productTariffRateMap);
            }
        }
        return itemServiceAmount;
    }

    @NotNull
    private Map<Long, List<OrderProductDeclarationResp>> getOrderIdKeyOrderDeclaraMap(List<Order> calTariffRateOrders) {
        Map<Long, List<OrderProductDeclarationResp>> orderIdKeyOrderDeclaraMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(calTariffRateOrders)) {
            List<Long> calTariffRateOrderIds = calTariffRateOrders.stream().map(Order::getId).collect(Collectors.toList());
            List<OrderProductDeclarationResp> orderDeclarationsByOrderIds = orderProductDeclarationFeign.getOrderDeclarationsByOrderIds(new BaseListReqDto(calTariffRateOrderIds));
            orderIdKeyOrderDeclaraMap = orderDeclarationsByOrderIds.stream().collect(Collectors.groupingBy(OrderProductDeclarationResp::getOrderId));
        }
        return orderIdKeyOrderDeclaraMap;
    }

    @NotNull
    private Map<String, OrderDeclarationDto> getCountryCodeProductIdKeyOrderDeclarationMap(Long logisticsId, List<Order> calTariffRateOrders) {
        Map<String, OrderDeclarationDto> countryCodeProductIdKeyOrderDeclarationDtoMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(calTariffRateOrders)) {
            Map<String, List<Order>> countryKeyOrders = calTariffRateOrders.stream().collect(Collectors.groupingBy(o -> o.getAddress().getCountry()));
            for (Map.Entry<String, List<Order>> entry : countryKeyOrders.entrySet()) {
                String countryCode = entry.getKey();
                Order orderFirst = entry.getValue().get(0);
                List<Long> productIds = Lists.newArrayList();
                for (Order order : entry.getValue()) {
                    productIds.addAll(order.getItems().stream().map(OrderItem::getProductId).collect(Collectors.toList()));
                }
                Map<Long, OrderDeclarationDto> declarationMap = orderService.getDeclaration(orderFirst.getMerchantId(), logisticsId, productIds, orderFirst.getMerchantStoreId(), countryCode);
                declarationMap.forEach((k, v) -> countryCodeProductIdKeyOrderDeclarationDtoMap.put(countryCode + "_" + k, v));
            }
        }
        return countryCodeProductIdKeyOrderDeclarationDtoMap;
    }


    //    public List<Order> batchUpdateLogisticsPaymentSuccess(PaymentDto payment, BatchUpdateLogistics batchUpdateLogistics) {
//        List<BatchUpdateLogisticsOrder> batchUpdateLogisticsOrders = batchUpdateLogisticsOrderService.findByBatchId(batchUpdateLogistics.getId());
//        List<BatchUpdateLogisticsOrderItem> batchUpdateLogisticsOrderItems = batchUpdateLogisticsOrderItemService.findByBatchId(batchUpdateLogistics.getId());
//        List<Long> orderIds = batchUpdateLogisticsOrders.stream().map(BatchUpdateLogisticsOrder::getOrderId).collect(Collectors.toList());
//        IdsSearchHelper param = new IdsSearchHelper();
//        param.setIds(orderIds);
//        Map<Long, OrderImportExtraInfoDto> orderImportExtraInfoMap = orderImportExtraInfoFeign.findMapByIds(param);
//        List<PutLogisticsInfoDto> putLogisticsInfoDtos = Lists.newArrayList();
//        for (BatchUpdateLogisticsOrder batchUpdateLogisticsOrder : batchUpdateLogisticsOrders) {
//            PutLogisticsInfoDto putLogisticsInfoDto = new PutLogisticsInfoDto();
//            putLogisticsInfoDto.setOrderId(batchUpdateLogisticsOrder.getOrderId());
//            putLogisticsInfoDto.setTransactionCode(batchUpdateLogisticsOrder.getTrasactionCode());
//            putLogisticsInfoDto.setCommissionCharge(batchUpdateLogisticsOrder.getCommissionCharge());
//            putLogisticsInfoDto.setOrderNo(batchUpdateLogisticsOrder.getOrderNo());
//            putLogisticsInfoDto.setUpdateOrderImportInfo(orderImportExtraInfoMap.getOrDefault(batchUpdateLogisticsOrder.getOrderId(), new OrderImportExtraInfoDto()));
//
//            BatchPutLogisticsOrderOrderDto batchPutLogisticsOrderOrderDto = new BatchPutLogisticsOrderOrderDto();
//            batchPutLogisticsOrderOrderDto.setCountryExpressInfoNewId(batchUpdateLogisticsOrder.getCountryExpressInfoId());
//            batchPutLogisticsOrderOrderDto.setOrderId(batchUpdateLogisticsOrder.getOrderId());
//            batchPutLogisticsOrderOrderDto.setOrderNo(batchUpdateLogisticsOrder.getOrderNo());
//            batchPutLogisticsOrderOrderDto.setOldCarriageAmount(batchUpdateLogisticsOrder.getOldCarriageAmount());
//            batchPutLogisticsOrderOrderDto.setAddressId(batchUpdateLogisticsOrder.getAddressId());
//            batchPutLogisticsOrderOrderDto.setNewCarriageAmount(batchUpdateLogisticsOrder.getNewCarriageAmount());
//            batchPutLogisticsOrderOrderDto.setOldServiceAmount(batchUpdateLogisticsOrder.getOldServiceAmount());
//            batchPutLogisticsOrderOrderDto.setNewServiceAmount(batchUpdateLogisticsOrder.getNewServiceAmount());
//            putLogisticsInfoDto.setBatchPutLogisticsOrderOrderDto(batchPutLogisticsOrderOrderDto);
//            List<BatchPutLogisticsOrderItemDto> items = Lists.newArrayList();
//            for (BatchUpdateLogisticsOrderItem batchUpdateLogisticsOrderItem : batchUpdateLogisticsOrderItems) {
//                if (batchUpdateLogisticsOrderItem.getOrderId().equals(batchUpdateLogisticsOrder.getOrderId())) {
//                    BatchPutLogisticsOrderItemDto item = new BatchPutLogisticsOrderItemDto();
//                    item.setId(batchUpdateLogisticsOrderItem.getOrderItemId());
//                    item.setOldServiceAmount(batchUpdateLogisticsOrderItem.getOldServiceAmount());
//                    item.setNewServiceAmount(batchUpdateLogisticsOrderItem.getNewServiceAmount());
//                    items.add(item);
//                }
//            }
//            putLogisticsInfoDto.setBatchPutLogisticsOrderItemDtos(items);
//            putLogisticsInfoDtos.add(putLogisticsInfoDto);
//            orderIds.add(batchUpdateLogisticsOrder.getOrderId());
//        }
//
//        List<PaymentDto> paymentDtos = Lists.newArrayList(payment);
//        if (NumberUtils.greaterZero(batchUpdateLogistics.getTenantPaymentId())) {
//            paymentDtos.addAll(paymentFeign.batchPay(PaymentsPayParam.builder().paymentIds(Lists.newArrayList(batchUpdateLogistics.getTenantPaymentId())).build()));
//        }
//        List<RefundDto> refundDtos = Lists.newArrayList();
//        if (NumberUtils.greaterZero(batchUpdateLogistics.getTenantRefundId())) {
//            refundDtos = refundFeign.batchCreateByPrepareId(RefundsCreateByParamParam.builder().ids(Lists.newArrayList(batchUpdateLogistics.getTenantRefundId())).build());
//        } else {
//            refundDtos = Lists.newArrayList();
//        }
//        PaymentPayRefund paymentPayRefund = new PaymentPayRefund().setPayments(paymentDtos).setRefundDtos(refundDtos);
//
//        MerchantBillDisposeMoney merchantBillDisposeMoney = merchantUserAccountService.getDisposeMoney(payment.getSourceTenantId(), batchUpdateLogistics.getMerchantId(), payment.getSourceUserId());
//
//        //订单id-是否更新运单号 Map
//        Map<Long, Boolean> updateCarriageNoMap = Maps.newHashMap();
//        List<OrderPutLogisticsUpdateParcelBO> orderPutLogisticsUpdateParcelBOs = new ArrayList<>();
//        List<Order> updateOrders = this.batchUpdateLogistics(paymentPayRefund, merchantBillDisposeMoney, batchUpdateLogistics, putLogisticsInfoDtos, payment.getMethod(), payment.getSourceUserId(), updateCarriageNoMap, orderPutLogisticsUpdateParcelBOs);
//        if (updateOrders != null) {
//            List<OrderDeclarationApplyCarriageNoMessage> paramList = Lists.newArrayList();
//            for (Order updateOrder : updateOrders) {
//                OrderDeclarationApplyCarriageNoMessage message = new OrderDeclarationApplyCarriageNoMessage();
//                message.setOrderId(updateOrder.getId());
//                message.setLogisticsId(updateOrder.getLogisticsId());
//                message.setIsApplyCarriage(true);
//                Boolean isUpdateCarriageNo = updateCarriageNoMap.get(updateOrder.getId());
//                if (isUpdateCarriageNo != null && !isUpdateCarriageNo) {
//                    message.setIsApplyCarriage(false);
//                }
//                paramList.add(message);
//
//            }
//            afterCommitExecutor.execute(new Runnable() {
//                @Override
//                public void run() {
//                    Map<Long, OrderPutLogisticsUpdateParcelBO> map = orderPutLogisticsUpdateParcelBOs.stream().collect(Collectors.toMap(OrderPutLogisticsUpdateParcelBO::getOrderId, Function.identity()));
//                    log.info("更新订单 更新包裹map：{}", JSON.toJSON(map));
//
//                    for (OrderDeclarationApplyCarriageNoMessage param : paramList) {
//                        OrderEsSyncSqsMsg msg = new OrderEsSyncSqsMsg();
//                        msg.setOrderId(param.getOrderId());
//                        if (param.getIsApplyCarriage()) {
//                            msg.setUpdateCarriageNo(BasePoConstant.YES_STRING);
//                        } else {
//                            msg.setUpdateCarriageNo(BasePoConstant.NO_STRING);
//                        }
//                        OrderRefreshMsg orderRefreshMsg = new OrderRefreshMsg();
//                        orderRefreshMsg.setId(param.getOrderId());
//                        orderRefreshMsg.setUserId(payment.getSourceUserId());
//                        // orderEventService.sendMessage(msg, OrderUpdateConstant.ORDER_UPDATE_TOPIC, OrderUpdateConstant.EVENT_ORDER_LOGISTICS_UPDATE);
////                        orderEventService.sendMessage(msg, OrderUpdateConstant.ORDER_UPDATE_TOPIC, OrderUpdateConstant.EVENT_ORDER_UPDATE);
//                        rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.EVENT_ORDER_UPDATE, msg);
//
//                        if (map.containsKey(param.getOrderId())) {
//                            OrderPutLogisticsUpdateParcelBO bo = map.get(param.getOrderId());
//                            OrderPutLogisticsUpdateParcelMsg updateParcelMsg = new OrderPutLogisticsUpdateParcelMsg();
//                            updateParcelMsg.setOrderId(bo.getOrderId());
//                            updateParcelMsg.setOperateType(bo.getOperateType());
//                            updateParcelMsg.setLogisticsId(bo.getLogisticsId());
//                            updateParcelMsg.setCountryExpressInfoId(bo.getCountryExpressInfoId());
////                            orderEventService.sendMessage(updateParcelMsg, OrderUpdateConstant.ORDER_PUT_LOGISTICS_UPDATE_PARCEL);
//                            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.ORDER_PUT_LOGISTICS_UPDATE_PARCEL, updateParcelMsg);
//                        }
//
//                        orderEventService.sendOrderUpdateTypeMsg(param.getOrderId(), payment.getSourceUserId());
//
//                    }
//                }
//            });
//        }
//        return updateOrders;
//
//
//    }
    public List<Order> batchUpdateLogisticsPaymentSuccessNew(PaymentDto payment, BatchUpdateLogistics batchUpdateLogistics) {
        List<BatchUpdateLogisticsOrder> batchUpdateLogisticsOrders = batchUpdateLogisticsOrderService.findByBatchId(batchUpdateLogistics.getId());
        List<BatchUpdateLogisticsOrderItem> batchUpdateLogisticsOrderItems = batchUpdateLogisticsOrderItemService.findByBatchId(batchUpdateLogistics.getId());
        List<Long> orderIds = batchUpdateLogisticsOrders.stream().map(BatchUpdateLogisticsOrder::getOrderId).collect(Collectors.toList());
        IdsSearchHelper param = new IdsSearchHelper();
        param.setIds(orderIds);
        Map<Long, OrderImportExtraInfoDto> orderImportExtraInfoMap = orderImportExtraInfoFeign.findMapByIds(param);
        List<PutLogisticsInfoDto> putLogisticsInfoDtos = Lists.newArrayList();
        for (BatchUpdateLogisticsOrder batchUpdateLogisticsOrder : batchUpdateLogisticsOrders) {
            PutLogisticsInfoDto putLogisticsInfoDto = new PutLogisticsInfoDto();
            putLogisticsInfoDto.setOrderId(batchUpdateLogisticsOrder.getOrderId());
            putLogisticsInfoDto.setTransactionCode(batchUpdateLogisticsOrder.getTrasactionCode());
            putLogisticsInfoDto.setCommissionCharge(batchUpdateLogisticsOrder.getCommissionCharge());
            putLogisticsInfoDto.setOrderNo(batchUpdateLogisticsOrder.getOrderNo());
            putLogisticsInfoDto.setUpdateOrderImportInfo(orderImportExtraInfoMap.getOrDefault(batchUpdateLogisticsOrder.getOrderId(), new OrderImportExtraInfoDto()));

            BatchPutLogisticsOrderOrderDto batchPutLogisticsOrderOrderDto = new BatchPutLogisticsOrderOrderDto();
            batchPutLogisticsOrderOrderDto.setCountryExpressInfoNewId(batchUpdateLogisticsOrder.getCountryExpressInfoId());
            batchPutLogisticsOrderOrderDto.setOrderId(batchUpdateLogisticsOrder.getOrderId());
            batchPutLogisticsOrderOrderDto.setOrderNo(batchUpdateLogisticsOrder.getOrderNo());
            batchPutLogisticsOrderOrderDto.setOldCarriageAmount(batchUpdateLogisticsOrder.getOldCarriageAmount());
            batchPutLogisticsOrderOrderDto.setAddressId(batchUpdateLogisticsOrder.getAddressId());
            batchPutLogisticsOrderOrderDto.setNewCarriageAmount(batchUpdateLogisticsOrder.getNewCarriageAmount());
            batchPutLogisticsOrderOrderDto.setOldServiceAmount(batchUpdateLogisticsOrder.getOldServiceAmount());
            batchPutLogisticsOrderOrderDto.setNewServiceAmount(batchUpdateLogisticsOrder.getNewServiceAmount());
            putLogisticsInfoDto.setBatchPutLogisticsOrderOrderDto(batchPutLogisticsOrderOrderDto);
            List<BatchPutLogisticsOrderItemDto> items = Lists.newArrayList();
            for (BatchUpdateLogisticsOrderItem batchUpdateLogisticsOrderItem : batchUpdateLogisticsOrderItems) {
                if (batchUpdateLogisticsOrderItem.getOrderId().equals(batchUpdateLogisticsOrder.getOrderId())) {
                    BatchPutLogisticsOrderItemDto item = new BatchPutLogisticsOrderItemDto();
                    item.setId(batchUpdateLogisticsOrderItem.getOrderItemId());
                    item.setOldServiceAmount(batchUpdateLogisticsOrderItem.getOldServiceAmount());
                    item.setNewServiceAmount(batchUpdateLogisticsOrderItem.getNewServiceAmount());
                    items.add(item);
                }
            }
            putLogisticsInfoDto.setBatchPutLogisticsOrderItemDtos(items);
            putLogisticsInfoDtos.add(putLogisticsInfoDto);
            orderIds.add(batchUpdateLogisticsOrder.getOrderId());
        }

        //订单id-是否更新运单号 Map
        Map<Long, Boolean> updateCarriageNoMap = Maps.newHashMap();
        List<OrderPutLogisticsUpdateParcelBO> orderPutLogisticsUpdateParcelBOs = new ArrayList<>();
        List<Order> updateOrders = this.batchUpdateLogisticsNew(batchUpdateLogistics, putLogisticsInfoDtos,
            payment.getMethod(), payment.getSourceUserId(), updateCarriageNoMap, orderPutLogisticsUpdateParcelBOs);


        if (updateOrders != null) {
            List<OrderDeclarationApplyCarriageNoMessage> paramList = Lists.newArrayList();
            for (Order updateOrder : updateOrders) {
                OrderDeclarationApplyCarriageNoMessage message = new OrderDeclarationApplyCarriageNoMessage();
                message.setOrderId(updateOrder.getId());
                message.setLogisticsId(updateOrder.getLogisticsId());
                message.setIsApplyCarriage(true);
                Boolean isUpdateCarriageNo = updateCarriageNoMap.get(updateOrder.getId());
                if (isUpdateCarriageNo != null && !isUpdateCarriageNo) {
                    message.setIsApplyCarriage(false);
                }
                paramList.add(message);

            }
            afterCommitExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    Map<Long, OrderPutLogisticsUpdateParcelBO> map = orderPutLogisticsUpdateParcelBOs.stream().collect(Collectors.toMap(OrderPutLogisticsUpdateParcelBO::getOrderId, Function.identity()));
                    log.info("更新订单 更新包裹map：{}", JSON.toJSON(map));

                    for (OrderDeclarationApplyCarriageNoMessage param : paramList) {
                        OrderEsSyncSqsMsg msg = new OrderEsSyncSqsMsg();
                        msg.setOrderId(param.getOrderId());
                        if (param.getIsApplyCarriage()) {
                            msg.setUpdateCarriageNo(BasePoConstant.YES_STRING);
                        } else {
                            msg.setUpdateCarriageNo(BasePoConstant.NO_STRING);
                        }
                        OrderRefreshMsg orderRefreshMsg = new OrderRefreshMsg();
                        orderRefreshMsg.setId(param.getOrderId());
                        orderRefreshMsg.setUserId(payment.getSourceUserId());
                        // orderEventService.sendMessage(msg, OrderUpdateConstant.ORDER_UPDATE_TOPIC, OrderUpdateConstant.EVENT_ORDER_LOGISTICS_UPDATE);
//                        orderEventService.sendMessage(msg, OrderUpdateConstant.ORDER_UPDATE_TOPIC, OrderUpdateConstant.EVENT_ORDER_UPDATE);
                        rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.EVENT_ORDER_UPDATE, msg);

                        if (map.containsKey(param.getOrderId())) {
                            OrderPutLogisticsUpdateParcelBO bo = map.get(param.getOrderId());
                            OrderPutLogisticsUpdateParcelMsg updateParcelMsg = new OrderPutLogisticsUpdateParcelMsg();
                            updateParcelMsg.setOrderId(bo.getOrderId());
                            updateParcelMsg.setOperateType(bo.getOperateType());
                            updateParcelMsg.setLogisticsId(bo.getLogisticsId());
                            updateParcelMsg.setCountryExpressInfoId(bo.getCountryExpressInfoId());
//                            orderEventService.sendMessage(updateParcelMsg, OrderUpdateConstant.ORDER_PUT_LOGISTICS_UPDATE_PARCEL);
                            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.ORDER_PUT_LOGISTICS_UPDATE_PARCEL, updateParcelMsg);
                        }

                        orderEventService.sendOrderUpdateTypeMsg(param.getOrderId(), payment.getSourceUserId());

                    }
                }
            });
        }
        return updateOrders;


    }


    @GlobalTransactional
    public List<Order> batchUpdateLogisticsAliPaySuccess(PaymentDto payment) {
        BatchUpdateLogistics batchUpdateLogistics = batchUpdateLogisticsService.findByPaymentId(payment.getId());
        if (batchUpdateLogistics == null) {
            return null;
        }
        List<Order> updateOrders = null;
        updateOrders = batchUpdateLogisticsPaymentSuccessNew(payment, batchUpdateLogistics);

        return updateOrders;

    }

    public PutLogisticsRespDto getPutLogisticsInfos(Long merchantId, Long userId, Address address, Long logisticsId, Long tailLogisticsChannelId, List<Long> ids) {
        PutLogisticsRespDto putLogisticsRespDto = new PutLogisticsRespDto();
        TenantLogisticsRespDto logisticsRespDto = tenantLogisticsFeign.getDtoById(logisticsId);
        Assert.validateNull(logisticsRespDto, "物流不存在");
        if (CollectionUtils.isEmpty(ids)) {
            putLogisticsRespDto.setPutLogisticsInfoDtos(Lists.newArrayList());
            return putLogisticsRespDto;
        }
        String tailLogisticsChannelCode = "";
        if (NumberUtils.greaterZero(tailLogisticsChannelId)) {
            LogisticsChannelRespDto channelRespDto = logisticsChannelFeign.getById(tailLogisticsChannelId);
            tailLogisticsChannelCode = channelRespDto.getCode();
        }
        List<Order> orders = orderService.findByIds(ids);
        List<String> sheinAutoImportOrderNos = ListUtil.toValueDistinctList(orders, Order::getOutOrderNo
                , i -> MerchantStorePlatformEnum.SHEIN.equalsCode(i.getMerchantStorePlatformCode())
                        && StrUtil.isNotBlank(i.getOutOrderNo()) && com.sdsdiy.orderapi.constant.OrderOriginType.isAutoImport(i.getOriginType()));
        List<String> sheinPerformance = platformOrderExtendFeign.matchValueByOutIds(PlatformOrderExtendValueEnum.SHEIN_PERFORMANCE, BaseListDto.of(sheinAutoImportOrderNos), com.sdsdiy.orderapi.constant.OrderOriginType.AUTO_IMPORT.getCode());
        Assert.validateNotEmpty(sheinPerformance, "勾选订单存在shein线上物流自动导入订单，不支持批量修改物流");
        orderService.formatOrderItems(orders);
        orderService.formatLogistics(orders);
        List<TemuOnlineLogisticsCheckDto> temuChecks = Lists.newArrayList();
        for (Order order : orders) {
            TemuOnlineLogisticsCheckDto checkDto = new TemuOnlineLogisticsCheckDto();
            checkDto.setOrderOriginType(order.getOriginType());
            checkDto.setOrderSyncStatus(order.getSyncStatus());
            checkDto.setPlatformCode(order.getMerchantStorePlatformCode());
            checkDto.setOutOrderNo(order.getOutOrderNo());
            checkDto.setLogisticsId(logisticsId);
            checkDto.setIsCheckLogistics(true);
            checkDto.setTailLogisticsChannelCode(tailLogisticsChannelCode);
            checkDto.setTailLogisticsChannelId(tailLogisticsChannelId);
            temuChecks.add(checkDto);
        }
        orderUpdateCheckFeign.temuOnlineLogisticsCheckBatchForMerchant(new BaseListDto<>(temuChecks));
        checkTenantLogisticsChange(logisticsId, orders);
        if (address == null) {
            orderService.formatAddress(orders);
        } else {
            // 地址信息不足
            Assert.validateFalse(AddressService.checkAddress(address), EnumPaymentCheck.ADDRESS.getMsg());
            for (Order order : orders) {
                order.setAddress(address);
                order.setAddressId(address.getId());
            }
        }
        return this.getPutLogisticsInfosByOrders(merchantId, userId, logisticsId, orders, address != null);
    }

    private void checkTenantLogisticsChange(Long logisticsId, List<Order> orders) {
        List<Order> noPainOrders = orders.stream().filter(o -> OrderStatus.noPain(o.getStatus())).collect(Collectors.toList());
        if (CollUtil.isEmpty(noPainOrders)) {
            return;
        }
        Long tenantId = noPainOrders.get(0).getTenantId();
        List<Long> orderIds = noPainOrders.stream().map(o -> o.getId()).collect(Collectors.toList());

        IdsSearchHelper idsSearchHelper = new IdsSearchHelper();
        idsSearchHelper.setIds(orderIds);
        List<OrderAmountRespDTO> orderAmountDtos = orderAmountFeign.findByIds(idsSearchHelper);
        if (CollUtil.isEmpty(orderAmountDtos)) {
            return;
        }
        //sds产品
        List<Long> sdsProductOrderIds = orderAmountDtos.stream().filter(oa -> !EnumOrderPayType.onlyPayTenant(oa.getPaymentType())).map(oa -> oa.getId()).collect(Collectors.toList());
        if (CollUtil.isEmpty(sdsProductOrderIds)) {
            return;
        }
        BaseListReqDto baseListReqDto = new BaseListReqDto();
        baseListReqDto.setIdList(Lists.newArrayList(logisticsId));
        Map<Long, LogisticsAndLogisticsSourceResp> map = tenantLogisticsFeign.getLogisticsAndLogisticsSource(tenantId, baseListReqDto);
        LogisticsAndLogisticsSourceResp logisticsSourceResp = map.get(logisticsId);
        Assert.validateNull(logisticsSourceResp, "物流不存在");
        if (logisticsSourceResp.getDistributionProductLogisticsSource().equals(logisticsSourceResp.getLogisticsSource())) {
            return;
        }
        if (logisticsSourceResp.getLogisticsSource().equals(DistributionProductLogisticsSourceEnum.PRODUCT_TENANT.name())) {
            //租户物流下，当未开放区域,则允许sds物流
            List<Long> noOpenAreaOrderLogisticsIds = getNoOpenAreaOrderLogisticsIds(noPainOrders);
            Assert.validateTrue(!noOpenAreaOrderLogisticsIds.contains(logisticsId), "物流发生变动");
        } else {
            Assert.wrong("物流发生变动");
        }
    }

    @NotNull
    private List<Long> getNoOpenAreaOrderLogisticsIds(List<Order> noPainOrders) {
        if (CollUtil.isEmpty(noPainOrders)) {
            return Lists.newArrayList();
        }
        List<Long> noOpenAreaOrderLogisticsIds = Lists.newArrayList();
        List<Long> issuingBayAreaIds = noPainOrders.stream().filter(o -> null != o.getIssuingBayAreaId() && o.getIssuingBayAreaId() > 0).map(o -> o.getIssuingBayAreaId()).collect(Collectors.toList());
        if (CollUtil.isEmpty(issuingBayAreaIds)) {
            return Lists.newArrayList();
        }
        List<IssuingBayAreaRespDto> issuingBayAreaRespDtos = issuingBayAreaFeign.findByIds("", issuingBayAreaIds);
        if (CollUtil.isEmpty(issuingBayAreaRespDtos)) {
            return Lists.newArrayList();
        }
        List<IssuingBayAreaRespDto> noOpenAreas = issuingBayAreaRespDtos.stream().filter(a -> null != a.getIsOpenToOtherTenant() && a.getIsOpenToOtherTenant() == 0).collect(Collectors.toList());
        if (CollUtil.isEmpty(noOpenAreas)) {
            return Lists.newArrayList();
        }
        List<Long> areaIds = noOpenAreas.stream().map(a -> a.getId()).collect(Collectors.toList());
        for (Order noPainOrder : noPainOrders) {
            if (areaIds.contains(noPainOrder.getIssuingBayAreaId())) {
                noOpenAreaOrderLogisticsIds.add(noPainOrder.getLogisticsId());
            }
        }
        return noOpenAreaOrderLogisticsIds;
    }

    public void checkConsignmentOrders(Long uId, List<Order> orders) {
        if (CollUtil.isEmpty(orders)) {
            return;
        }
        List<Order> consignmentOrders = orders.stream().filter(o -> o.getLogistics().getCodeId().equals(LogisticsCodeIdEnum.CONSIGNMENT.getCodeId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(consignmentOrders)) {
            return;
        }
        List<String> orderNos = consignmentOrders.stream().map(o -> o.getNo()).collect(Collectors.toList());
        List<AdminPrepaidRespDto> adminPrepaidRespDtos = adminPrepaidFeign.getByOrderNos(uId, orderNos);
        if (CollUtil.isEmpty(adminPrepaidRespDtos)) {
            return;
        }
        List<AdminPrepaidRespDto> hasPackingList = adminPrepaidRespDtos.stream().filter(ap -> ap.getStatus() > AdminPrepaidConstant.PREPAID_STEP_WAITPACK).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(hasPackingList)) {
            throw new BusinessException("寄付清单已装箱,无法进行修改");
        }
    }

}
