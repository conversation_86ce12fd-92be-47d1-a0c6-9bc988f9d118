package com.ps.ps.feign.order;


import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.orderapi.api.order.OrderRefundApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "SERVICE-ORDER", contextId = "OrderRefundFeign", url = MicroServiceEndpointConstant.SERVICE_ORDER)
public interface OrderRefundFeign extends OrderRefundApi {
}
