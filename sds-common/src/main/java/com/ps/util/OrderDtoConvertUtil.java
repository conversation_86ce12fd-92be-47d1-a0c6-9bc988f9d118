package com.ps.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.ps.ps.service.PaymentService;
import com.ps.support.BeanUtils;
import com.ps.tool.cbt.common.util.StringUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.enums.CarriageStatusEnum;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.enums.onlineorder.OnlineOrderStatusMergedEnum;
import com.sdsdiy.common.base.helper.MapListUtil;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.common.base.helper.TimeUtil;
import com.sdsdiy.designproductapi.dto.base.DesignProductRespDto;
import com.sdsdiy.logisticsapi.constant.CarriageNoRecodeConstant;
import com.sdsdiy.logisticsapi.enums.EurIossServiceProviderEnum;
import com.sdsdiy.logisticsapi.enums.TrackMilestoneEnum;
import com.sdsdiy.logisticsdata.dto.base.CountryExpressInfoNewRespDto;
import com.sdsdiy.logisticsdata.dto.base.LogisticsRespDto;
import com.sdsdiy.logisticsdata.util.CarriageTrackUtil;
import com.sdsdiy.orderapi.constant.EnumOrderPayType;
import com.sdsdiy.orderapi.constant.PickUpLogisticsInfoStatusConstant;
import com.sdsdiy.orderapi.dto.ImportedPlatformOrderFbaItemRespDto;
import com.sdsdiy.orderapi.dto.MerchantStoreRespDTO;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderapi.dto.OrderSyncHistoryDto;
import com.sdsdiy.orderapi.dto.base.OrderItemImportProductDetailsItemDto;
import com.sdsdiy.orderapi.dto.order.*;
import com.sdsdiy.orderapi.dto.orderfba.OrderFbaCarriageDetailRespDto;
import com.sdsdiy.orderapi.dto.platformorder.ImportedPlatformOrderDto;
import com.sdsdiy.orderapi.dto.platformorder.ImportedPlatformOrderItemDto;
import com.sdsdiy.orderapi.util.OrderEsQueryUtil;
import com.sdsdiy.orderdata.constant.ParcelConstant;
import com.sdsdiy.orderdata.constant.order.OrderExtendValueEnum;
import com.sdsdiy.orderdata.constant.order.PlatformOrderExtendValueEnum;
import com.sdsdiy.orderdata.dto.*;
import com.sdsdiy.orderdata.dto.onlineorder.PlatformOrderExtendDto;
import com.sdsdiy.orderdata.dto.order.item.OrderItemCancelQtyDTO;
import com.sdsdiy.orderdata.dto.orderitem.OrderItemExtendInfoDTO;
import com.sdsdiy.orderdata.dto.parcel.OrderParcelDTO;
import com.sdsdiy.orderdata.enums.DeliveryTypeEnum;
import com.sdsdiy.orderdata.enums.OrderItemChangeTypeEnum;
import com.sdsdiy.orderdata.enums.OrderParcelStatusEnum;
import com.sdsdiy.paymentapi.constant.PaymentChannelMethodEnum;
import com.sdsdiy.paymentapi.constant.PaymentMethodEnum;
import com.sdsdiy.productapi.dto.ProductDto;
import com.sdsdiy.productdata.dto.auth.MerchantAuthProductParentRespDTO;
import com.sdsdiy.productdata.dto.product.SmallOrderPriceDto;
import com.sdsdiy.userapi.dto.crm.resp.CrmUserRespDTO;
import com.ziguang.base.dto.OrderSyncHistoryDTO;
import com.ziguang.base.dto.order.MerchantOrderRespDto;
import com.ziguang.base.model.*;
import com.ziguang.base.support.DateUtil;
import com.ziguang.base.support.contant.*;
import com.ziguang.base.vo.OrderProfitVo;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单DTO的转换方法
 *
 * <AUTHOR>
 */
public class OrderDtoConvertUtil {
    public static final Map<Integer, String> fbaTypeMap = new HashMap<>();
    public static final Map<Integer, String> blackListTypeMap = new HashMap<>();
    public static final Map<Integer, String> refundStatusMap = new HashMap<>();
    public static final Map<Integer, String> carriageStatusMap = new HashMap<>();
    public static final Map<Integer, String> orderSynStatusMap = new HashMap<>();
    public static final Map<Integer, String> carriageTrackStatusMap = new HashMap<>();

    static {
        // FBA 类型
        fbaTypeMap.put(0, "NOAMZ");
        fbaTypeMap.put(1, "FBA");
        fbaTypeMap.put(2, "FBM");

        // 黑名单类型
        blackListTypeMap.put(0, "UNKNOWN");
        blackListTypeMap.put(1, "NOT");
        blackListTypeMap.put(2, "BUYER");
        blackListTypeMap.put(3, "ADDRESS");
        blackListTypeMap.put(4, "ADDRESS_BUYER");

        // 退款类型
        refundStatusMap.put(0, "NO_SHOW");
        refundStatusMap.put(1, "NO_REFUND");
        refundStatusMap.put(2, "REFUNDED");

        // 运单状态
        carriageStatusMap.put(1, "PROCESSING");
        carriageStatusMap.put(2, "SUCCESS");
        carriageStatusMap.put(4, "FAILURE");

        // 运单同步状态
        orderSynStatusMap.put(0, "NOT");
        // 这个实际上等待同步中
        orderSynStatusMap.put(1, "PROCESSING");
        orderSynStatusMap.put(2, "SUCCESS");
        orderSynStatusMap.put(3, "WAIT_SEND");
        orderSynStatusMap.put(5, "EMPET_SEND");
        orderSynStatusMap.put(6, "EMPET_SENDED");
        // 第三方平台处理中，命名已经被1占用
        orderSynStatusMap.put(7, "IN_PROCESSING");
        orderSynStatusMap.put(10, "TEMU_ADDRESS_CHANGED_FAILED_RETRY");
        orderSynStatusMap.put(11, "TEMU_ADDRESS_CHANGED_FAILED_NO_RETRY");
        orderSynStatusMap.put(99, "FAILURE");

        // 物流跟踪状态
        carriageTrackStatusMap.put(1, "CREATE");
        carriageTrackStatusMap.put(2, "SHIP");
        carriageTrackStatusMap.put(3, "COLLECT");
        carriageTrackStatusMap.put(4, "TRANSIT");
        carriageTrackStatusMap.put(5, "SIGN");
        carriageTrackStatusMap.put(6, "ELSE");
    }

    public static void formatMerchantOrderRespDtoAliexpressInfo(
            CommonOrderRespDto.ParcelDTO commonParcel, MerchantOrderRespDto.Parcel respParcel) {
        if (commonParcel == null || respParcel == null) {
            return;
        }
        CommonOrderRespDto.AliexpressInfo aliexpressInfo = commonParcel.getAliexpressInfo();
        if (null == aliexpressInfo) {
            return;
        }
        respParcel.setAliexpressInfo(BeanUtils.copyProperties(aliexpressInfo, MerchantOrderRespDto.AliexpressInfo.class));
    }

    public static void formatMerchantOrderRespDtoOutPoInfo(
            CommonOrderRespDto.ParcelDTO commonParcel, MerchantOrderRespDto.Parcel respParcel) {
        if (commonParcel == null || respParcel == null) {
            return;
        }
        CommonOrderRespDto.OutPoInfo outPoInfo = commonParcel.getOutPoInfo();
        if (null == outPoInfo) {
            return;
        }
        respParcel.setOutPoInfo(BeanUtils.copyProperties(outPoInfo, MerchantOrderRespDto.OutPoInfo.class));
    }

    public static void formatMerchantOrderRespDtoParcelTailLogistics(
            CommonOrderRespDto.ParcelDTO commonParcel, MerchantOrderRespDto.Parcel respParcel) {
        if (commonParcel == null || respParcel == null) {
            return;
        }
        CommonOrderRespDto.TailLogistics tailLogistics = commonParcel.getTailLogistics();
        if (null == tailLogistics) {
            return;
        }
        respParcel.setTailLogistics(BeanUtils.copyProperties(tailLogistics, MerchantOrderRespDto.TailLogistics.class));
        OrderCarriageRespDto carriage = commonParcel.getOrderCarriageRespDto();
        if (carriage != null) {
            respParcel.getTailLogistics().setCarriageStatus(carriageStatusMap.get(carriage.getTransferCarriageStatus()));
            if (CarriageNoRecodeConstant.STATUS_SUCCESS.equals(carriage.getTransferCarriageStatus())) {
                respParcel.getTailLogistics().setCarriageNo(carriage.getTransferCarriageNo());
            } else {
                // 不成功不返回尾程单号
                carriage.setTransferCarriageNo("");
                respParcel.getLogistics().getCarriage().setTransferCarriageNo("");
                respParcel.getTailLogistics().setCarriageMsg(carriage.getTransferCarriageMsg());
            }
            carriage.setTransferCarriageMsg(null);
        }
    }

    public static void commonOrderRespAmount2PriceDifference(CommonOrderRespDto orderDto
            , MerchantOrderRespDto respDto) {
        CommonOrderRespDto.AmountDTO amountDTO = orderDto.getAmountDTO();
        MerchantOrderRespDto.PriceDifference difference = new MerchantOrderRespDto.PriceDifference();
        difference.setUnPaid(amountDTO.getUnPaid());
        respDto.setPriceDifference(difference);
    }

    public static void formatMerchantOrderRespDtoOrderItemQty(CommonOrderRespDto.OrderItem commonItem
            , MerchantOrderRespDto.OrderItem merchantItem) {
        OrderItemCancelQtyDTO canceledItemQtyDto = commonItem.getCanceledItemQtyDto();
        if (canceledItemQtyDto != null) {
            merchantItem.setNum(merchantItem.getNum() + canceledItemQtyDto.getCancelTotalQty());
            Map<String, Integer> typeQtyMap = canceledItemQtyDto.getTypeQtyMap();
            if (typeQtyMap != null) {
                merchantItem.setMerchantCancelQty(typeQtyMap.get(OrderItemChangeTypeEnum.MERCHANT_CANCEL.code));
                merchantItem.setRejectCancel(typeQtyMap.get(OrderItemChangeTypeEnum.QC_REJECT_CANCEL.code));
                merchantItem.setLoseCancel(typeQtyMap.get(OrderItemChangeTypeEnum.QC_LESS_CANCEL.code));
            }
        } else if (com.sdsdiy.orderdata.enums.OrderStatus.CANCEL.equalsStatus(commonItem.getOrderItemRespDto().getStatus())) {
            merchantItem.setMerchantCancelQty(merchantItem.getNum());
        }
    }

    public static void formatMerchantOrderRespDtoOrderItemExtendInfo(CommonOrderRespDto.OrderItem commonItem
            , MerchantOrderRespDto.OrderItem merchantItem) {
        OrderItemExtendInfoDTO orderItemExtendInfo = commonItem.getOrderItemExtendInfo();
        if (orderItemExtendInfo == null) {
            return;
        }
        if (merchantItem.getImportProduct() == null) {
            merchantItem.setImportProduct(new MerchantOrderRespDto.ImportProduct());
        }
        merchantItem.getImportProduct().setImportStyleNumber(orderItemExtendInfo.getImportStyleNumber());
        merchantItem.getImportProduct().setImportExtCode(orderItemExtendInfo.getImportExtCode());
        merchantItem.getImportProduct().setImportProductId(orderItemExtendInfo.getImportProductId());
        merchantItem.getImportProduct().setImportSkuId(orderItemExtendInfo.getImportSkuId());
        merchantItem.getImportProduct().setImportProductSkuId(orderItemExtendInfo.getImportProductSkuId());
        merchantItem.getImportProduct().setImportSkcId(orderItemExtendInfo.getImportSkcId());
        merchantItem.getImportProduct().setImportSellerSku(orderItemExtendInfo.getImportSellerSku());
    }

    public static void formatMerchantOrderRespDtoItemEndProduct(MerchantOrderRespDto.OrderItem merchantItem
            , ProductDto productDto, Long merchantTenantId, DesignProductRespDto designProductRespDto
            , Map<Long, MerchantAuthProductParentRespDTO> authProductParentMap) {
        if (Objects.isNull(productDto.getId()) || Objects.isNull(designProductRespDto)) {
            return;
        }
        EndProductType endProductType = EndProductUtil
                .getEndProductType(authProductParentMap.get(productDto.getParentId()), productDto, merchantTenantId);
        MerchantOrderRespDto.EndProduct endProduct = new MerchantOrderRespDto.EndProduct();
        endProduct.setFitLevel(designProductRespDto.getFitLevel());
        endProduct.setDeleteFlag(BasePoConstant.DELETE_STATUS.equals(designProductRespDto.getStatus()));
        endProduct.setValid(EndProductUtil.validEndProductByType(endProductType));
        endProduct.setStyleNumber(designProductRespDto.getStyleNumber());
        merchantItem.setEndProduct(endProduct);
    }

    public static void formatMerchantOrderRespDtoOriginalCarriage(CommonOrderRespDto commonOrder
            , MerchantOrderRespDto respDto) {
        MerchantOrderRespDto.OriginalCarriage originalCarriage = new MerchantOrderRespDto.OriginalCarriage();
        respDto.setOriginalCarriage(originalCarriage);
        OrderRespDto orderRespDto = commonOrder.getOrderRespDto();
        if (NumberUtils.greaterZero(orderRespDto.getLogisticsId())) {
            originalCarriage.setLogisticsName(orderRespDto.getCarriageName());
        } else if (NumberUtils.greaterZero(orderRespDto.getPayTime()) && DeliveryTypeEnum.FBA != commonOrder.getDeliveryType()) {
            // 已付款 且 不是FBA
            originalCarriage.setLogisticsName("手动物流");
        }
        originalCarriage.setCarriageNo(orderRespDto.getCarriageNo());
        CommonOrderRespDto.TailLogistics orderTailLogistics = commonOrder.getOrderTailLogistics();
        if (orderTailLogistics != null) {
            MerchantOrderRespDto.TailLogistics tailLogistics = new MerchantOrderRespDto.TailLogistics();
            originalCarriage.setTailLogistics(tailLogistics);
            tailLogistics.setLogisticsChannelId(orderTailLogistics.getLogisticsChannelId());
            tailLogistics.setName(orderTailLogistics.getName());
            tailLogistics.setServiceProviderId(orderTailLogistics.getServiceProviderId());
        }
        // 物流妥投时间
        CarriageTrackUtil.formatCarriageTimeStr(
                commonOrder.getCountryExpressInfoNewRespDto(), CountryExpressInfoNewRespDto::getPrescriptionStart
                , CountryExpressInfoNewRespDto::getPrescriptionEnd, CountryExpressInfoNewRespDto::getPrescriptionType
                , respDto, MerchantOrderRespDto::setCarriageTrackStatistics
                , respDto.getCarriageTrackStatistics());
    }

    public static void commonOrderRespParcel2MerchantOrderParcel(CommonOrderRespDto commonOrder
            , MerchantOrderRespDto respDto, Boolean isExport) {
        List<CommonOrderRespDto.ParcelDTO> commonParcelList = commonOrder.getParcelList();
        //导出的 不过滤掉取消订单的包裹
        if (CollUtil.isEmpty(commonParcelList) || (OrderStatus.isCancel(commonOrder.getOrderRespDto().getStatus()) && Boolean.FALSE.equals(isExport))) {
            respDto.setParcelList(Collections.emptyList());
            return;
        }
        boolean isJit = commonOrder.getDeliveryType() == DeliveryTypeEnum.JIT;
        boolean isTemuFully = commonOrder.getDeliveryType() == DeliveryTypeEnum.TEMU_FULLY;
        List<MerchantOrderRespDto.Parcel> merchantParcelList = new ArrayList<>();
        commonParcelList.forEach(i -> {
            MerchantOrderRespDto.Parcel parcel = new MerchantOrderRespDto.Parcel();
            OrderParcelDTO orderParcelDTO = i.getOrderParcelDTO();
            parcel.setId(orderParcelDTO.getId());
            // 订单列表这边采购单的特殊展示需求
            parcel.setParcelName(isJit || isTemuFully
                    ? ParcelConstant.formatPoOrderName(orderParcelDTO.getSortWeight()) : orderParcelDTO.getParcelName());
            parcel.setStatus(orderParcelDTO.getStatus());
            parcel.setTotalQty(orderParcelDTO.getTotalQty());
            parcel.setItemQty(i.getParcelItemList() == null ? 0 : i.getParcelItemList().size());
            parcel.setCarriageAmount(orderParcelDTO.getCarriageAmount());
            parcel.setDeliveryTime(orderParcelDTO.getDeliveryTime());
            parcel.setCountryExpressInfo(countryExpress2MerchantCountryExpress(i.getCountryExpressInfoNewRespDto()));

            LogisticsRespDto logisticsRespDto = i.getLogisticsRespDto();
            OrderCarriageRespDto carriageRespDto = i.getOrderCarriageRespDto();
            // 物流妥投时间
            if (carriageRespDto != null) {
                CarriageTrackUtil.formatCarriageTimeStr(
                        carriageRespDto, OrderCarriageRespDto::getPrescriptionStart
                        , OrderCarriageRespDto::getPrescriptionEnd, OrderCarriageRespDto::getPrescriptionType
                        , parcel, MerchantOrderRespDto.Parcel::setCarriageTrackStatistics
                        , parcel.getCarriageTrackStatistics());
            } else {
                CarriageTrackUtil.formatCarriageTimeStr(
                        i.getCountryExpressInfoNewRespDto(), CountryExpressInfoNewRespDto::getPrescriptionStart
                        , CountryExpressInfoNewRespDto::getPrescriptionEnd, CountryExpressInfoNewRespDto::getPrescriptionType
                        , parcel, MerchantOrderRespDto.Parcel::setCarriageTrackStatistics
                        , parcel.getCarriageTrackStatistics());
            }
            // 物流
            parcel.setLogistics(logistics2MerchantLogistics(logisticsRespDto, carriageRespDto));
            // 运单
            parcel.getLogistics().setCarriage(order2MerchantCarriage(respDto, commonOrder.getOrderRespDto()
                    , orderParcelDTO, carriageRespDto, i.getOrderSyncHistoryList()));
            parcel.setBatchNo(i.getBatchNo());
            if (commonOrder.getDeliveryType() == DeliveryTypeEnum.ZT) {
                // 自提 特殊处理
                if (i.getPickUpLogisticsInfoDto() != null) {
                    parcel.setStatus(BasePoConstant.yesOrNo(PickUpLogisticsInfoStatusConstant.pickedUp.equals(i.getPickUpLogisticsInfoDto().getStatus())));
                    parcel.getLogistics().getCarriage().setNo(i.getPickUpLogisticsInfoDto().getPickupCode());
                } else {
                    parcel.setStatus(BasePoConstant.NO);
                }
            }
            merchantParcelList.add(parcel);
            if (isJit) {
                formatMerchantOrderRespDtoAliexpressInfo(i, parcel);
            }
            if (isTemuFully) {
                formatMerchantOrderRespDtoOutPoInfo(i, parcel);
            }
            formatMerchantOrderRespDtoParcelTailLogistics(i, parcel);
        });
        respDto.setParcelList(merchantParcelList);
        if (isJit) {
            // 截止发货时间
            long minPreArriveTime = commonParcelList.stream().filter(i -> i.getAliexpressInfo() != null
                            && NumberUtils.greaterZero(i.getAliexpressInfo().getAliexpressPreArriveTime()))
                    .mapToLong(i -> i.getAliexpressInfo().getAliexpressPreArriveTime()).min().orElse(0);
            if (NumberUtils.greaterZero(minPreArriveTime)) {
                // 如多个po单，则取最近的一个截止发货时间
                respDto.setLatestShipTime(minPreArriveTime + "");
            }
            // 预约时间
            List<Long> preTimeList = commonParcelList.stream().filter(i -> i.getAliexpressInfo() != null
                            && NumberUtils.greaterZero(i.getAliexpressInfo().getAliexpressPreTime()))
                    .map(i -> i.getAliexpressInfo().getAliexpressPreTime()).collect(Collectors.toList());
            if (preTimeList.size() > 0 && preTimeList.size() == commonParcelList.size()) {
                // 多个po单，则取最后一个po单的预约时间，如只有部分po单预约，则不显示预约时间，不算完成预约
                preTimeList.sort(Comparator.reverseOrder());
                respDto.getTimePoint().setAliexpressPreTime(preTimeList.get(0));
            }
        }
        if (isTemuFully) {
            // 截止发货时间
            long minPreArriveTime = commonParcelList.stream().filter(i -> i.getOutPoInfo() != null
                            && NumberUtils.greaterZero(i.getOutPoInfo().getPoExpirationTime()))
                    .mapToLong(i -> i.getOutPoInfo().getPoExpirationTime()).min().orElse(0);
            if (NumberUtils.greaterZero(minPreArriveTime)) {
                // 如多个po单，则取最近的一个截止发货时间
                respDto.setLatestShipTime(minPreArriveTime + "");
            }
        }
    }


    public static OrderEsQueryDto merchantOrderQueryDto2OrderEsQueryDto(MerchantOrderQueryDto merchantOrderQueryDto) {
        return OrderEsQueryUtil.merchantOrderQueryDto2OrderEsQueryDto(merchantOrderQueryDto);
    }

    public static OrderEsQueryDto podOrderQueryDto2OrderEsQueryDto(PodOrderQueryDto podOrderQueryDto) {
        return OrderEsQueryUtil.podOrderQueryDto2OrderEsQueryDto(podOrderQueryDto);
    }

    public static void formatMerchantOrderRespDtoAmountInfo(CommonOrderRespDto commonOrder, MerchantOrderRespDto merchantOrder) {
        MerchantOrderRespDto.AmountInfo amountInfo = new MerchantOrderRespDto.AmountInfo();
        merchantOrder.setAmountInfo(amountInfo);
        OrderRespDto orderRespDto = commonOrder.getOrderRespDto();
        amountInfo.setCarriageAmount(BigDecimal.valueOf(orderRespDto.getCarriageAmount()));
        amountInfo.setRefundCarriageAmount(BigDecimal.valueOf(orderRespDto.getRefundCarriageAmount()));
        // 商户剩余物流费 扣除已退款的
        BigDecimal merchantCurrentCarriageAmount = amountInfo.getCarriageAmount().subtract(amountInfo.getRefundCarriageAmount());
        // 租户物流费 默认=商户
        BigDecimal tenantCurrentCarriageAmount = merchantCurrentCarriageAmount;
        amountInfo.setTenantCarriagePrice(amountInfo.getCarriageAmount());
        OrderAmountRespDTO orderAmountRespDTO = commonOrder.getOrderAmountRespDTO();
        if (orderAmountRespDTO != null && EnumOrderPayType.payTenantToTenant(orderAmountRespDTO.getPaymentType())) {
            // 分销订单 则取分销的金额
            amountInfo.setTenantCarriagePrice(BigDecimal.valueOf(orderAmountRespDTO.getTenantCarriageAmount()));
            tenantCurrentCarriageAmount = NumberUtil.sub(orderAmountRespDTO.getTenantCarriageAmount(), (Number) orderAmountRespDTO.getTenantRefundCarriageAmount());
        }
        // 利润
        amountInfo.setTenantCarriageProfit(NumberUtil.sub(merchantCurrentCarriageAmount, tenantCurrentCarriageAmount).max(BigDecimal.ZERO));
    }


    public static MerchantOrderRespDto order2MerchantOrderRespDto(OrderRespDto order, CommonOrderRespDto commonOrderRespDto) {
        if (order == null) {
            return null;
        }
        OrderEsDto orderEsDto = commonOrderRespDto.getOrderEsDto();
        ImportedPlatformOrderDto importedPlatformOrderDto = commonOrderRespDto.getImportedPlatformOrderDto();

        MerchantOrderRespDto merchantOrderRespDto = new MerchantOrderRespDto();
        merchantOrderRespDto.setTimePoint(new MerchantOrderRespDto.TimePoint());
        merchantOrderRespDto.setPlatformOrderName(order.getPlatformOrderName());
        merchantOrderRespDto.setId(order.getId());
        merchantOrderRespDto.setTenantId(order.getTenantId());
        merchantOrderRespDto.setProductTenantId(orderEsDto.getProductTenantId());
        merchantOrderRespDto.setAddressId(order.getAddressId());
        merchantOrderRespDto.setStoreId(order.getMerchantStoreId());
        merchantOrderRespDto.setCustomerId(order.getCustomerId());
        merchantOrderRespDto.setNo(order.getNo());
        merchantOrderRespDto.setOutOrderNo(order.getOutOrderNo());
        merchantOrderRespDto.setCarriageName(order.getCarriageName());
        merchantOrderRespDto.setCountryExpressInfoId(order.getCountryExpressInfoId());
        merchantOrderRespDto.setLogisticsId(order.getLogisticsId());
        merchantOrderRespDto.setDeliveryType(commonOrderRespDto.getDeliveryType().code);
//        merchantOrderRespDto.setUsername(order.getUsername()); // 此字段在OrderService方法中计算
        merchantOrderRespDto.setOriginType(order.getOriginType());
        merchantOrderRespDto.setOrigin(order.getOrigin());
        merchantOrderRespDto.setOriginId(order.getOriginId());
        merchantOrderRespDto.setDesignStatus(order.getDesignStatus());
        merchantOrderRespDto.setTotalAmount(BigDecimal.valueOf(order.getTotalAmount()));
        merchantOrderRespDto.setProductAmount(BigDecimal.valueOf(order.getProductAmount()));
        merchantOrderRespDto.setStatus(order.getStatus());
        merchantOrderRespDto.setAdvanceOrderStatus(orderEsDto.getAdvanceOrderStatus());
        merchantOrderRespDto.setServiceAmount(BigDecimal.valueOf(order.getServiceAmount()));
        merchantOrderRespDto.setGmtCreated(dateLongToString(order.getCreatedTime()));
        merchantOrderRespDto.setGmtConfirmTime(dateLongToString(order.getConfirmTime()));
        merchantOrderRespDto.setGmtFinished((order.getAdvanceTime() != null && order.getAdvanceTime() > 0) ? dateLongToString(order.getAdvanceTime()) : dateLongToString(order.getFinishTime()));
        merchantOrderRespDto.setDesignFinishTime(dateLongToString(order.getDesignFinishTime()));
        merchantOrderRespDto.setCancelTime(dateLongToString(order.getCancelTime()));
        merchantOrderRespDto.setShelveReleaseTime(order.getShelveReleaseTime());
        merchantOrderRespDto.setRefundStatus(order.getRefundStatus());
        merchantOrderRespDto.setAutoCancelFlag(StrUtil.isNotBlank(order.getCancelRemark()) && "已自动取消".equals(order.getCancelRemark().trim()));
        merchantOrderRespDto.setInvalidIssuingBayFlag(order.getIssuingBayId() == 0);
        merchantOrderRespDto.setAcrossIssuingBayFlag(order.getIssuingBayId() == 0);
        merchantOrderRespDto.setRemark(order.getRemark());
        merchantOrderRespDto.setCancelRemark(order.getCancelRemark());
        merchantOrderRespDto.setAdvanceFlag(order.getIsAdvance() == 2);
        merchantOrderRespDto.setPrepaidNo(order.getPrepaidNo());
        merchantOrderRespDto.setRefundTotalAmount(BigDecimal.valueOf(order.getRefundAmount()));
        merchantOrderRespDto.setRefundCarriageAmount(BigDecimal.valueOf(order.getRefundCarriageAmount()));
        merchantOrderRespDto.setBeAfterServiceOrder(order.getBeAfterServiceOrder());
        merchantOrderRespDto.setExtendMap(order.getExtendMap());

        if (CharSequenceUtil.isNotBlank(orderEsDto.getOnlineOrderStatus())) {
            OnlineOrderStatusMergedEnum mergedEnum = OnlineOrderStatusMergedEnum.getByCode(orderEsDto.getOnlineOrderStatus());
            merchantOrderRespDto.setOnlineOrderStatusName(mergedEnum.getName());
            merchantOrderRespDto.setOnlineOrderStatusCode(mergedEnum.getCode());
            merchantOrderRespDto.setOrderCanRecovery(mergedEnum.getOrderCanRecovery());
        }

        // 付款状态默认未支付
        merchantOrderRespDto.setPayStatus(PaymentService.STATUS_NO_PAIN);
        if (null != order.getPayTime() && order.getPayTime() > 0) {
            merchantOrderRespDto.setPayStatus(PaymentService.STATUS_PAIN);
        }
        merchantOrderRespDto.setGmtPay(dateLongToString(order.getPayTime()));


        if (ObjectUtil.isNotNull(importedPlatformOrderDto)) {
            merchantOrderRespDto.setFbaType(fbaTypeMap.get(importedPlatformOrderDto.getFbaType()));
        } else if (com.sdsdiy.orderapi.constant.OrderOriginType.MANUAL_IMPORT.getValue().equals(order.getOriginType())) {
            // 手动导入默认FBM
            merchantOrderRespDto.setFbaType(fbaTypeMap.get(2));
        } else {
            merchantOrderRespDto.setFbaType(fbaTypeMap.get(0));
        }

        SiteCountry siteCountry = SiteCountry.findByCode(order.getSite());
        if (ObjectUtil.isNotNull(siteCountry)) {
            MerchantOrderRespDto.SiteCountry siteC = new MerchantOrderRespDto.SiteCountry();
            siteC.setSite(order.getSite());
            siteC.setName(siteCountry.getName());
            merchantOrderRespDto.setSiteCountry(siteC);
        }

        //物流来源
        merchantOrderRespDto.setLogisticsSource(order.getLogisticsSource());
        // 是否已装箱
        merchantOrderRespDto.setIsPrepaidBox(order.getIsPrepaidBox());
        // 分销商
        boolean isDistribution = !merchantOrderRespDto.getTenantId().equals(merchantOrderRespDto.getProductTenantId());
        merchantOrderRespDto.setIsDistribution(isDistribution);
        if(isDistribution){
            merchantOrderRespDto.setDistributorName(null!=commonOrderRespDto.getTenantRespDto()?
                commonOrderRespDto.getTenantRespDto().getName():"");
            merchantOrderRespDto.setSupplierName(null!=commonOrderRespDto.getProductTenantRespDto()?
                commonOrderRespDto.getProductTenantRespDto().getName():"");
        }
        return merchantOrderRespDto;
    }

    public static List<MerchantOrderRespDto.OrderItem> orderItem2MerchantOrderRespDtoOrderItem(List<OrderItemRespDto> orderItemList) {
        ArrayList<MerchantOrderRespDto.OrderItem> result = new ArrayList<>(orderItemList.size());
        orderItemList.forEach(orderItem -> result.add(orderItem2MerchantOrderRespDtoOrderItem(orderItem)));
        return result;
    }

    public static MerchantOrderRespDto.OrderItem orderItem2MerchantOrderRespDtoOrderItem(OrderItemRespDto orderItem) {
        if (orderItem == null) {
            return null;
        }

        boolean emptyProduct = orderItem.getProductId() == null || orderItem.getProductId() == 0;
        boolean emptyEndProduct = orderItem.getEndProductId() == null || orderItem.getEndProductId() == 0;

        MerchantOrderRespDto.OrderItem merOrderItem = new MerchantOrderRespDto.OrderItem();
        merOrderItem.setId(orderItem.getId());
        merOrderItem.setOrderId(orderItem.getOrderId());
        merOrderItem.setNo(orderItem.getNo());
        merOrderItem.setSerial(orderItem.getSerial());
        merOrderItem.setStatus(orderItem.getStatus());
        merOrderItem.setProductId(orderItem.getProductId());
        merOrderItem.setKeyId(orderItem.getKeyId());
        merOrderItem.setEndProductId(orderItem.getEndProductId());
        merOrderItem.setTextureName(orderItem.getTextureName());
        merOrderItem.setSellerSku(orderItem.getSellerSku());
        merOrderItem.setProductName(orderItem.getProductName());
        merOrderItem.setImgUrl(orderItem.getImgUrl());
        merOrderItem.setProductSize(orderItem.getProductSize());
        merOrderItem.setNum(orderItem.getNum());
        merOrderItem.setOriginPrice(orderItem.getOriginPrice());
        merOrderItem.setPrice(orderItem.getPrice());
        merOrderItem.setFactoryId(orderItem.getFactoryId());
        merOrderItem.setOutOrderItemId(orderItem.getOutOrderItemId());
        merOrderItem.setRefundStatus(refundStatusMap.get(orderItem.getRefundStatus()));
        merOrderItem.setCompensationStatus(orderItem.getCompensationStatus());
        merOrderItem.setDesignStatus((!emptyProduct && emptyEndProduct) ? "WAIT" : "FINISH");
        merOrderItem.setAssociateStatus((emptyEndProduct && emptyProduct) ? "WAITING" : "FINISH");
        merOrderItem.setIntelligenceMatchStatus(orderItem.getIntelligenceMatchStatus());
        merOrderItem.setSupplyPrice(orderItem.getSupplyPrice());
        merOrderItem.setAmount(orderItem.getAmount());
        merOrderItem.setProductPrototypeType(orderItem.getProductPrototypeType());
        return merOrderItem;
    }

    public static MerchantOrderRespDto.ImportProduct orderImportRecordItem2ImportProduct(ImportedPlatformOrderItemDto orderImportRecordItem, ImportedPlatformOrderFbaItemRespDto fbaItemRespDto) {
        MerchantOrderRespDto.ImportProduct importProduct = new MerchantOrderRespDto.ImportProduct();
        if (orderImportRecordItem != null) {
            importProduct.setId(orderImportRecordItem.getId());
            importProduct.setImportSku(orderImportRecordItem.getSku());
            importProduct.setAsinUrl(orderImportRecordItem.getAsin());
            importProduct.setImportProductName(orderImportRecordItem.getProductName());
            importProduct.setImportColor(orderImportRecordItem.getProductColor());
            importProduct.setImportSize(orderImportRecordItem.getProductSize());
            importProduct.setImportImg(orderImportRecordItem.getProductImg());
            importProduct.setImportImgUrls(null);
        } else if (fbaItemRespDto != null) {
            importProduct.setId(fbaItemRespDto.getId());
            importProduct.setImportSku(fbaItemRespDto.getImportSku());
            importProduct.setImportProductName(fbaItemRespDto.getImportProductName());
//            importProduct.setAmazonAsinUrl(fbaItemRespDto.getImportAsin()); // 此字段在OrderService方法中计算
//            importProduct.setImportImgUrls(fbaItemRespDto.getImportImgs()); // 此字段在OrderService方法中计算
        }
        return importProduct;
    }

    public static MerchantOrderRespDto.ImportProduct orderImportRecordItem2ImportProduct(OrderItemImportProductDetailsItemDto productDetailsItemDto) {
        MerchantOrderRespDto.ImportProduct importProduct = new MerchantOrderRespDto.ImportProduct();
        if (productDetailsItemDto == null) {
            return importProduct;
        }

        String importImgs = productDetailsItemDto.getImportImgs();
        List<String> importImgUrls = ObjectUtil.isNotNull(importImgs) ? JSONArray.parseArray(importImgs, String.class) : null;
        importProduct.setId(productDetailsItemDto.getId());
        importProduct.setImportSku(productDetailsItemDto.getImportSku());
        importProduct.setAsinUrl(productDetailsItemDto.getAsinUrl());
        importProduct.setImportProductName(productDetailsItemDto.getImportProductName());
        importProduct.setImportColor(productDetailsItemDto.getImportColor());
        importProduct.setImportSize(productDetailsItemDto.getImportSize());
        importProduct.setImportImg(CollectionUtils.isNotEmpty(importImgUrls) ? importImgUrls.get(0) : null);
        importProduct.setImportImgUrls(importImgUrls);
        //亚马逊定制信息
        importProduct.setBuyerCustomInfoResult(productDetailsItemDto.getBuyerCustomInfoResult());
        importProduct.setCustomZipUrl(productDetailsItemDto.getCustomZipUrl());
        importProduct.setBuyerCustomSnapshotImgUrls(productDetailsItemDto.getBuyerCustomSnapshotImgUrls());
        importProduct.setBuyerCustomOriginOrderNo(productDetailsItemDto.getBuyerCustomOriginOrderNo());
        List<String> buyerCustomSnapshotImgUrls = productDetailsItemDto.getBuyerCustomSnapshotImgUrls();
        if (CollUtil.isNotEmpty(buyerCustomSnapshotImgUrls)) {
            importProduct.setImportImg(buyerCustomSnapshotImgUrls.get(0));
            importProduct.setImportImgUrls(buyerCustomSnapshotImgUrls);
        }

        if (CollUtil.isEmpty(importProduct.getBuyerCustomSnapshotImgUrls())) {
            importProduct.setBuyerCustomSnapshotImgUrls(Lists.newArrayList(importProduct.getImportImg()));
        }

        return importProduct;
    }

    public static List<MerchantOrderRespDto.Address> address2MerchantOrderAddress(List<AddressRespDto> addressList) {
        ArrayList<MerchantOrderRespDto.Address> result = new ArrayList<>(addressList.size());
        addressList.forEach(address -> result.add(address2MerchantOrderAddress(address)));
        return result;
    }

    public static MerchantOrderRespDto.Address address2MerchantOrderAddress(AddressRespDto address) {
        if (address == null) {
            return null;
        }

        String countryName = CharSequenceUtil.isNotBlank(address.getCountry()) ? EnumNationality.getName(address.getCountry()) : null;

        MerchantOrderRespDto.Address result = new MerchantOrderRespDto.Address();
        result.setId(address.getId());
        result.setCountry(address.getCountry());
        result.setCityCode(address.getCityCode());
        result.setProvince(address.getProvince());
        result.setCountryDecode(countryName);
        result.setConsignee(address.getConsignee());
        result.setCountryName(countryName);
        result.setCity(address.getCity());
        result.setDetail(address.getDetail());
        result.setProvinceCode(address.getProvinceCode());
        result.setMobilePhone(CharSequenceUtil.isNotEmpty(address.getCellphone()) ? address.getCellphone() : address.getMobilePhone());
        result.setPostcode(address.getPostcode());
        result.setEmail(address.getEmail());
        result.setType(address.getType());
        result.setAddressDetail1(address.getAddressDetail1());
        result.setAddressDetail2(address.getAddressDetail2());
        result.setAddressDetail3(address.getAddressDetail3());

        return result;
    }

    public static List<MerchantOrderRespDto.MerchantStore> merchantStore2MerchantOrderMerchantStore(List<MerchantStoreRespDTO> merchantStoresList) {
        ArrayList<MerchantOrderRespDto.MerchantStore> result = new ArrayList<>(merchantStoresList.size());
        merchantStoresList.forEach(merchantStore -> result.add(merchantStore2MerchantOrderMerchantStore(merchantStore)));
        return result;
    }

    public static MerchantOrderRespDto.MerchantStore merchantStore2MerchantOrderMerchantStore(MerchantStoreRespDTO merchantStore) {
        if (merchantStore == null) {
            return null;
        }

        MerchantStorePlatformEnum platformEnum = MerchantStorePlatformEnum.getByCode(merchantStore.getMerchantStorePlatformCode());
        MerchantOrderRespDto.MerchantStore result = new MerchantOrderRespDto.MerchantStore();
        result.setId(merchantStore.getId());
        result.setName(merchantStore.getName());
        result.setStatus(merchantStore.getStatus());
        result.setPlatformCode(merchantStore.getMerchantStorePlatformCode());
        result.setIsPopChoice(merchantStore.getIsPopChoice());
        result.setType(merchantStore.getType());
        result.setPlatformName(ObjectUtil.isNotNull(platformEnum) ? platformEnum.getName() : null);
        return result;
    }

    public static MerchantOrderRespDto.Merchant merchant2MerchantOrderMerchant(Long currentTenantId,Boolean isDistribution,Merchant merchant) {
        if (merchant == null) {
            return null;
        }
        boolean sdsdiy = TenantCommonConstant.isSdsdiy(currentTenantId);
        boolean otherTenantMerchant = !merchant.getTenantId().equals(currentTenantId);

        MerchantOrderRespDto.Merchant result = new MerchantOrderRespDto.Merchant();
        result.setId(merchant.getId());
        result.setName(!sdsdiy&&otherTenantMerchant?merchant.getMerchantNo():merchant.getName());
        return result;
    }

    /**
     * Boolean boolCheckIsFreePay = freePayMerchantFeign.checkIsFreePay(merchantRespDto.getTenantId(),merchantRespDto.getId(),operator.getUserId());
     * //是否开启线上支付
     * boolean tenantOpenOnlinePay
     * 装饰 payment
     *
     * @return
     */
    public static MerchantOrderRespDto.Payment decoratorOrderPayment(Boolean boolCheckIsFreePay, boolean tenantOpenOnlinePay, CommonOrderRespDto commonOrderRespDto) {
        if (commonOrderRespDto.getOrderAmountRespDTO() == null) {
            return null;
        }

        MerchantOrderRespDto.Payment result = new MerchantOrderRespDto.Payment();
        result.setPaymentMethod(commonOrderRespDto.getOrderAmountRespDTO().getPaymentMethod());
        if (OrderStatus.unpain(commonOrderRespDto.getOrderRespDto().getStatus())) {
            //待付款 要根据用户与产品做判断
            result.setPaymentChannelMethod(PaymentChannelMethodEnum.ONLINE.getCode());
            if (EnumOrderPayType.onlyPayTenant(commonOrderRespDto.getOrderAmountRespDTO().getPaymentType())) {
                //sds产品一定是线上 所以只需要判断租户产品
                if (boolCheckIsFreePay || !tenantOpenOnlinePay) {
                    //未开启线上支付 或者面密的时候是线下支付
                    result.setPaymentChannelMethod(PaymentChannelMethodEnum.OFFLINE.getCode());
                }
            }
        } else if (StringUtil.isNotBlank(result.getPaymentMethod())) {
            //如果有维护支付信息则使用支付信息
            if (PaymentMethodEnum.isOffline(result.getPaymentMethod())) {
                result.setPaymentChannelMethod(PaymentChannelMethodEnum.OFFLINE.getCode());
            } else {
                result.setPaymentChannelMethod(PaymentChannelMethodEnum.ONLINE.getCode());
            }
        }

        return result;
    }

    public static MerchantOrderRespDto.Factory factory2MerchantOrderFactory(Factory factory) {
        if (factory == null) {
            return null;
        }

        MerchantOrderRespDto.Factory result = new MerchantOrderRespDto.Factory();
        result.setId(factory.getId());
        result.setName(factory.getName());
        return result;
    }

    public static MerchantOrderRespDto.CountryExpressInfo countryExpress2MerchantCountryExpress(CountryExpressInfoNewRespDto countryExpressInfoNew) {
        if (countryExpressInfoNew == null) {
            return null;
        }
        MerchantOrderRespDto.CountryExpressInfo result = new MerchantOrderRespDto.CountryExpressInfo();
        result.setId(countryExpressInfoNew.getId());
        result.setPrescriptionEnd(countryExpressInfoNew.getPrescriptionEnd());
        result.setPrescriptionStart(countryExpressInfoNew.getPrescriptionStart());
        result.setPrescriptionType(countryExpressInfoNew.getPrescriptionType());
        return result;
    }

    public static MerchantOrderRespDto.Logistics logistics2MerchantLogistics(LogisticsRespDto logistics, OrderCarriageRespDto orderCarriage) {
        MerchantOrderRespDto.Logistics result = new MerchantOrderRespDto.Logistics();
        if (logistics == null) {
            if (orderCarriage != null) {
                result.setId(orderCarriage.getLogisticsId());
                result.setName(orderCarriage.getCarriageName());
            }
            return result;
        }
        result.setServiceProviderId(logistics.getServiceProviderId());
        result.setIsEurIossCheck(EurIossServiceProviderEnum.isIossProvider(logistics.getServiceProviderId()));
        result.setId(logistics.getId());
        result.setName(logistics.getName());
        result.setChannelType(logistics.getChannelType());
        result.setCodeId(logistics.getCodeId());
        result.setExpressStatus(logistics.getExpressStatus());
        result.setCustomUploadLabel((logistics.getCustomUploadLable() != null) && (logistics.getCustomUploadLable() == 1));
        return result;
    }

    public static MerchantOrderRespDto.Compensation order2MerchantCompensation(OrderRespDto order, List<CommonOrderRespDto.ParcelDTO> parcelList) {
        if (order == null) {
            return null;
        }
        Integer customUploadLabel = null;
        if (CollUtil.isNotEmpty(parcelList) && parcelList.get(0).getLogisticsRespDto() != null) {
            customUploadLabel = parcelList.get(0).getLogisticsRespDto().getCustomUploadLable();
        }

        Integer compensationStatus = order.getCompensationStatus();
        Long compensationTime = order.getCompensationTime();
        Long compensationFinishTime = order.getCompensationFinishTime();
        Long finishTime = order.getFinishTime();
        Long cancelTime = order.getCancelTime();
        Integer status = order.getStatus();
        Long accomplishTime = order.getAccomplishTime();
        Long uploadLaberTime = order.getUploadLaberTime();
        Long shelveBeginTime = order.getShelveBeginTime();
        Integer durationTimeStatus = CommonStatus.ONLINE.getStatus();
        if (compensationStatus != null && compensationStatus != CompensationStatus.NONE.getStatus() && compensationTime != null) {
            if (compensationFinishTime != null && compensationFinishTime > 0) {
                durationTimeStatus = CommonStatus.OFFLINE.getStatus();
            } else if (finishTime != null && finishTime > 0) {
                durationTimeStatus = CommonStatus.OFFLINE.getStatus();
            } else if (cancelTime != null && cancelTime > 0) {
                durationTimeStatus = CommonStatus.OFFLINE.getStatus();
            } else if (status != null && status == OrderStatus.SHELVE.getStatus()) {
                durationTimeStatus = CommonStatus.OFFLINE.getStatus();
            } else if (accomplishTime != null && accomplishTime > 0) {
                //质检通过后且未完成，且需要用户主动上传pdf时按质检时间
                if (customUploadLabel != null && customUploadLabel.equals(CommonStatus.ONLINE.getStatus())) {
                    //用户未上传pdf 或者 用户上传时间低于质检时间，则按质检时间此时冻结
                    if (uploadLaberTime == null || uploadLaberTime.equals(0L) || accomplishTime < uploadLaberTime) {
                        durationTimeStatus = CommonStatus.OFFLINE.getStatus();
                    }
                }
            }
        }

        Long durationTime = null;
        if (compensationStatus != null && compensationStatus != CompensationStatus.NONE.getStatus() && compensationTime != null) {
            Long endTime = System.currentTimeMillis();
            if (compensationFinishTime != null && compensationFinishTime > 0) {
                endTime = compensationFinishTime;
            } else if (finishTime != null && finishTime > 0) {
                endTime = finishTime;
            } else if (cancelTime != null && cancelTime > 0) {
                endTime = cancelTime;
            } else if (status != null && status == OrderStatus.SHELVE.getStatus()) {
                endTime = shelveBeginTime;
            } else if (accomplishTime != null && accomplishTime > 0) {
                //质检通过后且未完成，且需要用户主动上传pdf时按质检时间
                if (customUploadLabel != null && customUploadLabel.equals(CommonStatus.ONLINE.getStatus())) {
                    //用户未上传pdf 或者 用户上传时间低于质检时间，则按质检时间
                    if (uploadLaberTime == null || uploadLaberTime.equals(0L) || accomplishTime < uploadLaberTime) {
                        endTime = accomplishTime;
                    }
                }
            }
            durationTime = endTime - compensationTime;
        }

        MerchantOrderRespDto.Compensation result = new MerchantOrderRespDto.Compensation();
        result.setAmount(BigDecimal.valueOf(order.getCompensationAmount()));
        result.setStatus(order.getCompensationStatus());
        result.setDurationTime(durationTime);
        result.setDurationTimeStatus(durationTimeStatus);
        return result;
    }

    public static MerchantOrderRespDto.IssuingBayArea issuingBayArea2MerchantIssuingBayArea(IssuingBayArea issuingBayArea) {
        if (issuingBayArea == null) {
            return null;
        }

        MerchantOrderRespDto.IssuingBayArea result = new MerchantOrderRespDto.IssuingBayArea();
        result.setId(issuingBayArea.getId());
        result.setName(issuingBayArea.getName());
        result.setType(issuingBayArea.getType());
        return result;
    }

    public static List<MerchantOrderRespDto.IssuingBayArea> issuingBayArea2MerchantIssuingBayArea(List<IssuingBayArea> issuingBayAreaList) {
        ArrayList<MerchantOrderRespDto.IssuingBayArea> result = new ArrayList<>(issuingBayAreaList.size());
        issuingBayAreaList.forEach(issuingBayArea -> result.add(issuingBayArea2MerchantIssuingBayArea(issuingBayArea)));
        return result;
    }

    public static MerchantOrderRespDto.IssuingBay issuingBay2MerchantIssuingBay(IssuingBay issuingBay) {
        if (issuingBay == null) {
            return null;
        }

        MerchantOrderRespDto.IssuingBay result = new MerchantOrderRespDto.IssuingBay();
        result.setId(issuingBay.getId());
        result.setName(issuingBay.getName());
        result.setProvince(issuingBay.getProvince());
        result.setCity(issuingBay.getCity());
        result.setCountryName(issuingBay.getCountryName());
        result.setDetail(issuingBay.getDetail());
        return result;
    }

    public static List<MerchantOrderRespDto.IssuingBay> issuingBay2MerchantIssuingBay(List<IssuingBay> issuingBayList) {
        ArrayList<MerchantOrderRespDto.IssuingBay> result = new ArrayList<>(issuingBayList.size());
        issuingBayList.forEach(issuingBay -> result.add(issuingBay2MerchantIssuingBay(issuingBay)));
        return result;
    }

    public static MerchantOrderRespDto.OrderFba orderFba2MerchantOrderFba(OrderFba orderFba) {
        if (orderFba == null) {
            return null;
        }

        MerchantOrderRespDto.OrderFba result = new MerchantOrderRespDto.OrderFba();
        result.setId(orderFba.getId());
        result.setPickupCode(orderFba.getPickupCode());
        result.setStatus(orderFba.getStatus());
        result.setOrderId(orderFba.getOrderId());
        result.setCarriageMoney(BigDecimal.valueOf(orderFba.getCarriageMoney()));
        result.setProductedStatus(orderFba.getProductedStatus());
        List<String> boxLabels = orderFba.parseLabelsJson();
        result.setBoxLabels(boxLabels);
        //装箱各种时间
        result.setGmtProductedTime(formatDate(orderFba.getProductedTime()));
        result.setGmtEncasementTime(formatDate(orderFba.getEncasementTime()));
        result.setGmtUploadLabelTime(formatDate(orderFba.getUploadLabelTime()));
        result.setGmtCalculateLogisticsTime(formatDate(orderFba.getCalculateLogisticsTime()));
        result.setGmtPayCarriageTime(formatDate(orderFba.getPayCarriageTime()));
        result.setGmtBoxLabelPrintedTime(formatDate(orderFba.getBoxLabelPrintedTime()));
        result.setGmtShippedTime(formatDate(orderFba.getShippedTime()));
        return result;
    }

    private static String formatDate(Long time) {
        if (time != null && time > 0) {
            return DateUtil.longToString(time);
        }
        return "";
    }

    public static MerchantOrderRespDto.OrderFbaCarriageDetail orderFbaCarriageDetail2MerchantOrderFbaCarriageDetail(OrderFbaCarriageDetailRespDto orderFbaCarriageDetailRespDto, OrderFba orderFba) {
        MerchantOrderRespDto.OrderFbaCarriageDetail result = new MerchantOrderRespDto.OrderFbaCarriageDetail();
        if (orderFbaCarriageDetailRespDto == null) {
            //旧订单
            if (null == orderFba) {
                return null;
            }
            result.setId(orderFba.getOrderId());
            result.setTotalAmount(BigDecimal.valueOf(orderFba.getCarriageMoney()));
            result.setCurrentCarriageAmount(BigDecimal.valueOf(orderFba.getCarriageMoney()));
            return result;
        }

        result.setId(orderFbaCarriageDetailRespDto.getId());
        result.setTotalAmount(orderFbaCarriageDetailRespDto.getTotalAmount());
        result.setCurrentCarriageAmount(orderFbaCarriageDetailRespDto.getCurrentCarriageAmount());
        return result;
    }

    public static List<MerchantOrderRespDto.OrderFba> orderFba2MerchantOrderFba(List<OrderFba> orderFbaList) {
        ArrayList<MerchantOrderRespDto.OrderFba> result = new ArrayList<>(orderFbaList.size());
        orderFbaList.forEach(orderFba -> result.add(orderFba2MerchantOrderFba(orderFba)));
        return result;
    }

    public static MerchantOrderRespDto.Carriage order2MerchantCarriage(MerchantOrderRespDto merchantOrderRespDto
            , OrderRespDto order, OrderParcelDTO orderParcelDTO
            , OrderCarriageRespDto orderCarriage, List<OrderSyncHistoryDto> orderSyncHistoryList) {
        if (orderCarriage == null) {
            return new MerchantOrderRespDto.Carriage();
        }
        MerchantOrderRespDto.Carriage carriage = new MerchantOrderRespDto.Carriage();
        carriage.setStatus(carriageStatusMap.get(orderCarriage.getCarriageStatus()));
        carriage.setNo(orderCarriage.getCarriageNo());
        carriage.setCarriageMsg(orderCarriage.getCarriageMsg());
        carriage.setTransferCarriageNo(orderCarriage.getTransferCarriageNo());
        carriage.setLaberPdf(orderCarriage.getLaberPdf());
        carriage.setLatestTrackMilestone(orderCarriage.getLatestTrackMilestone());
        carriage.setPrescriptionEnd(orderCarriage.getPrescriptionEnd());
        carriage.setPrescriptionStart(orderCarriage.getPrescriptionStart());
        carriage.setPrescriptionType(orderCarriage.getPrescriptionType());
        carriage.setLatestTrackMilestoneDifference(0L);
        if (orderCarriage.getLatestTrackMilestoneTime() != null) {
            long time = orderCarriage.getLatestTrackMilestoneTime().getTime();
            if (TrackMilestoneEnum.NO_RECORD.name().equals(orderCarriage.getLatestTrackMilestone())) {
                time = orderParcelDTO.getDeliveryTime();
            }
            if (TrackMilestoneEnum.PICKUP.name().equals(orderCarriage.getLatestTrackMilestone())) {
                //未发货的记成0
                if (OrderParcelStatusEnum.PACKING.equalsCode(orderParcelDTO.getStatus())) {
                    carriage.setLatestTrackMilestoneDifference(0L);
                } else {
                    carriage.setLatestTrackMilestoneDifference(TimeUnit.MILLISECONDS.toDays(System.currentTimeMillis() - orderParcelDTO.getDeliveryTime()));
                }
            }
            if (time >= System.currentTimeMillis()) {
                carriage.setLatestTrackMilestoneDifference(0L);
            } else {
                carriage.setLatestTrackMilestoneDifference(TimeUnit.MILLISECONDS.toDays(System.currentTimeMillis() - time));
            }
        }
        Integer orderSyncStatus = order.getSyncStatus();
        Integer parcelSyncStatus = orderParcelDTO.getCarriageSyncStatus();
        MerchantOrderRespDto.MerchantStore merchantStore = merchantOrderRespDto.getMerchantStore();
        // 同步按钮，全开/全关，根据订单的状态判断就好了
        carriage.setShowSyncButton(false);
        boolean jitOrder = OrderExtendValueEnum.JIT.isMatch(order.getExtendMap());
        // jit单不同步
        if (OrderOriginType.AUTO_IMPORT.getValue().equals(order.getOriginType()) && !jitOrder) {
            if (CarriageStatusEnum.STATUS_SUCCESS.getCode().equals(orderCarriage.getCarriageStatus())) {
                if (OrderSyncStatus.showSyncButton(orderSyncStatus)) {
                    carriage.setShowSyncButton(true);
                }
            }
            if (CarriageStatusEnum.STATUS_STAY.getCode().equals(orderCarriage.getCarriageStatus())
                    && OrderStatus.normal(order.getStatus()) &&
                    merchantStore != null &&
                    OrderSyncStatus.NONE.getStatus().equals(orderSyncStatus) &&
                    MerchantStorePlatformEnum.SELL_FAST.getCode().equalsIgnoreCase(merchantStore.getPlatformCode())) {
                carriage.setShowSyncButton(true);
            }
        }
        // 同步状态以包裹的为准
        carriage.setSyncStatus(orderSynStatusMap.get(parcelSyncStatus));
        carriage.setSyncError(orderParcelDTO.getCarriageSyncError());
        carriage.setTrackStatus(carriageTrackStatusMap.get(orderCarriage.getCarriageTrackStatus()));
        carriage.setCarriageExpireTimestamp(orderCarriage.getCarriageExpireTimestamp());

        List<MerchantOrderRespDto.CarriageHistory> carriageHistoryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderSyncHistoryList)) {
            orderSyncHistoryList.forEach(i -> {
                MerchantOrderRespDto.CarriageHistory carriageHistory = new MerchantOrderRespDto.CarriageHistory();
                carriageHistory.setOrderId(i.getOrderId());
                carriageHistory.setSyncDate(dateLongToString(i.getCreatedTime()));
                carriageHistory.setNo(i.getNo());
                carriageHistory.setError(i.getError());
                carriageHistoryList.add(carriageHistory);
            });
        }
        carriage.setHistory(carriageHistoryList);
        return carriage;
    }

    public static MerchantOrderRespDto.ExclusiveAuthorizationPrice exclusiveAuthorizationPrice(OrderItemPriceRespDto orderItemPriceRespDto) {
        if (orderItemPriceRespDto == null) {
            return null;
        }

        MerchantOrderRespDto.ExclusiveAuthorizationPrice authorizationPrice = new MerchantOrderRespDto.ExclusiveAuthorizationPrice();
        authorizationPrice.setBatchMinNum(orderItemPriceRespDto.getBatchMinNum());
        authorizationPrice.setBatchPrice(BigDecimal.valueOf(orderItemPriceRespDto.getBatchPrice()));
        authorizationPrice.setOnePrice(BigDecimal.valueOf(orderItemPriceRespDto.getOnePrice()));
        return authorizationPrice;
    }

    public static MerchantOrderRespDto.AccumulatePriceInfo accumulatePriceInfo(OrderItemPriceRespDto orderItemPriceRespDto) {
        if (orderItemPriceRespDto == null) {
            return null;
        }

        MerchantOrderRespDto.AccumulatePriceInfo accumulatePriceInfo = new MerchantOrderRespDto.AccumulatePriceInfo();
        accumulatePriceInfo.setCurrentPrice(BigDecimal.valueOf(orderItemPriceRespDto.getCurrentPrice()));
        accumulatePriceInfo.setNextPrice(BigDecimal.valueOf(orderItemPriceRespDto.getNextPrice()));
        accumulatePriceInfo.setCurrentLevel(orderItemPriceRespDto.getCurrentLevel());
        accumulatePriceInfo.setNextLevel(orderItemPriceRespDto.getNextLevel());
        accumulatePriceInfo.setNextDifferenceNum(orderItemPriceRespDto.getNextDifferenceNum());
        return accumulatePriceInfo;
    }

    public static List<MerchantOrderRespDto.PlatformPrice> platformPriceInfo(String platformPrice) {
        if (StrUtil.isBlank(platformPrice)) {
            return null;
        }
        return JSONArray.parseArray(platformPrice, MerchantOrderRespDto.PlatformPrice.class);
    }

    public static List<SmallOrderPriceDto> smallOrderPlatformPriceInfo(String platformPrice) {
        if (StrUtil.isBlank(platformPrice)) {
            return null;
        }
        return JSONArray.parseArray(platformPrice, SmallOrderPriceDto.class);
    }

    public static MerchantOrderRespDto.Profit profitVo2Profit(OrderProfitVo orderProfitVo) {
        if (orderProfitVo == null) {
            return null;
        }

        MerchantOrderRespDto.Profit profit = new MerchantOrderRespDto.Profit();
        profit.setCommitRate(doubleToBigDecimal(orderProfitVo.getCommitRate()));
        profit.setCost(doubleToBigDecimal(orderProfitVo.getCost()));
        profit.setCurrencySign(orderProfitVo.getCurrencySign());
        profit.setExchangeRate(doubleToBigDecimal(orderProfitVo.getExchangeRate()));
        profit.setLogisticsAmount(doubleToBigDecimal(orderProfitVo.getLogisticsAmount()));
        profit.setProductAmount(doubleToBigDecimal(orderProfitVo.getProductAmount()));
        profit.setProfitRate(orderProfitVo.getProfitRate());
        profit.setProfitRateStatus(orderProfitVo.getProfiteRateStatus());
        profit.setStatus(orderProfitVo.getStatus());
        profit.setTaxAmount(doubleToBigDecimal(orderProfitVo.getTaxAmount()));
        profit.setTotalAmount(doubleToBigDecimal(orderProfitVo.getTotalAmount()));
        profit.setRealIncome(orderProfitVo.getRealIncome());

        profit.setProductTaxAmount(orderProfitVo.getProductTaxAmount());
        profit.setShippingTaxAmount(orderProfitVo.getShippingTaxAmount());
        profit.setFeeAmount(orderProfitVo.getFeeAmount());
        profit.setCommissionAmount(orderProfitVo.getCommissionAmount());
        profit.setDiscountAmount(orderProfitVo.getDiscountAmount());

        BigDecimal commission = null;
        if (null != profit.getCommitRate() && null != profit.getTotalAmount()) {
            commission = NumberUtil.mul(profit.getTotalAmount(), profit.getCommitRate());
        }
        profit.setCommission(commission);
        return profit;
    }

    public static List<MerchantOrderRespDto.CarriageHistory> synHistory2MerchantCarriageHistory(List<OrderSyncHistoryDTO> orderSyncHistoryDTOList) {
        ArrayList<MerchantOrderRespDto.CarriageHistory> result = new ArrayList<>(orderSyncHistoryDTOList.size());
        orderSyncHistoryDTOList.forEach(historyDTO -> result.add(synHistory2MerchantCarriageHistory(historyDTO)));
        return result;
    }

    public static MerchantOrderRespDto.CarriageHistory synHistory2MerchantCarriageHistory(OrderSyncHistoryDTO orderSyncHistoryDTO) {
        if (orderSyncHistoryDTO == null) {
            return null;
        }

        MerchantOrderRespDto.CarriageHistory carriageHistory = new MerchantOrderRespDto.CarriageHistory();
        carriageHistory.setError(orderSyncHistoryDTO.getError());
        carriageHistory.setNo(orderSyncHistoryDTO.getNo());
        carriageHistory.setSyncDate(dateLongToString(orderSyncHistoryDTO.getCreatedTime()));
        carriageHistory.setOrderId(orderSyncHistoryDTO.getOrderId());
        return carriageHistory;
    }

    public static String dateLongToString(Long date) {
        if (date == null || date <= 0) {
            return null;
        }
        return com.ziguang.base.support.DateUtil.longToString(date);
    }

    public static String secLongToString(Long sec) {
        if (sec == null || sec <= 0) {
            return null;
        }
        return dateLongToString(sec * 1000L);
    }

    private static BigDecimal doubleToBigDecimal(Double doubleN) {
        if (doubleN == null) {
            return null;
        }
        return BigDecimal.valueOf(doubleN);
    }

    public static <T> List<T> splitBySpaceOrDou(String str, Class<T> clazz) {
        if (StrUtil.isBlank(str)) {
            return null;
        }

        String[] splitArr = {str};
        if (str.trim().contains(" ")) {
            splitArr = str.split(" ");
        } else if (str.contains(",")) {
            splitArr = str.split(",");
        }

        ArrayList arrayList = new ArrayList<>();
        if (clazz == Integer.class) {
            for (String s : splitArr) {
                arrayList.add(Integer.valueOf(s));
            }
            return arrayList;
        } else if (clazz == Long.class) {
            for (String s : splitArr) {
                arrayList.add(Long.valueOf(s));
            }
        } else {
            Collections.addAll(arrayList, splitArr);
        }
        return (List<T>) arrayList;
    }

    /**
     * MerchantOrderRespDto数据的最终整理，需保证merchantOrderRespDto前置数据完整可用
     */
    public static void formatMerchantOrderRespDto(MerchantOrderRespDto merchantOrderRespDto, CommonOrderRespDto commonOrderRespDto) {
        merchantOrderRespDto.setBeSelfTake(DeliveryTypeEnum.ZT.equalsCode(merchantOrderRespDto.getDeliveryType()));
        merchantOrderRespDto.setPickupInfo(getPickupInfo(merchantOrderRespDto, commonOrderRespDto));
        merchantOrderRespDto.setItemsDesignStatus(getItemsDesignStatus(merchantOrderRespDto.getDesignStatus(), merchantOrderRespDto.getItems()));
        merchantOrderRespDto.setStatusHistory(getOrderStatusHistory(merchantOrderRespDto));
        merchantOrderRespDto.setInspectionFlag(getInspectionFlag(merchantOrderRespDto.getOrderFba(), commonOrderRespDto));
        merchantOrderRespDto.setStatus(getStatusCoverByAdvanceOrderStatus(merchantOrderRespDto));
    }

    public static void formatPodOrderRespDto(MerchantOrderRespDto merchantOrderRespDto, CommonOrderRespDto commonOrderRespDto) {
        merchantOrderRespDto.setBeSelfTake(DeliveryTypeEnum.ZT.equalsCode(merchantOrderRespDto.getDeliveryType()));
        merchantOrderRespDto.setPickupInfo(getPickupInfo(merchantOrderRespDto, commonOrderRespDto));
        merchantOrderRespDto.setItemsDesignStatus(getItemsDesignStatus(merchantOrderRespDto.getDesignStatus(), merchantOrderRespDto.getItems()));
        merchantOrderRespDto.setStatusHistory(getOrderStatusHistory(merchantOrderRespDto));
        merchantOrderRespDto.setInspectionFlag(getInspectionFlag(merchantOrderRespDto.getOrderFba(), commonOrderRespDto));
    }

    /**
     * 订单状态的覆写，以满足特定业务需要
     */
    private static Integer getStatusCoverByAdvanceOrderStatus(MerchantOrderRespDto merchantOrderRespDto) {
        Integer orderStatus = merchantOrderRespDto.getStatus();
        Integer advanceOrderStatus = merchantOrderRespDto.getAdvanceOrderStatus();
        if (orderStatus == null || advanceOrderStatus == null) {
            return null;
        }

        List<Integer> allowStatusList = Lists.newArrayList(com.sdsdiy.orderdata.enums.OrderStatus.WAIT_DESIGN.getStatus(), com.sdsdiy.orderdata.enums.OrderStatus.FINISH.getStatus());
        if (allowStatusList.contains(advanceOrderStatus)) {
            // 由于order表的status字段本身不会有待设计7这样的状态，但当订单又是待设计状态时，却需要给前端待设计状态，所以使用advanceOrderStatus来覆盖原来的订单状态，以保持对外状态一致
            return advanceOrderStatus;
        }
        return orderStatus;
    }

    private static Boolean getInspectionFlag(MerchantOrderRespDto.OrderFba orderFba, CommonOrderRespDto commonOrderRespDto) {
        if (orderFba != null && orderFba.getProductedStatus() != null && orderFba.getProductedStatus() == 2) {
            // fba 质检完成
            return true;
        }
        List<CommonOrderRespDto.ParcelDTO> parcelList = commonOrderRespDto.getParcelList();
        if (CollUtil.isNotEmpty(parcelList) && parcelList.stream().anyMatch(i -> i.getPickUpLogisticsInfoDto() != null)) {
            // 散单自提 质检完成
            return true;
        }
        return false;
    }

    private static List<MerchantOrderRespDto.OrderHistory> getOrderStatusHistory(MerchantOrderRespDto merchantOrderRespDto) {
        List<MerchantOrderRespDto.OrderHistory> list = Lists.newArrayList();
        if (StrUtil.isNotBlank(merchantOrderRespDto.getGmtCreated())) {
            list.add(new MerchantOrderRespDto.OrderHistory(OrderStatus.UNPAIN.getStatus(), merchantOrderRespDto.getGmtCreated()));
        }
        if (StrUtil.isNotBlank(merchantOrderRespDto.getGmtPay())) {
            list.add(new MerchantOrderRespDto.OrderHistory(OrderStatus.PAIN.getStatus(), merchantOrderRespDto.getGmtPay()));
        }
        if (StrUtil.isNotBlank(merchantOrderRespDto.getDesignFinishTime())) {
            list.add(new MerchantOrderRespDto.OrderHistory(OrderStatus.DESIGN_FINISH.getStatus(), merchantOrderRespDto.getDesignFinishTime()));
        }
        if (StrUtil.isNotBlank(merchantOrderRespDto.getGmtConfirmTime())) {
            list.add(new MerchantOrderRespDto.OrderHistory(OrderStatus.STOCK_UP.getStatus(), merchantOrderRespDto.getGmtConfirmTime()));
        }
        // 速卖通 预约时间
        if (merchantOrderRespDto.getTimePoint() != null && NumberUtils.greaterZero(merchantOrderRespDto.getTimePoint().getAliexpressPreTime())) {
            list.add(new MerchantOrderRespDto.OrderHistory(210
                    , TimeUtil.format(merchantOrderRespDto.getTimePoint().getAliexpressPreTime(), "")));
        }
        // 部分发货时间
        if (CollUtil.isNotEmpty(merchantOrderRespDto.getParcelList()) && merchantOrderRespDto.getParcelList().size() > 1) {
            long partShipTime = merchantOrderRespDto.getParcelList().stream().filter(i -> NumberUtils.greaterZero(i.getDeliveryTime()))
                    .mapToLong(MerchantOrderRespDto.Parcel::getDeliveryTime).min().orElse(0);
            if (partShipTime > 0) {
                list.add(new MerchantOrderRespDto.OrderHistory(OrderStatus.PART_SHIPPED.getStatus(), TimeUtil.format(partShipTime, "")));
            }
        }
        if (StrUtil.isNotBlank(merchantOrderRespDto.getCancelTime())) {
            list.add(new MerchantOrderRespDto.OrderHistory(OrderStatus.CANCEL.getStatus(), merchantOrderRespDto.getCancelTime()));
        }
        if (StrUtil.isNotBlank(merchantOrderRespDto.getGmtFinished())) {
            list.add(new MerchantOrderRespDto.OrderHistory(OrderStatus.FINISH.getStatus(), merchantOrderRespDto.getGmtFinished()));
        }
        return list;
    }

    public static Integer getCanCompensation(OrderRespDto order, Payment payment, List<MerchantOrderRespDto.OrderItem> orderItems) {
        Integer status = order.getStatus();
        if (status == null) {
            return CommonStatus.OFFLINE.getStatus();
        }
        if (!TenantCommonConstant.isSdsdiy(order.getTenantId())) {
            //非sds租户
            return CommonStatus.OFFLINE.getStatus();
        }
        if (OrderStatus.PAIN.getStatus() != status && OrderStatus.STOCK_UP.getStatus() != status) {
            return CommonStatus.OFFLINE.getStatus();
        }
        if (System.currentTimeMillis() < expectShipTime(order, payment, orderItems)) {
            return CommonStatus.OFFLINE.getStatus();
        }

        if (OrderOrigin.isFba(order.getOrigin())) {
            return CommonStatus.OFFLINE.getStatus();
        }

        if (EndProductType.isPrivate(order.getProductionType())) {
            return CommonStatus.OFFLINE.getStatus();
        }

        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (MerchantOrderRespDto.OrderItem item : orderItems) {
                if (item.getCompensationStatus() != null && item.getCompensationStatus().equalsIgnoreCase(EnumProductCompensationStatus.OPEN.getStauts())) {
                    return CommonStatus.ONLINE.getStatus();
                }
            }
        }
        return CommonStatus.OFFLINE.getStatus();
    }

    public static Integer getShipDeadlineDayNum(OrderRespDto order, Payment payment, List<MerchantOrderRespDto.OrderItem> orderItemList) {
        Long expectShipTime = expectShipTime(order, payment, orderItemList);
        return (int) ((expectShipTime - System.currentTimeMillis()) / (24L * 60 * 60 * 1000));
    }

    public static MerchantOrderRespDto.CrmUser getCrmUser(CrmUserRespDTO crmUserRespDTO) {
        if (crmUserRespDTO == null) {
            return null;
        }
        MerchantOrderRespDto.CrmUser crmUser = new MerchantOrderRespDto.CrmUser();
        crmUser.setId(crmUserRespDTO.getId());
        crmUser.setName(crmUserRespDTO.getName());
        return crmUser;
    }

    private static Long expectShipTime(OrderRespDto order, Payment payment, List<MerchantOrderRespDto.OrderItem> orderItemList) {
        if (!NumberUtils.greaterZero(order.getPayTime())) {
            return 0L;
        }
        if (order.getPrepareTime() == null) {
            return 0L;
        }
        if (order.getShelveTime() == null) {
            return 0L;
        }
        if (order.getStatus() == null) {
            return 0L;
        }
        if (order.getIsAdvance() != null && OrderAdvance.YES.getStatus() == order.getIsAdvance()) {
            return 0L;
        }
        if (order.getStatus() != OrderStatus.PAIN.getStatus() && order.getStatus() != OrderStatus.STOCK_UP.getStatus()) {
            return 0L;
        }
        if (order.getDesignStatus() == null || order.getDesignStatus().equals(OrderDesignStatus.WAIT.name())) {
            return 0L;
        }
        Long holidayBegin = null;
        Long holidayEnd = null;
        for (MerchantOrderRespDto.OrderItem item : orderItemList) {
            if (item.getProductSupply() == null) {
                continue;
            }
            if (CommonStatus.ONLINE.getStatus().equals(item.getProductSupply().getHolidayStatus())) {
                if (holidayBegin == null || item.getProductSupply().getHolidayStartTime() < holidayBegin) {
                    holidayBegin = item.getProductSupply().getHolidayStartTime();
                }
                if (holidayEnd == null || item.getProductSupply().getHolidayEndTime() < holidayEnd) {
                    holidayEnd = item.getProductSupply().getHolidayEndTime();
                }
            }
        }
        Long beginTime = order.getDesignFinishTime() == null || order.getDesignFinishTime().equals(0L) ? order.getPayTime() : order.getDesignFinishTime();
        long holidayLeftTime = 0L;
        if (holidayBegin != null && holidayEnd != null) {
            if (beginTime < holidayBegin) {
                holidayLeftTime = holidayBegin - beginTime;
                if (order.getPrepareTime() + order.getShelveTime() < holidayLeftTime) {
                    holidayLeftTime = 0L;
                } else {
                    holidayLeftTime = holidayEnd - holidayBegin;
                }

            } else if (beginTime < holidayEnd) {
                beginTime = holidayEnd;
            }
        }
        return beginTime + order.getPrepareTime() + order.getShelveTime() + holidayLeftTime;
    }

    /**
     * 详单设计完成状态 "" 无意义  | NO 未完成 | ALL 全部完成
     */
    private static String getItemsDesignStatus(String designStatus, List<MerchantOrderRespDto.OrderItem> items) {
        if (designStatus == null) {
            return "";
        }
        if (designStatus.equals(OrderDesignStatus.FINISH.name())) {
            return "ALL";
        }
        if (CollectionUtils.isEmpty(items)) {
            return "NO";
        }
        for (MerchantOrderRespDto.OrderItem item : items) {
            if (item.getStatus() != null && item.getStatus() == OrderStatus.PAIN.getStatus() && item.getEndProductId().equals(0L)) {
                return "NO";
            }
        }
        return "ALL";
    }

    private static MerchantOrderRespDto.PickupInfo getPickupInfo(MerchantOrderRespDto merchantOrderRespDto, CommonOrderRespDto commonOrderRespDto) {
        MerchantOrderRespDto.PickupInfo pickupInfo = new MerchantOrderRespDto.PickupInfo();
        pickupInfo.setBusinessHours("周一至周六 09:00-18:00");
        MerchantOrderRespDto.IssuingBay issuingBay = merchantOrderRespDto.getIssuingBay();
        if (issuingBay != null) {
            pickupInfo.setDetailAddress(StringCaseUtil.join(" ", Arrays.asList(issuingBay.getProvince(), issuingBay.getCity(), issuingBay.getDetail())));
            pickupInfo.setPoint(issuingBay.getName());
        }
        if (merchantOrderRespDto.getOrderFba() != null) {
            pickupInfo.setPickupCode(merchantOrderRespDto.getOrderFba().getPickupCode());
        }
        if (DeliveryTypeEnum.ZT == commonOrderRespDto.getDeliveryType()) {
            boolean allShipped = commonOrderRespDto.getParcelList().size() > 0 &&
                    commonOrderRespDto.getParcelList().stream().allMatch(i -> OrderParcelStatusEnum.SHIPPED.equalsCode(i.getOrderParcelDTO().getStatus()));
            pickupInfo.setAllShipped(BasePoConstant.yesOrNo(allShipped));
        }
        return pickupInfo;
    }


    public static MerchantOrderRespDto.OrderInterceptRecord getOrderInterceptRecord(OrderInterceptRecordRespDto orderInterceptRecordRespDto) {
        if (orderInterceptRecordRespDto == null) {
            return null;
        }
        MerchantOrderRespDto.OrderInterceptRecord orderInterceptRecord = new MerchantOrderRespDto.OrderInterceptRecord();
        orderInterceptRecord.setId(orderInterceptRecordRespDto.getId());
        orderInterceptRecord.setStatus(orderInterceptRecordRespDto.getStatus());
        return orderInterceptRecord;
    }

    public static void formatMerchantOrderRespDtoPlatformOrderExtend(MerchantOrderRespDto merchantOrderRespDto, CommonOrderRespDto commonOrderRespDto) {
        List<PlatformOrderExtendDto> extendList = commonOrderRespDto.getPlatformOrderExtendList();
        if (CollUtil.isEmpty(extendList)) {
            merchantOrderRespDto.setPlatformOrderExtendMap(Collections.emptyMap());
            return;
        }
        MapListUtil<String, String> mapListUtil = MapListUtil.instance();
        extendList.forEach(i -> {
            List<String> values = PlatformOrderExtendValueEnum.valueStr2List(i.getExtendValue());
            mapListUtil.addDistinctList(i.getExtendKey(), values);
        });
        merchantOrderRespDto.setPlatformOrderExtendMap(mapListUtil.getMaps());
    }
}
