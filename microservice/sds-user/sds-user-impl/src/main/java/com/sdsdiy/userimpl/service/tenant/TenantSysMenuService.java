package com.sdsdiy.userimpl.service.tenant;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.userapi.constant.TenantConstant;
import com.sdsdiy.userapi.dto.tenant.TenantSysMenuDto;
import com.sdsdiy.userimpl.entity.po.tenant.TenantSysMenu;
import com.sdsdiy.userimpl.entity.po.tenant.TenantSysRole;
import com.sdsdiy.userimpl.entity.po.tenant.TenantSysRoleMenu;
import com.sdsdiy.userimpl.mapper.tenant.TenantSysMenuMapper;
import io.seata.common.util.CollectionUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sdsdiy.userapi.constant.TenantConstant.MESSAGE_PUSH;


/**
 * (TenantSysDept)表业务接口
 *
 * <AUTHOR>
 * @since 2021-09-27 10:39:22
 */
@DS("common")
@Service
@Log4j2
public class TenantSysMenuService extends ServiceImpl<TenantSysMenuMapper, TenantSysMenu> {

    @Resource
    TenantSysRoleService tenantSysRoleService;
    @Resource
    TenantSysRoleMenuService tenantSysRoleMenuService;

    public List<TenantSysMenu> findAllList(List<Long> appIds, Long tenantId) {
        boolean isSdsdiy = TenantCommonConstant.isSdsdiy(tenantId);

//        List<Long> notInId = new ArrayList<>();
//        notInId.add(TenantConstant.DISTRIBUTE_FBA_ORDER);
//        notInId.add(TenantConstant.ONLINE_PAYMENT_MODULE);

        return lambdaQuery()
                .eq(TenantSysMenu::getDelFlag, TenantConstant.IS_NO)
                .in(TenantSysMenu::getAppId, appIds)
//                .notIn(Boolean.TRUE.equals(isSdsdiy), TenantSysMenu::getId, notInId)
                .notIn(!isSdsdiy, TenantSysMenu::getId, Lists.newArrayList(MESSAGE_PUSH))
                .orderByAsc(TenantSysMenu::getSort).list();
    }

    public List<TenantSysMenu> findByUserId(long userId, List<Long> appIds, String delFlag, Long tenantId) {
        return this.baseMapper.findByUserId(userId, appIds, delFlag, tenantId);
    }

    public List<TenantSysMenu> findByParentIdsLike(String delFlag, String parentIds) {
        LambdaQueryWrapper<TenantSysMenu> wrapper = Wrappers.<TenantSysMenu>lambdaQuery();
        wrapper.eq(TenantSysMenu::getDelFlag, delFlag)
                .like(TenantSysMenu::getParentIds, parentIds)
                .orderByAsc(TenantSysMenu::getSort);

        return this.list(wrapper);
    }

    public List<TenantSysMenu> findByIds(List<Long> ids) {
        LambdaQueryWrapper<TenantSysMenu> wrapper = Wrappers.<TenantSysMenu>lambdaQuery();
        wrapper.in(TenantSysMenu::getId, ids)
                .eq(TenantSysMenu::getDelFlag, TenantConstant.IS_NO).
                orderByAsc(TenantSysMenu::getSort);

        return this.list(wrapper);
    }

    public void updateParentIds(long id, long parentId, String parentIds) {
        LambdaUpdateWrapper<TenantSysMenu> wrapper = Wrappers.<TenantSysMenu>lambdaUpdate();
        wrapper.eq(TenantSysMenu::getId, id)
                .set(TenantSysMenu::getParentId, parentId)
                .set(TenantSysMenu::getParentIds, parentIds);
        this.update(wrapper);
    }


    public void delRoleMenuRetation(Long tenantId, List<Long> appIds) {
        List<Long> noChildMenuIds = findNoChildParentMenu(appIds);

        List<TenantSysRole> roles = tenantSysRoleService.findAllList(tenantId, TenantConstant.IS_NO);
        List<Long> roleIds = roles.stream().map(TenantSysRole::getId).collect(Collectors.toList());
        List<TenantSysRoleMenu> relations = Lists.newArrayList();
        roleIds.forEach(roleId -> {
            noChildMenuIds.forEach(menuId -> {
                TenantSysRoleMenu relation = new TenantSysRoleMenu();
                relation.setMenuId(menuId).setRoleId(roleId);
                relations.add(relation);
            });
        });

        if (CollectionUtil.isNotEmpty(relations)) {
            tenantSysRoleMenuService.getBaseMapper().delBatchRelations(relations);
        }


    }

    /**
     * 根据appIds 获取父级菜单项
     *
     * @param appIds
     */
    public List<Long> findNoChildParentMenu(List<Long> appIds) {
        // 用appid 查看对应所有的功能项的menu 查看 tenant
        // 全项 和 可用项进行对比
        List<TenantSysMenu> menus = lambdaQuery().
                eq(TenantSysMenu::getDelFlag, TenantConstant.IS_NO).
                in(TenantSysMenu::getAppId, appIds).list();

        // 获取无子项的一级节点
        List<TenantSysMenuDto> allMenusTree = getNoChild(menus);
        List<Long> menuIds = allMenusTree.stream().map(TenantSysMenuDto::getId).collect(Collectors.toList());
        return menuIds;
    }


    public List<TenantSysMenuDto> getNoChild(List<TenantSysMenu> menus) {
        List<TenantSysMenuDto> menuTreeDtos = RelationsBinder.convertAndBind(menus, TenantSysMenuDto.class);
        Map<Long, List<TenantSysMenuDto>> menuMap = menuTreeDtos.stream().collect(Collectors.groupingBy(TenantSysMenuDto::getParentId));
        //一级菜单
        List<TenantSysMenuDto> parentMenus = menuMap.get(TenantConstant.MENU_DEFAULT_PARENT_ID);
        packageMenuTree(parentMenus, menuMap);

        // 排除掉无子节点的 一级节点
        List<TenantSysMenuDto> results = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(parentMenus)) {
            parentMenus.forEach(item -> {
                if (CollectionUtils.isEmpty(item.getSubMenus()) && TenantConstant.MENU_TYPE_PERMIT.equals(item.getMenuType())) {
                    results.add(item);
                }
            });
        }

        return results;
    }


    public List<TenantSysMenuDto> getAllMenTree(List<TenantSysMenu> menus) {
        List<TenantSysMenuDto> menuTreeDtos = RelationsBinder.convertAndBind(menus, TenantSysMenuDto.class);
        Map<Long, List<TenantSysMenuDto>> menuMap = menuTreeDtos.stream().collect(Collectors.groupingBy(TenantSysMenuDto::getParentId));
        //一级菜单
        List<TenantSysMenuDto> parentMenus = menuMap.get(TenantConstant.MENU_DEFAULT_PARENT_ID);
        packageMenuTree(parentMenus, menuMap);

        // 排除掉无子节点的 一级节点
        List<TenantSysMenuDto> results = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(parentMenus)) {
            parentMenus.forEach(item -> {
                if (CollectionUtils.isNotEmpty(item.getSubMenus()) || TenantConstant.MENU_TYPE_MENU.equals(item.getMenuType())) {
                    results.add(item);
                }
            });
        }

        return results;
    }

    private void packageMenuTree(List<TenantSysMenuDto> parentMenus, Map<Long, List<TenantSysMenuDto>> menuMap) {
        List<TenantSysMenuDto> nextParentMenus = new ArrayList<>();
        if (CollectionUtil.isEmpty(parentMenus)) {
            return;
        }
        parentMenus.forEach(p -> {
            if (menuMap.containsKey(p.getId())) {
                p.setSubMenus(menuMap.get(p.getId()));
                nextParentMenus.addAll(menuMap.get(p.getId()));
            }
        });
        packageMenuTree(nextParentMenus, menuMap);
    }

}