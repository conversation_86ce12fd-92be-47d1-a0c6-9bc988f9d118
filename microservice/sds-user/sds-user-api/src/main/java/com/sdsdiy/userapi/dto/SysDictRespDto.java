package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.base.entity.dto.BaseDTO;
import lombok.Data;

import java.util.Date;

/**
 * (SysDictRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-10-07 10:25:13
 */
@Data
public class SysDictRespDto extends BaseDTO {

    private String value;
    private String label;
    private String type;
    private String description;
    private Double sort;
    private String parentId;
    private String createBy;
    private Date createDate;
    private String updateBy;
    private Date updateDate;
    private String remarks;
    private String delFlag;
    private String notLabel;

}