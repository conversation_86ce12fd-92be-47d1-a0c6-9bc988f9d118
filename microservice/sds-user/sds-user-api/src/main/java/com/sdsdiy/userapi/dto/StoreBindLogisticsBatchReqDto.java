package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: bin_lin
 * @date: 2020/11/16 13:57
 * @desc:
 */
@NoArgsConstructor
@Data
public class StoreBindLogisticsBatchReqDto {
    private Long logisticId;
    private Long expressLogisticId;
    @NotEmpty(message = "商铺id列表不能为空")
    private List<Long> merchantStoreList;

    private Long logisticChannelId;
    private Long expressLogisticChannelId;
    @ApiModelProperty("半托管物流渠道")
    private Long popChoiceLogisticsChannelId;
}
