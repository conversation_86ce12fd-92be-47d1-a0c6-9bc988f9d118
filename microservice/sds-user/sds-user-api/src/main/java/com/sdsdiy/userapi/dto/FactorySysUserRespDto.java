package com.sdsdiy.userapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import com.sdsdiy.userdata.dto.factory.FactorySysUserOtherTokenDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户表(FactorySysUserRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-07-10 10:44:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactorySysUserRespDto {
    /**
     * 编号
     */
    @DtoDefault
    private Long id;
    /**
     * 归属部门
     */
    @DtoDefault
    private String officeId;
    /**
     * 姓名
     */
    @DtoDefault
    private String username;
    /**
     * 密码
     */
    @DtoDefault
    private String password;
    /**
     * 密码盐
     */
    @DtoDefault
    private String salt;
    /**
     * 工号
     */
    @DtoDefault
    private String no;
    /**
     * 邮箱
     */
    @DtoDefault
    private String email;
    /**
     * 电话
     */
    private String phone;
    /**
     * 用户类型
     */
    private String userType;
    /**
     * 创建时间
     */
    @DtoDefault
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 备注信息
     */
    private String remarks;
    /**
     * 删除标记
     */
    private String delFlag;

    private String osUsername;

    private Integer osUserId;
    /**
     * 工厂id
     */
    @DtoDefault
    private Long factoryId;

    private FactorySysUserOtherTokenDTO otherPlatformToken;

}