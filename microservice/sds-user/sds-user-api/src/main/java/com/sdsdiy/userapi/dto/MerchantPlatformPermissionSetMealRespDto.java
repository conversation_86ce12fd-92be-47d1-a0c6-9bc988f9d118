package com.sdsdiy.userapi.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.sdsdiy.common.dtoconvert.annotation.DtoBind;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
@Data
public class MerchantPlatformPermissionSetMealRespDto {

    @DtoDefault()
    private Long id;

    private Long platformGoodsOrderId;

    @DtoDefault()
    private Long platformPermissionSetMealId;

    @DtoDefault()
    private Long merchantId;
    @DtoDefault()
    private String setMealName;

    /**
     * 标识码
     */
    @DtoDefault()
    private String code;

    /**
     * 商品类型 1 权限套餐 2 权限增值服务
     */
    @DtoDefault()
    private Integer setMealType;

    /**
     * 商品类型(细)
     */
    @DtoDefault()
    private Integer levelType;

    /**
     * 记录状态 0 未启用 1 使用中 2 失效(type 1 次数用完 2 过期)
     */
    @DtoDefault()
    private Integer status;

    /**
     * 记录类型 0 免费套餐 1 商户套餐 2 商户增值项 3 平台赠送
     */
    private Integer sourceType;

    /**
     * 权限有效周期(天)
     */
    private Integer period;

    /**
     * 权限周期单(显示用)
     */
    private String periodUnits;

    /**
     * 权限有效开始时间
     */
    @DtoDefault()
    private Long startTime;

    /**
     * 权限有效结束时间
     */
    @DtoDefault()
    private Long endTime;

    @DtoDefault()
    private String startTimeStr;

    @DtoDefault()
    private String endTimeStr;

    @DtoDefault()
    private BigDecimal level;

    @DtoDefault()
    private Integer isDelete;

    @JsonIgnore
    @DtoBind(selfField = "id", relateField = "merchantPlatformPermissionSetMealId", condition = "is_delete=0", provider = "com.sdsdiy.userimpl.relationprovider.MerchantPlatformPermissionProvider")
    private List<MerchantPlatformPermissionDto> items = Lists.newArrayList();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPlatformGoodsOrderId() {
        return platformGoodsOrderId;
    }

    public void setPlatformGoodsOrderId(Long platformGoodsOrderId) {
        this.platformGoodsOrderId = platformGoodsOrderId;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public String getSetMealName() {
        return setMealName;
    }

    public void setSetMealName(String setMealName) {
        this.setMealName = setMealName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public Integer getSetMealType() {
        return setMealType;
    }

    public void setSetMealType(Integer setMealType) {
        this.setMealType = setMealType;
    }

    public Integer getLevelType() {
        return levelType;
    }

    public void setLevelType(Integer levelType) {
        this.levelType = levelType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getPeriodUnits() {
        return periodUnits;
    }

    public void setPeriodUnits(String periodUnits) {
        this.periodUnits = periodUnits == null ? null : periodUnits.trim();
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public String getStartTimeStr() {
        if (startTime != null && startTime > 0) {
            return DateUtil.format(DateUtil.date(startTime), DatePattern.NORM_DATETIME_PATTERN);
        }
        return "";
    }

    public String getEndTimeStr() {
        if (endTime != null && endTime > 0) {
            return DateUtil.format(DateUtil.date(endTime), DatePattern.NORM_DATETIME_PATTERN);
        }
        return "";
    }

    public List<MerchantPlatformPermissionDto> getItems() {
        return items;
    }

    public void setItems(List<MerchantPlatformPermissionDto> items) {
        this.items = items;
    }

    public void setStartTimeStr(String startTimeStr) {
        this.startTimeStr = startTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public BigDecimal getLevel() {
        return level;
    }

    public void setLevel(BigDecimal level) {
        this.level = level;
    }

    public Long getPlatformPermissionSetMealId() {
        return platformPermissionSetMealId;
    }

    public void setPlatformPermissionSetMealId(Long platformPermissionSetMealId) {
        this.platformPermissionSetMealId = platformPermissionSetMealId;
    }
}