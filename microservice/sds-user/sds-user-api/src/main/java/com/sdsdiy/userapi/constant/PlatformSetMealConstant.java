package com.sdsdiy.userapi.constant;

import com.sdsdiy.userdata.enums.MemberLevelEnum;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: zmy
 * @Date: 2020/9/28 11:44
 * @Description:
 */
public class PlatformSetMealConstant {

    /**
     * 产品数量权限
     */
    public static String PERMISSION_ITEM_PRODUCT_NUMBER = "product_number";

    /**
     * 会员类型，1：会员特权，2：增值服务,3:租户增值服务
     */
    public static Integer SET_MEAL_TYPE_MEMBER = 1;
    public static Integer SET_MEAL_TYPE_ADD_SERVICE = 2;
    public static Integer SET_MEAL_TYPE_TENANT_ADD_SERVICE = 3;

    /**
     * 年的天数
     */
    public static Integer SET_MEAL_MEMBER_PERIOD = 365;

    /**
     * 套餐或增值服务购买权限，1：所有人，2：付费会员
     **/
    public static final int ALL_USER = 1;
    public static final int FEE_USER = 2;

    /**
     * levelType会员套餐类型，0：免费，1：付费
     **/
    public static final int SET_MEAL_FREE = 0;
    public static final int SET_MEAL_FEE = 1;

    /**
     * 会员状态，0：未启用，1:使用中，2：失效
     */
    public static Integer MERCHANT_SET_MEAL_STATUS_NOT_ENABLE = 0;
    public static Integer MERCHANT_SET_MEAL_STATUS_ACTIVE = 1;
    public static Integer MERCHANT_SET_MEAL_STATUS_INVALID = 2;

    public static String FREE_SET_MEAL = "free_set_meal";

    public static final long KB = 1024;

    public static final long MB = 1024 * 1024;

    public static final long GB = 1024 * 1024 * 1024;

    public static final int NORMAL = 1;

    public static final int BLOCKIN = 4;

    /**
     * 套餐生成方式 1 续费 2 升级 3 授权
     */
    public static final int OPTION_TYPE_RENEW = 1;
    public static final int PERMISSION_SET_MEAL_UPGRADE = 2;
    public static final int PERMISSION_SET_MEAL_AUTHORIZE = 3;

    /**
     * 记录类型 0 免费套餐 1 商户套餐 2 商户增值项 3 平台赠送 4 平台授权会员
     */
    public static final int SOURCE_TYPE_FREE = 0;
    public static final int SOURCE_TYPE_USER_SET_MEAL = 1;
    public static final int SOURCE_TYPE_USER_VALUE_ADDED = 2;
    public static final int SOURCE_TYPE_PLATFORM_GIVE_AWAY = 3;
    public static final int SOURCE_TYPE_PLATFORM_AUTHORIZE = 4;

    //权限类型 1 消耗 2 计量 3 授权 4 复杂
    public static final int TYPE_TIME = 1;
    public static final int TYPE_CAPACITY = 2;
    public static final int TYPE_AUTHORIZATION = 3;
    public static final int TYPE_COMPLEX = 4;

    /**
     * 要统计的权限类型
     */
    public static final List<Integer> STAT_PERMISSION_TYPE_LIST = Arrays.asList(TYPE_TIME, TYPE_CAPACITY);

    /**
     * 消费者-权限额度
     */
    public static final String GID_CONSUMER_STAT_PERMISSION = "GID_CONSUMER_STAT_PERMISSION";
    /**
     * 消费者-用户消费
     */
    public static final String GID_CONSUMER_USER_OPERATION_HISTORY = "GID_CONSUMER_USER_OPERATION_HISTORY";
    /**
     * redis 权限额度 消费者MQ
     */
    public static final String CACHE_SDS_STAT_PERMISSION_MQ = "SDS_STAT_PERMISSION_OPT:%s";
    /**
     * 1分钟
     */
    public static final long CACHE_EXPIRE_1_MINUTE = 60;

    public static boolean isFreeLevel(BigDecimal level) {
        return null != level && MemberLevelEnum.V0.eq(level);
    }

    public static boolean isStartLevel(BigDecimal level) {
        return null != level && MemberLevelEnum.V1.eq(level);
    }

    public static boolean isExclusiveLevel(BigDecimal level) {
        return null != level && MemberLevelEnum.Vd.eq(level);
    }

    public static boolean isProfessionalLevel(BigDecimal level) {
        return null != level && MemberLevelEnum.V2.eq(level);
    }

    public static boolean isEnterPriseLevel(BigDecimal level) {
        return null != level && MemberLevelEnum.V3.eq(level);
    }

}
