package com.sdsdiy.userapi.constant.enums;

/**
 * <AUTHOR>
 * @date 2021/7/23
 */
public enum CrmUserRoleEnum {
    //
    ADMIN("超级管理员", "admin"),
    MANAGER("主管", "manager"),
    NORMAL("业务员", "normal"),
    ;

    public final String name;
    public final String role;

    CrmUserRoleEnum(String name, String role) {
        this.name = name;
        this.role = role;
    }

    public static boolean isAdmin(String role) {
        return ADMIN.role.equalsIgnoreCase(role);
    }

    public static boolean isSuperior(String role) {
        return MANAGER.role.equalsIgnoreCase(role);
    }
}
