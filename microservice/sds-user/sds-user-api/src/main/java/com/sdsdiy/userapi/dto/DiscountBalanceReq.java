package com.sdsdiy.userapi.dto;

/**
 * @author: bin_lin
 * @date: 2021/7/16 17:42
 * @desc:
 */

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class DiscountBalanceReq {
    @NotNull
    private Long userId;
    @NotNull
    private Long merchantId;
    private BigDecimal discountBalance;
    private BigDecimal discountFreeGold;
    private String balanceType;
}
