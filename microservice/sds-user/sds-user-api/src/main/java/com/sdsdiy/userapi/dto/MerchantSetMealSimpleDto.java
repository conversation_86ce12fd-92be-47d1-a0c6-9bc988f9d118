package com.sdsdiy.userapi.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.Data;
@Data
public class MerchantSetMealSimpleDto {

    private Long id;
    private String setMealName;
    private String code;
    /**
     * 权限有效开始时间
     */
    private Long startTime;

    /**
     * 权限有效结束时间
     */
    private Long endTime;

    private String startTimeStr;

    private String endTimeStr;


    public String getStartTimeStr() {
        if (startTime != null && startTime > 0) {
            return DateUtil.format(DateUtil.date(startTime), DatePattern.NORM_DATETIME_PATTERN);
        }
        return "";
    }

    public String getEndTimeStr() {
        if (endTime != null && endTime > 0) {
            return DateUtil.format(DateUtil.date(endTime), DatePattern.NORM_DATETIME_PATTERN);
        }
        return "";
    }
}