package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: zmy
 * @Date: 2020/9/27 17:27
 * @Description: 授权会员body
 */
@Data
public class AuthorizeSetMealReqDto {

    @NotNull(message = "授权会员不能为空")
    private String code;
    @NotNull(message = "会员周期开始时间不能为空")
    private Date startTime;
    @NotNull(message = "会员周期结束时间不能为空")
    private Date endTime;
    @NotNull(message = "补差价不能为空")
    private BigDecimal priceDifferent;
    private String remark;
    @NotNull(message = "支付密码不能为空")
    private String password;

    /**是否免密码*/
    private Boolean noNeedPassword;

    private Long platformGoodsId;
    private Long platformPermissionSetMealId;
    private String paymentMethod;
    /**
     * 操作人id
     */
    private Long operator;
    @ApiModelProperty("支付账户类型 saas: 商户saas账户 tenant:租户账户")
    private String paymentAccountType;
    @ApiModelProperty("租户id")
    private Long tenantId;

}
