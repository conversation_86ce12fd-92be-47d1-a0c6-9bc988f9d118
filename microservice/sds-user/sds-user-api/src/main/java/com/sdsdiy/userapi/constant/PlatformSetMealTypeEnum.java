package com.sdsdiy.userapi.constant;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 套餐枚举类
 * @Date 20:40 2020/9/29
 */
public enum PlatformSetMealTypeEnum {
    //
    FREE_SET_MEAL("free_set_meal", 1,0, "免费版(V0)"),
    START_SET_MEAL("start_set_meal",1,1, "创业版(V1)"),
    EXCLUSIVE_SET_MEAL("exclusive_set_meal",1,1, "独立站尊享版(Vd)"),
    PROFESSIONAL_SET_MEAL("professional_set_meal",1,1, "专业版(V2)"),
    ENTERPRISE_SET_MEAL("enterprise_set_meal",1,1, "企业版(V3)"),
    INCREASE_FREE("value_added_services",2, 0, "增值套餐"),
    INCREASE_FEE("value_added_services",2, 1, "增值套餐"),
    TENANT_VALUE_ADDED_SERVICES("tenant_value_added_services",3, 1, "租户增值服务");

    private String mealCode;

    /**
     * 1 特权套餐 2 增值服务
     * {@link PlatformSetMealConstant}
     */
    private Integer setMealType;

    /**会员特权：0 免费，1 付费
     * 增值服务：0 都可购买，1 付费会员才可购买
     * {@link PlatformSetMealConstant}
     * */
    private Integer levelType;

    private String mealName;

    PlatformSetMealTypeEnum(String mealCode, Integer setMealType, Integer levelType, String mealName) {
        this.mealCode = mealCode;
        this.setMealType = setMealType;
        this.levelType = levelType;
        this.mealName = mealName;
    }

    public static String getMealNameByMealCode(String code){
        PlatformSetMealTypeEnum[] values = PlatformSetMealTypeEnum.values();
        for(PlatformSetMealTypeEnum v:values){
            if(v.getMealCode().equals(code)){
                return v.getMealName();
            }
        }
        return "";
    }

    public static String authProductPrice(String currentMealCode,String targetMealCode){
        List<String> v2Orv3Codes= Lists.newArrayList(PlatformSetMealTypeEnum.PROFESSIONAL_SET_MEAL.mealCode,PlatformSetMealTypeEnum.ENTERPRISE_SET_MEAL.mealCode);
        if(!v2Orv3Codes.contains(currentMealCode)&&v2Orv3Codes.contains(targetMealCode)){
            return AuthProductPriceConstant.AUTH;
        }
        if(v2Orv3Codes.contains(currentMealCode)&&!v2Orv3Codes.contains(targetMealCode)){
            return AuthProductPriceConstant.CANCEL_AUTH;
        }
        return AuthProductPriceConstant.NONE;

    }

    /**
     * 包含旧的月套餐 年套餐
     * @return
     */
    public static List<String> membershipCodeList(){
        return Lists.newArrayList(
                START_SET_MEAL.mealCode,
                EXCLUSIVE_SET_MEAL.mealCode,
                PROFESSIONAL_SET_MEAL.mealCode,
                ENTERPRISE_SET_MEAL.mealCode,
                "month_set_meal",
                "year_set_meal"
        );
    }

    public String getMealCode() {
        return mealCode;
    }

    public void setMealCode(String mealCode) {
        this.mealCode = mealCode;
    }

    public Integer getSetMealType() {
        return setMealType;
    }

    public static Integer setMealTypeOfIncrease(){
        return INCREASE_FREE.getSetMealType();
    }

    public void setSetMealType(Integer setMealType) {
        this.setMealType = setMealType;
    }

    public Integer getLevelType() {
        return levelType;
    }

    public void setLevelType(Integer levelType) {
        this.levelType = levelType;
    }

    public String getMealName() {
        return mealName;
    }

    public void setMealName(String mealName) {
        this.mealName = mealName;
    }
}
