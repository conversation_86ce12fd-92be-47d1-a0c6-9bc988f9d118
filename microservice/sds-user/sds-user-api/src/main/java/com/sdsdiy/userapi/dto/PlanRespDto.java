package com.sdsdiy.userapi.dto;

import lombok.Data;

import java.util.Date;

@Data
public class PlanRespDto {

    /**mealName*/
    private String name;
    /**merchantPlatformPermissionId*/
    private Long id;
    /**总的数量 比如1*/
    private Long total;
    /**比如1048576 有*/
    private Long totalValue;
    private Date startTime;
    private Date endTime;
    /**单位，比如G*/
    private String unit;
    private String periodUnits;

    private Integer status;
    /**已使用 无单位的，比如122530*/
    private Double count;
    /**有单位的已使用，比如119.66MB*/
    private String countValue;
}

