package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: bin_lin
 * @date: 2021/6/23 16:24
 * @desc:
 */
@Data
public class UserAccountBalanceAllocationParam {
    @NotNull
    @ApiModelProperty(value = "id")
    private Long id;
    @NotBlank
    @ApiModelProperty(value = "分配类型 inCommon共用 allocation分配")
    private String balanceType;
    @ApiModelProperty(value = "余额")
    private BigDecimal balance;
    @ApiModelProperty(value = "赠送金")
    private BigDecimal freeGold;
}
