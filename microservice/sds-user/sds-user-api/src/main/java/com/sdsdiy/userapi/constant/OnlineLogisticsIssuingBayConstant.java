package com.sdsdiy.userapi.constant;

import lombok.Getter;

/**
 * @author: zmy
 * @desc:
 */
public interface OnlineLogisticsIssuingBayConstant {

    /**
     * temu类型，半托管：temu_semi，全托管：temu_fully
     */
    @Getter
    enum TemuTypeEnum {
        TEMU_SEMI("temu_semi", "半托管"),
        TEMU_FULLY("temu_fully", "全托管"),
        ;

        private final String code;
        private final String desc;

        TemuTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }


}
