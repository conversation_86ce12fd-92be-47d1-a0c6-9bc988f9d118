package com.sdsdiy.userapi.dto.storeSettleIn.supplierProfitSplitRatioConfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/22
 */
@ApiModel(description = "供应商分账比例配置响应实体类")
@Data
@NoArgsConstructor()
public class SupplierProfitSplitRatioConfigRespDto {
    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "供应商名称")
    private String name;
    @ApiModelProperty(value = "供应商类型")
    private String type;
    @ApiModelProperty(value = "公司类型",notes = "individual-个人 company-企业")
    private String companyType;
    @ApiModelProperty(value = "是否主商户",notes = "0：否 1：是")
    private Integer mainFlag;
    @ApiModelProperty(value = "供应商分账比例配置")
    private SupplierProfitSplitRatioConfigDto ratioConfig;
   
}
