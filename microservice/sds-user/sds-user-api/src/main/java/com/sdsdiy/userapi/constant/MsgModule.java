package com.sdsdiy.userapi.constant;

/**
 * <AUTHOR>
 * @date 2021/6/28
 */
public enum MsgModule {
    MERCHANT_PROFILE("merchant_profile", "商户权限等相关信息更新"),
    SEND_NOTIFICATION("send_notification", "用户消息更新"),
    PRODUCT_FINISH("product_finish","成品完成"),
    PUBLIC_FAIL("public_fail","发布失败"),
    PUBLIC_SUCCESS("public_success","发布成功"),
    FINANCIAL_REMITTANCE("financial_remittance","财务汇款"),
    USER_PERMISSION("user_permission","用户权限"),
    ORDER_DETAIL_FAIL("order_detail_fail","订单明细导出"),
    ;


    private String module;
    private String remark;

    MsgModule(String module, String remark) {
        this.module = module;
        this.remark = remark;
    }
    public String getModule() {
        return module;
    }
}
