package com.sdsdiy.userapi.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 产品库子账号权限表(SysUserProductPermissionReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2021-03-02 10:42:51
 */
@Data
public class MerchantSysUserProductPermissionReqDto implements Serializable {

    private Long id;

    private Long merchantSysUserId;

    private Long merchantId;

    private String productLibraryType;

    private Date createTime;

    private Long createUid;

    private Date updateTime;

    private Long updateUid;

    private Integer isDelete;

}