package com.sdsdiy.userapi.dto;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import com.sdsdiy.userapi.dto.base.MerchantSysOfficeReqDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/23
 */
@Data
public class MerchantSysUserDto {
    private static final long serialVersionUID = -1592423094819265354L;


    @DtoDefault
    private Long id;

    private MerchantSysOfficeReqDto office;    // 归属部门
    @DtoDefault
    private String username;// 登录名
    @DtoDefault
    private String password;// 密码
    @JsonProperty("repassword")
    private String rePassword; //确认新密码
    @DtoDefault
    private String no;        // 工号
    @DtoDefault
    private String email;    // 邮箱
    @DtoDefault
    private String phone;    // 电话
    @DtoDefault
    private String userType;// 用户类型
    private String loginFlag;    // 是否允许登陆
    @DtoDefault
    private String salt;
    @JsonProperty("office_id")
    @DtoDefault
    private Long officeId;
    @DtoDefault
    private Long merchantId;
    @JsonProperty("merchant_name")
    private String merchantName;
    private MerchantSysRoleDto role;    // 根据角色查询用户条件
    private String osUsername;
    private Long osUserId;
    private String code;
    @JsonProperty("factory_code")
    private String factoryCode;
    @JsonProperty("contact_tel")
    private String contactTel;
    private Long factoryId;
    private Integer status;
    private Boolean isPermission;
    /***
     * <AUTHOR> v3.9.7 国家码
     */
    @DtoDefault
    private String phoneCountryCode;
    @DtoDefault
    private String thirdPartyUsername;
    @DtoDefault
    private Date createDate;
    @DtoDefault
    private Date updateDate;
    @DtoDefault
    private String delFlag;
    private List<MerchantSysRoleDto> roleList = Lists.newArrayList();

    public List<Long> getRoleIdList() {
        List<Long> roleIdList = Lists.newArrayList();
        for (MerchantSysRoleDto role : roleList) {
            roleIdList.add(role.getId());
        }
        return roleIdList;
    }
    public void setRoleIdList(List<Long> roleIdList) {
        roleList = Lists.newArrayList();
        for (Long roleId : roleIdList) {
            MerchantSysRoleDto role = new MerchantSysRoleDto();
            role.setId(roleId);
            roleList.add(role);
        }
    }

    public boolean isBoss() {
        boolean flag = false;
        List<MerchantSysRoleDto> roleList = this.getRoleList();
        if(CollUtil.isNotEmpty(roleList)){
            for (MerchantSysRoleDto r : this.getRoleList()) {
                if (r.isBoss()) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }
}
