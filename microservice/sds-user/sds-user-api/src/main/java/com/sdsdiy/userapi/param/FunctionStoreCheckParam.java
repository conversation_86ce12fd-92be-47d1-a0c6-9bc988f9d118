package com.sdsdiy.userapi.param;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class FunctionStoreCheckParam {
    @ApiModelProperty("创建用户id")
    @NotNull
    private Long userId;
    @ApiModelProperty("平台code")
    @NotBlank
    private String merchantStorePlatformCode;
    @ApiModelProperty("次数")
    @NotNull
    private Long time = 1L;

}
