package com.sdsdiy.userapi.constant;


import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.userapi.constant.enums.NotificationChannelEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public enum EnumNotificationTitle {

    PLATFORM_RECHARGE("平台充值", 9, 1, "", NotificationChannelEnum.ACCOUNT_BALANCE, "余额充值", "已成功"),
    WITHDRAW_REFUSE("您有一笔提现被驳回", 1, 3, "", NotificationChannelEnum.ACCOUNT_BALANCE, "余额提现", "已失败"),
    WITHDRAW_SUCCESS("您有一笔提现已汇出", 2, 3, "", NotificationChannelEnum.ACCOUNT_BALANCE, "余额提现", "已成功"),

    OPEN_MONTH_SET_MEAL("开通月会员", 3, 3, "", NotificationChannelEnum.ACCOUNT_MEMBERSHIP_PACKAGE, "月费会员开通", "已成功"),
    OPEN_YEAR_SET_MEAL("开通年会员", 3, 3, "", NotificationChannelEnum.ACCOUNT_MEMBERSHIP_PACKAGE, "年费会员开通", "已成功"),
    OPEN_ADD_VALUE_SET_MEAL("功能额度已增加", 4, 3, "", NotificationChannelEnum.ACCOUNT_MEMBERSHIP_PACKAGE, "功能额度变更", "已增加"),
    CHANGE_SET_MEAL("会员套餐变更", 5, 3, "", NotificationChannelEnum.ACCOUNT_MEMBERSHIP_PACKAGE, "会员套餐变更", "已变更"),
    SET_MEAL_ONLY_A_MONTH("会员套餐即将到期", 6, 3, "", NotificationChannelEnum.ACCOUNT_MEMBERSHIP_PACKAGE, "会员套餐到期", "即将到期"),
    SET_MEAL_ONLY_24_HOUR("会员套餐即将到期", 7, 3, "", NotificationChannelEnum.ACCOUNT_MEMBERSHIP_PACKAGE, "会员套餐到期", "即将到期"),
    SET_MEAL_ONLY_7_DAY("会员套餐即将到期", 8, 3, "", NotificationChannelEnum.ACCOUNT_MEMBERSHIP_PACKAGE, "会员套餐到期", "即将到期"),

    MERCHANT_ACCOUNT_FUNCTION_QUOTA_INIT_SUCCESS("子账号额度自动分配成功", 39, 3, "子账号：%s，初始功能额度已自动分配成功", NotificationChannelEnum.ACCOUNT_INFORMATION, "子账号额度自动分配", "已成功"),
    MERCHANT_ACCOUNT_FUNCTION_QUOTA_INIT_FAIL("子账号额度自动分配失败", 40, 3, "子账号：%s，初始功能额度因主商户额度不足，分配失败，请自行手动分配额度。", NotificationChannelEnum.ACCOUNT_INFORMATION, "子账号额度自动分配", "已失败"),
    DOMAIN_HANDEL_SUCCESS("域名处理结果通知", 10, 4, "", NotificationChannelEnum.ACCOUNT_INFORMATION, "域名处理结果", "已成功"),
    DOMAIN_HANDEL_FAIL("域名处理结果通知", 11, 4, "", NotificationChannelEnum.ACCOUNT_INFORMATION, "域名处理结果", "已失败"),
    DOMAIN_HANDEL_DELETE("删除域名通知", 12, 4, "", NotificationChannelEnum.ACCOUNT_INFORMATION, "删除域名", "已成功"),


    ADMIN_PREPAID_OFFLINE_PAY_ALI_PAY("寄付运费支付通知", 43, 3, "线下已使用支付宝支付一批垫付订单，扣款批次:%s ,总金额:%s元", NotificationChannelEnum.BILL_PAYMENT_CONFIRMATION, "寄付运费支付", "已支付"),
    OFFLINE_PAY_BALANCE_DEBIT("线下付款代扣通知", 44, 3, "平台已代扣%s，扣款金额:%s元 详情:%s ", NotificationChannelEnum.BILL_PAYMENT_CONFIRMATION, "线下付款平台代扣", "已支付"),
    OFFLINE_PAY_ALI_PAY("线下付款支付通知", 45, 3, "线下已使用支付宝支付%s，扣款金额:%s元 详情:%s", NotificationChannelEnum.BILL_PAYMENT_CONFIRMATION, "线下付款支付", "已支付"),

    OFFLINE_PAY_ALI_REFUND("线下退款通知", 46, 3, "您有一笔支付宝%s，存在重复支付", NotificationChannelEnum.BILL_REFUND, "线下退款支付", "已退款"),
    CHANGE_ORDER_REFUND("换单差价退款通知", 47, 3, "平台已退款换单差价，退款金额：%s元，订单号：%s，%s", NotificationChannelEnum.BILL_REFUND, "换单差价退款", "已退款"),

    ORDER_END_OFFLINE_PAY("订单完结差价", 48, 3, "您有一笔订单完结差价账单，账单ID:%s，请及时补款", NotificationChannelEnum.BILL_PAYMENT_SUPPLEMENT, "订单完结差价补缴", "待支付"),
    CHANGE_WAYBILL_OFFLINE_PAY("换单差价", 49, 3, "您有一笔换单差价账单，账单ID:%s，请及时补款", NotificationChannelEnum.BILL_PAYMENT_SUPPLEMENT, "换单差价补缴", "待支付"),
    CAL_VOLUME_OFFLINE_PAY("计抛差价", 50, 3, "您有一笔计抛差价账单，账单ID:%s，请及时补款", NotificationChannelEnum.BILL_PAYMENT_SUPPLEMENT, "计抛差价补缴", "待支付"),
    CUSTOMS_DUTY_OFFLINE_PAY("海关税费", 51, 3, "您有一笔海关税费账单，账单ID:%s，请及时补款", NotificationChannelEnum.BILL_PAYMENT_SUPPLEMENT, "海关税费补缴", "待支付"),
    OTHER_OFFLINE_PAY("其他用途", 52, 3, "您有一笔其他用途账单，账单ID:%s，请及时补款", NotificationChannelEnum.BILL_PAYMENT_SUPPLEMENT, "其他用途账单补缴", "待支付"),
    ADMIN_PREPAID_INPUT_COST("寄付运费生成通知", 53, 3, "您有一批垫付运费已生成，请及时支付", NotificationChannelEnum.BILL_PAYMENT_SUPPLEMENT, "寄付运费待支付", "待支付"),
    ADMIN_PREPAID_OFFLINE_PAY_BALANCE_DEBIT("寄付运费代扣通知", 54, 3, "平台已代扣一批垫付订单，扣款批次:%s ,总金额:%s元", NotificationChannelEnum.BILL_PAYMENT_SUPPLEMENT, "寄付运费代扣", "已成功"),


    ORDER_CANCEL_REFUSE("订单号%s取消申请被驳回", 13, 5, "", NotificationChannelEnum.ORDER_CANCELLATION, "订单取消申请", "已失败"),
    ORDER_CANCEL_PASS("订单号%s取消申请已通过", 14, 5, "", NotificationChannelEnum.ORDER_CANCELLATION, "订单取消申请", "已通过"),
    ORDER_FEFUND_SUCCESS("订单号%s取消退款已到账!", 15, 5, "", NotificationChannelEnum.ORDER_CANCELLATION, "订单取消退款", "已到账"),

    ORDER_CARRAGE_FEE_SUCCEESS("订单号%s运费退回通知", 13, 8, "", NotificationChannelEnum.ORDER_AFTER_SALES, "订单运费退回通知", "已通过"),
    COMPENSATION("订单赔付通知", 17, 10, "", NotificationChannelEnum.ORDER_AFTER_SALES, "订单赔付", "已到账"),
    ORDER_APPLY_REFUND("订单号%s售后申请被驳回", 13, 7, "", NotificationChannelEnum.ORDER_AFTER_SALES, "售后重发申请 ", "已失败"),
    ORDER_APPLY_PASS("订单号%s售后申请已通过", 14, 7, "", NotificationChannelEnum.ORDER_AFTER_SALES, "售后重发申请 ", "已通过"),
    ORDER_AFTERMARKET_REFUSE("订单号%s售后申请被驳回", 13, 6, "", NotificationChannelEnum.ORDER_AFTER_SALES, "售后退款申请", "已失败"),
    ORDER_AFTERMARKET_PASS("订单号%s售后申请已通过", 14, 6, "", NotificationChannelEnum.ORDER_AFTER_SALES, "售后退款申请", "已通过"),
    ORDER_AFTERMARKET_SUCCESS("订单号%s售后申请已到账", 15, 6, "", NotificationChannelEnum.ORDER_AFTER_SALES, "售后退款到账", "已到账"),
    ORDER_FBA_AFTERMARKET_PASS("订单号%s售后申请已到账", 27, 6, "", NotificationChannelEnum.ORDER_AFTER_SALES, "售后退款到账", "已到账"),
    CARRIAGE_BACK("退回妥投处理通知", 28, 3, "订单号%s因退回妥投，处理方式为%s", NotificationChannelEnum.ORDER_AFTER_SALES, "订单退回妥投处理", "已到账"),

    SHELVE_REALSE_AUTO("搁置订单自动释放", 16, 9, "", NotificationChannelEnum.ORDER_MODIFICATION, "搁置订单释放", "已释放"),
    ORDER_SD_PRODUCT_AMOUNT_CHANGE("订单改价通知", 30, 13, "", NotificationChannelEnum.ORDER_MODIFICATION, "订单价格修改", "已通过"),
    ORDER_PRODUCT_AMOUNT_CHANGE("订单改价通知", 18, 11, "", NotificationChannelEnum.ORDER_MODIFICATION, "订单价格修改", "已通过"),

    ORDER_COMPLETE("订单发货", 55, 3, "您的订单已发货，订单号：%s，运单号：%s", NotificationChannelEnum.ORDER_DELIVERY, "订单发货", "已发货"),
    JIT_ORDER_COMPLETE("JIT订单发货", 56, 3, "您的JIT订单已发货，订单号：%s，运单号：%s", NotificationChannelEnum.ORDER_DELIVERY, "JIT订单发货", "已发货"),
    PREPAID_ORDER_COMPLETE("寄付通知", 20, 3, "", NotificationChannelEnum.ORDER_DELIVERY, "寄付订单发货", "已发货"),

    FBA_COMPLETE_CALCULATE_LOGISTICS_FEE("FBA订单已核算运费", 24, 14, "", NotificationChannelEnum.FBA_ORDER, "FBA订单运费核算", "已完成"),
    FBA_COMPLETE_SELF_PACKING("FBA订单完成装箱(自提)", 24, 14, "", NotificationChannelEnum.FBA_ORDER, "FBA订单装箱(自提)", "已完成"),
    FBA_TO_BE_DELIVERED("FBA订单待发货(直发)", 25, 14, "", NotificationChannelEnum.FBA_ORDER, "FBA订单待发货(直发)", "已完成"),
    FBA_SEND_OUT_PACKING("FBA订单已发货", 26, 14, "", NotificationChannelEnum.FBA_ORDER, "FBA订单发货", "已发货"),
    FBA_SD_ZT("自提通知", 29, 14, "", NotificationChannelEnum.FBA_ORDER, "订单自提", "待自提"),
    FBA_ZT("自提通知", 19, 12, "", NotificationChannelEnum.FBA_ORDER, "FBA订单自提", "待自提"),


    AUTH_ONE_PRICE("授权价", 21, 13, "", NotificationChannelEnum.PRODUCT_PRICE_ADJUSTMENT, "产品授权价调整", "已完成"),
    AUTH_ACCUMULATE_PRICE("调整累计价档位", 22, 13, "", NotificationChannelEnum.PRODUCT_PRICE_ADJUSTMENT, "累计价档位调整", "已完成"),
    AUTH_ALL_ACCUMULATE_PRICE("授权累计价", 23, 13, "", NotificationChannelEnum.PRODUCT_PRICE_ADJUSTMENT, "授权累计价调整", "已完成"),

    PRODUCT_INQUIRY_AUDIT_SUCCESS("特惠申请审核成功通知", 33, 3, "恭喜您，您申请的产品：%s，已为您争取到特惠价，一次性下单即可享受特惠价，有效期截止至：%s", NotificationChannelEnum.PRODUCT_DISCOUNT_ADJUSTMENT, "产品特惠申请", "已成功"),
    PRODUCT_INQUIRY_AUDIT_FAIL("特惠申请审核失败通知", 34, 3, "抱歉，您申请的产品：%s，特惠价申请失败", NotificationChannelEnum.PRODUCT_DISCOUNT_ADJUSTMENT, "产品特惠申请", "已失败"),
    PRODUCT_INQUIRY_PRODUCT_ONLINE("产品上架，限时特惠价生效通知", 35, 3, "您申请的产品：%s，%s，已重新上架，您可以继续享受特惠价", NotificationChannelEnum.PRODUCT_DISCOUNT_ADJUSTMENT, "特惠产品上架", "已上架"),
    PRODUCT_INQUIRY_PRODUCT_OFFLINE("产品下架，限时特惠价失效通知", 36, 3, "抱歉，您申请的产品：%s，%s，已下架，暂时无法下单，若在特惠期间重新上架，则可以继续享受特惠价", NotificationChannelEnum.PRODUCT_DISCOUNT_ADJUSTMENT, "特惠产品下架", "已下架"),
    PRODUCT_INQUIRY_PRODUCT_SUPPLY_AVAILABLE("产品供应关系生效，限时特惠价生效通知", 37, 3, "您申请的产品：%s，%s，可以提供优惠的工厂已重新供应，您可以继续享受特惠价", NotificationChannelEnum.PRODUCT_DISCOUNT_ADJUSTMENT, "供应链特惠调整", "已生效"),
    PRODUCT_INQUIRY_PRODUCT_SUPPLY_UNAVAILABLE("产品供应关系失效，限时特惠价失效通知", 38, 3, "您申请的产品：%s，%s，可以提供优惠的工厂已停止供应，特惠价已失效", NotificationChannelEnum.PRODUCT_DISCOUNT_ADJUSTMENT, "供应链特惠调整", "已失效"),

    OFFICE_MATERIAL_OFFSHELF("官方素材下架通知", 31, 20, "", NotificationChannelEnum.PRODUCT_LISTING_DELISTING, "官方素材下架", "已下架"),
    PRODUCT_OFFLINE("产品下架通知", 32, 21, "产品：%s，产品编号 :%s，因供应链优化调整原因，部分或所有变体即将下架，请知悉，点击了解详情", NotificationChannelEnum.PRODUCT_LISTING_DELISTING, "平台产品下架", "已下架"),
    PRODUCT_ONE_PIECE_SUPPLY_CHAINS_OFFLINE("产品一件起订产能线下架通知", 41, 21, "产品：%s，产品编号 :%s，因%s供应链优化调整原因，部分或所有变体即将下架，请知悉，点击了解详情", NotificationChannelEnum.PRODUCT_LISTING_DELISTING, "产品一件起订产能线下架", "已下架"),
    PRODUCT_SMALL_ORDER_SUPPLY_CHAINS_OFFLINE("产品小单起订产能线下架通知", 42, 21, "产品：%s，产品编号 :%s，因%s供应链优化调整原因，部分或所有变体即将下架，请知悉，点击了解详情", NotificationChannelEnum.PRODUCT_LISTING_DELISTING, "产品小单起订产能线下架", "已下架"),


    SYSTEM_CREATE_MESSAGE_PUSH("系统创建推送消息", 57, 3, "%s", NotificationChannelEnum.CUSTOMIZATION_GROUP, "", ""),

    ;
    //新增枚举 记录下当下status
    private final Integer currentStatus = 57;

    private final String title;
    private final String content;
    private final Integer status;
    private final Integer type;
    /**
     * 分组
     */
    private final NotificationChannelEnum channelEnum;
    /**
     * 事件（短信中使用）
     */
    private final String event;
    /**
     * 表述（短信中使用）
     */
    private final String state;


    EnumNotificationTitle(String code, Integer status, Integer type, String content, NotificationChannelEnum channelEnum, String event, String state) {
        this.title = code;
        this.status = status;
        this.type = type;
        this.content = content;
        this.channelEnum = channelEnum;
        this.event = event;
        this.state = state;
    }

    public static String getGroupTypeByStatus(Integer status) {
        for (EnumNotificationTitle notificationTitle : EnumNotificationTitle.values()) {
            if (notificationTitle.getStatus().equals(status)) {
                return notificationTitle.getChannelEnum().getGroupType();
            }
        }
        log.error("根据status获取GroupType异常status ：{}", status);
        return "";
    }

    public static EnumNotificationTitle getByStatus(Integer status) {
        for (EnumNotificationTitle notificationTitle : EnumNotificationTitle.values()) {
            if (notificationTitle.getStatus().equals(status)) {
                return notificationTitle;
            }
        }
        throw new BusinessException("根据status获取EnumNotificationTitle异常status ：" + status);
    }
}
