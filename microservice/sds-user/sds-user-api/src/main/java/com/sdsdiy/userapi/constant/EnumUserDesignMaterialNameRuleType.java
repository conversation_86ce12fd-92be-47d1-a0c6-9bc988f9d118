package com.sdsdiy.userapi.constant;

import com.sdsdiy.common.base.constant.EventConstant;
import com.sdsdiy.common.base.exception.BusinessException;

/**
 * @Author:
 * @Date: 2020/6/8 00:50
 * @Description: 权限额度事件tag通知
 */
public enum EnumUserDesignMaterialNameRuleType {
    ALL_LAYER("all_layer","所有图层"),
    TOP_LAYER("top_layer","顶层素材名"),
    ALL_AND_BACK("all_and_back","全部包括背景图")

    ;
    private String code;
    private String desc;

    EnumUserDesignMaterialNameRuleType(String code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public static EnumUserDesignMaterialNameRuleType getByCode(String code){
        for (EnumUserDesignMaterialNameRuleType value : EnumUserDesignMaterialNameRuleType.values()) {
            if(value.code.equalsIgnoreCase(code)){
                return value;
            }
        }
        return null;
    }

    public static EnumUserDesignMaterialNameRuleType checkByCode(String code){
        for (EnumUserDesignMaterialNameRuleType value : EnumUserDesignMaterialNameRuleType.values()) {
            if(value.code.equalsIgnoreCase(code)){
                return value;
            }
        }
        throw new BusinessException("code不合法");
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
