package com.sdsdiy.userapi.constant.enums;

import lombok.Getter;

/**
 * 线索类型
 *
 * <AUTHOR>
 * @date 2021/7/30
 */
@Getter
public enum CrmClueType {
    //
    CLUE("clue", "线索"),
    MERCHANT("merchant", "商户"),
    ;
    public final String type;
    public final String desc;

    CrmClueType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static boolean isMerchant(String type) {
        return MERCHANT.type.equalsIgnoreCase(type);
    }
}
