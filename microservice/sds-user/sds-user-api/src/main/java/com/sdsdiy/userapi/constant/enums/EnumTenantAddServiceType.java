package com.sdsdiy.userapi.constant.enums;

import com.google.common.collect.Lists;
import com.sdsdiy.common.base.enums.ApplicationClassificationsEnum;
import com.sdsdiy.userapi.constant.tenant.TenantMemberPriceConstant;

import java.util.List;

/**
 * @description:
 * @Auther: zmy
 * @Date: 2021/10/21 16:29
 */
public enum EnumTenantAddServiceType {
    TYPE_MERCHANT(Lists.newArrayList(ApplicationClassificationsEnum.MEMBER_PRICE.getCode(),ApplicationClassificationsEnum.MERCHANT_VALUE_ADD_SERVICE.getCode()), TenantMemberPriceConstant.TYPE_MERCHANT),
    TYPE_TENANT(Lists.newArrayList(ApplicationClassificationsEnum.TENANT_VALUE_ADD_SERVICE.getCode()), TenantMemberPriceConstant.TYPE_TENANT),
    ;
    public final List<String> classifications;
    public final String type;

    EnumTenantAddServiceType(List<String> classifications, String type) {
        this.classifications = classifications;
        this.type = type;
    }

    public static EnumTenantAddServiceType getEnumByClassification(String classification) {
        EnumTenantAddServiceType[] values = EnumTenantAddServiceType.values();
        for (EnumTenantAddServiceType enumTenantAddServiceType : values) {
            List<String> classifications = enumTenantAddServiceType.getClassifications();
            if (classifications.contains(classification)) {
                return enumTenantAddServiceType;
            }
        }
        return null;
    }


    public String getType() {
        return type;
    }

    public List<String> getClassifications() {
        return classifications;
    }
}
