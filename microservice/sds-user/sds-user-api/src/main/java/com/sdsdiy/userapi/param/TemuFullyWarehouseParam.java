package com.sdsdiy.userapi.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Temu全托管仓库表单参数
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Temu全托管仓库表单参数")
public class TemuFullyWarehouseParam {
    
    @ApiModelProperty(value = "temu全托管仓库id")
    private Long id;

    @ApiModelProperty(value = "仓库名称")
    private String name;

    @ApiModelProperty(value = "发货仓id列表")
    private List<List<Long>> issuingBayIds;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "省份code")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市code")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区code")
    private String districtCode;

    @ApiModelProperty(value = "区名称")
    private String districtName;

    @ApiModelProperty(value = "城镇code")
    private String townCode;

    @ApiModelProperty(value = "城镇名称")
    private String townName;

    @ApiModelProperty(value = "详细地址")
    private String detail;

    @ApiModelProperty(value = "仓库面积类型")
    private Integer warehouseArea;

    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;

    @ApiModelProperty(value = "联系人")
    private String contactName;

    @ApiModelProperty(value = "仓库类型")
    private Integer warehouseType;

    @ApiModelProperty(value = "temu类型，半托管temu_semi，全托管temu_fully")
    private String temuType;
    
    @ApiModelProperty(value = "平台码")
    private String platformCode;
}
