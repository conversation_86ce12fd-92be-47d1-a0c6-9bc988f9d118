package com.sdsdiy.userapi.dto.application;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 应用授权统计(ApplicationAuthorizedStatistics)RespDto类
 * <AUTHOR>
 * @since 2021-10-27 20:09:33
 */
@Data
public class ApplicationAuthorizedStatisticsRespDto implements Serializable {
    private static final long serialVersionUID = 6553488510448327403L;
    //租户id列表
    private Long tenantId;
    //可授权总个数
    private Integer totalNum;
    //可授权总个数
    private Integer isShowNum;
}
