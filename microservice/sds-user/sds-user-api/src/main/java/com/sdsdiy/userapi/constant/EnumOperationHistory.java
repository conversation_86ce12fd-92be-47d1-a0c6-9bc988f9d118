package com.sdsdiy.userapi.constant;

public enum EnumOperationHistory {
    MERCHANT_DOCK("merchant_dock","商户消费了金额 %f,赠送金 %f,账号类型 %s"),
    MERCHANT_RESTITUTION("merchant_restitution","商户回退了金额 %f,赠送金 %f,账号类型 %s "),
    MERCHANT_ALLOCATION_ACCOUNT_BALANCE("merchant_allocation_account_balance","商户总分配金额 %f,赠送金 %f"),
    ACCOUNT_ALLOCATION_BALANCE("account_allocation_balance","商户分配子账号accountId %d,金额 %f,赠送金 %f"),
    ACCOUNT_RETURN_BALANCE("account_return_balance","子账号反还金额 %f,赠送金 %f"),

    ACCOUNT_ALLOCATION_FUNCTION_PARAM("account_allocation_function_param","商户分配子账号accountId=%s,分配类型为=%s "),
    ACCOUNT_ALLOCATION_FUNCTION("account_allocation_function","商户分配子账号permissionId=%d,共用额度 %d,共用使用额度 %d"),
    ACCOUNT_ALLOCATION_FUNCTION_REST_USED("account_allocation_function_rest_used","商户分配子账号重置使用量permissionId=%d,共用额度 %d,共用使用额度 %d"),
    ACCOUNT_RETURN_FUNCTION("account_return_function","子账号反还 permissionId=%d, 额度 %d,使用额度%d, 共用额度 %d,共用使用额度 %d"),
    ;
    String type;
    String remark;

    EnumOperationHistory(String type, String remark) {
        this.type = type;
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public String getRemark() {
        return remark;
    }
}
