package com.sdsdiy.userapi.constant;

import com.sdsdiy.common.base.constant.ResponseCodeStatusEnum;

/**
 * @see com.sdsdiy.userdata.enums.FunctionQuotasEnum
 * <AUTHOR>
 * @date 2021/6/28
 */
@Deprecated
public enum MerchantPermission {
    //
    SUB_ACCOUNT("sub_account", ResponseCodeStatusEnum.SUB_ACCOUNT_ERROR),
    PRODUCT_DESIGN("product_design",ResponseCodeStatusEnum.PRODUCT_DESIGN_ERROR),
    MATERIAL_CAPACITY("material_capacity",ResponseCodeStatusEnum.MATERIAL_CAPACITY_ERROR),
    PRIVATE_TEMPLATE("private_template",ResponseCodeStatusEnum.PRIVATE_TEMPLATE_ERROR),
    MATERIAL_KEYWORD_EXTRACT("material_keyword_extract",ResponseCodeStatusEnum.MATERIAL_KEYWORD_EXTRACT_ERROR),
    DOMAIN_NAME("domain_name_service",ResponseCodeStatusEnum.DOMAIN_NAME_ERROR),
    AUTOMATION_GENERATE_BARCODE("automation_generate_barcode", ResponseCodeStatusEnum.AUTOMATION_GENERATE_BARCODE_ERROR),
    STORE_NUMBER("store_number", ResponseCodeStatusEnum.STORE_NUMBER_ERROR),
    MAIN_PICTURE_WATERMARK("main_picture_watermark", ResponseCodeStatusEnum.MAIN_PICTURE_WATERMARK_ERROR),
    MY_THEME("my_theme", ResponseCodeStatusEnum.MY_THEME_ERROR),
    CUSTOM_FONT("custom_font", ResponseCodeStatusEnum.CUSTOM_FONT_ERROR),
    ;
    private String code;
    private ResponseCodeStatusEnum errorMsg;

    MerchantPermission(String code, ResponseCodeStatusEnum errorMsg) {
        this.code = code;
        this.errorMsg = errorMsg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public ResponseCodeStatusEnum getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(ResponseCodeStatusEnum errorMsg) {
        this.errorMsg = errorMsg;
    }

    public static MerchantPermission getByCode(String code){
        MerchantPermission[] values = MerchantPermission.values();
        for(MerchantPermission v:values){
            if(v.getCode().equals(code)){
                return v;
            }
        }
        return null;
    }
}
