package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.dtoconvert.annotation.DtoBind;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import com.sdsdiy.userdata.enums.MemberLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * sds平台商品(PlatformGoodsRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-10-07 10:20:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformGoodsRespDto extends BaseDTO {

    @ApiModelProperty("商品名称")
    @DtoDefault()
    private String name;
    @DtoDefault
    private Integer status;
    @ApiModelProperty("商品类型")
    @DtoDefault
    private Integer type;
    @ApiModelProperty("限制会员等级")
    @DtoDefault()
    private Integer limitLevel;
    @ApiModelProperty("排序")
    @DtoDefault()
    private Integer sort;
    @ApiModelProperty("上架开始时间")
    private Long startTime;
    @ApiModelProperty("上架结束时间")
    private Long endTime;
    @ApiModelProperty("商品描述")
    @DtoDefault()
    private String description;

    /**
     * 非数据库字段，当前商业是否可购买
     */
    @ApiModelProperty("当前商户是否可购买")
    @DtoDefault()
    private Boolean purchasable;
    /**
     * 开发版本，高于此版本的数据线上不显示
     */
    @DtoDefault()
    private Integer devVersion;

    /**
     * 套餐
     */
    @ApiModelProperty("套餐信息（附带权限信息）")
    @DtoBind(selfField = "id", relateField = "platformGoodsId"
            , condition = "is_delete=0"
            , showField = "platformPermissionList", provider = "com.sdsdiy.userimpl.relationprovider.PlatformPermissionSetMealV2Provider")
    private List<PlatformPermissionSetMealRespDto> setMealList;

    /**
     * 套餐
     */
    @ApiModelProperty("套餐信息（不带权限信息）")
    @DtoBind(selfField = "id", relateField = "platformGoodsId"
            , condition = "is_delete=0"
            , provider = "com.sdsdiy.userimpl.relationprovider.PlatformPermissionSetMealV2Provider")
    private List<PlatformPermissionSetMealRespDto> baseSetMealList;

    public PlatformPermissionSetMealRespDto getSetMeal() {
        return CollectionUtils.isEmpty(setMealList) ? null : setMealList.stream()
                .filter(i -> MemberLevelEnum.isYear(i.getPeriod()) || MemberLevelEnum.isPermanent(i.getPeriod())).findFirst().orElse(setMealList.get(0));
    }

    public PlatformPermissionSetMealRespDto getBaseSetMeal() {
        return CollectionUtils.isEmpty(baseSetMealList) ? null : baseSetMealList.stream()
                .filter(i -> MemberLevelEnum.isYear(i.getPeriod()) || MemberLevelEnum.isPermanent(i.getPeriod())).findFirst().orElse(baseSetMealList.get(0));
    }
}