package com.sdsdiy.userapi.constant;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * @author: zmy
 * @desc:
 */
public interface MerchantEuLabelRuleConstant {

    /**
     * 图标类型，系统：system，自定义：custom
     */
    @Getter
    enum IconTypeEnum {
        SYSTEM("system", "系统"),
        CUSTOM("custom", "自定义"),
        ;

        private final String code;
        private final String desc;

        IconTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }

    /**
     * 关联方式，自定义关联：custom，全部关联：all
     */
    @Getter
    enum relMethodEnum {
        ALL("all", "全部关联"),
        CUSTOM("custom", "自定义关联"),
        ;

        private final String code;
        private final String desc;

        relMethodEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }

    /**
     * 关联类型,店铺：store,产品：product
     */
    @Getter
    enum TypeEnum {
        STORE("store", "店铺"),
        PRODUCT("product", "产品"),
        ;

        private final String code;
        private final String desc;

        TypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static TypeEnum getByCode(String code) {
            for (TypeEnum typeEnum : TypeEnum.values()) {
                if (typeEnum.getCode().equals(code)) {
                    return typeEnum;
                }
            }
            return null;
        }

        public static List<TypeEnum> getOtherTypesByCode(String code) {
            List<TypeEnum> otherTypes= Lists.newArrayList();
            for (TypeEnum typeEnum : TypeEnum.values()) {
                if (!typeEnum.getCode().equals(code)) {
                    otherTypes.add(typeEnum);
                }
            }
            return otherTypes;
        }

    }

}
