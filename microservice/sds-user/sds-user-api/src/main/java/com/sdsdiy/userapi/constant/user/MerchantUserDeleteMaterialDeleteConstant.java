package com.sdsdiy.userapi.constant.user;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/05/17 15:14
 **/
public interface MerchantUserDeleteMaterialDeleteConstant {


    enum StatusEnum {
        WAIT("wait", "等待"),
        ONGOING("ongoing", "处理中"),
        SUCCESS("success", "成功"),
        FAIL("fail", "失败"),
        ;
        @Getter
        private final String value;
        private final String desc;

        StatusEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static StatusEnum getByValue(String value) {
            StatusEnum[] values = StatusEnum.values();
            for (StatusEnum resultEnum : values) {
                if (resultEnum.getValue().contains(value)) {
                    return resultEnum;
                }
            }
            return null;
        }
    }

    enum DesignProductOperationTypeEnum {
        DELETE("delete", "删除"),
        RETAIN("retain", "保留"),
        ;
        @Getter
        private final String value;
        private final String desc;

        DesignProductOperationTypeEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static DesignProductOperationTypeEnum getByValue(String value) {
            DesignProductOperationTypeEnum[] values = DesignProductOperationTypeEnum.values();
            for (DesignProductOperationTypeEnum resultEnum : values) {
                if (resultEnum.getValue().contains(value)) {
                    return resultEnum;
                }
            }
            return null;
        }
    }
}
