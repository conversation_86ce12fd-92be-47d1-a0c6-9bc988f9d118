package com.sdsdiy.userapi.constant.enums;

import lombok.Getter;

/**
 * @author: bin_lin
 * @date: 2024/8/22 12:23
 * @desc:
 */
@Getter
public enum NotificationTypeGroupEnum {
    USER("USER", "账户"),
    BILL("BIL<PERSON>", "账单"),
    ORDER("ORDER", "订单"),
    PRODUCT("PRODUCT", "产品"),
    ;
    public final String type;
    public final String desc;

    NotificationTypeGroupEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public boolean equals(String type) {
        return this.type.equalsIgnoreCase(type);
    }
}
