package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品库子账号权限表(SysUserProductPermissionRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2021-03-02 10:42:51
 */
@Data
public class MerchantSysUserProductPermissionRespDto implements Serializable {

    @DtoDefault
    private Long id;
    @DtoDefault
    private Long merchantSysUserId;
    private Long merchantId;
    @DtoDefault
    private String productLibraryType;
    private Date createTime;
    private Long createUid;
    private Date updateTime;
    private Long updateUid;
    private Integer isDelete;

}