package com.sdsdiy.userapi.dto.storeSettleIn.supplierSettleIn;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/22
 */
@Data
@NoArgsConstructor
@ApiModel("供应商-个体户信息")
public class SupplierSettleInIndividualInfoDto {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "供应商入驻ID")
    private Long supplierSettleInId;

    @ApiModelProperty(value = "法人姓名")
    @NotEmpty(message = "姓名不能为空")
    @Length(max = 50,  message = "姓名长度不能超过50字符")
    private String legalPersonName;

    @ApiModelProperty(value = "证件号码")
    @NotEmpty(message = "证件号码不能为空")
    @Length(max = 50,  message = "证件号码不能超过50字符")
    private String idCardNo;

    @ApiModelProperty(value = "证件生效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idCardEffectiveDate;

    @ApiModelProperty(value = "证件截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idCardExpirationDate;

    @ApiModelProperty(value = "身份证照片正面文件路径")
    @NotEmpty(message = "身份证照片不能为空")
    private String idCardPhotoFront;

    @ApiModelProperty(value = "身份证照片背面文件路径")
    @NotEmpty(message = "身份证照片不能为空")
    private String idCardPhotoBack;

    @ApiModelProperty(value = "是否长期，0否，1是")
    private Integer isInfinite;

    @ApiModelProperty(value = "联系电话")
    @NotEmpty(message = "联系电话不能为空")
    @Length(max = 50,  message = "联系电话不能超过50字符")
    private String phone;

    @ApiModelProperty(value = "合作协议文件路径")
    @NotEmpty(message = "合作协议文件不能为空")
    private String agreementFilePath;

    @ApiModelProperty(value = "合作协议文件名")
    @NotEmpty(message = "合作协议文件名不能为空")
    private String agreementFileName;
}
