package com.sdsdiy.userapi.constant;

import com.sdsdiy.common.base.exception.BusinessException;

/**
 * <AUTHOR>
 * @date 2021/6/28
 */
@Deprecated
public enum PurposeNameStatus {
    ALI_RECHARGE(1,"支付宝充值","支付宝充值",""),
    UNDER_LINE_RECHARGE(2,"线下充值","线下充值",""),
    PLATFORM_RECHARGE(3,"平台充值","平台充值",""),
    PAY_PROFESSIONAL_MEMBER(4, "购买专业版(V2)","购买%N专业版(V2)","professional_set_meal"),
    AUTHORIZED_MEMBER(5, "授权会员","授权%N会员补差价",""),
    PAY_START_MEMBER(6, "购买创业版(V1)","购买%N创业版(V1)","start_set_meal"),
    PAY_ADDED_SERVICE(7, "购买增值服务","购买%N%S%S","value_added_services"),
    PAY_PRODUCT(8, "购买产品","购买%S...等%N件商品",""),
    PAY_ENTERPRISE_MEMBER(9, "购买企业版(V3)","购买%N企业版(V3)","enterprise_set_meal"),
    ;

    final Integer status;
    final String specificPurpose;
    final String orderName;
    final String code;




    PurposeNameStatus(Integer status, String specificPurpose,String orderName,String code) {
        this.status = status;
        this.specificPurpose = specificPurpose;
        this.orderName = orderName;
        this.code = code;
    }
    public String getOrderName() {
        return orderName;
    }
    public String getSpecificPurpose() {
        return specificPurpose;
    }
    public Integer getStatus() {
        return status;
    }

    public static PurposeNameStatus getByCode(String code){
        for (PurposeNameStatus purposeNameStatus : PurposeNameStatus.values()) {
            if(purposeNameStatus.code.equals(code)){
                return purposeNameStatus;
            }
        }
        throw new BusinessException("code状态值有误!");
    }
}
