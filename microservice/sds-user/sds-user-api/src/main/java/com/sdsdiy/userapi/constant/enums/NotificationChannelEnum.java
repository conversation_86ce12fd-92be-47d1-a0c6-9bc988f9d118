package com.sdsdiy.userapi.constant.enums;

import lombok.Getter;

/**
 * @author: bin_lin
 * @date: 2024/8/22 12:23
 * @desc: 数据库表 message_channel对应
 */
@Getter
public enum NotificationChannelEnum {
    CUSTOMIZATION_GROUP(0L, null, "自定义组"),
    ACCOUNT_BALANCE(1L, NotificationTypeGroupEnum.USER.getType(), "账户余额"),
    ACCOUNT_MEMBERSHIP_PACKAGE(2L, NotificationTypeGroupEnum.USER.getType(), "账户会员套餐"),
    ACCOUNT_INFORMATION(3L, NotificationTypeGroupEnum.USER.getType(), "账户信息"),

    BILL_PAYMENT_CONFIRMATION(4L, NotificationTypeGroupEnum.BILL.getType(), "账单支付确认"),
    BILL_REFUND(5L, NotificationTypeGroupEnum.BILL.getType(), "账单退款"),
    BILL_PAYMENT_SUPPLEMENT(6L, NotificationTypeGroupEnum.BILL.getType(), "账单补缴"),

    ORDER_CANCELLATION(7L, NotificationTypeGroupEnum.ORDER.getType(), "订单取消"),
    ORDER_AFTER_SALES(8L, NotificationTypeGroupEnum.ORDER.getType(), "订单售后"),
    ORDER_MODIFICATION(9L, NotificationTypeGroupEnum.ORDER.getType(), "订单变更"),
    ORDER_DELIVERY(10L, NotificationTypeGroupEnum.ORDER.getType(), "订单发货"),
    FBA_ORDER(11L, NotificationTypeGroupEnum.ORDER.getType(), "FBA订单"),

    PRODUCT_PRICE_ADJUSTMENT(12L, NotificationTypeGroupEnum.PRODUCT.getType(), "产品价格调整"),
    PRODUCT_DISCOUNT_ADJUSTMENT(13L, NotificationTypeGroupEnum.PRODUCT.getType(), "产品优惠调整"),
    PRODUCT_LISTING_DELISTING(14L, NotificationTypeGroupEnum.PRODUCT.getType(), "产品上下架"),

    //新增渠道需要同步数据到message_channel表上
    ;

    public final Long id;
    public final String groupType;
    public final String desc;

    NotificationChannelEnum(Long id, String groupType, String desc) {
        this.id = id;
        this.groupType = groupType;
        this.desc = desc;
    }

}
