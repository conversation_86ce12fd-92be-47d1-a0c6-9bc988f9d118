package com.sdsdiy.userapi.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class VipServiceRespDto {
    private String code;
    private List<SetMealUsedUserRespDto> users;
    private List<PlanRespDto> plans;

    public VipServiceRespDto(String code) {
        this.code = code;
    }
}
