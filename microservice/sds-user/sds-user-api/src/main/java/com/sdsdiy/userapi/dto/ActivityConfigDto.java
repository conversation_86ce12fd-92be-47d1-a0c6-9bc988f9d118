package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/21
 */
@Data
public class ActivityConfigDto implements Serializable, Comparable<ActivityConfigDto> {
    private static final long serialVersionUID = -3345489654644582102L;
    @DtoDefault
    private Long id;

    @DtoDefault
    private String activityLocation;
    @DtoDefault
    private String settingItem;

    @DtoDefault
    private String type;

    @DtoDefault
    @Size(min = 1, max = 20, message = "主标题字符数必须在1-20之间")
    private String mainTitle;

    @DtoDefault
    @Size(max = 20, message = "副标题字符数必须在1-20之间")
    private String subTitle;

    @DtoDefault
    @Size(max = 5, message = "icon字符数必须在1-5之间")
    private String icon;
//    v6.17 banner可以有多张，使用bannerImgList字段,该字段不再使用，一段时间后弃用
    @DtoDefault
    @Size(max = 255, message = "链接字符数必须在1-255之间")
    private String link;
    @DtoDefault
    @Size(max = 255, message = "图片字符数必须在1-255之间")
    private String bannerImg;
    @DtoDefault
    private Date startTime;
    @DtoDefault
    private Date endTime;
    @DtoDefault
    private Date updateTime;
    @DtoDefault
    private String bannerColorHex;
    @DtoDefault
    @Valid
    private List<ActivityBannerImgDto> bannerImgList;

    private List<List<RenderingsConfigDto>> renderingsList;


    @Override
    public int compareTo(ActivityConfigDto o) {
        if (this.getActivityLocation() == null) {
            return -1;
        }
        if (o == null || o.getActivityLocation() == null) {
            return 1;
        }
        return this.getActivityLocation().compareTo(o.getActivityLocation());
    }
}
