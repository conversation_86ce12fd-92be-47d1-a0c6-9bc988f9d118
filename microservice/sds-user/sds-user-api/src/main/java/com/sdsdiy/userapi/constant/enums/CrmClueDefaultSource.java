package com.sdsdiy.userapi.constant.enums;

/**
 * <AUTHOR>
 * @date 2021/8/6
 */
public enum CrmClueDefaultSource {
    //
    NATURE(1L, "自然获客"),
    INVITE(2L, "邀请注册"),
    ;
    public final Long id;
    public final String desc;

    CrmClueDefaultSource(Long id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public static Long getRegisterSource(Long inviteUid) {
        if (inviteUid == null || inviteUid <= 0) {
            return NATURE.id;
        }
        return INVITE.id;
    }
}
