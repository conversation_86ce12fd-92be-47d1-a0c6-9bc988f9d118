package com.sdsdiy.userapi.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantBalanceLogParam {

    private String tradeUuid;
    private Long merchantId;
    private BigDecimal originBalance;
    private BigDecimal originFreeMoney;
    private BigDecimal originSaasBalance;
    private BigDecimal originSaasFreeMoney;
    private BigDecimal disposeBalance;
    private BigDecimal disposeFreeMoney;
    private BigDecimal disposeSaasBalance;
    private BigDecimal disposeSaasFreeMoney;
    private Long createTime;
    private String remark;
}
