package com.sdsdiy.userapi.constant.user;

import com.sdsdiy.common.base.exception.BusinessException;
import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/05/17 15:14
 **/
public interface MerchantUserOrderedDataConstant {
    @Getter
    enum TypeEnum {
        THEME("theme", "主题"),
        MATERIAL("material", "素材"),
        ;

        private final String value;
        private final String desc;

        TypeEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }
        public static String getByValue(String value) {
            String actualValue = null;
            TypeEnum[] values = TypeEnum.values();
            for (TypeEnum e : values) {
                if (e.getValue().contains(value)) {
                    actualValue = e.getValue();
                    break;
                }
            }
            return Optional.ofNullable(actualValue).orElseThrow(() -> new BusinessException("类型错误"));
        }


    }
}
