package com.sdsdiy.userapi.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.dtoconvert.annotation.DtoBind;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * (MerchantRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-07-07 13:46:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantRespDto extends BaseDTO {

    @DtoDefault()
    private String name;
    @DtoDefault()
    private String contactName;
    @DtoDefault()
    private String contactTel;
    @DtoDefault
    private Long createdTime;
    private String host;
    private Integer status;
    private String salt;
    @DtoDefault()
    private String code;
    private Integer deploymentPatterns;
    private Long packageValidity;
    private Long lastUseTime;
    private String adminAccount;
    private String adminPasswd;
    private String adminPasswdSalt;
    private Integer isPermanentPackage;
    private Integer activeStatus;
    private String password;
    private Integer supplyType;
    private Integer isAnalyze;
    private Integer accountType;
    private Integer isInsider;
    @DtoDefault()
    private Double balance;
    @DtoDefault()
    private Double freeGold;
    private Long lastByGoodsTime;
    private BigDecimal consumeTotal;
    private String channel;
    @DtoDefault()
    private String merchantNo;


    /**
     * 0 不鉴黄 1 鉴黄
     */
    private Integer isIdentifyPornographic;
    @DtoDefault()
    private Long turnIntoTestAt;

    /**
     * 当天套餐是否更新，1:是，0:否
     */
    @DtoDefault()
    private Integer setMealUpdate;
    private String balanceType;
    /**
     * 共用余额
     */
    @DtoDefault()
    private Double inCommonBalance;

    /**租户id*/
    @DtoDefault()
    private Long tenantId;
    /**主商户id*/
    @DtoDefault()
    private Long mainMerchantId;
    /**是否开过户，yes：是，no：否*/
    private String openMemberFlag;

    /**
     * 共用赠送金
     */
    @DtoDefault()
    private Double inCommonFreeGold;
    @DtoDefault()
    private Integer tenantOpenOnlinePay;

    /**
     * 商户当前会员等级
     */
    @DtoDefault()
    private BigDecimal memberLevel;

    @DtoDefault()
    @JsonProperty("setMeal")
    private MerchantPlatformPermissionSetMealRespDto currentSetMealDto;

    @JsonIgnore
    @DtoBind(selfField = "id", relateField = "merchantId", condition = "is_delete=0", orderClause = "start_time", showField = "items", provider = "com.sdsdiy.userimpl.relationprovider.MerchantPlatformPermissionSetMealProvider")
    private List<MerchantPlatformPermissionSetMealRespDto> items = Lists.newArrayList();

    @JsonIgnore
    @DtoBind(selfField = "id", relateField = "merchantId", condition = "is_delete=0", orderClause = "start_time", provider = "com.sdsdiy.userimpl.relationprovider.MerchantPlatformPermissionSetMealProvider")
    private List<MerchantPlatformPermissionSetMealRespDto> baseItems = Lists.newArrayList();

    private Integer disable;

    @ApiModelProperty("商户认证类型：none-未认证，personal-个人，enterprise-企业")
    private String merchantCertificationType;
}
