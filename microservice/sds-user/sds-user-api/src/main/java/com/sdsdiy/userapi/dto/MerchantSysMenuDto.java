package com.sdsdiy.userapi.dto;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/23
 */
@Data
public class MerchantSysMenuDto {
    @DtoDefault
    private Long id;
    @DtoDefault
    private String parentId;
    @DtoDefault
    private String parentIds;
    @DtoDefault
    private String name;
    @DtoDefault
    private Double sort;
    @DtoDefault
    private String href;
    @DtoDefault
    private String target;
    @DtoDefault
    private String icon;
    @DtoDefault
    private String isShow;
    @DtoDefault
    private String permission;
    @DtoDefault
    private Date createDate;
    @DtoDefault
    private String remarks;
    @DtoDefault
    private String delFlag;
    @DtoDefault
    private Integer type;
    @DtoDefault
    private Integer roleOfficePermissionType;
    @DtoDefault
    private Integer extendType;
    @DtoDefault
    private Long showFlag;
    @DtoDefault
    private Integer manageFlag;
    @DtoDefault
    private Integer permissionType;

    public List<Long> getParentIdList() {
        List<Long> ids = Lists.newArrayList();
        if (StringUtils.isNotBlank(parentIds)){
            String[] res = parentIds.split(",");
            for (String re : res) {
                if(StringUtils.isNotBlank(re)){
                    Long parentId = Long.valueOf(re);
                    if(parentId > 0){
                        ids.add(parentId);
                    }
                }
            }
        }
        ids.add(this.getId());
        return ids;
    }
}
