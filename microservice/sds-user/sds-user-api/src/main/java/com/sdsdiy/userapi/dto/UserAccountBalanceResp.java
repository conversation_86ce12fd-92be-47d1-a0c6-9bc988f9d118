package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: bin_lin
 * @date: 2021/6/23 16:24
 * @desc:
 */
@Data
public class UserAccountBalanceResp {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "余额")
    private BigDecimal balance;
    @ApiModelProperty(value = "赠送金")
    private BigDecimal freeGold;
    @ApiModelProperty(value = "余额")
    private BigDecimal merchantBalance;
    @ApiModelProperty(value = "赠送金")
    private BigDecimal merchantFreeGold;

    /**
     * 金额类型 inCommon 共用  allocation 分配
     */
    private String balanceType;

}
