package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * temu全托管仓库
 *
 * <AUTHOR>
 * @date 2025/1/2
 */
@ApiModel("temu全托管仓库")
@Data
public class TemuFullyWarehouseDTO implements Serializable {

    @ApiModelProperty(value = "temu全托管仓库id")
    private Long id;

    @ApiModelProperty(value = "仓库名称")
    private String name;

    @ApiModelProperty(value = "发货仓id列表")
    private String issuingBayIds;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "省份code")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市code")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;
    
    @ApiModelProperty(value = "区code")
    private String districtCode;

    @ApiModelProperty(value = "区名称")
    private String districtName;

    @ApiModelProperty(value = "城镇code")
    private String townCode;

    @ApiModelProperty(value = "城镇名称")
    private String townName;

    @ApiModelProperty(value = "详细地址")
    private String detail;
    
    @ApiModelProperty(value = "街道名称")
    private String street;

    @ApiModelProperty(value = "仓库面积类型")
    private Integer warehouseArea;

    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;

    @ApiModelProperty(value = "联系人")
    private String contactName;

    @ApiModelProperty(value = "仓库类型")
    private Integer warehouseType;

    @ApiModelProperty(value = "temu类型，半托管temu_semi，全托管temu_fully")
    private String temuType;

    @ApiModelProperty(value = "发货仓列表")
    private List<TemuIssuingBayDTO> issuingBayList;

    @ApiModelProperty(value = "平台码")
    private String platformCode;

}
