package com.sdsdiy.userapi.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.sdsdiy.userapi.dto.base.MerchantSysRoleRespDto;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/23
 */
@Data
public class MerchantSysRoleDto {

    public static String ROLE_TYPE_ASSIGNMENT = "assignment";
    public static String ROLE_TYPE_BOSS = "boss";

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long merchantId;

    private String name;

    private String roleType;

    private String dataScope;

    private String useable;

    private Date createDate;

    private Date updateDate;

    private String remarks;

    private String delFlag;

    private Long userId;

    private List<MerchantSysMenuDto> menuList = Lists.newArrayList(); // 拥有菜单列表

    private List<MerchantSysRoleMenuDto> roleMenuList;

    private List<MerchantSysRoleMenuDto> menu;

    @JsonProperty("is_boss")
    public Boolean isBoss(){
        return getRoleType()!=null && getRoleType().equals(ROLE_TYPE_BOSS);
    }

    public List<String> getMenuIdList() {
        List<String> menuIdList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(menuList)){
            if(CollectionUtils.isNotEmpty(roleMenuList)){
                List<Long> longList = roleMenuList.stream().map(MerchantSysRoleMenuDto::getMenuId).collect(Collectors.toList());
                for (Long id:longList){
                    MerchantSysMenuDto menu = new MerchantSysMenuDto();
                    menu.setId(id);
                    menuList.add(menu);
                }
            }
        }
        for (MerchantSysMenuDto menu : menuList) {
            menuIdList.add(menu.getId()+"");
        }
        return menuIdList;
    }
    @JsonProperty("menu_ids")
    public List<Long>  getMenuIds() {
        List<Long> menuIdList = Lists.newArrayList();
        for (MerchantSysMenuDto menu : menuList) {
            menuIdList.add(menu.getId());
        }
        return menuIdList;
    }
    @JsonProperty("menu_ids")
    public void setMenuIds(List<Long> menuIds) {
        menuList = Lists.newArrayList();
        for (Long menuId : menuIds) {
            MerchantSysMenuDto menu = new MerchantSysMenuDto();
            menu.setId(menuId);
            menuList.add(menu);
        }
    }

    public List<MerchantSysRoleMenuDto> getMenu() {
        return menu;
    }

    public void setMenu(List<MerchantSysRoleMenuDto> menu) {
        this.menu = menu;
    }
}
