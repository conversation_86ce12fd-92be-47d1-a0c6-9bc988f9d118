package com.sdsdiy.userapi.constant;

/**
 * <AUTHOR>
 * @version V1.0
 * @program: microservice-parent
 * @Package com.sdsdiy.userapi.constant
 * @Description: TODO
 * @date 2021/7/6 16:23
 */
public class TenantConstant {

    public final static String TENANT_POD_REGISTER_MERCHANT = "TENANT_POD_REGISTER_MERCHANT";
    public final static String RESET_PASSWORD = "RESET_PASSWORD";
    public final static String GIRL_VERIFY_CODE = "GIRL_VERIFY_CODE";

    public final static String USER_MASTER = "master";
    public final static String USER_SLAVE = "slave";

    public final static String BOSS = "boss";
    public final static String ROOT = "root";
    public final static String DICT_PHONE_COUNTRY_CODE = "86";


    public final static String MENU_TYPE_PERMIT = "permit";
    public final static String MENU_TYPE_MENU = "menu";

    public final static String DATA_SCOPE_PERSONAL = "personal";
    public final static String DATA_SCOPE_DEPT = "dept";
    public final static String DATA_SCOPE_DEPTANDSUB = "deptAndSub";
    public final static String DATA_SCOPE_COMPANY = "company";

    public final static String IS_YES = "Y";
    public final static String IS_NO = "N";


    public final static Long DEPT_DEFAULT_PARENT_ID = 0L;
    public final static String DEPT_DEFAULT_PARENT_IDS = "0,";

    public final static Long MENU_DEFAULT_PARENT_ID = 1L;

    public final static Long BASE_APPLICATION_MODULE_ID = 0L;
    public final static Long ONLINE_PAYMENT_MODULE = 52L;
    public final static Long DISTRIBUTE_FBA_ORDER = 87L;
    public final static Long MESSAGE_PUSH = 99L;

    /**
     * 状态，valid：有效，invalid：无效
     */
    public static final String STATUS_VALID = "valid";
    public static final String STATUS_INVALID = "invalid";

    /**
     * 付款人
     */
    public static final String PAYER_TYPE_MERCHANT = "merchant";
    public static final String PAYER_TYPE_TENANT = "tenant";

    /**
     * 部署类型，business：创业版、trade工贸版、brand品牌版
     */
    public static final String TYPE_BUSINESS = "business";
    public static final String TYPE_TRADE = "trade";
    public static final String TYPE_BRAND = "brand";

    /**
     * 默认账号密码
     */
    public static final String DEFAULT_SALT = "24d3f220f2db89f9";
    public static final String DEFAULT_PWD = "8108a8bc93ea7b6e21a137052b38ef62680cab88";

    public static final String DEFAULT_ENCODE_PWD = "ziwei@369";

    public final static String EVEN_TENANT_TURN_TO_TEST = "even_tenant_turn_to_test";
    public final static String EVEN_TENANT_REMOVE_TEST = "even_tenant_remove_test";
}
