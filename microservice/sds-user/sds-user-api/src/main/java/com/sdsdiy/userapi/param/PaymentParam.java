package com.sdsdiy.userapi.param;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentParam {

    private String account;
    private String no;
    private String outNo;
    private String method;
    private String imgUrl;
    private Integer status;
    private Long createdTime;
    private Long payTime;
    private BigDecimal totalAmount;
    private Integer payType;
    private BigDecimal balance;
    private BigDecimal freeGold;
    private BigDecimal refundBalance;
    private BigDecimal refundFreeGold;
    private BigDecimal refundTotalAmount;
    private Long merchantId;
    private Integer addConsume;
    private Long userId;
    private Long sourceTenantId;
    private Long sourceMerchantId;
    private Long sourceUserId;
    private Long targetTenantId;
    private Long targetMerchantId;
    private String sourceRole;
    private String targetRole;
}
