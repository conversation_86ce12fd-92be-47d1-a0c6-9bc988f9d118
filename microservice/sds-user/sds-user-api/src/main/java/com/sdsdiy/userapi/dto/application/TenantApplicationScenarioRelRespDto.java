package com.sdsdiy.userapi.dto.application;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 租户应用场景-应用(TenantApplicationScenarioRel)RespDto类
 *
 * <AUTHOR>
 * @since 2021-10-27 10:09:43
 */
@Data
public class TenantApplicationScenarioRelRespDto implements Serializable {
    private static final long serialVersionUID = 106745714090847534L;
    /**
    * 主键id
    */    
    private Long id;
    /**
    * 租户id
    */    
    private Long tenantId;
    /**
    * 应用场景id
    */    
    private Long applicationScenarioId;
    /**
    * 应用id
    */    
    private Long applicationId;
    /**
    * 是否显示 0、不显示 1、显示
    */    
    private Integer isShow;
    /**
    * 是否开启 0、关闭 1、开启
    */    
    private Integer isOpen;
    /**
    * 显示标识 0、只对sdsdiy可见 1、对全部应用场景可见
    */    
    private Integer showFlag;
    /**
    * 数值
    */    
    private BigDecimal value;
    @ApiModelProperty("应用code")
    private String applicationCode;
    /**
     * 应用类型 SYSTEN_DEFAULT:系统预设;FREE_OPEN_OR_CLOSED:自由开启/关闭;ONLY_OPEN:仅开启
     * {@link com.sdsdiy.common.base.enums.ApplicationTypeEnum}
     */
    @ApiModelProperty("应用类型 SYSTEN_DEFAULT:系统预设;FREE_OPEN_OR_CLOSED:自由开启/关闭;ONLY_OPEN:仅开启")
    private String applicationType;
}