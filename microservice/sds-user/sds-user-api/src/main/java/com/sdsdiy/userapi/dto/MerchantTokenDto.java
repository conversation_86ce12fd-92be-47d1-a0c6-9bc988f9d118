package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.util.Date;

/**
 * 商户token表Dto
 * <AUTHOR>
 * @date 2021/6/30
 */
@Data
public class MerchantTokenDto {
    @DtoDefault
    private Long merchantId;
    @DtoDefault
    private Long userId;
    @DtoDefault
    private String accessToken;
    private String platformType;
    private String extraInfo;
    @DtoDefault
    private Date expireTime;

}
