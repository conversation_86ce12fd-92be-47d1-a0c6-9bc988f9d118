package com.sdsdiy.userapi.param;

import lombok.Data;
import lombok.NoArgsConstructor;
 /**
  * 功能描述: <br>
  * @Author: lin_bin
  * @Date: 2022/3/7 16:49
  */
@Data
@NoArgsConstructor
public class FunctionConsumeParam {
     private String code;
     private Long time;
     @Deprecated
     private Long addedTime;

     public FunctionConsumeParam(String code, Long time, Long addedTime) {
         this.code = code;
         this.time = time;
         this.addedTime = addedTime;
     }

     public FunctionConsumeParam(String code) {
         this.code = code;
         this.time = 1L;
         this.addedTime = 0L;
     }
 }
