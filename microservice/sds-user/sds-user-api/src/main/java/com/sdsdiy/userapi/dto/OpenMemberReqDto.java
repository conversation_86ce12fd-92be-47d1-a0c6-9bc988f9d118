package com.sdsdiy.userapi.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @Auther: zmy
 * @Date: 2021/10/21 10:02
 */
@Data
public class OpenMemberReqDto {
    /**商户id*/
    private Long merchantId;
    /**操作人*/
    private Long operator;
    /**操作密码*/
    private String password;
    /**续费、授权，PlatformSetMealConstant.OPTION_TYPE*/
    private Integer optionType;
    /**开通个数*/
    private Integer value;
    /**支付方式 余额、支付宝、空（无需付费）EnumPaymentMethod*/
    private String paymentMethod;
    /**套餐商品id*/
    private Long platformGoodsId;
    /**
     * 套餐id
     */
    private Long platformPermissionSetMealId;
    /**支付金额*/
    private BigDecimal totalAmount;
    /**开始时间*/
    private Date startTime;
    /**结束时间*/
    private Date endTime;
}
