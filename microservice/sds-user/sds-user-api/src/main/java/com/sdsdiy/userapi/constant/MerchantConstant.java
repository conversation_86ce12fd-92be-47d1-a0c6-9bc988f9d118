package com.sdsdiy.userapi.constant;

import lombok.Data;

/**
 * @Author: zmy
 * @Date: 2021/6/17 14:23
 * @Description:
 */
@Data
public class MerchantConstant {
    /**商户类型，agent：独立部署，sub：子商户*/
    public static final String MERCHANT_TYPE_AGENT="agent";
    public static final String MERCHANT_TYPE_SUB="sub";
    /**活跃状态 1活跃 0不活跃 */
    public static final Integer MERCHANT_ACTIVE_STATUS_YES = 1;
    public static final Integer MERCHANT_ACTIVE_STATUS_NO = 0;

    public static final Integer MERCHANT_STATUS_YES = 1;
    public static final Integer MERCHANT_STATUS_NO = 2;

    /**
     * 0 普通用户，1 月结用户，2设计师用户
     */
    public static final Integer ACCOUNT_TYPE_NORMAL = 0;
    public static final Integer ACCOUNT_TYPE_MONTH = 1;
    public static final Integer ACCOUNT_TYPE_DESIGNER = 2;


    /**
     * 删除标记（0：正常；1：删除；2：审核；）
     */
    public static final String DEL_FLAG_NORMAL = "0";
    public static final String DEL_FLAG_DELETE = "1";
    public static final String DEL_FLAG_AUDIT = "2";

    /**
     * 余额状态
     */
    public static final int IS_YES_USE_BALANCE = 1;
    public static final int IS_NO_USE_BALANCE = 2;

    /**
     * 是否开过付费会员
     */
    public static final String OPEN_MEMBER_FLAG_YES = "yes";
    public static final String OPEN_MEMBER_FLAG_NO = "no";

    public static final Long ZIGUANG_ID = 1L;

    public final static String EVEN_MERCHANT_TURN_TO_TEST = "even_merchant_turn_to_test";
    public final static String EVEN_MERCHANT_REMOVE_TEST = "even_merchant_remove_test";


    public final static String DEFAULT_TENANT_CODE = "sdspod";

}
