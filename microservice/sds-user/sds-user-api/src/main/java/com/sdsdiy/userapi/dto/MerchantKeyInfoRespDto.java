package com.sdsdiy.userapi.dto;

import com.sdsdiy.productapi.dto.product.CumulativePriceRespDto;
import lombok.Data;

import java.util.List;

/**
 * @Author: zmy
 * @Date: 2020/9/27 14:45
 * @Description: 账号关键信息
 */
@Data
public class MerchantKeyInfoRespDto {

    private productDesignRespDto productDesign;
    private List<CumulativePriceRespDto> cumulativePrice;
    private OnePriceRespDto onePrice;
}
