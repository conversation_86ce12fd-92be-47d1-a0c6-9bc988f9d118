package com.sdsdiy.userapi.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: bin_lin
 * @date: 2021/7/6 19:45
 * @desc:
 */
@Data
public class FunctionAllocationResp {
    @NotEmpty(message = "不能为空")
    private List<Long> ids;

    @NotBlank(message = "类型不能为空")
    private String functionType;

    private List<FunctionAllocationParam> allocations;

}
