package com.sdsdiy.userapi.constant.enums;

import lombok.Getter;

/**
 * @author: bin_lin
 * @date: 2022/7/12 16:13
 * @desc: 分销物流来源
 */
public enum DistributionProductLogisticsSourceEnum {
    PRODUCT_TENANT("产品租户来源","自有物流"),
    ORDER_TENANT("订单租户来源","租户物流");
    @Getter
    private final String desc;
    @Getter
    private final String showName;

    DistributionProductLogisticsSourceEnum(String desc,String showName) {
        this.desc = desc;
        this.showName = showName;
    }

    public static boolean isTenantLogistics(String logisticsSource){
        return ORDER_TENANT.name().equalsIgnoreCase(logisticsSource);
    }

    public static boolean isSdsLogistics(String logisticsSource){
        return PRODUCT_TENANT.name().equalsIgnoreCase(logisticsSource);
    }

    public static DistributionProductLogisticsSourceEnum getByName(String name) {
        DistributionProductLogisticsSourceEnum[] values = DistributionProductLogisticsSourceEnum.values();
        for (DistributionProductLogisticsSourceEnum logisticsSourceEnum : values) {
            if (logisticsSourceEnum.name().equalsIgnoreCase(name)) {
                return logisticsSourceEnum;
            }
        }
        return null;
    }

}
