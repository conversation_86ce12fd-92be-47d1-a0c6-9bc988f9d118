package com.sdsdiy.userapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.dtoconvert.annotation.DtoBind;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import com.sdsdiy.userdata.constant.UserConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商户权限套餐(PlatformPermissionSetMealRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-10-07 10:06:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformPermissionSetMealRespDto extends BaseDTO {

    @ApiModelProperty("标识码")
    @DtoDefault()
    private String code;
    @ApiModelProperty("商品Id")
    @DtoDefault()
    private Long platformGoodsId;
    @ApiModelProperty("商品类型 1 商户权限套餐 2 商户权限增值服务 3 租户权限增值服务")
    @DtoDefault()
    private Integer setMealType;
    @ApiModelProperty("等级类型 0、免费会员可买 1、付费会员可买")
    @DtoDefault()
    private Integer levelType;
    @ApiModelProperty("会员套餐等级，0：增值，1：免费，2：创业版，3：专业版，4：企业版")
    @DtoDefault()
    private BigDecimal level;
    @ApiModelProperty("权限有效周期(天)")
    @DtoDefault()
    private Integer period;
    @ApiModelProperty("权限周期单位")
    @DtoDefault()
    private String periodUnits;
    @ApiModelProperty("单位")
    @DtoDefault()
    private String unit;
    @DtoDefault()
    @ApiModelProperty("价格")
    private BigDecimal price;
    @DtoDefault()
    @ApiModelProperty("首购价格")
    private BigDecimal firstPrice;

    @DtoDefault()
    @ApiModelProperty("特价")
    private BigDecimal salePrice;
    @DtoDefault()
    @ApiModelProperty("特价开始时间")
    private Long salePriceStartTime;
    @DtoDefault()
    @ApiModelProperty("特价结束时间")
    private Long salePriceEndTime;
    /**
     * 权限集合
     */
    @ApiModelProperty("权限列表")
    @JsonProperty("permissionItems")
    @DtoBind(selfField = "id", relateField = "platformPermissionSetMealId"
            , condition = "is_delete=0 and dev_version in (" + UserConstant.PLATFORM_GOODS_DATA_VERSION_LIST_STR + ")"
            , provider = "com.sdsdiy.userimpl.relationprovider.PlatformPermissionV2Provider")
    private List<PlatformPermissionRespDto> platformPermissionList = Lists.newArrayList();
}