package com.sdsdiy.userapi.dto.storeSettleIn.supplierSettleIn;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/22
 */
@Data
@NoArgsConstructor
@ApiModel("供应商-公司信息")
public class SupplierSettleInCompanyInfoDto {
    @ApiModelProperty(value = "主键")
    private Long id;
    
    @ApiModelProperty(value = "入驻供应商id")
    private Long supplierSettleInId;
    
    @ApiModelProperty(value = "公司名称")
    @NotEmpty(message = "公司名称不能为空")
    @Length(max = 50,  message = "公司名称长度不能超过50")
    private String companyName;
    
    @ApiModelProperty(value = "公司营业执照")
    @NotEmpty(message = "公司营业执照不能为空")
    @Length(max = 50,  message = "公司营业执照长度不能超过50")
    private String businessLicense;
    
    @ApiModelProperty(value = "公司营业执照文件")
    @NotEmpty(message = "公司营业执照文件不能为空")
    private String businessLicenseFilePath;

    @ApiModelProperty(value = "公司营业执照文件名")
    @NotEmpty(message = "公司营业执照文件名不能为空")
    private String businessLicenseFileName;
    
    @ApiModelProperty(value = "营业终止日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date terminationDate;
    
    @ApiModelProperty(value = "是否长期",notes = "0：否 1：是")
    private Integer isInfinite;
    
    @ApiModelProperty(value = "经营范围")
    @NotEmpty(message = "经营范围不能为空")
    @Length(max = 50,  message = "公司营业执照长度不能超过50")
    private String businessScope;
    
    @ApiModelProperty(value = "合作协议文件")
    private String agreementFilePath;

    @ApiModelProperty(value = "合作协议文件名")
    @NotEmpty(message = "合作协议文件名不能为空")
    private String agreementFileName;
}
