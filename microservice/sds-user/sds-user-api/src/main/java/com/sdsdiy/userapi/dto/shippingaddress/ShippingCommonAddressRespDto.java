package com.sdsdiy.userapi.dto.shippingaddress;

import com.sdsdiy.common.base.entity.dto.BaseDTO;
import lombok.Data;

/**
 * f常用发货地址记录(ShippingCommonAddress)RespDto类
 *
 * <AUTHOR>
 * @since 2022-05-18 19:13:58
 */
@Data
public class ShippingCommonAddressRespDto extends BaseDTO {
    private static final long serialVersionUID = 831081849743544716L;

    /**
     * ${column.comment}
     */
    private Long userId;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 收件人
     */
    private String consignee;
    /**
     * 国家
     */
    private String country;
    /**
     * 省
     */
    private String province;
    /**
     * 省ID
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市ID
     */
    private String cityCode;
    /**
     * 固定电话
     */
    private String cellphone;
    /**
     * 移动电话
     */
    private String mobilePhone;
    /**
     * 详细地址
     */
    private String detail;
    /**
     * 邮政编码
     */
    private String postcode;
    /**
     * ${column.comment}
     */
    private Long createTime;
    /**
     * ${column.comment}
     */
    private String md5;
    
}