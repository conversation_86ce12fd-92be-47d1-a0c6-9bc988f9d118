package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/6/28
 */
@Data
public class WalletUseBalanceRespDto {
    @ApiModelProperty("1:使用余额，2:不使用余额")
    private Integer status;
    @ApiModelProperty("余额")
    private BigDecimal balance;
    @ApiModelProperty("赠送金")
    private BigDecimal freeGold;
    @ApiModelProperty("可用余额")
    private BigDecimal useBalance;
    @ApiModelProperty("可用赠送金")
    private BigDecimal useFreeGold;
    @ApiModelProperty("支付金额")
    private BigDecimal paymentAmount;

}
