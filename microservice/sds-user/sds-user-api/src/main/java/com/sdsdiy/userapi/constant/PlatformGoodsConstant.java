package com.sdsdiy.userapi.constant;

import com.google.common.collect.Maps;
import com.sdsdiy.paymentapi.constant.SubjectContentType;

import java.util.Map;

/**
 * @Author: zmy
 * @Date: 2020/9/28 11:44
 * @Description:
 */
public class PlatformGoodsConstant {


    /**
     * 商品状态，1：上架，0：下架
     */
    public static Integer STATUS_UP = 1;
    public static Integer STATUS_DOWN = 0;


    //套餐id和商户subject对应关系，如果套餐id改变，这里需要修改
    public static Map<Long, SubjectContentType> platformGoodsIdToMerchantSubjectMap = Maps.newHashMap();

    static {
        platformGoodsIdToMerchantSubjectMap.put(2L, SubjectContentType.MERCHANT_BUY_MEMBERSHIP);
        platformGoodsIdToMerchantSubjectMap.put(3L, SubjectContentType.MERCHANT_BUY_MEMBERSHIP);
        platformGoodsIdToMerchantSubjectMap.put(9L, SubjectContentType.MERCHANT_BUY_MEMBERSHIP);
        platformGoodsIdToMerchantSubjectMap.put(14L, SubjectContentType.MERCHANT_BUY_MEMBERSHIP);

        platformGoodsIdToMerchantSubjectMap.put(4L, SubjectContentType.MERCHANT_BUY_DESIGN_COUNT);
        platformGoodsIdToMerchantSubjectMap.put(5L, SubjectContentType.MERCHANT_BUY_IMAGE_GALLERY_CAPACITY);
        platformGoodsIdToMerchantSubjectMap.put(6L, SubjectContentType.MERCHANT_BUY_PRIVATE_TEMPLATE);
        platformGoodsIdToMerchantSubjectMap.put(10L, SubjectContentType.MERCHANT_BUY_SMART_KEYING_IMAGE);
        platformGoodsIdToMerchantSubjectMap.put(11L, SubjectContentType.MERCHANT_BUY_IMAGE_CLARIFY);
        platformGoodsIdToMerchantSubjectMap.put(7L, SubjectContentType.MERCHANT_BUY_EXTRACT_KEYWORD);
        platformGoodsIdToMerchantSubjectMap.put(8L, SubjectContentType.MERCHANT_BUY_HOST);
        platformGoodsIdToMerchantSubjectMap.put(15L, SubjectContentType.MERCHANT_BUY_HASHRATE);
    }

    //套餐id和商户subject对应关系，如果套餐id改变，这里需要修改
    public static Map<Long, SubjectContentType> platformGoodsIdToTenantSubjectMap = Maps.newHashMap();

    static {
        platformGoodsIdToTenantSubjectMap.put(2L, SubjectContentType.TENANT_BUY_MEMBERSHIP_FOR_MERCHANT);
        platformGoodsIdToTenantSubjectMap.put(3L, SubjectContentType.TENANT_BUY_MEMBERSHIP_FOR_MERCHANT);
        platformGoodsIdToTenantSubjectMap.put(9L, SubjectContentType.TENANT_BUY_MEMBERSHIP_FOR_MERCHANT);
        platformGoodsIdToTenantSubjectMap.put(14L, SubjectContentType.TENANT_BUY_MEMBERSHIP_FOR_MERCHANT);

        platformGoodsIdToTenantSubjectMap.put(4L, SubjectContentType.TENANT_BUY_DESIGN_COUNT_FOR_MERCHANT);
        platformGoodsIdToTenantSubjectMap.put(5L, SubjectContentType.TENANT_BUY_IMAGE_GALLERY_CAPACITY_FOR_MERCHANT);
        platformGoodsIdToTenantSubjectMap.put(6L, SubjectContentType.TENANT_BUY_PRIVATE_TEMPLATE_FOR_MERCHANT);
        platformGoodsIdToTenantSubjectMap.put(10L, SubjectContentType.TENANT_BUY_SMART_KEYING_IMAGE_FOR_MERCHANT);
        platformGoodsIdToTenantSubjectMap.put(11L, SubjectContentType.TENANT_BUY_IMAGE_CLARIFY_FOR_MERCHANT);
        platformGoodsIdToTenantSubjectMap.put(7L, SubjectContentType.TENANT_BUY_EXTRACT_KEYWORD_FOR_MERCHANT);
        platformGoodsIdToTenantSubjectMap.put(8L, SubjectContentType.TENANT_BUY_HOST_FOR_MERCHANT);
        platformGoodsIdToTenantSubjectMap.put(12L, SubjectContentType.TENANT_BUY_ORDER_AMOUNT);
        platformGoodsIdToTenantSubjectMap.put(13L, SubjectContentType.TENANT_BUY_PUBLIC_TEMPLATE_AMOUNT);
        platformGoodsIdToTenantSubjectMap.put(15L, SubjectContentType.TENANT_BUY_CALCULATE_POINTS);
    }

}
