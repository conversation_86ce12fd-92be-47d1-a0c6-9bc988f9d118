package com.sdsdiy.userapi.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
 /**
  * 功能描述: <br>
  * @Author: lin_bin
  * @Date: 2022/3/9 16:55
  */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FunctionRestitutionParam {
     private String code;
     private String subCode;
     private Long time;
     private Long addedTime;

     public FunctionRestitutionParam(String code, Long time, Long addedTime) {
         this.code = code;
         this.time = time;
         this.addedTime = addedTime;
     }

     public FunctionRestitutionParam(String code) {
         this.code = code;
         this.time = 1L;
         this.addedTime = 0L;
     }
 }
