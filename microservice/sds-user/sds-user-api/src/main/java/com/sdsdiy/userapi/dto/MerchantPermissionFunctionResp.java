package com.sdsdiy.userapi.dto;

import com.sdsdiy.userapi.constant.MerchantPlatformPermissionConstant;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
public class MerchantPermissionFunctionResp {
    private Long merchantId;
    /**
     * 权限码
     */
    private String code;
    /**
     * 总可用次数
     */
    private Long totalValue;
    /**
     * 已使用次数
     */
    private Long usedValue;

    /**剩余可用额度*/
    private Long availableValue;
    /**
     * 权限类型 1 数次 2 容量 3 授权
     */
    private Integer type;
    /**
     * 功能分配类型
     */
    private String functionType;
//    /**
//     * 共用的totalValue值
//     */
//    private Long inCommonTotalValue;
//    /**
//     * 共用的消耗值
//     */
//    private Long inCommonUsedValue;

    /**
     * 权限状态 0 权限冻结（降级容量溢出） 1 权限可用 2 权限不可用（可用数为0等原因）
     */
    private Integer status;


    public MerchantPermissionFunctionResp(String code) {
        this.code = code;
        this.totalValue = 0L;
        this.usedValue = 0L;
//        this.inCommonTotalValue = 0L;
//        this.inCommonUsedValue = 0L;
        this.status = MerchantPlatformPermissionConstant.STATUS_UNUSABLE;
    }

    public Long getAvailableValue() {
        if(null==totalValue||totalValue==0L){
            return 0L;
        }
        if(null==usedValue||usedValue<0L){
            usedValue=0L;
        }
        return totalValue-usedValue;
    }
}
