package com.sdsdiy.userapi.constant;

import lombok.Getter;

/**
 * @author: bin_lin
 * @date: 2024/8/21 17:30
 * @desc:
 */
public interface MessagePushConstant {

    @Getter
    enum StatusEnum {
        WAIT("待发布"),
        FINISH("已发布");

        private final String desc;

        StatusEnum(String desc) {
            this.desc = desc;
        }

    }
    @Getter
    enum SendMethodEnum {
        SMS("短信"),
        WEB("站内");

        private final String desc;

        SendMethodEnum(String desc) {
            this.desc = desc;
        }

    }
}
