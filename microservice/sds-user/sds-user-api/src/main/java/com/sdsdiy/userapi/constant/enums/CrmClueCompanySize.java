package com.sdsdiy.userapi.constant.enums;

import cn.hutool.core.util.StrUtil;

/**
 * 线索-公司规模
 *
 * <AUTHOR>
 * @date 2021/8/10
 */
public enum CrmClueCompanySize {
    //
    _0(0, ""),
    _1(1, "5人内"),
    _5_10(5, "5-10人"),
    _11_50(11, "11-50人"),
    _51_100(51, "51-100人"),
    _101(101, "100人以上"),
    ;
    public final Integer value;
    public final String desc;

    CrmClueCompanySize(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static CrmClueCompanySize getByValue(String value) {
        if (StrUtil.isBlank(value)) {
            return _0;
        }
        try {
            return getByValue(Integer.parseInt(value.trim()));
        } catch (NumberFormatException e) {
            return _0;
        }
    }

    public static CrmClueCompanySize getByValue(Integer value) {
        if (value == null || value < _1.value) {
            return _0;
        } else if (value < _5_10.value) {
            return _1;
        } else if (value < _11_50.value) {
            return _5_10;
        } else if (value < _51_100.value) {
            return _11_50;
        } else if (value < _101.value) {
            return _51_100;
        } else {
            return _101;
        }
    }
}
