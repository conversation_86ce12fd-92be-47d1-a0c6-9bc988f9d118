package com.sdsdiy.userapi.constant.enums;

import com.sdsdiy.userapi.dto.crm.resp.CrmClueHistoryDTO;

/**
 * <AUTHOR>
 * @date 2021/8/2
 */
public enum CrmClueHistoryType {
    // 线索变更记录类型
    CREATE("create", "录入"),
    REGISTER("register", "注册"),
    TRANSFER("transfer", "业务员转移"),
    DEFINE("define", "界定"),
    DELETE("delete", "删除"),
    RECOVER("recover", "恢复"),
    MODIFY("modify", "修改"),
    ;
    public final String type;
    public final String desc;

    CrmClueHistoryType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public CrmClueHistoryDTO buildHistoryDto() {
        return new CrmClueHistoryDTO(this.type);
    }
}
