package com.sdsdiy.userapi.dto.storeSettleIn.supplierProfitSplitRatioConfig;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class SupplierProfitSplitRatioConfigDto {
    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "供应商入驻ID")
    private Long supplierSettleInId;
    @ApiModelProperty(value = "银联商户号（唯一）")
    private String unionpayMerchantNo;
    @ApiModelProperty(value = "业务终端号")
    private String terminalNo;
    @ApiModelProperty(value = "比例")
    private BigDecimal ratio;
}