package com.sdsdiy.userapi.constant.merchant;

import com.sdsdiy.common.base.exception.BusinessException;
import lombok.Getter;

/**
 * @author: zmy
 */
public interface MerchantUserAuthLoginConstant {

    /**
     * 授权类型，验证码：captcha,微信：wechat
     */
    @Getter
    enum AuthTypeEnum {
        CAPTCHA("captcha", "验证码"),
        WECHAT("weChat", "微信");
        private final String code;
        private final String desc;

        AuthTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static AuthTypeEnum getByCode(String code) {
            for (AuthTypeEnum authTypeEnum : AuthTypeEnum.values()) {
                if (authTypeEnum.getCode().equalsIgnoreCase(code)) {
                    return authTypeEnum;
                }
            }
            throw new BusinessException("授权登录类型不正确");
        }
    }
    @Getter
    enum RefreshTokenStatusEnum {
        WAIT("wait", "等待"),
        ONGOING("ongoing", "进行中");
        private final String code;
        private final String desc;

        RefreshTokenStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }
}
