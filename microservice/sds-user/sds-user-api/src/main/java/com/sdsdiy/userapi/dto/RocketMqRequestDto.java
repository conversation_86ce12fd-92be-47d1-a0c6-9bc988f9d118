package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class RocketMqRequestDto {
    @ApiModelProperty(value = "实例ids")
    @NotEmpty
//    @Valid
    private List<String> instanceIds;
    @ApiModelProperty(value = "创建的topic")
    private String topic;
    @ApiModelProperty(value = "创建的topics")
    private List<String> topics;
    @ApiModelProperty(value = "创建的group")
    private String group;
    @ApiModelProperty(value = "创建的groupType")
    private String groupType;
    @ApiModelProperty(value = "创建的messageType")
    private Integer messageType;
    @ApiModelProperty(value = "备注")
    private String remark;
}
