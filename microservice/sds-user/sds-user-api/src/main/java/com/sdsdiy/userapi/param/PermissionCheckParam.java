package com.sdsdiy.userapi.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @author: bin_lin
 * @date: 2021/7/29 15:22
 * @desc:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PermissionCheckParam {
    @NotNull
    private Long merchantId;
    @NotNull
    private Long userId;
    @NotBlank
    private String code;
    private String subCode;
    @NotNull
    private Long time;

}
