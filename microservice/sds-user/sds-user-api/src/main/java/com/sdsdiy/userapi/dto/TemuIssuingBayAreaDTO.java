package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * temu发货仓区域实体
 * <AUTHOR>
 * @date 2025/1/2
 */
@ApiModel("租户发货仓区域")
@Data
@AllArgsConstructor
public class TemuIssuingBayAreaDTO implements Serializable {
    @ApiModelProperty(value = "发货仓区域id")
    private Long bayAreaId;
    
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;
    
    @ApiModelProperty(value = "区域地址")
    private String address;
    
    @ApiModelProperty("发货仓列表")
    private List<TemuIssuingBayDTO> issuingBayList;

}
