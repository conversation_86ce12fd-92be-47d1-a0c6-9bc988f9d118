package com.sdsdiy.userapi.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.models.auth.In;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/6/28
 */
@Data
public class PaymentOldDto {

    private Long id;
    @NotBlank
    private String account;
    @NotBlank
    private String no;
    @NotBlank
    private String outNo;
    @NotBlank @Length(max=8)
    private String method;
    @NotBlank @Length(max=1024)
    private String imgUrl;
    @NotNull @Max(127)
    private Integer status;
    private Long createdTime;
    @NotNull
    private Long payTime;
    private Double totalAmount;
    private Integer payType;
    private Double balance;
    private Double freeGold;
    private Double refundBalance;
    private Double refundFreeGold;
    private Double refundTotalAmount;
    private Long merchantId;
    private Integer addConsume;
    private Long userId;
    private String commitCode;
}
