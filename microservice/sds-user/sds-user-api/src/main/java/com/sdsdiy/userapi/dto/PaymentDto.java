package com.sdsdiy.userapi.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Deprecated
public class PaymentDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 数量（废弃）
     */
    private String account;

    private String subject;

    /**
     * 编号
     */
    private String no;

    /**
     * 第三方订单号
     */
    private String outNo;

    /**
     * 支付方式
     */
    private String method;

    /**
     * 图片url
     */
    private String imgUrl;

    /**
     * 状态，1:未支付，2:已支付，99:无效
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createdTime;

    /**
     * 支付时间
     */
    private Long payTime;

    /**
     * 总金额(支付那个时刻的金额，如果后续订单变动不更改)
     */
    private BigDecimal totalAmount;

    /**
     * 1商品下单，2支付，3充值，4订单物流修改支付，5：fba订单物流支付，6：批量更新无路，7:更新订单，8:素材订单支付，9:官方素材设计支付
     * 1、商品支付类型，2、会员支付，3、商户充值，4、订单物流修改支付，5、fba订单物流支付，6、批量更新物流，7、更新订单，8、租户充值，9、租户会员支付类型
     */
    private Integer payType;

    /**
     * 需要支付的金额
     */
    private BigDecimal balance;

    /**
     * 需要支付的赠送金
     */
    private BigDecimal freeGold;

    /**
     * 已退款的余额(原先用于记录订单退款金额，废弃)
     */
    private BigDecimal refundBalance;

    /**
     * 已退款的赠送金(原先用于记录退款金额，废弃)
     */
    private BigDecimal refundFreeGold;

    /**
     * 退款的总金额(原先用于记录退款金额，废弃)
     */
    private BigDecimal refundTotalAmount;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 是否添加到商户年度消费
     */
    private Integer addConsume;

    /**
     * 操作者id
     */
    private Long userId;

    /**
     * 支付的租户ID
     */
    private Long sourceTenantId;

    /**
     * 支付的商户ID
     */
    private Long sourceMerchantId;

    /**
     * 支付的用户ID
     */
    private Long sourceUserId;

    /**
     * 收款的租户ID
     */
    private Long targetTenantId;

    /**
     * 收款的商户ID
     */
    private Long targetMerchantId;

    /**
     * 支付的角色，merchant-商户，tenant-租户，saas-saas
     */
    private String sourceRole;

    /**
     * 收款的角色，merchant-商户，tenant-租户，saas-saas
     */
    private String targetRole;

    /*** 商户余额支付使用的余额类型 */
    private Integer merchantBalanceUsedType;

    /**
     * BillSpecificPurposeEnum枚举类型
     */
    private String specificPurpose;
    /**
     * PurposeType
     */
    private String PurposeType;


}
