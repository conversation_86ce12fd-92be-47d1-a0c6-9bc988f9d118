package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

/**
 * 租户表(TenantReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2021-10-20 15:42:05
 */
@Data
public class TenantFactoryNumRespDto {
    /**
     * 主键id
     */
    @DtoDefault()
    private Long id;
    /**
     * 租户名称
     */
    @DtoDefault()
    private String name;
    /**
     * 主商户id
     */
    @DtoDefault()
    private Long merchantId;
    /**
     * 可用工厂数
     */
    @DtoDefault()
    private Integer factoryNum;
    /**
     * 已经使用用工厂数
     */
    @DtoDefault()
    private Integer usedFactoryNum;

}