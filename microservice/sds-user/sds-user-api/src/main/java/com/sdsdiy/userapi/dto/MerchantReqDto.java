package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * (MerchantReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-07-07 13:46:40
 */
@Data
public class MerchantReqDto implements Serializable {

    @DtoDefault
    private Long id;

    @DtoDefault
    private String name;
    @DtoDefault
    private String contactName;
    @DtoDefault
    @NotBlank(message = "手机号不能为空")
    private String contactTel;
    @DtoDefault
    private Long createdTime;
    @DtoDefault
    private String host;
    @DtoDefault
    private Object status;
    @DtoDefault
    private String salt;
    @DtoDefault
    @NotBlank(message = "商户号不能为空")
    private String code;
    @DtoDefault
    private Integer deploymentPatterns;
    @DtoDefault
    private Long packageValidity;
    @DtoDefault
    private Long lastUseTime;
    @DtoDefault
    private String adminAccount;
    @DtoDefault
    private String adminPasswd;
    @DtoDefault
    private String adminPasswdSalt;
    @DtoDefault
    private Integer isPermanentPackage;
    @DtoDefault
    private Object activeStatus;
    @DtoDefault
    @NotBlank(message = "密码不能为空")
    private String password;
    @DtoDefault
    private Object supplyType;
    @DtoDefault
    private Integer isAnalyze;
    @DtoDefault
    private Object accountType;
    @DtoDefault
    private Object isInsider;
    @DtoDefault
    private Double balance;
    @DtoDefault
    private Object freeGold;
    @DtoDefault
    private Long lastByGoodsTime;
    @DtoDefault
    private Double consumeTotal;
    @DtoDefault
    private String channel;
    @DtoDefault
    private String merchantNo;

    @NotBlank(message = "验证码不能为空")
    private String verifyCode;

    @NotBlank(message = "电话国家码不能为空")
    private String phoneCountryCode;

    private Boolean checkVerifyCode;

    /**租户id*/
    private Long tenantId;
    /**主商户id*/
    private Long mainMerchantId;
    /**租户是否开启线上支付*/
    private Integer tenantOpenOnlinePay;

    /**
     * 是否禁用  1是
     * 只有租户自主注册商户才有可能禁用
     */
    private Integer disable;

    /**
     * 商户当前会员等级
     */
    private BigDecimal memberLevel;
}