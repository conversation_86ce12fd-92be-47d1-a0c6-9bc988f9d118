package com.sdsdiy.userapi.constant.enums;

import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 线索意向度
 *
 * <AUTHOR>
 * @date 2021/7/29
 */
public enum CrmClueIntentionLevel {
    //
    S("S(会员)"),
    A("A(高意向)"),
    B("B(中意向)"),
    C("C(低意向)"),
    D("D(搁置)"),
    ;


    public final String desc;

    CrmClueIntentionLevel(String desc) {
        this.desc = desc;
    }

    private static final Map<String, CrmClueIntentionLevel> LEVEL_MAP;

    static {
        LEVEL_MAP = new HashMap<>(5);
        for (CrmClueIntentionLevel level : CrmClueIntentionLevel.values()) {
            LEVEL_MAP.put(level.name(), level);
        }
    }

    public static String getDescByLevel(String level) {
        CrmClueIntentionLevel intentionLevel = LEVEL_MAP.get(level);
        return intentionLevel == null ? "" : intentionLevel.desc;
    }

    public static String checkType(String in) {
        if (StrUtil.isBlank(in)) {
            return "";
        }
        String level = ReUtil.get(PatternPool.WORD, in, 0);
        if (StrUtil.isBlank(level)) {
            return "";
        }
        boolean contains = LEVEL_MAP.containsKey(level.toUpperCase());
        return contains ? level.toUpperCase() : "";
    }
}
