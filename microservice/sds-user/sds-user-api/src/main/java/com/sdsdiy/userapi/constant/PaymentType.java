package com.sdsdiy.userapi.constant;

/**
 * <AUTHOR>
 * @date 2021/6/28
 */
@Deprecated
public enum PaymentType {
    GOODS_ORDER(1,"商品支付类型"),
    PLATFORM_GOODS_ORDER(2,"会员支付类型"),
    RECHARGE(3,"充值"),
    PLATFORM_MODIFY_ORDER(4,"订单物流修改支付"),
    PLATFORM_PAY_FBA_ORDER_CARRIAGE(5,"FBA订单物流支付"),
    PLATFORM_PAY_BATCH_UPDATE_LOGISTICS(6,"批量更新物流"),
    UPDATE_ORDER(7,"更新订单"),
    ;
    Integer status;
    String desc;

    PaymentType(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
    public Integer getStatus() {
        return status;
    }
}
