package com.sdsdiy.userapi.dto.shippingaddress;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * f常用发货地址记录(ShippingCommonAddress)ReqDto类
 *
 * <AUTHOR>
 * @since 2022-05-18 19:13:58
 */
@Data
public class ShippingCommonAddressReqDto implements Serializable {
    private static final long serialVersionUID = 268413718680608748L;

    private Long id;

    private Long userId;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 收件人
     */
    @ApiModelProperty("发货人")
    private String consignee;
    /**
     * 国家
     */
    @ApiModelProperty("国家")
    private String country;
    /**
     * 省
     */
    @ApiModelProperty("省份")
    private String province;
    /**
     * 省ID
     */
    @ApiModelProperty("省份code")
    private String provinceCode;
    /**
     * 市
     */
    @ApiModelProperty("城市")
    private String city;
    /**
     * 市ID
     */
    @ApiModelProperty("城市code")
    private String cityCode;
    /**
     * 固定电话
     */
    @ApiModelProperty("固话")
    private String cellphone;
    /**
     * 移动电话
     */
    @ApiModelProperty("手机号码")
    @Size(max = 64, message = "移动电话长度最长64")
    private String mobilePhone;
    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String detail;
    /**
     * 邮政编码
     */
    @ApiModelProperty("邮编")
    private String postcode;

    private Long createTime;

    private String md5;

}