package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.util.Date;

/**
 * 商户权限(PlatformPermissionRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-10-07 10:34:55
 */
@Data
public class PlatformPermissionRespDto extends BaseDTO {

    private Long platformPermissionSetMealId;
    @DtoDefault()
    private String code;
    @DtoDefault()
    private Integer type;
    @DtoDefault()
    private Integer isIncrease;
    @DtoDefault()
    private Integer isPeriod;
    @DtoDefault()
    private Integer value;
    @DtoDefault()
    private Integer multiple;
    @DtoDefault()
    private String unit;
    @DtoDefault()
    private Integer periodDay;
    @DtoDefault()
    private String periodUnits;
    private Integer isDelete;
    private Date createTime;
    /**
     * 开发版本，高于此版本的数据线上不显示
     */
    @DtoDefault()
    private Integer devVersion;
}