package com.sdsdiy.userapi.constant.user;

import com.sdsdiy.common.base.helper.Assert;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/05/17 15:14
 **/
public interface MerchantUserDeleteDataHistoryConstant {
    @Getter
    enum TypeEnum {
        DESIGN_PRODUCT(MerchantUserDeleteHistoryConstant.DataTypeEnum.MATERIAL_DESIGN_PRODUCT.getValue(),"designProduct", "成品", Boolean.FALSE),
        THEME(MerchantUserDeleteHistoryConstant.DataTypeEnum.MATERIAL_DESIGN_PRODUCT.getValue(),"theme", "主题", Boolean.TRUE),
        MATERIAL(MerchantUserDeleteHistoryConstant.DataTypeEnum.MATERIAL_DESIGN_PRODUCT.getValue(),"material", "素材", Boolean.TRUE),

        PRODUCT_TEMPLATE(MerchantUserDeleteHistoryConstant.DataTypeEnum.STORE_AND_ORDER.getValue(),"productTemplate", "产品模版", Boolean.FALSE),
        PRICING_TEMPLATE(MerchantUserDeleteHistoryConstant.DataTypeEnum.STORE_AND_ORDER.getValue(),"pricingTemplate", "定价模版", Boolean.FALSE),
        STORE_ORDER(MerchantUserDeleteHistoryConstant.DataTypeEnum.STORE_AND_ORDER.getValue(),"storeOrder", "店铺及订单", Boolean.FALSE),
        ;
        private final String dataType;
        private final String value;
        private final String desc;
        /**
         * 删除的时候，需要基于出单数据
         */
        private final Boolean deleteNeedOrderedData;

        TypeEnum(String dataType,String value, String desc, Boolean deleteNeedOrderedData) {
            this.dataType = dataType;
            this.value = value;
            this.desc = desc;
            this.deleteNeedOrderedData = deleteNeedOrderedData;
        }

        public static TypeEnum getByValue(String value) {
            TypeEnum[] values = TypeEnum.values();
            for (TypeEnum resultEnum : values) {
                if (resultEnum.getValue().contains(value)) {
                    return resultEnum;
                }
            }
            return null;
        }

        public static List<TypeEnum> getByDataType(String dataType) {
            TypeEnum[] values = TypeEnum.values();
            return Arrays.stream(values).filter(i -> i.getDataType().equals(dataType)).collect(Collectors.toList());
        }
    }

    enum StatusEnum {
        NONE("none", "无"),
        WAIT("wait", "等待"),
        ONGOING("ongoing", "处理中"),
        SUCCESS("success", "成功"),
        FAIL("fail", "失败"),
        ;
        @Getter
        private final String value;
        private final String desc;

        StatusEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static StatusEnum getByValue(String value) {
            StatusEnum[] values = StatusEnum.values();
            for (StatusEnum resultEnum : values) {
                if (resultEnum.getValue().contains(value)) {
                    return resultEnum;
                }
            }
            return null;
        }

        public static String getInitStatus(String type, boolean isDelete) {
            TypeEnum typeEnum = TypeEnum.getByValue(type);
            Assert.validateTrue(null == typeEnum, "数据错误");
            if (isDelete && typeEnum.deleteNeedOrderedData) {
                return StatusEnum.NONE.getValue();
            }
            return StatusEnum.WAIT.getValue();
        }
    }
}
