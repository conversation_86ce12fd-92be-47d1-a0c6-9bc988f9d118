package com.sdsdiy.userapi.dto.platformgoods;

import lombok.Data;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 租户权限套餐记录(PlatformGoodOrderRel)ReqDto类
 *
 * <AUTHOR>
 * @since 2021-11-08 20:20:58
 */
@Data
public class PlatformGoodOrderRelReqDto implements Serializable {
    private static final long serialVersionUID = -71547402234706207L;
    /**
    * 主键
    */
    private Long id;
    /**
    * 平台商品订单id
    */
    private Long platformGoodOrderId;
    /**
    * 租户平台权限id
    */
    private Long tenantPlatformPermissionId;
    /**
    * 数值
    */
    private Integer value;
    /**
    * 倍数(为适应产品变态显示设计)
    */
    private Integer multiple;
    /**
    * 数值x倍数
    */
    private Long totalValue;

}