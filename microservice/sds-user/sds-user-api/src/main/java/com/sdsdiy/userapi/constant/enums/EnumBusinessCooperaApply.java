package com.sdsdiy.userapi.constant.enums;

/**
 * @description:
 * @Auther: zmy
 * @Date: 2021/9/7 15:34
 */
public enum EnumBusinessCooperaApply {
    //1.生产商 2.服务商 3.独立部署 4.POD商户

    PRODUCT_PROVIDER(EnumBusinessCooperaApplyType.PRODUCT_PROVIDER,1, ""),
    SERVICE_PROVIDER(EnumBusinessCooperaApplyType.SERVICE_PROVIDER,2, ""),
    AGENT(EnumBusinessCooperaApplyType.AGENT,3, ""),
    POD_360(EnumBusinessCooperaApplyType.POD,4, "360"),
    POD_BAIDU(EnumBusinessCooperaApplyType.POD,5, "baidu"),
    POD_SHENMA(EnumBusinessCooperaApplyType.POD,6, "shenma"),
    POD_SOUGOU(EnumBusinessCooperaApplyType.POD,7, "sougou"),
    LADY(EnumBusinessCooperaApplyType.LADY,8, ""),
    AGENT_BAIDU(EnumBusinessCooperaApplyType.AGENT,9,"baidu"),
    AGENT_SOCIALMEDIA(EnumBusinessCooperaApplyType.AGENT,10,"socialmedia"),
            ;

    private EnumBusinessCooperaApplyType typeEnum;
    private int sort;
    private String channel;

    EnumBusinessCooperaApply(EnumBusinessCooperaApplyType typeEnum,int sort, String channel) {
        this.typeEnum = typeEnum;
        this.sort = sort;
        this.channel = channel;
    }
    public static EnumBusinessCooperaApply getByTypeEnum(EnumBusinessCooperaApplyType typeEnum) {
        for (EnumBusinessCooperaApply enumBusinessCooperaApply : values()) {
            if(enumBusinessCooperaApply.getTypeEnum().equals(typeEnum)){
                return enumBusinessCooperaApply;
            }
        }
        return null;
    }

    public EnumBusinessCooperaApplyType getTypeEnum() {
        return typeEnum;
    }

    public void setTypeEnum(EnumBusinessCooperaApplyType typeEnum) {
        this.typeEnum = typeEnum;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}
