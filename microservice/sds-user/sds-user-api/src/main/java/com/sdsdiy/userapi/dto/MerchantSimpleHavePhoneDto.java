package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/7/7
 */
@Data
public class MerchantSimpleHavePhoneDto {
    @DtoDefault()
    @ApiModelProperty("商户id")
    private Long id;
    @DtoDefault()
    @ApiModelProperty("商户名称")
    private String name;
    @DtoDefault()
    @ApiModelProperty("商户号")
    private String merchantNo;
    @DtoDefault()
    @ApiModelProperty("手机号")
    private String contactTel;
    @ApiModelProperty("隐藏的手机号")
    private String contactTelHidden;

}
