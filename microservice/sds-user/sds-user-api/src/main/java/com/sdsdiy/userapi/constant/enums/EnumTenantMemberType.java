package com.sdsdiy.userapi.constant.enums;

import com.google.common.collect.Lists;
import com.sdsdiy.userapi.constant.PlatformSetMealConstant;

import java.util.List;

/**
 * @description:
 * @Auther: zmy
 * @Date: 2021/10/21 16:29
 */
public enum EnumTenantMemberType {

    SET_MEAL_TYPE_MEMBER(Lists.newArrayList(PlatformSetMealConstant.SET_MEAL_TYPE_MEMBER), "member"),
    SET_MEAL_TYPE_ADD_SERVICE(Lists.newArrayList(PlatformSetMealConstant.SET_MEAL_TYPE_ADD_SERVICE,PlatformSetMealConstant.SET_MEAL_TYPE_TENANT_ADD_SERVICE), "addService"),
            ;
    public final List<Integer> setMealTypes;
    public final String value;

    EnumTenantMemberType(List<Integer> setMealTypes, String value) {
        this.setMealTypes = setMealTypes;
        this.value = value;
    }

    public static EnumTenantMemberType getEnumBySetMealType(Integer setMealType) {
        EnumTenantMemberType[] values = EnumTenantMemberType.values();
        for (EnumTenantMemberType enumTenantMemberType : values) {
            if (enumTenantMemberType.getSetMealTypes().contains(setMealType)) {
                return enumTenantMemberType;
            }
        }
        return null;
    }

    public List<Integer> getSetMealTypes() {
        return setMealTypes;
    }

    public String getValue() {
        return value;
    }
}
