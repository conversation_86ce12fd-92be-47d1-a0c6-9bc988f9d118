package com.sdsdiy.userapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: zmy
 * @Date: 2020/10/8 13:36
 * @Description:
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class GenSetMealParamReqDto {
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;
    /**
     * 订单ID
     */
    @NotNull(message = "platformGoodOrderId不能为空")
    private Long platformGoodOrderId;
    /**
     * 商品ID
     */
    @NotNull(message = "platformGoodsId不能为空")
    private Long platformGoodsId;
    /**
     * 套餐id
     */
    @NotNull(message = "setMealId不能为空")
    private Long platformPermissionSetMealId;
    @NotNull(message = "数量不能为空")
    private Integer num;
    /**
     * 周期数(增值套餐专用)
     */
    @NotNull(message = "numPeriod不能为空")
    private Integer numPeriod;

    private Date startTime;
    private Date endTime;

    /**
     * 1：续费，2：升级
     */
    @NotNull(message = "optionType不能为空")
    private Integer optionType;

    @JsonProperty("total_amount")
    private BigDecimal totalAmount;

    private String adminUserName;

    /**
     * platform_permission_v2的code
     */
    private String code;

    public GenSetMealParamReqDto(Integer num, Integer numPeriod) {
        this.num = num;
        this.numPeriod = numPeriod;
    }
}
