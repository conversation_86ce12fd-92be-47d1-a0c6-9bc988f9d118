package com.sdsdiy.userapi.constant.enums;

/**
 * @description:
 * @Auther: zmy
 * @Date: 2021/9/7 15:34
 */
public enum EnumBusinessCooperaApplyType {
    //1.生产商 2.服务商 3.独立部署 4.POD商户

    PRODUCT_PROVIDER(1, "生产商"),
    SERVICE_PROVIDER(2, "服务商"),
    AGENT(3, "独立部署"),
    POD(4, "POD商户"),
    LADY(5, "女装加盟"),
            ;

    private int type;
    private String description;

    EnumBusinessCooperaApplyType(int type, String description) {
        this.type = type;
        this.description = description;
    }
    public static EnumBusinessCooperaApplyType getByType(int type) {
        for (EnumBusinessCooperaApplyType enumBusinessCooperaApply : values()) {
            if(enumBusinessCooperaApply.getType()==type){
                return enumBusinessCooperaApply;
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
