package com.sdsdiy.userapi.constant.enums;

import com.sdsdiy.userapi.dto.storeSettleIn.supplierSettleIn.SupplierSettleInTypeEnumDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 供应商类型枚举
 * <AUTHOR>
 * @date 2025/5/27
 */

@AllArgsConstructor
@Getter
public enum SupplierSettleInTypeEnum {

    TECHNICAL_SUPPORT("technical_support",  "技术支持"),
    MATERIAL_SUPPLY("material_supply",  "商品原料供应"),
    LOGISTICS("logistics_transportation",  "物流运输"),
    MODEL_SUPPORT("model_support",  "建模支持"),
    FACTORY("factory",  "工厂")
    ;
    
    private final String code;
    private final String name;
    
    public static List<SupplierSettleInTypeEnumDto> getTypeList(){
        SupplierSettleInTypeEnum[] values = SupplierSettleInTypeEnum.values();
        List<SupplierSettleInTypeEnumDto> typeList = new ArrayList<>();
        for (SupplierSettleInTypeEnum value : values){
            SupplierSettleInTypeEnumDto supplierSettleInTypeEnumDto = new SupplierSettleInTypeEnumDto();
            supplierSettleInTypeEnumDto.setCode(value.getCode());
            supplierSettleInTypeEnumDto.setName(value.getName());
            typeList.add(supplierSettleInTypeEnumDto);
        }
        return typeList;
    }
    
    
}
