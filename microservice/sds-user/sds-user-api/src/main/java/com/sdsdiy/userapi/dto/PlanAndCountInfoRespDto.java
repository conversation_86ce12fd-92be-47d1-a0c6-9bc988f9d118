package com.sdsdiy.userapi.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * @Author: zmy
 * @Date: 2020/9/30 10:30
 * @Description:
 */
@Data
public class PlanAndCountInfoRespDto {

    private List<Long> merchantPlatformPermissionDtoIds = Lists.newArrayList();
    private List<PlanRespDto> planRespDtos = Lists.newArrayList();
    private Long totalCount = 0L;
    private Long usedCount = 0L;
    private Long noUsedCount = 0L;
}
