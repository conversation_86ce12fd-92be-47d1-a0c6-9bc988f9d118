package com.sdsdiy.userapi.dto.storeSettleIn.supplierSettleIn.req;

import com.sdsdiy.userapi.dto.storeSettleIn.supplierSettleIn.SupplierSettleInCompanyInfoDto;
import com.sdsdiy.userapi.dto.storeSettleIn.supplierSettleIn.SupplierSettleInIndividualInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2025/5/22
 */
@Data
@NoArgsConstructor
public class SupplierSettleInReqParam {
    @ApiModelProperty(value = "供应商名称")
    @NotEmpty(message = "供应商名称不能为空")
    @Length(max = 50,message = "供应商名称长度不能超过50")
    private String name;
    @NotEmpty(message = "供应商类型不能为空")
    @ApiModelProperty(value = "供应商类型")
    private String type;
    @ApiModelProperty(value = "公司类型",notes = "individual-个人 company-企业")
    private String companyType;
    @ApiModelProperty(value = "是否主商户",notes = "0：否 1：是")
    private Integer mainFlag;
    @ApiModelProperty(value = "公司信息",notes = "公司类型为individual时，该字段不传")
    private SupplierSettleInCompanyInfoDto companyInfo;
    @ApiModelProperty(value = "法人信息",notes = "公司类型为company时，该字段不传")
    private SupplierSettleInIndividualInfoDto individualInfo;
    private Long createUid;
    private Long updateUid;
}
