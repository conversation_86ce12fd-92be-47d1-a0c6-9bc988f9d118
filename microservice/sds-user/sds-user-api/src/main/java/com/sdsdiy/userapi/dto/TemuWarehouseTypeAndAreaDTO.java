package com.sdsdiy.userapi.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * temu发货仓类型和仓库面积列表DTO
 * <AUTHOR>
 * @date 2025/1/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemuWarehouseTypeAndAreaDTO {

    /**
     * 仓库面积列表
     */
    private List<Map<String,String>> warehouseAreaList;
    /**
     * 仓库类型列表
     */
    private List<Map<String,String>> issingBayTypeList;
    
    
}
