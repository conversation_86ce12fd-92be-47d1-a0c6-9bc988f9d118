package com.sdsdiy.userapi.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import com.sdsdiy.userapi.constant.EnumOperationHistory;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/21
 */
@Data
public class OperationHistoryMsg  {
    String type;
    String remark;
    Long merchantId;
    Long userId;
    String content;
    Date notifyTime;

    public OperationHistoryMsg() {
    }

    public OperationHistoryMsg(EnumOperationHistory enumOperationHistory, String remark, Long merchantId, Long userId, String content, Date notifyTime) {
        this.type = enumOperationHistory.getType();
        this.remark = remark;
        this.merchantId = merchantId;
        this.userId = userId;
        this.content = content;
        this.notifyTime = notifyTime;
    }
}
