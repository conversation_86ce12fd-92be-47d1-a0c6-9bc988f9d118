package com.sdsdiy.userapi.dto.merchantstore;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 店铺(MerchantStoreReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-11-16 13:47:54
 */
@Data
public class MerchantStoreSaveParam implements Serializable {

    private Long merchantId;

    @ApiModelProperty(value = "部门ID")
    private Long merchantSysOfficesId;
    @NotNull(message = "平台码不能为空")
    @ApiModelProperty(value = "平台码")
    private String merchantStorePlatformCode;
    @NotNull(message = "创建用户ID不能为空")
    @ApiModelProperty(value = "创建用户ID")
    private Long creatorId;
    @NotNull(message = "店铺名(同平台不重名)不能为空")
    @ApiModelProperty(value = "店铺名(同平台不重名)")
    private String name;
    @NotNull(message = "店铺代号不能为空")
    @ApiModelProperty(value = "店铺代号")
    private String code;

    @ApiModelProperty(value = "站点系")
    private String siteDepartment;
    @ApiModelProperty(value = "站点")
    private String site;
    @ApiModelProperty(value = "亚马逊授权")
    private Integer amzAuthorized;
    @ApiModelProperty(value = "亚马逊授权状态:0 授权失败 1未授权 2授权成功 3 授权失效")
    private Integer amzAuthorizedStatus;
    @ApiModelProperty(value = "自动同步订单")
    private Integer orderSync;
    @ApiModelProperty(value = "自动同步运单号")
    private Integer trackingNumberSync;
    @ApiModelProperty(value = "同步运单号方式: 1 运单号生成后立即同步运单号 2 运单号生成后第二天当地时间 3 产品发出时同步运单号4物流有跟踪数据后同步运单号")
    private Integer trackingNumberSyncMode;
    @ApiModelProperty(value = "同步运单号时间")
    private Long trackingNumberSyncTime;
    @ApiModelProperty(value = "Seller ID")
    private String sellerId;
    @ApiModelProperty(value = "MWS Auth Token")
    private String mwsAuthToken;
    @ApiModelProperty(value = "0 禁用 1 正常 ")
    private Integer status;
    @ApiModelProperty(value = "系统店铺: 0 否 1 是")
    private Integer isSystem;
    @ApiModelProperty(value = "速卖通发货地址")
    private Long aliexpressSenderAddressId;
    @ApiModelProperty(value = "速卖通退款地址")
    private Long aliexpressRefundAddressId;
    @ApiModelProperty(value = "速卖通发货失败的类型RETURN 退回 DESTROY 销毁")
    private String sendFailHandle;
    @ApiModelProperty(value = "1代表速卖通新的账号")
    private Integer newAliexpressAccount;
    @ApiModelProperty(value = "过期时间")
    private Long authExpireTime;
    @ApiModelProperty(value = "速卖通产品宝授权状态 1未授权 2授权成功 3授权失效")
    private Integer aliProductStatus;
    @ApiModelProperty(value = "负责人id 主关联id")
    private Long inChargeUserId;
    @ApiModelProperty(value = "授权时间，秒")
    private Long authTime;
    private String orderSyncType;

    @ApiModelProperty(value = "是否半托管")
    private Integer isPopChoice;

    @ApiModelProperty(value = "货币类型", notes = "CNY,USD", required = false)
    private String currency;

    @ApiModelProperty(value = "运单号生成时机")
    private String carriageApplyType;

    private String clientId;
    private String clientSecret;
    private Boolean autoAckOrder;
    private String type;
    private Long onlineLogisticsLimitTime;
    private String skuType;
}
