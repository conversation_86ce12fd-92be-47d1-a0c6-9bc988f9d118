package com.sdsdiy.userapi.constant.merchantuseraccount;

/**
 * @author: bin_lin
 * @date: 2024/8/5 11:06
 * @desc:
 */
public interface MerchantUserFunctionLimitConstant {
    String TYPE_FIXED = "FIXED";
    String TYPE_DAILY = "DAILY";
    String SOURCE_INIT = "INIT";
    String SOURCE_OLD_INIT = "OLD_INIT";
    String SOURCE_MERCHANT = "MERCHANT";
    String MERCHANT_FUNCTION_INIT_LOCK_KEY_PREFIX = "merchant_function_init_lock_key:";
    String MERCHANT_FUNCTION_INIT_ALL_MERCHANT_LOCK_KEY = "merchant_function_init_all_merchant_lock_key";

}
