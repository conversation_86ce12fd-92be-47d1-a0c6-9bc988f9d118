package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PermissionStatMessageDto {

    private Double count;
    private Long merchantPlatformPermissionId;
    private String code;
    private String unit;
    private Long merchantId;
    private Long userId;

    @ApiModelProperty(value = "额外要统计的功能code")
    private String statCode;
    @ApiModelProperty(value = "额外要统计的功能计数")
    private BigDecimal statCount;

}