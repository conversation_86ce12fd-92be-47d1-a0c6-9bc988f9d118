package com.sdsdiy.userapi.constant;

/**
 * <AUTHOR>
 * @date 2021/6/22
 */
public class DesignerConstant {

    public final static String REGITSTER = "DESIGNER_REGITSTER";
    public final static String RESET_PASSWORD = "RESET_PASSWORD";

    public final static String USER_MASTER = "master";
    public final static String USER_SLAVE = "slave";

    public final static String BOSS = "boss";

    public final static String MENU_TYPE_PERMIT = "permit";
    public final static String MENU_TYPE_MENU = "menu";

    public final static String DATA_SCOPE_PERSONAL = "personal";
    public final static String DATA_SCOPE_DEPT = "dept";
    public final static String DATA_SCOPE_DEPTANDSUB = "deptAndSub";
    public final static String DATA_SCOPE_COMPANY = "company";

    public final static Integer IS_DELETE_YES = 1;
    public final static Integer IS_DELETE_NO = 0;

    /**
     * 审核状态
     */
    public enum certificationStatus {
        notAudit("notAudit", "未审核"),
        waitAudit("waitAudit", "待审核"),
        pass("pass", "通过"),
        notPass("notPass", "不通过");

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }

        private String name;
        private String desc;

        certificationStatus(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }

        public static String getDescByName(String name) {
            certificationStatus[] values = certificationStatus.values();
            for (int i = 0; i < values.length; i++) {
                if (values[i].getName().equals(name)) {
                    return values[i].getDesc();
                }
            }
            return null;
        }
    }


    /**
     * 身份审核状态
     */
    public enum cardAuthStatus {
        fail("fail", "认证失败"),
        pass("pass", "认证通过"),
        notPass("notPass", "认证不通过");

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }

        private String name;
        private String desc;

        cardAuthStatus(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }

        public static String getDescByName(String name) {
            certificationStatus[] values = certificationStatus.values();
            for (int i = 0; i < values.length; i++) {
                if (values[i].getName().equals(name)) {
                    return values[i].getDesc();
                }
            }
            return null;
        }
    }


    /**
     * 提交审核类型
     */
    public enum studioType {

        personal("personal", "个人"),
        company("company", "公司");

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }

        private String name;
        private String desc;

        studioType(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }

        public static String getDescByName(String name) {
            certificationStatus[] values = certificationStatus.values();
            for (int i = 0; i < values.length; i++) {
                if (values[i].getName().equals(name)) {
                    return values[i].getDesc();
                }
            }
            return null;
        }
    }


    /**
     * 目前的权限功能细粒度到菜单
     * 功能权限
     */
    public enum permitEnum {
        material(1L, "素材市场", "designer:material"),
        requirement(2L, "需求定制", "designer:requirement"),
        bill(3L, "账单", "designer:bill"),
        mine(4L, "我的", ""),
        set(5L, "设置", ""),

        user(6L, "用户管理", "designer:user"),
        role(7L, "角色管理", "designer:role"),
        dept(8L, "部门管理", "designer:dept");

        public String getPermit() {
            return permit;
        }

        public String getName() {
            return name;
        }

        public Long getId() {
            return id;
        }

        private String permit;
        private String name;
        private Long id;

        permitEnum(Long id, String name, String permit) {
            this.id = id;
            this.name = name;
            this.permit = permit;
        }
    }


}
