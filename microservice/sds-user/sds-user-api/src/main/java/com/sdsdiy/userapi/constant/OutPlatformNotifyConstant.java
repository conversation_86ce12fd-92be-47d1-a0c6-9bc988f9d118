package com.sdsdiy.userapi.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @program: microservice-parent
 * @Package com.sdsdiy.userapi.constant
 * @Description: TODO
 * @date 2021/7/6 16:23
 */
public class OutPlatformNotifyConstant {

    public final static String EVENT_TYPE_ORDER_FINISH = "order_finish";
    public final static String EVENT_TYPE_ORDER_LOGISTICS_UPDATE = "logistics_update";

    public final static String URI_ORDER_FINISH = "order_finish";




    public final static Long NOTIFY_MERCHANT_ZIGUANG = 1L;
    public final static Long NOTIFY_MERCHANT_CMS = 51L;
    public static boolean isNotifyMerchant(Long merchantId){
        if(NOTIFY_MERCHANT_CMS.equals(merchantId)){
            return true;
        }
        return NOTIFY_MERCHANT_ZIGUANG.equals(merchantId);
    }

}
