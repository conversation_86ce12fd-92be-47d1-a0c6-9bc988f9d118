package com.sdsdiy.userapi.constant;

import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class MerchantStoreConstant {

    /**
     * 店铺授权成功事件
     */
    public final static String MERCHANT_STORE_AUTH_EVENT = "MERCHANT_STORE_AUTH_EVENT";

    public static final Map<String, Integer> COUNT_SORT = new HashMap<>();

    static {
        COUNT_SORT.put(MerchantStorePlatformEnum.AMZ.getCode(), 1);
        COUNT_SORT.put(MerchantStorePlatformEnum.SELL_FAST.getCode(), 20);
        COUNT_SORT.put(MerchantStorePlatformEnum.TEMU.getCode(), 30);
        COUNT_SORT.put(MerchantStorePlatformEnum.DHGATE.getCode(), 40);
        COUNT_SORT.put(MerchantStorePlatformEnum.ETSY.getCode(), 50);
        COUNT_SORT.put(MerchantStorePlatformEnum.WISH.getCode(), 60);
        COUNT_SORT.put(MerchantStorePlatformEnum.SHOPIFY.getCode(), 70);
        COUNT_SORT.put(MerchantStorePlatformEnum.Shoplazza.getCode(), 80);
        COUNT_SORT.put(MerchantStorePlatformEnum.OZON.getCode(), 90);
        COUNT_SORT.put(MerchantStorePlatformEnum.SHOPEE.getCode(), 100);
        COUNT_SORT.put(MerchantStorePlatformEnum.JUMIA.getCode(), 110);
        COUNT_SORT.put(MerchantStorePlatformEnum.LAZADA.getCode(), 120);
        COUNT_SORT.put(MerchantStorePlatformEnum.CAT_HOT.getCode(), 130);
        COUNT_SORT.put(MerchantStorePlatformEnum.TAOBAO.getCode(), 140);
        COUNT_SORT.put(MerchantStorePlatformEnum.EBAY.getCode(), 150);
        COUNT_SORT.put(MerchantStorePlatformEnum.TIKTOK.getCode(), 160);
        COUNT_SORT.put(MerchantStorePlatformEnum.OTHER.getCode(), 170);
    }

    public static class AmsAuthStatus {
        public static final int FAIL = 0;

        public static final int NOT_AUTH = 1;

        public static final int SUCCESS = 2;

        public static final int INVALID = 3;
    }

    public static class AliProductAuthStatus {

        public static final int NOT_AUTH = 1;

        public static final int SUCCESS = 2;

        public static final int INVALID = 3;
    }
}
