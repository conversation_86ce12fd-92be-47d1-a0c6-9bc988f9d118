package com.sdsdiy.userapi.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaasReceiveChannelParam {

    private Integer channelType;
    private String appId;
    private String appPublicSecret;
    private String appPrivateSecret;
    private Integer enable;
    private Integer isDelete;
    private Integer sort;
    private Long createUid;
    private Long updateUid;
    private Long deleteTimestamp;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
}
