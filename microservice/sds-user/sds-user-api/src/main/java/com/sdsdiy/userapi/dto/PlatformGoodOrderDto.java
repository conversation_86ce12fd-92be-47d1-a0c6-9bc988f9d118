package com.sdsdiy.userapi.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/6/28
 */
@Data
public class PlatformGoodOrderDto {
    private Long id;
    private String no;
    private Long platformGoodsId;
    /**
     * 套餐id
     */
    private Long platformPermissionSetMealId;
    private Long userId;
    private Integer status;
    private Long merchantId;
    private BigDecimal totalAmount;
    private Long paymentId;
    private String paymentMethod;
    private BigDecimal productPrice;
    private String platformGoodsName;
    private Integer value;
    private Long activityId;
    private Double usableBalance;
    private Double usableFreeGold;
    private Integer numPeriod;
    private Long createTime;
    private Long updateTime;
    /**
     * 付款人：租户id
     */
    private Long sourceTenantId;
    /**
     * 付款人：商户id
     */
    private Long sourceMerchantId;
    /**
     * 目标租户id
     */
    private Long tenantId;

    private Double freeGold;
    private String password;

    private Double balance;
    private String remark;
    private String commitCode;

    private Date startTime;

    private Date endTime;

}
