package com.sdsdiy.userapi.constant;

/**
 * <AUTHOR>
 * @date 2021/6/28
 */
@Deprecated
public enum PurposeTypeStatus {
    RECHARGE(1,"充值"),
    PAY(2,"支付"),
    WITHDRAW(3,"提现"),
    CASHBACK(4,"返现"),
    REFUNC(5,"退款"),
    COMPESATION(6,"赔付"),
    ;
    private Integer status;
    private String desc;

    PurposeTypeStatus(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static PurposeTypeStatus getByStatus(Integer status){
        for (PurposeTypeStatus value : values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;

    }
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
