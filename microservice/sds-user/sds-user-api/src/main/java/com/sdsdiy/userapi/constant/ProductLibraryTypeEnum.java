package com.sdsdiy.userapi.constant;

public enum ProductLibraryTypeEnum {

    SHARED("共享产品库", "共享产品库"),
    AUTHORIZATION("授权产品库","授权产品库"),
    ;
    private String code;
    private String description;

    ProductLibraryTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }


    public static ProductLibraryTypeEnum getByCode(String code){
        ProductLibraryTypeEnum[] values = ProductLibraryTypeEnum.values();
        for(ProductLibraryTypeEnum v:values){
            if(v.getCode().equals(code)){
                return v;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
