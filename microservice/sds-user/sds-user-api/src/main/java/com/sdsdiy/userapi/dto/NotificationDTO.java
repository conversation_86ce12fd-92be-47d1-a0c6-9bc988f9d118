package com.sdsdiy.userapi.dto;

import com.sdsdiy.userapi.constant.EnumNotificationTitle;
import com.sdsdiy.userapi.constant.MsgModule;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/3
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class NotificationDTO {
    private String content;
    private EnumNotificationTitle notificationTitle;
    private Long merchantId;
    private String remarks;
    private Long userId;
    private Long relateId;
    private String title;

    /**
     * 不为null则会pushMsgToWeb
     */
    private MsgModule msgModule;
    private List<String> imageUrls;

}
