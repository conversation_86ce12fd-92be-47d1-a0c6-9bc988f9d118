package com.sdsdiy.userapi.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: zmy
 * @Date: 2021/3/3 15:36
 * @Description:
 */
@Data
public class MerchantSysUserProductPermissionEditReqDto {

    private Long merchantId;
    @NotEmpty(message = "账号id不能为空")
    private List<Long> userIds;
    @NotNull(message = "权限类型不能为空")
    private String productLibraryType;
}
