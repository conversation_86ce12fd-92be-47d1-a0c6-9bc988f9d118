package com.sdsdiy.userapi.constant;

import lombok.Getter;

/**
 * @author: zmy
 * @desc:
 */
public interface MerchantEuLabelConstant {

    /**
     * 关联类型,店铺：store,产品：product,店铺和产品：storeAndProduct
     */
    @Getter
    enum RelTypeEnum {
        STORE("store", "店铺"),
        PRODUCT("product", "产品"),
        STORE_AND_PRODUCT("storeAndProduct", "店铺和产品"),
        ;

        private final String code;
        private final String desc;

        RelTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }

}
