package com.sdsdiy.userapi.constant;

/**
 * @Author: zmy
 * @Date: 2021/6/21 11:07
 * @Description:
 */
public class AgentMerchantConstant {
    /**状态，valid：有效，invalid：无效*/
    public static final String STATUS_VALID="valid";
    public static final String STATUS_INVALID="invalid";

    /**类型，normal：普通部署，distribution：分销商*/
    public static final String TYPE_NORMAL="normal";
    public static final String TYPE_DISTRIBUTION="distribution";

    /**子商户类型，main：主商户，sub：子商户*/
    public static final String MERCHANT_TYPE_MAIIN="main";
    public static final String MERCHANT_TYPE_SUB="sub";

    /**产品分配类型，all:全部，part:部分*/
    public static final String PRODUCT_RANGE_ALL="all";
    public static final String PRODUCT_RANGE_PART="part";

    /**官网开关, 0：关 1：开*/
    public static final Integer WEBSITE_OPEN = 1;
    public static final Integer WEBSITE_CLOSE = 0;

    /**banner状, 0:禁用　1:启用*/
    public static final Integer BANNER_ENABLE = 1;
    public static final Integer BANNER_DISABLE = 0;

    public static final int MAX_COUNT = 10;

    /**是否重定向*/
    public static final String YES = "yes";
    public static final String NO = "no";
}
