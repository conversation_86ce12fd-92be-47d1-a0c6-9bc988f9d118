package com.sdsdiy.userapi.constant;

import com.sdsdiy.common.base.constant.EventConstant;

/**
 * @Author:
 * @Date: 2020/6/8 00:50
 * @Description: 权限额度事件tag通知
 */
public enum EnumPermissionEvenTags {
    PERMISSION_CONSUME(EventConstant.EVENT_USER_OPT,"permission_consume","消耗额度"),
    PERMISSION_RESTITUTION(EventConstant.EVENT_USER_OPT,"permission_restitution","返还额度")
    ;
    private String topic;
    private String code;
    private String desc;

    EnumPermissionEvenTags(String topic, String code, String desc) {
        this.topic = topic;
        this.code = code;
        this.desc = desc;

    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
