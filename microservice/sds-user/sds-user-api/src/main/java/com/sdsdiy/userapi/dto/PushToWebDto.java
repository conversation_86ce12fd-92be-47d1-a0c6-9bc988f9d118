package com.sdsdiy.userapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/28
 */
@Data
public class PushToWebDto {
    public List<Long> ids;
    @JsonProperty("group_ids")
    public List<String> group_ids;
    public Msg msg;
    public String environment;

    public static class Msg {

        private String module;
        private Long timestamp;
        private Object content;

        public Msg() {
            timestamp = System.currentTimeMillis();
        }

        public Msg(String module) {
            this.module = module;
            timestamp = System.currentTimeMillis();
        }

        public Msg(String module, Object content) {
            this.module = module;
            this.content = content;
            timestamp = System.currentTimeMillis();
        }

        public String getModule() {
            return module;
        }

        public void setModule(String module) {
            this.module = module;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }

        public Object getContent() {
            return content;
        }

        public void setContent(Object content) {
            this.content = content;
        }
    }
}
