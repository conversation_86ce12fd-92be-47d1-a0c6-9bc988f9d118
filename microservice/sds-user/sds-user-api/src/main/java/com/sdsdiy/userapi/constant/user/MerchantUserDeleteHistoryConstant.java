package com.sdsdiy.userapi.constant.user;

import com.google.common.collect.Lists;
import com.sdsdiy.common.base.exception.BusinessException;
import lombok.Getter;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/05/17 15:14
 **/
public interface MerchantUserDeleteHistoryConstant {
    enum DataTypeEnum {
        MATERIAL_DESIGN_PRODUCT("materialAndDesignProduct", "素材和成品"),
        STORE_AND_ORDER("storeAndOrder", "店铺及订单"),
        ;
        @Getter
        private final String value;
        private final String desc;

        DataTypeEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }
        public static String getByValue(String value) {
            String dataType = null;
            DataTypeEnum[] values = DataTypeEnum.values();
            for (DataTypeEnum dataTypeEnum : values) {
                if (dataTypeEnum.getValue().contains(value)) {
                    dataType = dataTypeEnum.getValue();
                    break;
                }
            }
            return Optional.ofNullable(dataType).orElseThrow(() -> new BusinessException("数据类型错误"));
        }


    }

    enum OperationTypeEnum {
        DELETE("delete", "删除"),
        RETAIN_ALL("retainAll", "保留全部"),
        RETAIN_MATERIAL("retainMaterial", "保留素材"),
        ;
        @Getter
        private final String value;
        private final String desc;
        public static List<String> retains= Lists.newArrayList(RETAIN_ALL.getValue(),RETAIN_MATERIAL.getValue());

        OperationTypeEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static String getByValue(String value) {
            String operationType = null;
            OperationTypeEnum[] values = OperationTypeEnum.values();
            for (OperationTypeEnum operationTypeEnum : values) {
                if (operationTypeEnum.getValue().contains(value)) {
                    operationType = operationTypeEnum.getValue();
                    break;
                }
            }
            return Optional.ofNullable(operationType).orElseThrow(() -> new BusinessException("操作类型错误"));
        }

        public static boolean isDelete(String value) {
            String type = getByValue(value);
            return type.equals(DELETE.value);
        }

        public static boolean isTransfer(String value) {
            String type = getByValue(value);
            return type.equals(RETAIN_ALL.value) || type.equals(RETAIN_MATERIAL.value);
        }

        public static String getDesignProductOperatorType(String value){
            return DELETE.value.equals(value) || RETAIN_MATERIAL.value.equals(value) ? DesignProductOperationTypeEnum.DELETE.getValue() : DesignProductOperationTypeEnum.RETAIN.getValue();
        }
    }

    enum StatusEnum {
        WAIT("wait", "等待"),
        SUCCESS("success", "成功"),
        FAIL("fail", "失败"),
        ;
        @Getter
        private final String value;
        private final String desc;

        StatusEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static StatusEnum getByValue(String value) {
            StatusEnum[] values = StatusEnum.values();
            for (StatusEnum resultEnum : values) {
                if (resultEnum.getValue().contains(value)) {
                    return resultEnum;
                }
            }
            return null;
        }
    }

    enum DesignProductOperationTypeEnum {
        DELETE("delete", "删除"),
        RETAIN("retain", "保留"),
        ;
        @Getter
        private final String value;
        private final String desc;

        DesignProductOperationTypeEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static DesignProductOperationTypeEnum getByValue(String value) {
            DesignProductOperationTypeEnum[] values = DesignProductOperationTypeEnum.values();
            for (DesignProductOperationTypeEnum resultEnum : values) {
                if (resultEnum.getValue().contains(value)) {
                    return resultEnum;
                }
            }
            return null;
        }
    }
}
