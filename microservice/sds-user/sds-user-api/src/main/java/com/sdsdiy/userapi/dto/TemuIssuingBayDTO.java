package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
@ApiModel("租户仓库")
@Data
public class TemuIssuingBayDTO implements Serializable {
    
    @ApiModelProperty(value = "仓库id")
    private Long id;

    @ApiModelProperty(value = "区域id")
    private Long issuingBayAreaId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "国家code")
    private String country;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "省份code")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称")
    private String province;

    @ApiModelProperty(value = "城市code")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String city;

    @ApiModelProperty(value = "详细地址")
    private String detail;
    
    @ApiModelProperty(value = "仓库类型")
    private String warehouseType;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;
    
}

