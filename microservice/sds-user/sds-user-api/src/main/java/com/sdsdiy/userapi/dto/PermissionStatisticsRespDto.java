package com.sdsdiy.userapi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * (PermissionStatisticsRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-10-15 16:50:13
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PermissionStatisticsRespDto {

    private Long id;
    private Double usedCount;
    private Long merchantPlatformPermissionId;
    private String code;
    private String unit;
    private Long merchantId;
    private Long userId;

}