package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.util.Date;

@Data
public class MerchantPlatformPermissionDto {
    @DtoDefault()
    private Long id;

    @DtoDefault()
    private Long merchantPlatformPermissionSetMealId;

    @DtoDefault()
    private Long merchantId;

    //权限码
    @DtoDefault()
    private String code;

    //权限类型 1 消耗 2 计量 3 授权
    @DtoDefault()
    private Integer type;

    //记录状态 0 未启用 1 使用中 2 失效(type 1 次数用完 2 过期)
    @DtoDefault()
    private Integer status;

    //是否为增量权限（累加）非增量权限不可累加只取最大
    @DtoDefault()
    private Boolean isIncrease;

    //是否为限时权限
    @DtoDefault()
    private Boolean isPeriod;

    //权限有效开始时间
    @DtoDefault()
    private Long startTime;

    //权限有效结束时间
    @DtoDefault()
    private Long endTime;

    //数值
    @DtoDefault()
    private Integer value;

    //倍数(为适应产品变态显示设计)
    @DtoDefault()
    private Integer multiple;

    //数值x倍数
    @DtoDefault()
    private Long totalValue;

    //已消耗的数值
    @DtoDefault()
    private Long usedValue;

    //单位（显示符号）
    @DtoDefault()
    private String unit;

    @DtoDefault()
    private Boolean isDelete;

    @DtoDefault()
    private Date createTime;

    @DtoDefault()
    private Integer periodDay;
    @DtoDefault()
    private String periodUnits;

    private Long currentUsedValue;

    //共用的totalValue值
//    @DtoDefault()
//    private Long inCommonTotalValue;

    //共用的消耗值
    @DtoDefault()
    private Long inCommonUsedValue;
}