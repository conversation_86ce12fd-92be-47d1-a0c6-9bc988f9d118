package com.sdsdiy.userapi.param;


import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FunctionCheckParam {
    private Long userId;
    private String code;
    /***子代号*/
    private String subCode;
    private Long time;
    private Boolean isAdmin;
    private Boolean trowE;

    public FunctionCheckParam(Long userId, String code, Long time, Boolean isAdmin, Boolean trowE) {
        this.userId = userId;
        this.code = code;
        this.time = time;
        this.isAdmin = isAdmin;
        this.trowE = trowE;
    }

}
