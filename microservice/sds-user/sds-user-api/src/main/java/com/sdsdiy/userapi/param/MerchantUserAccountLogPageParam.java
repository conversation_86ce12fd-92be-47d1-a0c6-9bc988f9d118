package com.sdsdiy.userapi.param;

import com.sdsdiy.common.base.entity.dto.BasePageSelect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: bin_lin
 * @date: 2023/7/31 20:11
 * @desc:
 */
@Data
public class MerchantUserAccountLogPageParam extends BasePageSelect {
    @ApiModelProperty("账号id")
    @NotNull
    private Long merchantUserAccountId;

}
