package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

 /**
  * @Author: hzh
  * @Date: 2021/9/9 17:00
  */

@Data
public class RocketInstanceDto implements Serializable {
    private static final long serialVersionUID = -3345489654644582102L;
    @ApiModelProperty("实例名")
    private String name;
    @ApiModelProperty("实例id")
    private String instanceId;
    @ApiModelProperty("创建时间")
    private String createTime;
}
