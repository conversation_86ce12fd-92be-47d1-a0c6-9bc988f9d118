package com.sdsdiy.userapi.constant;

import cn.hutool.core.util.StrUtil;
import com.sdsdiy.paymentapi.constant.BalanceUsedType;
import com.sdsdiy.paymentapi.constant.PaymentMethodEnum;
import com.sdsdiy.userapi.dto.UserAccountBalanceResp;

/**
 * @author: bin_lin
 * @date: 2021/6/23 16:37
 * @desc:
 */
public class MerchantUserAccountConstant {
    /**
     * 商户总额度包含所有子账号
     */
    public static final String MERCHANT_ALL_BALANCE_TYPE = "merchant";
    /**
     * 用户额度
     */
    public static final String MERCHANT_USER_BALANCE_TYPE = "user";
    /**
     * 共用额度
     */
    public static final String IN_COMMON = "inCommon";
    /**
     * 分配额度
     */
    public static final String ALLOCATION = "allocation";
    /**
     * 归还额度
     */
    public static final String RETURN = "return";

    public static final String FUNCTION_ALLOCATIONM_SOURCE_MERCHANT = "MERCHANT";

    public static final String FUNCTION_ALLOCATIONM_SOURCE_INIT = "INIT";

    public static final String TOPIC_FUNCTION_ALLOCATION = "TOPIC_FUNCTION_ALLOCATION";

    public static final String GID_FUNCTION_ALLOCATION_RECORD = " GID_FUNCTION_ALLOCATION_RECORD";

    public static boolean inCommonAccount(UserAccountBalanceResp userAccountBalanceResp){
        return inCommonAccount(userAccountBalanceResp.getBalanceType());
    }

    public static boolean inCommonAccount(String balanceType){
        return StrUtil.isBlank(balanceType) ||  MerchantUserAccountConstant.IN_COMMON.equalsIgnoreCase(balanceType);
    }
    public static Long getSourceUserId(String balanceType, Long userId, String paymentMethod) {
        Long sourceUserId = 0L;
        if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)&&
            !MerchantUserAccountConstant.inCommonAccount(balanceType)){
            sourceUserId= userId;
        }
        return sourceUserId;
    }
    public static Long getTargetUserId(String balanceType, Long userId, String paymentMethod) {
        Long sourceUserId = 0L;
        if ((PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)||PaymentMethodEnum.OFFLINE.getCode().equalsIgnoreCase(paymentMethod))
            && !MerchantUserAccountConstant.inCommonAccount(balanceType)){
            sourceUserId= userId;
        }
        return sourceUserId;
    }

    public static Integer getMerchantBalanceTypeForPaymentOrRefund(String accountBalanceType, String paymentMethod) {
        Integer balanceType;
        if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(paymentMethod)||PaymentMethodEnum.OFFLINE.getCode().equalsIgnoreCase(paymentMethod)) {
            if (inCommonAccount(accountBalanceType)) {
                balanceType= BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType();
            } else {
                balanceType=BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType();
            }
        } else {
            balanceType=BalanceUsedType.NO_USE_BALANCE.getUsedType();
        }
        return balanceType;
    }
}
