package com.sdsdiy.userapi.dto.storeSettleIn.supplierProfitSplitRatioConfig.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/22
 */
@ApiModel(description = "供应商分账比例请求参数实体")
@Data
@NoArgsConstructor
public class SupplierProfitSplitRatioConfigReqParam {
    
    @ApiModelProperty(value = "主键")
    private Long id;
    
    @ApiModelProperty(value = "供应商入驻ID")
    private Long supplierSettleInId;

    @ApiModelProperty(value = "银联商户号（唯一）")
    @NotEmpty
    private String unionpayMerchantNo;

    @ApiModelProperty(value = "业务终端号")
    @NotEmpty
    private String terminalNo;

    @ApiModelProperty(value = "比例")
    @NotEmpty
    private BigDecimal ratio;
    
}
