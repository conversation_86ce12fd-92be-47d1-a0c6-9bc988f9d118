package com.sdsdiy.userapi.constant;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @program: microservice-parent
 * @Package com.sdsdiy.userapi.constant
 * @Description: TODO
 * @date 2021/7/6 16:23
 */
public class AgentSysUserConstant {

    public final static String USER_TYPE_BOSS = "boss";
    public final static String USER_TYPE_ROOT = "root";
    public final static List<String> SYNC_TYPES = Arrays.asList(new String[]{USER_TYPE_BOSS, USER_TYPE_ROOT});

}
