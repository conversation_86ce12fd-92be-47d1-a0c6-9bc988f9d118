package com.sdsdiy.userapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FunctionQuotasRespDto {
    /**店铺数量*/
    @ApiModelProperty("店铺数量")
    private VipServiceRespDto storeNumberService;
    /**设计次数*/
    @ApiModelProperty("设计次数")
    private VipServiceRespDto productDesignService;
    /**图库容量*/
    @ApiModelProperty("图库容量")
    private VipServiceRespDto materialCapacityService;
    /**私有模板*/
    @ApiModelProperty("私有模板")
    private VipServiceRespDto privateTemplateService;
    /**子账号*/
    @ApiModelProperty("子账号")
    private VipServiceRespDto subAccountService;
    /**关键字提取*/
    @ApiModelProperty("关键字提取")
    private VipServiceRespDto materialKeywordExtractService;
    /**独立域名*/
    @ApiModelProperty("独立域名")
    private VipServiceRespDto domainDameService;
    /**智能抠图*/
    @ApiModelProperty("智能抠图")
    private VipServiceRespDto mattingPictureService;
    /**图片清晰化*/
    @ApiModelProperty("图片清晰化")
    private VipServiceRespDto pictureClearService;
    /**我的主题*/
    @ApiModelProperty("我的主题")
    private VipServiceRespDto myThemeService;
    /**刺绣设计*/
    @ApiModelProperty("刺绣设计")
    private VipServiceRespDto embroideryDesignService;
    /**
     * ai绘图
     */
    @ApiModelProperty("AI绘图")
    private VipServiceRespDto aiDrawingService;

    @ApiModelProperty("算力")
    private VipServiceRespDto hashrateService;
}
