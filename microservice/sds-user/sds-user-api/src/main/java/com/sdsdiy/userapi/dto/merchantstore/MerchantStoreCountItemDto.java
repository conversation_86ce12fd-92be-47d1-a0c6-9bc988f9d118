package com.sdsdiy.userapi.dto.merchantstore;

import com.sdsdiy.userapi.constant.MerchantStoreConstant;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MerchantStoreCountItemDto implements Comparable<MerchantStoreCountItemDto> {

    private String platformCode;

    /**
     * 店铺数量
     */
    private Integer totalNum = 0;

    /**
     * 半托管店铺数量
     */
    private Integer popChoiceNum = 0;
    
    private Integer fullyNum = 0;
    private Integer localNum = 0;

    /**
     * 已经授权数量
     */
    private Integer authCount = 0;

    /**
     * 失效数量
     */
    private Integer invalidCount = 0;

    /**
     * 速卖通上货宝数量
     */
    private Integer aliProductAuthCount = 0;

    /**
     * 速卖通上货宝失效数量
     */
    private Integer aliProductInvalidCount = 0;

    /**
     * 排序规则
     * 1. 先按店铺数量排序
     * 2. 店铺数量一致按平台排序
     *
     * @param target
     *
     * @return
     */
    @Override
    public int compareTo(MerchantStoreCountItemDto target) {
        int result = target.getTotalNum() - this.getTotalNum();
        if (result != 0) {
            return result;
        }

        int currentPlatformSort = MerchantStoreConstant.COUNT_SORT.getOrDefault(this.getPlatformCode(), 0);
        int targetPlatformSort = MerchantStoreConstant.COUNT_SORT.getOrDefault(target.getPlatformCode(), 0);
        
        return currentPlatformSort - targetPlatformSort;
    }
}
