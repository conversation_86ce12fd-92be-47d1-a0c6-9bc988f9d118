package com.sdsdiy.userapi.dto;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/7/7
 */
@Data
public class MerchantSimpleDto {
    @DtoDefault()
    @ApiModelProperty("商户id")
    private Long id;
    @DtoDefault()
    @ApiModelProperty("商户名称")
    private String name;
    @DtoDefault()
    @ApiModelProperty("商户号")
    private String merchantNo;

    @DtoDefault()
    @ApiModelProperty("租户id")
    private Long tenantId;
    @DtoDefault()
    @ApiModelProperty("主商户id")
    private Long mainMerchantId;
    /**
     * 商户当前会员等级
     */
    @DtoDefault()
    private BigDecimal memberLevel;
}
