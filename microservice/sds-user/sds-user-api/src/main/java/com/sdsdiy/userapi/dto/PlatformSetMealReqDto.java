package com.sdsdiy.userapi.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * sds平台会员商品(PlatformSetMealReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-09-27 11:37:55
 */
@Data
public class PlatformSetMealReqDto implements Serializable {

    @NotNull(message = "主键id不能为空")
    private Long id;

    @NotNull(message = "会员套餐名称不能为空")
    private String mealName;

    @NotNull(message = "权限码不能为空")
    private String mealCode;

    @NotNull(message = "价格不能为空")
    private Double price;

    @NotNull(message = "特价不能为空")
    private Double salePrice;

    @NotNull(message = "是否特价打折:0：否，1：是不能为空")
    private Integer isDiscount;

    @NotNull(message = "特价针对的会员等级，默认1，免费版及以上。比如3，即专业版及以上的可享受不能为空")
    private Integer discountLimitLevel;

    @NotNull(message = "特价开始时间不能为空")
    private Date salePriceStartTime;

    @NotNull(message = "特价结束时间不能为空")
    private Date salePriceEndTime;

    @NotNull(message = "会员套餐状态 0 下架，1上架不能为空")
    private Integer status;

    @NotNull(message = "套餐类型，1：会员特权，2：增值服务不能为空")
    private Integer setMealType;

    @NotNull(message = "周期数值不能为空")
    private Integer period;

    @NotNull(message = "周期单位，年不能为空")
    private String periodUnit;

    @NotNull(message = "会员排序值不能为空")
    private Integer mealSort;

    @NotNull(message = "会员级别大小，0：增值，1：免费板，2：创业版，3：专业版，4：企业版不能为空")
    private Integer mealLevel;

    @NotNull(message = "创建者不能为空")
    private Long createUid;

    @NotNull(message = "更新者不能为空")
    private Long updateUid;

    @NotNull(message = "创建时间不能为空")
    private Date createTime;

    @NotNull(message = "更新时间不能为空")
    private Date updateTime;

    @NotNull(message = "是否删除:0：否，1：是不能为空")
    private Integer isDelete;

}