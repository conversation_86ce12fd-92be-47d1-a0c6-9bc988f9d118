package com.sdsdiy.statdata.dto.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/8/12
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class TenantProductDistributionMonthBillDTO implements Serializable {
    private static final long serialVersionUID = -689563445011860L;

    @ApiModelProperty(value = "账单月份，yyyy-MM")
    private String monthly;
     
    @ApiModelProperty(value = "本月利润")
    private BigDecimal profit;
    @ApiModelProperty(value = "本月销售额")
    private BigDecimal sales;
    @ApiModelProperty(value = "本月成本")
    private BigDecimal cost;
    @ApiModelProperty(value = "本月订单数")
    private Long orderNum;

    @ApiModelProperty(value = "分销商")
    private Long distributorNum;
    @ApiModelProperty(value = "出单分销商")
    private Long orderDistributorNum;

    @ApiModelProperty(value = "授信余额")
    private Balance balance;

    @Data
    public static class Balance {
        @ApiModelProperty(value = "剩余")
        private BigDecimal current;
        @ApiModelProperty(value = "原始")
        private BigDecimal original;
        @ApiModelProperty(value = "核销")
        private BigDecimal consumption;
        @ApiModelProperty(value = "充值")
        private BigDecimal recharge;
        @ApiModelProperty(value = "退款")
        private BigDecimal refund;
        @ApiModelProperty(value = "提现")
        private BigDecimal withdrawal;
    }

    public BigDecimal getBalanceCurrent() {
        return this.balance == null ? BigDecimal.ZERO : this.balance.getCurrent();
    }

    public BigDecimal getBalanceOriginal() {
        return this.balance == null ? BigDecimal.ZERO : this.balance.getOriginal();
    }

    public BigDecimal getBalanceConsumption() {
        return this.balance == null ? BigDecimal.ZERO : this.balance.getConsumption();
    }

    public BigDecimal getBalanceRecharge() {
        return this.balance == null ? BigDecimal.ZERO : this.balance.getRecharge();
    }

    public BigDecimal getBalanceRefund() {
        return this.balance == null ? BigDecimal.ZERO : this.balance.getRefund();
    }

    public BigDecimal getBalanceWithdrawal() {
        return this.balance == null ? BigDecimal.ZERO : this.balance.getWithdrawal();
    }
}
