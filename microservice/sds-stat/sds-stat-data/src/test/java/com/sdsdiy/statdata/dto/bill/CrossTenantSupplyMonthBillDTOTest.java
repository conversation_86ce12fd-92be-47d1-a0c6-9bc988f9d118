package com.sdsdiy.statdata.dto.bill;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

class CrossTenantSupplyMonthBillDTOTest {

    @Test
    void testBalanceGetters_WithNullBalance() {
        CrossTenantSupplyMonthBillDTO dto = new CrossTenantSupplyMonthBillDTO();
        dto.setBalance(null);

        // 所有balance相关的getter方法都应该返回BigDecimal.ZERO
        assertEquals(BigDecimal.ZERO, dto.getBalanceCurrent());
        assertEquals(BigDecimal.ZERO, dto.getBalanceOriginal());
        assertEquals(BigDecimal.ZERO, dto.getBalanceConsumption());
        assertEquals(BigDecimal.ZERO, dto.getBalanceRecharge());
        assertEquals(BigDecimal.ZERO, dto.getBalanceRefund());
        assertEquals(BigDecimal.ZERO, dto.getBalanceWithdrawal());
    }

    @Test
    void testBalanceGetters_WithValidBalance() {
        CrossTenantSupplyMonthBillDTO dto = new CrossTenantSupplyMonthBillDTO();
        CrossTenantSupplyMonthBillDTO.Balance balance = new CrossTenantSupplyMonthBillDTO.Balance();
        
        // 设置测试数据
        balance.setCurrent(new BigDecimal("1000.50"));
        balance.setOriginal(new BigDecimal("2000.00"));
        balance.setConsumption(new BigDecimal("500.25"));
        balance.setRecharge(new BigDecimal("300.75"));
        balance.setRefund(new BigDecimal("100.00"));
        balance.setWithdrawal(new BigDecimal("200.50"));
        
        dto.setBalance(balance);

        // 验证所有getter方法返回正确的值
        assertEquals(new BigDecimal("1000.50"), dto.getBalanceCurrent());
        assertEquals(new BigDecimal("2000.00"), dto.getBalanceOriginal());
        assertEquals(new BigDecimal("500.25"), dto.getBalanceConsumption());
        assertEquals(new BigDecimal("300.75"), dto.getBalanceRecharge());
        assertEquals(new BigDecimal("100.00"), dto.getBalanceRefund());
        assertEquals(new BigDecimal("200.50"), dto.getBalanceWithdrawal());
    }

    @Test
    void testBalanceGetters_WithPartialNullValues() {
        CrossTenantSupplyMonthBillDTO dto = new CrossTenantSupplyMonthBillDTO();
        CrossTenantSupplyMonthBillDTO.Balance balance = new CrossTenantSupplyMonthBillDTO.Balance();
        
        // 只设置部分值，其他保持null
        balance.setCurrent(new BigDecimal("1000.00"));
        balance.setOriginal(null);
        balance.setConsumption(new BigDecimal("500.00"));
        balance.setRecharge(null);
        balance.setRefund(new BigDecimal("100.00"));
        balance.setWithdrawal(null);
        
        dto.setBalance(balance);

        // 验证非null值正确返回，null值返回null（这是Balance对象内部的行为）
        assertEquals(new BigDecimal("1000.00"), dto.getBalanceCurrent());
        assertNull(dto.getBalanceOriginal());
        assertEquals(new BigDecimal("500.00"), dto.getBalanceConsumption());
        assertNull(dto.getBalanceRecharge());
        assertEquals(new BigDecimal("100.00"), dto.getBalanceRefund());
        assertNull(dto.getBalanceWithdrawal());
    }

    @Test
    void testBalanceGetters_WithZeroValues() {
        CrossTenantSupplyMonthBillDTO dto = new CrossTenantSupplyMonthBillDTO();
        CrossTenantSupplyMonthBillDTO.Balance balance = new CrossTenantSupplyMonthBillDTO.Balance();
        
        // 设置所有值为0
        balance.setCurrent(BigDecimal.ZERO);
        balance.setOriginal(BigDecimal.ZERO);
        balance.setConsumption(BigDecimal.ZERO);
        balance.setRecharge(BigDecimal.ZERO);
        balance.setRefund(BigDecimal.ZERO);
        balance.setWithdrawal(BigDecimal.ZERO);
        
        dto.setBalance(balance);

        // 验证所有getter方法返回BigDecimal.ZERO
        assertEquals(BigDecimal.ZERO, dto.getBalanceCurrent());
        assertEquals(BigDecimal.ZERO, dto.getBalanceOriginal());
        assertEquals(BigDecimal.ZERO, dto.getBalanceConsumption());
        assertEquals(BigDecimal.ZERO, dto.getBalanceRecharge());
        assertEquals(BigDecimal.ZERO, dto.getBalanceRefund());
        assertEquals(BigDecimal.ZERO, dto.getBalanceWithdrawal());
    }

    @Test
    void testBalanceObject_IndependentOfDTO() {
        // 测试Balance对象可以独立使用
        CrossTenantSupplyMonthBillDTO.Balance balance = new CrossTenantSupplyMonthBillDTO.Balance();
        balance.setCurrent(new BigDecimal("500.00"));
        balance.setOriginal(new BigDecimal("1000.00"));
        
        assertEquals(new BigDecimal("500.00"), balance.getCurrent());
        assertEquals(new BigDecimal("1000.00"), balance.getOriginal());
    }
}
