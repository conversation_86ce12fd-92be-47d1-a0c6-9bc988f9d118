# CrossTenantSupplyMonthBillDTO 方法补全说明

## 功能概述

参照 `getBalanceCurrent` 方法的实现模式，为 `CrossTenantSupplyMonthBillDTO` 类中的 `Balance` 对象的所有字段补全了对应的getter方法。

## 实现的方法

### 1. getBalanceCurrent() (原有方法)
```java
public BigDecimal getBalanceCurrent() {
    return this.balance == null ? BigDecimal.ZERO : this.balance.getCurrent();
}
```

### 2. 新增的方法

#### getBalanceOriginal()
```java
public BigDecimal getBalanceOriginal() {
    return this.balance == null ? BigDecimal.ZERO : this.balance.getOriginal();
}
```

#### getBalanceConsumption()
```java
public BigDecimal getBalanceConsumption() {
    return this.balance == null ? BigDecimal.ZERO : this.balance.getConsumption();
}
```

#### getBalanceRecharge()
```java
public BigDecimal getBalanceRecharge() {
    return this.balance == null ? BigDecimal.ZERO : this.balance.getRecharge();
}
```

#### getBalanceRefund()
```java
public BigDecimal getBalanceRefund() {
    return this.balance == null ? BigDecimal.ZERO : this.balance.getRefund();
}
```

#### getBalanceWithdrawal()
```java
public BigDecimal getBalanceWithdrawal() {
    return this.balance == null ? BigDecimal.ZERO : this.balance.getWithdrawal();
}
```

## Balance 内部类字段说明

```java
@Data
public static class Balance {
    @ApiModelProperty(value = "剩余")
    private BigDecimal current;      // 当前余额
    
    @ApiModelProperty(value = "原始")
    private BigDecimal original;     // 原始金额
    
    @ApiModelProperty(value = "核销")
    private BigDecimal consumption;  // 核销金额
    
    @ApiModelProperty(value = "充值")
    private BigDecimal recharge;     // 充值金额
    
    @ApiModelProperty(value = "退款")
    private BigDecimal refund;       // 退款金额
    
    @ApiModelProperty(value = "提现")
    private BigDecimal withdrawal;   // 提现金额
}
```

## 设计模式

所有新增的getter方法都遵循相同的设计模式：

1. **空值检查**：首先检查 `balance` 对象是否为 `null`
2. **默认值返回**：如果 `balance` 为 `null`，返回 `BigDecimal.ZERO`
3. **委托调用**：如果 `balance` 不为 `null`，委托给 `balance` 对象的对应getter方法

## 使用场景

这些方法主要用于：

1. **安全访问**：避免在访问balance字段时出现NullPointerException
2. **默认值处理**：当balance对象不存在时，提供合理的默认值（BigDecimal.ZERO）
3. **简化调用**：提供便捷的方法直接获取balance中的各个字段值
4. **业务逻辑**：在统计和计算场景中安全地获取各种余额数据

## 注意事项

1. **返回值处理**：当balance对象存在但其内部字段为null时，会返回null而不是BigDecimal.ZERO
2. **线程安全**：这些方法是线程安全的，因为它们只进行读取操作
3. **性能考虑**：每次调用都会进行null检查，但开销很小
4. **一致性**：所有方法都遵循相同的命名规范：`getBalance + 字段名`

## 测试覆盖

已创建完整的单元测试，覆盖以下场景：
- balance对象为null的情况
- balance对象存在且字段有值的情况
- balance对象存在但部分字段为null的情况
- 所有字段为零值的情况
- Balance对象的独立使用

## 扩展性

如果将来需要在Balance类中添加新字段，可以按照相同的模式添加对应的getter方法：

```java
public BigDecimal getBalance[FieldName]() {
    return this.balance == null ? BigDecimal.ZERO : this.balance.get[FieldName]();
}
```
