package com.sdsdiy.orderimpl.service.offlinepay;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.orderapi.constant.*;
import com.sdsdiy.orderapi.constant.event.message.OfflinePayResetMessage;
import com.sdsdiy.orderimpl.bo.BatchPaymentBo;
import com.sdsdiy.orderimpl.bo.OfflinePayRecordGen;
import com.sdsdiy.orderimpl.entity.po.*;
import com.sdsdiy.orderimpl.entity.po.progress.OrderProgress;
import com.sdsdiy.orderimpl.feign.NotificationFeign;
import com.sdsdiy.orderimpl.feign.PaymentFeign;
import com.sdsdiy.orderimpl.feign.payment.TransactionFeign;
import com.sdsdiy.orderimpl.manager.AdminPrepaidPaymentHistoryMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayPaymentMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayRecordMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayRecordOrderMapperManager;
import com.sdsdiy.orderimpl.manager.aliexpress.AliexpressJitPackageMapperManager;
import com.sdsdiy.orderimpl.service.OrderService;
import com.sdsdiy.orderimpl.service.order.PrepaidShippedService;
import com.sdsdiy.orderimpl.service.progress.OrderProgressService;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.param.PaymentOperateParam;
import com.sdsdiy.userapi.dto.NotificationDTO;
import io.seata.spring.annotation.GlobalTransactional;
import io.seata.tm.api.transaction.Propagation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.MybatisPlusConstant.LIMIT_ONE;
import static com.sdsdiy.orderapi.constant.OfflinePayRecordConstant.StatusEnum.DURING;
import static com.sdsdiy.orderapi.constant.OfflinePayRecordConstant.TradeTypeEnum.ALIEXPRESS_JIT;
import static com.sdsdiy.orderapi.constant.event.OrderProgressConstant.JIT_ORDER_CARRIAGE_FREIGHT_FINISH;
import static com.sdsdiy.paymentapi.constant.PurposeType.ADMIN_PREPAY_SHIPPING;
import static com.sdsdiy.paymentapi.constant.PurposeType.FINISHED_DIFF;

/**
 * @author: bin_lin
 * @date: 2023/10/23 12:06
 * @desc:
 */
@Slf4j
@Service
@DS("master")
@RequiredArgsConstructor(onConstructor_ = {@Autowired, @Lazy})
public class OfflinePaySaveService {

    private final PaymentFeign paymentFeign;
    private final NotificationFeign notificationFeign;

    private final OfflinePayRecordMapperManager offlinePayRecordMapperManager;
    private final OfflinePayRecordOrderMapperManager offlinePayRecordOrderMapperManager;
    private final OfflinePayPaymentMapperManager offlinePayPaymentMapperManager;
    private final AdminPrepaidPaymentHistoryMapperManager adminPrepaidPaymentHistoryMapperManager;
    private final AliexpressJitPackageMapperManager aliexpressJitPackageMapperManager;

    private final PrepaidShippedService prepaidShippedService;
    private final OrderService orderService;
    
    private final RocketMQTemplate rocketMQTemplate;
    private final OrderProgressService orderProgressService;
    private final OfflinePayAmountDetailService offlinePayAmountDetailService;
    private final TransactionFeign transactionFeign;
    @GlobalTransactional(rollbackFor = Exception.class)
    public void batchSave(List<OfflinePayRecord> offlinePayRecords,
                          List<OfflinePayAmountDetail> offlinePayAmountDetails,
                          OfflinePayPayment offlinePayPayment) {
        offlinePayPaymentMapperManager.save(offlinePayPayment);
        offlinePayRecordMapperManager.updateBatchById(offlinePayRecords);
        if (DURING.name().equals(offlinePayPayment.getStatus())) {
            this.updatePrepaidShipped(offlinePayRecords, DURING.name());
            this.updateJitShipCost(offlinePayRecords, OfflinePayRecordConstant.StatusEnum.DURING.name());
        }
        offlinePayAmountDetailService.reSave(offlinePayAmountDetails);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    public void batchSave(BatchPaymentBo batchPaymentBo) {
        OfflinePayRecordGen.formatOfflinePayRecordsPo(batchPaymentBo.getOfflinePayRecords(), batchPaymentBo.getOfflinePayPayment());
        offlinePayPaymentMapperManager.updateById(batchPaymentBo.getOfflinePayPayment());
        offlinePayRecordMapperManager.updateBatchById(batchPaymentBo.getOfflinePayRecords());
        if (OfflinePayRecordConstant.StatusEnum.PAID.name().equals(batchPaymentBo.getOfflinePayPayment().getStatus())) {
            updatePaidStatus(batchPaymentBo.getOfflinePayPayment(), batchPaymentBo.getOfflinePayRecords());
        }
    }

    @GlobalTransactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public PaymentDto updatePaidStatus(PaymentDto paymentParam, OfflinePayPayment payment, List<OfflinePayRecord> offlinePayRecords) {
        PaymentOperateParam paymentOperateParam = new PaymentOperateParam();
        paymentOperateParam.setTradeNo(paymentParam.getTradeNo());
        paymentOperateParam.setRecordBill(true);
        PaymentDto paymentDto = transactionFeign.operateTransaction(paymentParam.getId());
        updatePaidStatus(payment, offlinePayRecords);
        rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.TOP_EVENT_OFFLINE_PAY_RESET_SUMMARY, new OfflinePayResetMessage(offlinePayRecords.get(0).getMerchantId()));
        return paymentDto;
    }

    private void updatePaidStatus(OfflinePayPayment payment, List<OfflinePayRecord> offlinePayRecords) {
        this.updatePrepaidShipped(offlinePayRecords, OfflinePayRecordConstant.StatusEnum.PAID.name());
        this.updateJitShipCost(offlinePayRecords, OfflinePayRecordConstant.StatusEnum.PAID.name());
        offlinePayRecordMapperManager.lambdaUpdate()
                .eq(OfflinePayRecord::getOfflinePayPaymentId, payment.getId())
                .set(OfflinePayRecord::getStatus, OfflinePayRecordConstant.StatusEnum.PAID.name())
                .set(OfflinePayRecord::getPaymentTime, new Date())
                .update();
        offlinePayPaymentMapperManager.lambdaUpdate().eq(OfflinePayPayment::getId, payment.getId())
                .set(OfflinePayPayment::getStatus, OfflinePayRecordConstant.StatusEnum.PAID.name())
                .set(OfflinePayPayment::getPaymentTime, new Date())
                .update();
        for (OfflinePayRecord offlinePayRecord : offlinePayRecords) {
            NotificationDTO notificationDTO = OfflinePayRecordGen.generateNotification(offlinePayRecord).orElseThrow(() -> new BusinessException("支付方式不支持！"));
            notificationFeign.saveNotificationDTO(notificationDTO);
            this.finishDiffPaidProgressMessage(offlinePayRecord);
            this.jitOrderPaidProgressMessage(offlinePayRecord);
        }
    }

    private void updateJitShipCost(List<OfflinePayRecord> offlinePayRecords, String status) {
        List<Long> jitShipCostRecordIds = offlinePayRecords.stream()
                .filter(r -> ALIEXPRESS_JIT.getName().equals(r.getTradeType()))
                .map(OfflinePayRecord::getId)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(jitShipCostRecordIds)) {
            aliexpressJitPackageMapperManager.updatePayStatus(jitShipCostRecordIds,status);
        }
    }

    private void updatePrepaidShipped(List<OfflinePayRecord> offlinePayRecords, String status) {
        List<Long> adminPrepayRecordIds = offlinePayRecords.stream().filter(r -> ADMIN_PREPAY_SHIPPING.getCode().equals(r.getTradeType())).map(OfflinePayRecord::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(adminPrepayRecordIds)) {
            List<AdminPrepaidPaymentHistory> adminPayHistories = adminPrepaidPaymentHistoryMapperManager.lambdaQuery().in(AdminPrepaidPaymentHistory::getOfflinePayRecordId, adminPrepayRecordIds).list();
            Set<Long> adminPayHistoryIds = adminPayHistories.stream().map(AdminPrepaidPaymentHistory::getId).collect(Collectors.toSet());
            adminPrepaidPaymentHistoryMapperManager.lambdaUpdate().in(AdminPrepaidPaymentHistory::getId, adminPayHistoryIds).set(AdminPrepaidPaymentHistory::getStatus, status).update();
            Set<Long> adminPrepaidIdList = adminPayHistories.stream().map(AdminPrepaidPaymentHistory::getAdminPrepaidId).collect(Collectors.toSet());
            if (DURING.name().equals(status)) {
                prepaidShippedService.updatePayStatus(adminPrepaidIdList, AdminPrepaidConstant.PaymentStatusEnum.PAYMENT_OFFLINE.name());
            } else if (OfflinePayRecordConstant.StatusEnum.PAID.name().equals(status)) {
                prepaidShippedService.updatePayStatus(adminPrepaidIdList, AdminPrepaidConstant.PaymentStatusEnum.PAID.name());
            }
        }
    }

    public void finishDiffPaidProgressMessage(OfflinePayRecord offlinePayRecord) {
        if (!FINISHED_DIFF.getCode().equals(offlinePayRecord.getTradeType())) {
            return;
        }
        OfflinePayRecordOrder recordOrder = offlinePayRecordOrderMapperManager.lambdaQuery()
                .eq(OfflinePayRecordOrder::getOfflinePayRecordId, offlinePayRecord.getId())
                .last(LIMIT_ONE)
                .one();
        if (recordOrder == null) {
            return;
        }
        Order order = orderService.getByNo(recordOrder.getOrderNo());
        if (order == null) {
            return;
        }
        OrderProgress progress = new OrderProgress();
        progress.setOrderId(order.getId());
        progress.setTitle("订单完结补差");
        progress.setContent(StrUtil.format("已补差{}元", recordOrder.getPrice()));
        progress.setEtype("order_end_offline_pay_finish");
        progress.setOrderStatus(OrderProgressStatusConstant.STATELESS);
        progress.setOprateType(OrderOperatorConstant.OPERATOR_TYPE_ADMIN);
        progress.setCreateUid(0L);
        progress.setType(OrderProgressTypeConstant.ORDER_END_OFFLINE_PAY_FINISH);
        progress.setSendingTime(new Date());
        progress.setEid(order.getId());
        progress.setOprateId(0L);
        progress.setEvidenceImgs("");
        progress.setLaberPdf("");
        orderProgressService.savePo(progress);
    }

    public void jitOrderPaidProgressMessage(OfflinePayRecord offlinePayRecord) {
        if (!OfflinePayRecordConstant.TradeTypeEnum.ALIEXPRESS_JIT.getName().equals(offlinePayRecord.getTradeType())) {
            return;
        }
        List<OfflinePayRecordOrder> recordOrders = offlinePayRecordOrderMapperManager.lambdaQuery()
                .eq(OfflinePayRecordOrder::getOfflinePayRecordId, offlinePayRecord.getId())
                .list();
        if (CollectionUtil.isEmpty(recordOrders)) {
            return;
        }
        List<String> orderNos = recordOrders.stream().map(OfflinePayRecordOrder::getOrderNo).collect(Collectors.toList());
        List<Order> orderByOrderNos = orderService.getOrderByOrderNos(orderNos);
        if (CollectionUtil.isEmpty(orderByOrderNos)) {
            return;
        }
        List<OrderProgress> progresses = Lists.newArrayList();
        for (Order order : orderByOrderNos) {
            OrderProgress progress = new OrderProgress();
            progress.setOrderId(order.getId());
            progress.setTitle("Jit订单运费");
            progress.setContent(StrUtil.format("已补差{}元", offlinePayRecord.getAmount()));
            progress.setEtype(JIT_ORDER_CARRIAGE_FREIGHT_FINISH);
            progress.setOrderStatus(OrderProgressStatusConstant.STATELESS);
            progress.setOprateType(OrderOperatorConstant.OPERATOR_TYPE_ADMIN);
            progress.setCreateUid(0L);
            progress.setType(OrderProgressTypeConstant.JIT_ORDER_CARRIAGE_FREIGHT_FINISH);
            progress.setSendingTime(new Date());
            progress.setEid(order.getId());
            progress.setOprateId(0L);
            progress.setEvidenceImgs("");
            progress.setLaberPdf("");
            progresses.add(progress);
        }
        orderProgressService.savePoBatch(progresses);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRecordStatusByOverdueTime(List<Long> overdueTimePaymentsIds, List<Long> overdueTimeRecordIds, List<Long> prepaidPaymentHistoryIds, Set<Long> adminPrepaidIds) {
        offlinePayPaymentMapperManager.lambdaUpdate()
                .set(OfflinePayPayment::getStatus, OfflinePayRecordConstant.StatusEnum.UN_PAID.name())
                .set(OfflinePayPayment::getCloseTime, new Date())
                .in(OfflinePayPayment::getId, overdueTimePaymentsIds)
                .update();

        offlinePayRecordMapperManager.lambdaUpdate()
                .set(OfflinePayRecord::getStatus, OfflinePayRecordConstant.StatusEnum.UN_PAID.name())
                .set(OfflinePayRecord::getOfflinePayPaymentId, 0L)
                .set(OfflinePayRecord::getPayBatchNo, "")
                .set(OfflinePayRecord::getPaymentMethod, "")
                .set(OfflinePayRecord::getErrorLog, "")
                .set(OfflinePayRecord::getPaymentUrl, "")
                .set(OfflinePayRecord::getPaymentOverdueTime, 0L)
                .set(OfflinePayRecord::getCloseTime, new Date())
                .in(OfflinePayRecord::getId, overdueTimeRecordIds)
                .update();
        if (CollectionUtil.isNotEmpty(prepaidPaymentHistoryIds)) {
            adminPrepaidPaymentHistoryMapperManager.lambdaUpdate()
                    .set(AdminPrepaidPaymentHistory::getStatus, OfflinePayRecordConstant.StatusEnum.UN_PAID.name())
                    .in(AdminPrepaidPaymentHistory::getId, prepaidPaymentHistoryIds)
                    .update();
        }
        if (CollectionUtil.isNotEmpty(adminPrepaidIds)) {
            prepaidShippedService.updatePayStatus(Lists.newArrayList(adminPrepaidIds), AdminPrepaidConstant.PaymentStatusEnum.PENDING_PAYMENT.name());
        }
    }
}
