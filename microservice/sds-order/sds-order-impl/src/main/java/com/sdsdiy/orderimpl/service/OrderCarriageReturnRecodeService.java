package com.sdsdiy.orderimpl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.BaseListQueryDTO;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.common.consts.MerchantStoreTypeEnum;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.logistics.LogisticsTagConst;
import com.sdsdiy.core.mq.queue.payment.PaymentTagConst;
import com.sdsdiy.core.util.ListUtils;
import com.sdsdiy.logisticsapi.dto.CarriageDeclarationInfoReqDto;
import com.sdsdiy.logisticsapi.dto.GetNeedCancelCarriageByParcelReq;
import com.sdsdiy.logisticsapi.dto.LogisticsFreightRespDto;
import com.sdsdiy.logisticsapi.dto.SaveByOrderIdParam;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsdata.dto.base.CountryExpressInfoNewRespDto;
import com.sdsdiy.logisticsdata.dto.base.LogisticsRespDto;
import com.sdsdiy.logisticsdata.dto.base.msg.SendBackMsg;
import com.sdsdiy.logisticsdata.util.CarriageAmountUtil;
import com.sdsdiy.orderapi.constant.OrderOriginType;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.constant.event.message.admin.AdminManualAllocationWaybillNumberMessage;
import com.sdsdiy.orderapi.dto.AgainCarriageNoParam;
import com.sdsdiy.orderapi.dto.order.OrderApplyCarriageNoParam;
import com.sdsdiy.orderapi.dto.order.OrderCarriageRespDto;
import com.sdsdiy.orderapi.dto.ordercarriagereturnrecode.*;
import com.sdsdiy.orderdata.constant.order.OrderExtendKeyEnum;
import com.sdsdiy.orderdata.constant.order.OrderExtendValueEnum;
import com.sdsdiy.orderdata.constant.order.PlatformOrderExtendValueEnum;
import com.sdsdiy.orderdata.dto.AddressRespDto;
import com.sdsdiy.orderdata.dto.OrderItemRespDto;
import com.sdsdiy.orderdata.dto.parcel.OrderParcelItemDTO;
import com.sdsdiy.orderdata.dto.parcel.OrderParcelLogisticsFreightDTO;
import com.sdsdiy.orderdata.enums.DeliveryTypeEnum;
import com.sdsdiy.orderdata.enums.OrderParcelStatusEnum;
import com.sdsdiy.orderdata.enums.OrderStatus;
import com.sdsdiy.orderimpl.entity.po.*;
import com.sdsdiy.orderimpl.entity.po.parcel.OrderParcel;
import com.sdsdiy.orderimpl.environment.EnvironmentVariables;
import com.sdsdiy.orderimpl.feign.*;
import com.sdsdiy.orderimpl.feign.logistics.CarriageDeclarationInfoFeign;
import com.sdsdiy.orderimpl.feign.user.TenantFeign;
import com.sdsdiy.orderimpl.feign.user.TenantSysUserFeign;
import com.sdsdiy.orderimpl.linstener.OrderEventService;
import com.sdsdiy.orderimpl.manager.OrderExtendInfoMapperManager;
import com.sdsdiy.orderimpl.manager.PlatformOrderExtendManage;
import com.sdsdiy.orderimpl.mapper.OrderCarriageReturnRecodeMapper;
import com.sdsdiy.orderimpl.service.carriage.OrderApplyCarriageService;
import com.sdsdiy.orderimpl.service.carriage.OrderCarriageService;
import com.sdsdiy.orderimpl.service.order.OrderRefundService;
import com.sdsdiy.orderimpl.service.other.MerchantStoreService;
import com.sdsdiy.orderimpl.service.parcel.OrderParcelItemService;
import com.sdsdiy.orderimpl.service.parcel.OrderParcelLogisticsExpenseService;
import com.sdsdiy.orderimpl.service.parcel.OrderParcelService;
import com.sdsdiy.paymentapi.constant.OrderFinanceBillPeriodTypeEnum;
import com.sdsdiy.paymentapi.dto.msg.OrderFinanceUpdateMsg;
import com.sdsdiy.userapi.constant.EnumNotificationTitle;
import com.sdsdiy.userapi.constant.MsgModule;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.NotificationDTO;
import com.sdsdiy.userapi.dto.base.MerchantStoreRespDto;
import com.sdsdiy.userapi.dto.tenant.TenantRespDto;
import com.sdsdiy.userapi.dto.tenant.TenantSysUserDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.framework.AopContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.NO;
import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.logisticsapi.constant.CarriageNoRecodeConstant.*;
import static com.sdsdiy.orderapi.constant.OrderCarriageReturnRecodeConstant.*;

/**
 * 订单物流退回记录(OrderCarriageReturnRecode)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-01-03 14:19:53
 */
@Service("orderCarriageReturnRecodeService")
@DS("master")
@Slf4j
@RequiredArgsConstructor
public class OrderCarriageReturnRecodeService extends ServiceImpl<OrderCarriageReturnRecodeMapper, OrderCarriageReturnRecode> {
    private final OrderImportExtraInfoService orderImportExtraInfoService;
    @Resource
    @Lazy
    private OrderService orderService;
    @Resource
    private MerchantFeign merchantFeign;
    @Resource
    private TenantLogisticsFeign tenantLogisticsFeign;
    @Resource
    private OrderEventService orderEventService;
    @Resource
    private CarriageNoRecodeFeign carriageNoRecodeFeign;
    @Resource
    @Lazy
    private OrderApplyCarriageService orderApplyCarriageService;
    @Resource
    @Lazy
    private OrderCarriageService orderCarriageService;
    @Resource
    private NotificationFeign notificationFeign;
    @Resource
    private AddressService addressService;
    @Resource
    private CarriageDeclarationInfoFeign carriageDeclarationInfoFeign;
    @Resource
    private TenantSysUserFeign tenantSysUserFeign;
    @Resource
    private TenantFeign tenantFeign;
    @Resource
    private OrderParcelLogisticsExpenseService orderParcelLogisticsExpenseService;
    @Resource
    private OrderPreviewService orderPreviewService;
    @Resource
    private OrderParcelService orderParcelService;
    @Resource
    private OrderParcelItemService orderParcelItemService;
    @Resource
    private CountryExpressInfoNewFeign countryExpressInfoNewFeign;
    @Resource
    private RocketMQTemplate rocketMQTemplate;
    @Resource
    private OrderExtendInfoMapperManager orderExtendInfoMapperManager;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private EnvironmentVariables environmentVariables;
    @Resource
    private PlatformOrderExtendManage platformOrderExtendManage;
    @Resource
    private OrderRefundService orderRefundService;


    public PageListResultRespDto<OrderCarriageReturnRecodeRespDto> listByCondition(OrderCarriageReturnRecodeReqDto reqDto) {
        LambdaQueryWrapper<OrderCarriageReturnRecode> queryWrapper = Wrappers.lambdaQuery(OrderCarriageReturnRecode.class)
                .eq(reqDto.getLogisticsId() != null, OrderCarriageReturnRecode::getLogisticsId, reqDto.getLogisticsId())
                .eq(reqDto.getMerchantId() != null, OrderCarriageReturnRecode::getMerchantId, reqDto.getMerchantId())
                .eq(reqDto.getStatus() != null, OrderCarriageReturnRecode::getStatus, reqDto.getStatus())
                .ne(reqDto.getStatus() == null && reqDto.getBeginOrderHandleTime() != null && reqDto.getEndOrderHandleTime() != null, OrderCarriageReturnRecode::getStatus, STATUS_NO_HANDLE)
                .ge(reqDto.getBeginCreateTime() != null, OrderCarriageReturnRecode::getCreateTime, reqDto.getBeginCreateTime())
                .le(reqDto.getEndCreateTime() != null, OrderCarriageReturnRecode::getCreateTime, reqDto.getEndCreateTime())
                .ge(reqDto.getBeginOrderFinishTime() != null, OrderCarriageReturnRecode::getOrderFinishTime, reqDto.getBeginOrderFinishTime())
                .le(reqDto.getEndOrderFinishTime() != null, OrderCarriageReturnRecode::getOrderFinishTime, reqDto.getEndOrderFinishTime())
                .ge(reqDto.getBeginOrderHandleTime() != null, OrderCarriageReturnRecode::getOrderHandleTime, reqDto.getBeginOrderHandleTime())
                .le(reqDto.getEndOrderHandleTime() != null, OrderCarriageReturnRecode::getOrderHandleTime, reqDto.getEndOrderHandleTime())
                .in(CollectionUtil.isNotEmpty(reqDto.getIssuingBayIds()), OrderCarriageReturnRecode::getIssuingBayId, reqDto.getIssuingBayIds())
                .and(!StringUtils.isEmpty(reqDto.getKeyword()), wrapper -> wrapper
                        .like(OrderCarriageReturnRecode::getCarriageNo, reqDto.getKeyword().trim())
                        .or().like(OrderCarriageReturnRecode::getOrderNo, reqDto.getKeyword().trim())
                        .or().like(OrderCarriageReturnRecode::getNewCarriageNo, reqDto.getKeyword().trim())
                        .or().like(OrderCarriageReturnRecode::getMerchantName, reqDto.getKeyword().trim()));
        queryWrapper.orderByDesc(OrderCarriageReturnRecode::getId);
        Page<OrderCarriageReturnRecode> pagination = new Page<>(reqDto.getPage(), reqDto.getSize());
        Page<OrderCarriageReturnRecode> orderCarriageReturnRecodePage = baseMapper.selectPage(pagination, queryWrapper);
        List<OrderCarriageReturnRecode> records = orderCarriageReturnRecodePage.getRecords();
        List<OrderCarriageReturnRecodeRespDto> respList = RelationsBinder.convertAndBind(records, OrderCarriageReturnRecodeRespDto.class);
        PageListResultRespDto<OrderCarriageReturnRecodeRespDto> resultRespDto = new PageListResultRespDto<>();
        formatResp(respList);
        resultRespDto.setItems(respList);
        resultRespDto.setTotalCount((int) orderCarriageReturnRecodePage.getTotal());
        resultRespDto.setPage(orderCarriageReturnRecodePage.getPages());
        resultRespDto.setSize(orderCarriageReturnRecodePage.getSize());
        return resultRespDto;
    }

    public List<OrderCarriageReturnRecode> listByOrderParcelIdsAndStatus(List<Long> orderParcelIds, Integer status) {
        if (CollectionUtil.isEmpty(orderParcelIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<OrderCarriageReturnRecode> queryWrapper = Wrappers.lambdaQuery(OrderCarriageReturnRecode.class).in(CollectionUtil.isNotEmpty(orderParcelIds), OrderCarriageReturnRecode::getOrderId, orderParcelIds).eq(status != null, OrderCarriageReturnRecode::getStatus, status);
        return this.list(queryWrapper);
    }

    public List<OrderCarriageNoReturnUploadRecodeRespDto> changeCarriageNo(List<ChangeCarriageNoParam> params, List<Long> issuingBayIds) {
        List<String> carriageNos = params.stream().map(ChangeCarriageNoParam::getCarriageNo).collect(Collectors.toList());
        List<OrderCarriage> orderCarriages = orderCarriageService.findOrderCarriages(carriageNos);
        Assert.validateEmpty(orderCarriages, "导入的运单号不存在，请核对后重试");
        List<Long> orderIds = orderCarriages.stream().map(OrderCarriage::getOrderId).collect(Collectors.toList());
        List<Order> orders = orderService.listByIds(orderIds);
        Map<Long, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getId, o -> o));
        Map<String, List<OrderCarriage>> orderCarriageMap = orderCarriages.stream()
                .filter(ListUtils.distinctByKey(OrderCarriage::getCarriageNo))
                .collect(Collectors.groupingBy(OrderCarriage::getCarriageNo));
        Map<Long, String> merchantIdKeyNameValue = getMerchantIdKeyNameValue(orders);
        Map<Long, String> orderIdKeyExtendMap = orderExtendInfoMapperManager.mapByOrderIdAndCode(orderIds, OrderExtendKeyEnum.JIT_TYPE.getCode());
        Set<Long> logisticsIds = orders.stream().map(Order::getLogisticsId).collect(Collectors.toSet());
        List<LogisticsRespDto> logisticsResp = tenantLogisticsFeign.list(new BaseListReqDto(new ArrayList<>(logisticsIds)));
        Map<Long, String> logisticsMap = logisticsResp.stream().collect(Collectors.toMap(LogisticsRespDto::getId, LogisticsRespDto::getName));
        List<OrderCarriageNoReturnUploadRecodeRespDto> resultList = Lists.newArrayList();
        List<Long> needCancelCarriageInThirdPartyParcelIds = new ArrayList<>();

        //特殊拦截 temu半托管自动导入订单
        boolean haveTemuOrder = orders.stream().anyMatch(a -> MerchantStorePlatformEnum.TEMU.getCode().equalsIgnoreCase(a.getMerchantStorePlatformCode()));

        Map<Long, MerchantStore> merchantStoreMap = new HashMap<>();
        if (haveTemuOrder) {
            List<Long> merchantStoreIds = orders.stream().map(Order::getMerchantStoreId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(merchantStoreIds)) {
                List<MerchantStore> merchantStores = merchantStoreService.listByIds(merchantStoreIds);
                merchantStoreMap = merchantStores.stream().collect(Collectors.toMap(MerchantStore::getId, Function.identity()));
            }
        }
        Map<Long, MerchantStore> finalMerchantStoreMap = merchantStoreMap;

        params.forEach(param -> {
            OrderCarriageNoReturnUploadRecodeRespDto resp = new OrderCarriageNoReturnUploadRecodeRespDto();
            resp.setSerialNumber(param.getSerialNumber());
            resp.setCarriageNo(param.getCarriageNo());
            resp.setNewCarriageNo(param.getNewCarriageNo());
            List<OrderCarriage> orderCarriageList = orderCarriageMap.get(param.getCarriageNo());
            if (CollUtil.isEmpty(orderCarriageList)) {
                resp.setOrderNo("");
                resp.setFailMessage("订单不存在");
                resp.setResult(NO);
            } else {

                for (OrderCarriage orderCarriage : orderCarriageList) {

                    Order order = orderMap.get(orderCarriage.getOrderId());
                    resp.setResult(YES);
                    if (OrderExtendValueEnum.JIT.equalsCode(orderIdKeyExtendMap.get(orderCarriage.getOrderId()))) {
                        resp.setFailMessage("半托管订单不可添加");
                        resp.setOrderStatus(order.getStatus());
                        resp.setResult(NO);
                    } else if (MerchantStorePlatformEnum.SHEIN.equalsCode(order.getMerchantStorePlatformCode())
                            && OrderOriginType.isAutoImport(order.getOriginType())) {
                        resp.setFailMessage("shein半托管订单不可添加");
                        resp.setOrderStatus(order.getStatus());
                        resp.setResult(NO);
                    } else if (MerchantStorePlatformEnum.TEMU.getCode().equalsIgnoreCase(order.getMerchantStorePlatformCode()) && OrderOriginType.AUTO_IMPORT.getValue().equalsIgnoreCase(order.getOriginType())) {
                        MerchantStore merchantStore = finalMerchantStoreMap.get(order.getMerchantStoreId());
                        if (Objects.nonNull(merchantStore)
                                && MerchantStorePlatformEnum.TEMU.getCode().equalsIgnoreCase(merchantStore.getMerchantStorePlatformCode())) {
                            if (MerchantStoreTypeEnum.TEMU_LOCAL.getCode().equals(merchantStore.getType())) {
                                resp.setFailMessage("本本订单不可添加");
                                resp.setOrderStatus(order.getStatus());
                                resp.setResult(NO);
                            } else if (MerchantStoreTypeEnum.TEMU_SEMI.getCode().equals(merchantStore.getType())) {
                                resp.setFailMessage("半托管订单不可添加");
                                resp.setOrderStatus(order.getStatus());
                                resp.setResult(NO);
                            }
                        }
                    } else if (DeliveryTypeEnum.isTemuFully(order.getLogisticsCodeId())) {
                        resp.setFailMessage("temu全托管订单，不支持已完成换单");
                        resp.setOrderStatus(order.getStatus());
                        resp.setResult(NO);
                    } else if (YES.equals(resp.getResult()) && order.getStatus() != OrderStatus.FINISH.getStatus()) {
                        resp.setFailMessage("订单未完成");
                        resp.setOrderStatus(order.getStatus());
                        resp.setResult(NO);
                    } else if (YES.equals(resp.getResult()) && !issuingBayIds.contains(order.getIssuingBayId())) {
                        resp.setFailMessage("非本仓订单");
                        resp.setOrderStatus(order.getStatus());
                        resp.setResult(NO);
                    }
                    resp.setOrderNo(order.getNo());
                    if (YES.equals(resp.getResult())) {

                        ((OrderCarriageReturnRecodeService) AopContext.currentProxy())
                                .changeCarriageNoSave(merchantIdKeyNameValue, param, order, orderCarriage, logisticsMap);

                        if (StrUtil.isNotEmpty(param.getCarriageNo())
                                && StrUtil.isNotEmpty(param.getNewCarriageNo())
                                && !param.getCarriageNo().equals(param.getNewCarriageNo())) {
                            needCancelCarriageInThirdPartyParcelIds.add(orderCarriage.getOrderParcelId());
                        }
                    }
                }
            resultList.add(resp);
            }
        });

        if (CollUtil.isNotEmpty(needCancelCarriageInThirdPartyParcelIds)) {

            log.info("excel批量已完成换单，发送取消运单消息 orderParcelIds = {}", JSONObject.toJSONString(needCancelCarriageInThirdPartyParcelIds));

            GetNeedCancelCarriageByParcelReq req = new GetNeedCancelCarriageByParcelReq();
            req.setParcelIds(needCancelCarriageInThirdPartyParcelIds);
            rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, LogisticsTagConst.EVENT_RETURN_CODE_CANCEL_CARRIAGE, req);

        }

        return resultList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeCarriageNoSave(Map<Long, String> merchantIdKeyNameValue, ChangeCarriageNoParam param, Order order, OrderCarriage orderCarriage, Map<Long, String> logisticsMap) {
        OrderCarriageReturnRecode recode = new OrderCarriageReturnRecode();
        recode.setOrderParcelId(orderCarriage.getOrderParcelId());
        recode.setMerchantName(merchantIdKeyNameValue.get(order.getMerchantId()));
        recode.setNewCarriageNo(param.getNewCarriageNo());
        recode.setStatus(STATUS_CHANGE_NO);
        recode.setCarriageStatus(STATUS_SUCCESS);
        recode.setRemark(param.getRemark());
        recode.setUpdateUid(param.getUserId());
        recode.setCreateUid(param.getUserId());
        recode.setNewLogisticsName(logisticsMap.get(orderCarriage.getLogisticsId()));
        recode.setNewLogisticsId(orderCarriage.getLogisticsId());
        recode.setLogisticsSource(order.getLogisticsSource());
        recode.setTenantId(order.getTenantId());
        recode.setIssuingBayId(order.getIssuingBayId());
        OrderCarriageReturnRecode returnCode = this.saveOrUpdateByOrder(order, recode, true);
        orderCarriageService.updateCarriageByReturn(recode.getOrderId(), recode.getOrderParcelId(), orderCarriage.getLogisticsId(), null, null, param.getNewCarriageNo());

        SaveByOrderIdParam saveByOrderIdParam = new SaveByOrderIdParam();
        saveByOrderIdParam.setNewCarriageNo(param.getNewCarriageNo());
        saveByOrderIdParam.setOrderType(ORDER_TYPE_RETURN);
        saveByOrderIdParam.setBusinessId(returnCode.getId());
        saveByOrderIdParam.setUserId(param.getUserId());
        saveByOrderIdParam.setCarriageVersion(orderCarriage.getCarriageVersion() + 1);
        saveByOrderIdParam.setOrderParcelId(orderCarriage.getOrderParcelId());
        carriageNoRecodeFeign.saveChangeCarriageNoByOrderId(order.getId(), saveByOrderIdParam);

        sendOrderFinance(com.google.common.collect.Lists.newArrayList(recode), OrderFinanceBillPeriodTypeEnum.EXCEL_UPDATE_ORDER_CARRIAGE_NO);
        this.handleFinishSendMessage(STATUS_CHANGE_NO, recode.getOrderId());
        changeCarriageNoManuallyMessage(returnCode);
    }

    private OrderCarriageReturnRecode saveOrUpdateByOrder(Order order, OrderCarriageReturnRecode recode, boolean isUpdateCheck) {
        OrderCarriageRespDto orderCarriage = orderCarriageService.getOne(order.getId(), recode.getOrderParcelId(), null);
        recode.setOrderId(order.getId());
        recode.setOrderNo(order.getNo());
        recode.setOrderFinishTime(new Date(order.getFinishTime()));
        recode.setMerchantId(order.getMerchantId());
        recode.setAddressId(order.getAddressId());
        recode.setLogisticsId(orderCarriage.getLogisticsId());
        recode.setCarriageNo(orderCarriage.getCarriageNo());
        recode.setNewAddressId(order.getAddressId());
        if (order.getLogisticsId() == 0) {
            recode.setLogisticsName(order.getCarriageName());
        }
        OrderParcel orderParcel = orderParcelService.getById(recode.getOrderParcelId());
        if (orderParcel != null) {
            recode.setCarriageAmount(orderParcel.getCarriageAmount());
        } else {
            recode.setCarriageAmount(order.getCarriageAmount());
        }
        if (recode.getStatus() == null) {
            recode.setStatus(STATUS_NO_HANDLE);
        }
        if (recode.getStatus() == STATUS_CHANGE_NO) {
            recode.setOrderHandleTime(new Date());
        }
        if (isUpdateCheck) {
            LambdaQueryWrapper<OrderCarriageReturnRecode> queryWrapper = Wrappers.lambdaQuery(OrderCarriageReturnRecode.class)
                    .eq(OrderCarriageReturnRecode::getStatus, STATUS_NO_HANDLE)
                    .eq(OrderCarriageReturnRecode::getOrderParcelId, orderCarriage.getOrderParcelId())
                    .eq(OrderCarriageReturnRecode::getOrderId, recode.getOrderId());
            List<OrderCarriageReturnRecode> list = this.list(queryWrapper);
            if (CollectionUtil.isNotEmpty(list)) {
                recode.setId(list.get(0).getId());
                this.updateById(recode);
                return recode;
            }
        }
        save(recode);
        return recode;
    }

    private Map<Long, String> getMerchantIdKeyNameValue(List<Order> orders) {
        if (CollectionUtil.isEmpty(orders)) {
            return Maps.newHashMap();
        }
        List<Long> merchantIds = orders.stream().map(Order::getMerchantId).collect(Collectors.toList());
        List<MerchantRespDto> list = merchantFeign.list(new BaseListDto<>(merchantIds));
        return list.stream().collect(Collectors.toMap(MerchantRespDto::getId, MerchantRespDto::getName));
    }

    public boolean handle(OrderCarriageNoReturnHandleParam param, List<OrderCarriageReturnRecode> recodePos) {
        // 侵权无法选择云图发货
        checkInfringement(param, recodePos);

        List<Long> ids = param.getIds();
        List<OrderCarriageReturnRecode> recodes = Lists.newArrayList();
        for (Long id : ids) {
            OrderCarriageReturnRecode recode = BeanUtil.copyProperties(param, OrderCarriageReturnRecode.class);
            recode.setId(id);
            recode.setNewLogisticsName(param.getNewLogisticsName());
            recode.setOrderHandleTime(new Date());
            recodes.add(recode);
        }
        if (STATUS_REDIRECT == param.getStatus()) {
            //处理前先更新差价
            OrderCarriageReturnRecode recode = redirectHandle(param, recodePos, recodes);
            refundAndNotify(param, recode);
        }
        List<OrderCarriageReturnRecode> orderCarriageReturnRecodes = this.listByIds(ids);
        Map<Long, Long> idAndParcelIdMap = orderCarriageReturnRecodes.stream().collect(Collectors.toMap(OrderCarriageReturnRecode::getId, OrderCarriageReturnRecode::getOrderParcelId));

        boolean result = this.updateBatchById(recodes);

        if (Boolean.TRUE.equals(result)) {
            //已完成换单 旧运单去第三方物流系统申请取消
            Map<Long, OrderCarriageReturnRecode> recodeMap = recodePos.stream().collect(Collectors.toMap(OrderCarriageReturnRecode::getId, Function.identity()));
            List<Long> needCancelCarriageInThirdPartyParcelIds = new ArrayList<>();
            for (OrderCarriageReturnRecode recode : recodes) {
                OrderCarriageReturnRecode recodePo = recodeMap.get(recode.getId());

                if (Objects.nonNull(recodePo) && StrUtil.isNotEmpty(recodePo.getCarriageNo()) && !recodePo.getCarriageNo().equals(recode.getNewCarriageNo())) {
                    needCancelCarriageInThirdPartyParcelIds.add(idAndParcelIdMap.get(recode.getId()));
                }
            }

            if (CollUtil.isNotEmpty(needCancelCarriageInThirdPartyParcelIds)) {
                log.info("已完成换单，发送取消运单消息 orderParcelIds = {}", JSONObject.toJSONString(needCancelCarriageInThirdPartyParcelIds));

                GetNeedCancelCarriageByParcelReq req = new GetNeedCancelCarriageByParcelReq();
                req.setParcelIds(needCancelCarriageInThirdPartyParcelIds);
                req.setDonCancelCarriageNo(param.getNewCarriageNo());
                req.setDonCancelBusinessIds(ids);
                rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, LogisticsTagConst.EVENT_RETURN_CODE_CANCEL_CARRIAGE, req);
            }
        }
        //寄付类型的的发送已完成换单消息
        if (STATUS_REDIRECT == param.getStatus()) {
            sendOrderFinance(recodePos, OrderFinanceBillPeriodTypeEnum.ORDER_CARRIAGE_RETURN_CODE);
        }

        if (param.getIsIgnore() != null && YES.equals(param.getIsIgnore())) {
            List<String> carriageNos = recodePos.stream().map(OrderCarriageReturnRecode::getCarriageNo).collect(Collectors.toList());
            carriageNoRecodeFeign.updateIgnoreByCarriageNo(param.getUpdateUid(), YES, carriageNos, param.getIgnoreMessage());
        }

        return result;
    }

    private void checkInfringement(OrderCarriageNoReturnHandleParam param, List<OrderCarriageReturnRecode> recodePos) {
        List<Long> orderIds = recodePos.stream().map(i -> i.getOrderId()).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(orderIds)) {
            return;
        }
        boolean flagCheckLogistics = isCheckLogistics(param);
        if (flagCheckLogistics) {
            Map<Long, Boolean> matchBatch = orderExtendInfoMapperManager.isMatchBatch(orderIds, OrderExtendValueEnum.IMG_CHECK_STATUS_INFRINGEMENT);
            if(CollUtil.isNotEmpty(matchBatch)){
                boolean anyMatch = matchBatch.values().stream().anyMatch(BooleanUtil::isTrue);
                Assert.validateTrue(anyMatch, "订单存在侵权素材，不可使用云途渠道发货");
            }
        }
    }

    private boolean isCheckLogistics(OrderCarriageNoReturnHandleParam param) {
        if(!param.isNewAddressUs()){
            return false;
        }
        boolean flagCheckLogistics = false;
        TenantLogisticsRespDto tenantLogisticsRespDto = tenantLogisticsFeign.getDtoById(param.getNewLogisticsId());
        if (null != tenantLogisticsRespDto) {
            boolean isSds = TenantCommonConstant.isSdsdiy(tenantLogisticsRespDto.getTenantId());
            if (isSds) {
                if (environmentVariables.imgCheckLogisticsProviderServiceIdList().contains(tenantLogisticsRespDto.getServiceProviderId())) {
                    flagCheckLogistics = true;
                }
            }
        }
        return flagCheckLogistics;
    }

    private void sendOrderFinance(List<OrderCarriageReturnRecode> recodePos, OrderFinanceBillPeriodTypeEnum periodTypeEnum) {
        for (OrderCarriageReturnRecode recodePo : recodePos) {
            OrderFinanceUpdateMsg orderFinanceUpdateMsg = OrderFinanceUpdateMsg.builder()
                .orderId(recodePo.getOrderId())
                .orderNo(recodePo.getOrderNo())
                .billPeriodType(periodTypeEnum.getCode())
                .orderCarriageReturnCodeId(recodePo.getId())
                .createdAt(System.currentTimeMillis())
                .build();
            //mqTemplate.sendMessageAfterCommit(PaymentMq.ORDER_FINANCE_UPDATE_TOPIC, orderFinanceUpdateMsg);
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PAYMENT, PaymentTagConst.ORDER_FINANCE_UPDATE_TOPIC, orderFinanceUpdateMsg);
        }
    }

    public OrderCarriageReturnRecode redirectHandle(OrderCarriageNoReturnHandleParam param
            , List<OrderCarriageReturnRecode> recodePos, List<OrderCarriageReturnRecode> recodes) {
        OrderCarriageReturnRecode recode = recodePos.get(0);
        OrderCarriageReturnRecode updateRecode = recodes.get(0);
        updateRecode.setOrderId(recode.getOrderId());
        updateRecode.setOrderParcelId(recode.getOrderParcelId());
        updateRecode.setMerchantId(recode.getMerchantId());
        // 计算差价，默认情况下 租户差价=商户差价
        BigDecimal differentialPrice = CompareUtils.aElseB(param.getDifferentialPrice(), BigDecimal.ZERO);
        BigDecimal tenantDifferentialPrice = differentialPrice;
        if (CompareUtils.neZero(differentialPrice)) {
            BigDecimal tenantCarriageCommissionRate = orderImportExtraInfoService.findTenantCarriageCommissionRateById(recode.getOrderId());
            if (NumberUtils.greaterZero(tenantCarriageCommissionRate)) {
                // 有设置佣金比例时，扣除佣金后的租户差价
                tenantDifferentialPrice = CarriageAmountUtil.getTenantCarriageAmount(differentialPrice, tenantCarriageCommissionRate);
            }
        }
        //先保存下差价
        this.lambdaUpdate().eq(OrderCarriageReturnRecode::getId, recode.getId())
                .set(OrderCarriageReturnRecode::getDifferentialPrice, differentialPrice)
                .set(OrderCarriageReturnRecode::getTenantDifferentialPrice, tenantDifferentialPrice).update();
        try {
            if (param.getType() == TYPE_SYSTEM) {
                String newCarriageNo = this.redirectSystem(param, updateRecode);
                updateRecode.setNewCarriageNo(newCarriageNo);
            } else if (param.getType() == TYPE_MANUALLY) {
                manuallyMessage(recode, param);
                orderCarriageService.updateCarriageByReturn(recode.getOrderId(), recode.getOrderParcelId(), 0L, param.getNewLogisticsName(), param.getNewLabelUrl(), param.getNewCarriageNo());
            }
        } catch (Exception e) {
            this.lambdaUpdate().eq(OrderCarriageReturnRecode::getId, recode.getId())
                    .set(OrderCarriageReturnRecode::getDifferentialPrice, BigDecimal.ZERO)
                    .set(OrderCarriageReturnRecode::getTenantDifferentialPrice, BigDecimal.ZERO).update();
            throw e;
        }
        param.setTenantDifferentialPrice(tenantDifferentialPrice);
        return recode;
    }

    public void refundAndNotify(OrderCarriageNoReturnHandleParam param, OrderCarriageReturnRecode recode) {
        if (null == param.getDifferentialPrice() || param.getDifferentialPrice().compareTo(BigDecimal.ZERO) >= 0) {
            return;
        }
        //退款记账
        String logisticsName = getLogisticsName(param);
        Order order = orderService.findById(recode.getOrderId());
        MerchantRespDto merchantRespDto = merchantFeign.getMerchantById(order.getMerchantId());
        //orderPreviewService.changeOrderRefundForMerchantPay(param, logisticsName, order, merchantRespDto);
        orderRefundService.podRefundCompletedOrderChangePrice(param, logisticsName, order);
        //通知
        if (YES.equals(param.getIsInformMerchant())) {
            NotificationDTO notificationDTO = new NotificationDTO();
            String content = getContent(param, param.getDifferentialPrice().abs(), logisticsName, order);
            notificationDTO.setContent(content);
            notificationDTO.setMerchantId(order.getMerchantId());
            notificationDTO.setNotificationTitle(EnumNotificationTitle.CHANGE_ORDER_REFUND);
            notificationDTO.setMsgModule(MsgModule.SEND_NOTIFICATION);
            notificationFeign.saveNotificationDTO(notificationDTO);
        }
    }

    private static String getContent(OrderCarriageNoReturnHandleParam param, BigDecimal differentialPrice, String logisticsName, Order order) {
        String content = String.format(EnumNotificationTitle.CHANGE_ORDER_REFUND.getContent(), differentialPrice, order.getNo(), logisticsName);
        if (param.getType() == TYPE_MANUALLY) {
            content = content + "：" + param.getNewCarriageNo();
        }
        return content;
    }

    private String getLogisticsName(OrderCarriageNoReturnHandleParam param) {
        String logisticsName = "";
        if (param.getType() == TYPE_SYSTEM) {
            LogisticsRespDto logisticsRespDto = tenantLogisticsFeign.getOneLogisticsResp(param.getNewLogisticsId());
            logisticsName = logisticsRespDto.getName();
        } else if (param.getType() == TYPE_MANUALLY) {
            logisticsName = param.getNewLogisticsName();
        }
        return logisticsName;
    }


    private String redirectSystem(OrderCarriageNoReturnHandleParam param, OrderCarriageReturnRecode updateRecode) {
        if (param.getNewAddress() == null || param.getCarriageDeclarationInfo() == null) {
            throw new BusinessException("申报信息或者地址信息异常！");
        }
        Address address = BeanUtil.toBean(param.getNewAddress(), Address.class);
        if (address.getMerchantId() == null || address.getMerchantId() == 0) {
            address.setMerchantId(updateRecode.getMerchantId());
        }
        addressService.getOrSaveAddress(address);
        updateRecode.setNewAddressId(address.getId());

        CarriageDeclarationInfoReqDto carriageDeclarationInfo = param.getCarriageDeclarationInfo();
        carriageDeclarationInfo.setAddress(param.getNewAddress());
        carriageDeclarationInfo.getProductDeclarationList().forEach(p -> p.setDeclarePriceUsd(null));
        carriageDeclarationInfoFeign.save(param.getCarriageDeclarationInfo());
        return this.getCarriageNo(updateRecode, param.getUpdateUid(), address.getCountry());
    }

    public void update(Long id, AgainCarriageNoParam againCarriageNoParam) {
        OrderCarriageReturnRecode recode = new OrderCarriageReturnRecode();
        OrderCarriageReturnRecode one = this.getById(id);
        if (one != null && one.getStatus().equals(STATUS_REDIRECT) && one.getType() == TYPE_SYSTEM) {
            one.setNewLogisticsId(againCarriageNoParam.getNewLogisticsId());
            AddressRespDto newAddress = addressService.findById(one.getNewAddressId());
            Assert.validateNull(newAddress, "新地址异常！");
            recode.setNewCarriageNo(this.getCarriageNo(one, againCarriageNoParam.getUserId(), newAddress.getCountry()));
        } else {
            throw new BusinessException("非重寄系统生成运单！！");
        }
        recode.setId(id);
        recode.setNewLogisticsId(againCarriageNoParam.getNewLogisticsId());
        recode.setUpdateUid(againCarriageNoParam.getUserId());
        this.updateById(recode);
    }

    @Async
    public void handleFinishSendMessage(Integer status, Long orderId) {
        SendBackMsg sendBackMsg = new SendBackMsg();
        sendBackMsg.setOrderId(orderId);
        if (status == STATUS_PAID_BY_SHIPPER) {
            sendBackMsg.setMethod("寄付");
        }
        if (status == STATUS_DESTROY) {
            sendBackMsg.setMethod("销毁");
        }
        if (status == STATUS_REDIRECT) {
            sendBackMsg.setMethod("重寄");
        }
        if (status == STATUS_CHANGE_NO) {
            sendBackMsg.setMethod("同渠道换单");
        }
        notificationFeign.getFactorySysUserById(sendBackMsg);
    }


    public String getCarriageNo(OrderCarriageReturnRecode one, Long userId, String countryCode) {
        CountryExpressInfoNewRespDto countryCodeExpress = countryExpressInfoNewFeign.findByLogisticsIdAndCountryCode(one.getNewLogisticsId(), countryCode);
        Long countryExpressInfoNewId = 0L;
        if (countryCodeExpress != null && NumberUtils.greaterZero(countryCodeExpress.getId())) {
            countryExpressInfoNewId = countryCodeExpress.getId();
        }
        this.orderParcelService.updateLogistics(Collections.singletonList(one.getOrderParcelId())
                , one.getNewLogisticsId()
                , countryExpressInfoNewId, null);

        OrderApplyCarriageNoParam bo = new OrderApplyCarriageNoParam();
        bo.setOperationMode(OPERATION_MODE_STAFF);
        bo.setOrderId(one.getOrderId());
        bo.setOrderParcelId(one.getOrderParcelId());
        bo.setBusinessId(one.getId());
        bo.setUserId(userId);
        bo.setIsSyncOrder(true);
        bo.setNewAddressId(one.getNewAddressId());
        bo.setOrderType(ORDER_TYPE_RETURN);
        bo.setIsRestDeclaration(false);
        bo.setImmediatelyVerifyWeight(Boolean.TRUE);
        return orderApplyCarriageService.syncApplyCarriageNo(bo);
    }

    public void labelPrintingTime(Long id) {
        this.lambdaUpdate().eq(OrderCarriageReturnRecode::getId, id)
                .set(OrderCarriageReturnRecode::getLabelPrintingTime, System.currentTimeMillis())
                .update();
    }

    public List<LogisticsFreightRespDto> getLogisticsExpenses(Long id) {
        OrderCarriageReturnRecode returnRecode = this.getById(id);
        Assert.validateNull(returnRecode, "退单数据异常！");
        OrderParcelLogisticsFreightDTO param = new OrderParcelLogisticsFreightDTO();
        param.setOrderId(returnRecode.getOrderId());
        param.setOrderParcelId(returnRecode.getOrderParcelId());
        param.setNewAddressId(returnRecode.getNewAddressId());
        return orderParcelLogisticsExpenseService.getLogisticsFreight(param);
    }


    public int sendMessageToOrderFinance(Date beginTime, Date endTime) {
        List<OrderCarriageReturnRecode> recodePos = this.lambdaQuery().between(OrderCarriageReturnRecode::getCreateTime, beginTime, endTime)
                .gt(OrderCarriageReturnRecode::getStatus, 1)
                .list();
        int num = 0;
        for (OrderCarriageReturnRecode recodePo : recodePos) {
            OrderFinanceUpdateMsg orderFinanceUpdateMsg = OrderFinanceUpdateMsg.builder()
                    .orderId(recodePo.getOrderId())
                    .orderNo(recodePo.getOrderNo())
                    .billPeriodType(OrderFinanceBillPeriodTypeEnum.ORDER_CARRIAGE_RETURN_CODE.getCode())
                    .orderCarriageReturnCodeId(recodePo.getId())
                    .oldData(true)
                    .build();
            num++;
            //mqTemplate.sendMessageAfterCommit(PaymentMq.OLD_ORDER_FINANCE_UPDATE_TOPIC, orderFinanceUpdateMsg); 消费者已注释
        }
        return num;
    }


    public List<FindOrderCarriageReturnRecodeResp> findByCarriageNos(FindOrderCarriageReturnRecodeParam param) {
        List<OrderCarriage> orderCarriages = orderCarriageService.findOrderCarriages(param.getCarriageNos());
        if (CollectionUtil.isEmpty(orderCarriages)) {
            throw new BusinessException("运单获取到订单数据为空！！");
        }
        List<Long> orderIds = orderCarriages.stream().map(OrderCarriage::getOrderId).collect(Collectors.toList());
        List<Long> orderParcelIds = orderCarriages.stream().map(OrderCarriage::getOrderParcelId).collect(Collectors.toList());
        Map<Long, Order> orderMap = orderService.listByIds(orderIds).stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        Map<Long, OrderParcel> orderParcelMap = orderParcelService.findByIds(orderParcelIds).stream().collect(Collectors.toMap(OrderParcel::getId, Function.identity()));
        List<FindOrderCarriageReturnRecodeResp> recodes = Lists.newArrayList();
        Map<Long, Map<String, String>> orderIdMaps = orderExtendInfoMapperManager.mapsByOrderIds(orderIds);

        List<String> platformCodes = orderMap.values().stream().map(i -> i.getMerchantStorePlatformCode()).distinct().collect(Collectors.toList());
        List<String> outOrderNos = orderMap.values().stream().filter(i -> StrUtil.isNotBlank(i.getOutOrderNo())).map(i -> i.getOutOrderNo()).distinct().collect(Collectors.toList());
        Map<String, Map<String, Map<String, String>>> platformOrderExtendMap = this.platformOrderExtendManage
            .mapByOutIds(platformCodes,outOrderNos,com.sdsdiy.orderapi.constant.OrderOriginType.AUTO_IMPORT.getCode());

        //状态校验
        orderCarriages.forEach(orderCarriage -> {
            Map<String, String> extendMap = orderIdMaps.getOrDefault(orderCarriage.getOrderId(),Collections.emptyMap());
            //一年前的订单则不处理
            if (orderCarriage.getOrderFinishTime().before(DateUtil.offsetMonth(new Date(), -12))) {
                return;
            }
            Order order = orderMap.get(orderCarriage.getOrderId());
            Map<String, String> platformExtentMap = platformOrderExtendMap
                .getOrDefault(order.getMerchantStorePlatformCode(), Collections.emptyMap())
                .get(order.getOutOrderNo());
            OrderParcel orderParcel = orderParcelMap.get(orderCarriage.getOrderParcelId());
            Assert.validateNull(order, "订单信息异常！");
            Assert.validateNull(orderParcel, "包裹信息异常！");
            if (DeliveryTypeEnum.TEMU_FULLY.getCode().equals(orderParcel.getDeliveryType())) {
                throw new BusinessException("temu全托管订单，不支持已完成换单");
            }
            if (OrderExtendValueEnum.JIT.isMatch(extendMap)) {
                throw new BusinessException("半托管订单不可添加,订单号为:" + order.getNo());
            }
            if (!OrderParcelStatusEnum.SHIPPED.getCode().equals(orderParcel.getStatus())) {
                throw new BusinessException("包裹未发货不可添加,订单号为:" + order.getNo());
            }
            if (!param.getIssuingBayIds().contains(order.getIssuingBayId())) {
                throw new BusinessException("非本仓订单不可添加,订单号为:" + order.getNo());
            }
            if (OrderExtendValueEnum.TEMU_LOCAL_ORDER.isMatch(extendMap)) {
                throw new BusinessException("本本订单不可添加");
            }
            if (OrderExtendValueEnum.TEMU_SEMI_ORDER.isMatch(extendMap)) {
                throw new BusinessException("半托管订单不可添加");
            }
            if (PlatformOrderExtendValueEnum.SHEIN_SEMI_ORDER.isMatch(platformExtentMap)
                    && OrderOriginType.AUTO_IMPORT.equalsCode(order.getOriginType())) {
                throw new BusinessException("shein半托管订单不可添加，订单号为：" + order.getNo());
            }

            FindOrderCarriageReturnRecodeResp recode = new FindOrderCarriageReturnRecodeResp();
            recode.setOrderId(orderCarriage.getOrderId());
            recode.setOrderParcelId(orderCarriage.getOrderParcelId());
            recode.setParcelName("包裹" + orderParcel.getSortWeight());
            recode.setOrderNo(orderCarriage.getOrderNo());
            recode.setCarriageNo(orderCarriage.getCarriageNo());
            recodes.add(recode);
        });
        //是否存在处理中的
        List<OrderCarriageReturnRecode> recodeList = this.listByOrderParcelIdsAndStatus(orderParcelIds, STATUS_NO_HANDLE);
        if (CollectionUtil.isNotEmpty(recodeList)) {
            throw new BusinessException("订单未处理不可重复添加,输入的运单号为:" + recodeList.get(0).getCarriageNo() + "等");
        }
        return recodes;
    }

    public void saveBatchByOrderParcels(OrderCarriageNoReturnBatchAddParam param) {
        Set<Long> orderIds = param.getOrderParcels().stream().map(OrderCarriageNoReturnAddParam::getOrderId).collect(Collectors.toSet());
        Set<Long> orderParcelIds = param.getOrderParcels().stream().map(OrderCarriageNoReturnAddParam::getOrderParcelId).collect(Collectors.toSet());
        List<Order> orders = orderService.listByIds(orderIds);
        Map<Long, MerchantStoreRespDto> orderIdStoreMap = getOrderIdStoreMap(orders);
        for (Order order : orders) {
            MerchantStoreRespDto merchantStoreRespDto = orderIdStoreMap.get(order.getId());
            if (OrderOriginType.AUTO_IMPORT.getValue().equalsIgnoreCase(order.getOriginType()) &&
                    Objects.nonNull(merchantStoreRespDto)
                    && MerchantStorePlatformEnum.TEMU.getCode().equalsIgnoreCase(merchantStoreRespDto.getMerchantStorePlatformCode())
                    && YES.equals(merchantStoreRespDto.getIsPopChoice())) {
                throw new BusinessException("Temu半托管店铺自动导入订单不可已完成换单");
            }
        }
        List<OrderParcel> orderParcels = orderParcelService.findByIds(orderParcelIds);
        if (CollectionUtil.isEmpty(orderParcels) || CollectionUtil.isEmpty(orders)) {
            return;
        }
        Map<Long, String> merchantIdKeyNameValue = getMerchantIdKeyNameValue(orders);
        Map<Long, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        orderParcels.forEach(orderParcel -> {
            Order order = orderMap.get(orderParcel.getOrderId());
            if (order == null || !param.getIssuingBayIds().contains(order.getIssuingBayId())) {
                return;
            }
            OrderCarriageReturnRecode recode = new OrderCarriageReturnRecode();
            recode.setTenantId(order.getTenantId());
            recode.setOrderParcelId(orderParcel.getId());
            recode.setLogisticsSource(order.getLogisticsSource());
            recode.setIssuingBayId(order.getIssuingBayId());
            recode.setCreateUid(param.getCreatedUid());
            recode.setStatus(STATUS_NO_HANDLE);
            recode.setMerchantName(merchantIdKeyNameValue.get(order.getMerchantId()));
            saveOrUpdateByOrder(order, recode, false);
        });
    }

    public void formatResp(List<OrderCarriageReturnRecodeRespDto> respList) {
        if (CollectionUtil.isEmpty(respList)) {
            return;
        }
        Set<Long> orderParcelIds = respList.stream().map(OrderCarriageReturnRecodeRespDto::getOrderParcelId).collect(Collectors.toSet());
        Set<Long> logisticsIds = respList.stream().map(OrderCarriageReturnRecodeRespDto::getLogisticsId).collect(Collectors.toSet());
        Set<Long> newLogisticsIds = respList.stream().map(OrderCarriageReturnRecodeRespDto::getNewLogisticsId).collect(Collectors.toSet());
        logisticsIds.addAll(newLogisticsIds);
        List<LogisticsRespDto> logisticsResp = tenantLogisticsFeign.list(new BaseListReqDto(new ArrayList<>(logisticsIds)));
        Map<Long, OrderParcel> orderParcelMap = orderParcelService.findByIds(orderParcelIds).stream().collect(Collectors.toMap(OrderParcel::getId, Function.identity()));
        Map<Long, List<OrderParcelItemDTO>> orderParcelItemMap = this.orderParcelItemService.mapByParcelIds(new BaseListQueryDTO<>(orderParcelMap.keySet()));
        Map<Long, String> logisticsMap = logisticsResp.stream().collect(Collectors.toMap(LogisticsRespDto::getId, LogisticsRespDto::getName));
        Map<Long, LogisticsRespDto> logisticsRespMap = logisticsResp.stream().collect(Collectors.toMap(LogisticsRespDto::getId, Function.identity()));
        Map<Long, String> userNameMap = getUserNameMap(respList);
        List<Long> tenantIds = respList.stream().map(OrderCarriageReturnRecodeRespDto::getTenantId).collect(Collectors.toList());
        List<TenantRespDto> tenants = tenantFeign.getByIds(tenantIds);
        Map<Long, String> tenantMap = tenants.stream().collect(Collectors.toMap(TenantRespDto::getId, TenantRespDto::getName));
        // 租户物流佣金率
        Set<Long> orderIds = ListUtil.toValueSet(OrderCarriageReturnRecodeRespDto::getOrderId, respList);
        List<OrderImportExtraInfo> importExtraInfoList = orderImportExtraInfoService.findByIds(orderIds);
        Map<Long, Map<String, String>> orderIdKeyExtendMap = orderExtendInfoMapperManager.mapsByOrderIds(orderIds);
        Map<Long, BigDecimal> tenantCarriageCommissionRateMap = ListUtil.toMap(OrderImportExtraInfo::getId, OrderImportExtraInfo::getTenantCarriageCommissionRate, importExtraInfoList);
        // 店铺
        List<Order> orders = orderService.listByIds(orderIds);
        Map<Long, MerchantStoreRespDto> orderIdStoreMap = getOrderIdStoreMap(orders);

        for (OrderCarriageReturnRecodeRespDto respDto : respList) {
            if (StringUtils.isEmpty(respDto.getLogisticsName())) {
                respDto.setLogisticsName(logisticsMap.get(respDto.getLogisticsId()));
            }
            if (respDto.getStatus() == STATUS_REDIRECT && respDto.getNewLogisticsId() != 0 && StringUtils.isEmpty(respDto.getNewLogisticsName())) {
                respDto.setNewLogisticsName(logisticsMap.get(respDto.getNewLogisticsId()));
            }
            if (respDto.getNewLogisticsId() != 0 && logisticsRespMap.get(respDto.getNewLogisticsId()) != null) {
                LogisticsRespDto logisticsOne = logisticsRespMap.get(respDto.getNewLogisticsId());
                respDto.setNewServiceProviderId(logisticsOne.getServiceProviderId());
            }
            if (respDto.getStatus() == STATUS_REDIRECT && StringUtils.isEmpty(respDto.getNewLogisticsName())) {
                respDto.setNewLogisticsName(logisticsMap.get(respDto.getLogisticsId()));
            }
            respDto.setExtendMap(orderIdKeyExtendMap.get(respDto.getOrderId()));
            List<OrderItemRespDto> orderItems = respDto.getOrderItems();
            OrderParcel orderParcel = orderParcelMap.get(respDto.getOrderParcelId());
            int totalProductNum = 0;
            BigDecimal amount = BigDecimal.ZERO;
            List<OrderParcelItemDTO> parcelItemDTOS = orderParcelItemMap.get(respDto.getOrderParcelId());
            if (CollectionUtil.isNotEmpty(parcelItemDTOS)) {
                ArrayList<OrderItemRespDto> parcelOrderItems = Lists.newArrayList();
                Map<Long, OrderItemRespDto> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItemRespDto::getId, Function.identity()));
                totalProductNum = parcelItemDTOS.stream().mapToInt(OrderParcelItemDTO::getQty).sum();
                for (OrderParcelItemDTO item : parcelItemDTOS) {
                    OrderItemRespDto orderItemRespDto = orderItemMap.get(item.getOrderItemId());
                    if (orderItemRespDto == null) {
                        continue;
                    }
                    amount = amount.add(NumberUtil.mul(item.getQty(), orderItemRespDto.getAmount()));
                    OrderItemRespDto newOrderItem = BeanUtil.copyProperties(orderItemRespDto, OrderItemRespDto.class);
                    newOrderItem.setNum(item.getQty());
                    parcelOrderItems.add(newOrderItem);
                }
                respDto.setOrderItems(parcelOrderItems);
            } else {
                if (CollectionUtil.isNotEmpty(orderItems)) {
                    totalProductNum = orderItems.stream().mapToInt(OrderItemRespDto::getNum).sum();
                    amount = orderItems.stream().map(OrderItemRespDto::getAmount).reduce(BigDecimal::add).get();
                }
            }

            respDto.setCreateUserName(userNameMap.get(respDto.getCreateUid()));
            respDto.setHandleUserName(userNameMap.get(respDto.getUpdateUid()));
            if (STATUS_NO_HANDLE == respDto.getStatus()) {
                respDto.setOrderHandleTime(null);
                respDto.setHandleUserName(null);
            }
            respDto.setTenantName(tenantMap.get(respDto.getTenantId()));
            respDto.setAmount(amount);
            respDto.setTotalProductNum(totalProductNum);
            respDto.setParcelName(orderParcel == null ? "包裹1" : "包裹" + orderParcel.getSortWeight());
            respDto.setTenantCarriageCommissionRate(tenantCarriageCommissionRateMap.get(respDto.getOrderId()));
            respDto.setMerchantStore(orderIdStoreMap.get(respDto.getOrderId()));
        }
    }

    @NotNull
    private Map<Long, MerchantStoreRespDto> getOrderIdStoreMap(List<Order> orders) {
        List<Long> storeIds = orders.stream().map(i -> i.getMerchantStoreId()).distinct().collect(Collectors.toList());
        List<MerchantStore> merchantStores = merchantStoreService.listByIds(storeIds);
        List<MerchantStoreRespDto> merchantStoreRespDtos = BeanUtil.copyToList(merchantStores, MerchantStoreRespDto.class);
        Map<Long, MerchantStoreRespDto> merchantStoreRespDTOMap = ListUtil.toMap(MerchantStoreRespDto::getId, merchantStoreRespDtos);
        Map<Long, MerchantStoreRespDto> orderIdStoreMap=Maps.newHashMap();
        for (Order order : orders) {
            MerchantStoreRespDto merchantStoreRespDTO = merchantStoreRespDTOMap.get(order.getMerchantStoreId());
            orderIdStoreMap.put(order.getId(),merchantStoreRespDTO);
        }
        return orderIdStoreMap;
    }

    private Map<Long, String> getUserNameMap(List<OrderCarriageReturnRecodeRespDto> respList) {
        HashSet<Long> userIds = Sets.newHashSet();
        respList.forEach(r -> {
            userIds.add(r.getUpdateUid());
            userIds.add(r.getCreateUid());
        });
        List<Long> filterUserIds = userIds.stream().filter(id -> id != 0L).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(filterUserIds)) {
            return Maps.newHashMap();
        }
        List<TenantSysUserDto> users = tenantSysUserFeign.findByIds("", filterUserIds);
        return users.stream().collect(Collectors.toMap(TenantSysUserDto::getId, TenantSysUserDto::getUserName, (a, b) -> a));
    }

    /**
     * 功能描述: 消息发送 手动分配运单号
     *
     * @Author: lin_bin
     * @Date: 2021/1/5 17:33
     */
    private void manuallyMessage(OrderCarriageReturnRecode recode, OrderCarriageNoReturnHandleParam param) {
        OrderParcel orderParcel = orderParcelService.getById(recode.getOrderParcelId());
        AdminManualAllocationWaybillNumberMessage adminManualAllocationWaybillNumberMessage = new AdminManualAllocationWaybillNumberMessage();
        adminManualAllocationWaybillNumberMessage.setLogisticsName(param.getNewLogisticsName());
        adminManualAllocationWaybillNumberMessage.setLogisticsNo(param.getNewCarriageNo());
        adminManualAllocationWaybillNumberMessage.setParcelName(orderParcel == null ? "" : "包裹:" + orderParcel.getSortWeight());
        adminManualAllocationWaybillNumberMessage.setEid(recode.getOrderId());
        adminManualAllocationWaybillNumberMessage.setOrderId(recode.getOrderId());
        adminManualAllocationWaybillNumberMessage.setSendingTime(new Date());
        adminManualAllocationWaybillNumberMessage.setOperatorUid(param.getUpdateUid());
        adminManualAllocationWaybillNumberMessage.setDifferentialPrice(param.getDifferentialPrice() == null ? 0 : param.getDifferentialPrice().doubleValue());
        adminManualAllocationWaybillNumberMessage.setLaberPdf(param.getNewLabelUrl());
        orderEventService.sendOrderProcessMessage(adminManualAllocationWaybillNumberMessage, OrderProgressConstant.ADMIN_MANUAL_ALLOCATION_WAYBILL_NUMBER);
    }

    private void changeCarriageNoManuallyMessage(OrderCarriageReturnRecode recode) {
        OrderParcel orderParcel = orderParcelService.getById(recode.getOrderParcelId());
        AdminManualAllocationWaybillNumberMessage adminManualAllocationWaybillNumberMessage = new AdminManualAllocationWaybillNumberMessage();
        adminManualAllocationWaybillNumberMessage.setLogisticsName(recode.getNewLogisticsName());
        adminManualAllocationWaybillNumberMessage.setLogisticsNo(recode.getNewCarriageNo());
        adminManualAllocationWaybillNumberMessage.setParcelName(orderParcel == null ? "" : "包裹:" + orderParcel.getSortWeight());
        adminManualAllocationWaybillNumberMessage.setEid(recode.getOrderId());
        adminManualAllocationWaybillNumberMessage.setOrderId(recode.getOrderId());
        adminManualAllocationWaybillNumberMessage.setSendingTime(new Date());
        adminManualAllocationWaybillNumberMessage.setOperatorUid(recode.getUpdateUid());
        orderEventService.sendOrderProcessMessage(adminManualAllocationWaybillNumberMessage, OrderProgressConstant.ADMIN_MANUAL_ALLOCATION_WAYBILL_NUMBER);
    }


    //------------------------------即将废弃使用---------------------------
    public void saveBatchByOrderNos(OrderCarriageBatchParam param) {
        List<Order> orders = orderService.getOrderByOrderNos(param.getOrderNos());
        if (CollectionUtil.isEmpty(orders)) {
            return;
        }
        Map<Long, String> merchantIdKeyNameValue = getMerchantIdKeyNameValue(orders);
        orders.forEach(order -> {
            if (!param.getIssuingBayIds().contains(order.getIssuingBayId())) {
                return;
            }
            OrderCarriageReturnRecode recode = new OrderCarriageReturnRecode();
            recode.setTenantId(order.getTenantId());
            recode.setLogisticsSource(order.getLogisticsSource());
            recode.setIssuingBayId(order.getIssuingBayId());
            recode.setCreateUid(param.getCreatedUid());
            recode.setStatus(STATUS_NO_HANDLE);
            recode.setMerchantName(merchantIdKeyNameValue.get(order.getMerchantId()));
            saveOrUpdateByOrder(order, recode, false);
        });
    }
}