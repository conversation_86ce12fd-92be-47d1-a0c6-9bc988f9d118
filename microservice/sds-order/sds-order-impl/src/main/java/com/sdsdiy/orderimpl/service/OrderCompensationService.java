package com.sdsdiy.orderimpl.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.enums.CompensationStatus;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.core.base.util.DateUtil;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.util.OrderCodeUtil;
import com.sdsdiy.orderapi.constant.OrderConstant;
import com.sdsdiy.orderapi.constant.OrderOriginType;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.constant.event.message.system.SystemEndReminderOrderMessage;
import com.sdsdiy.orderapi.dto.order.OrderRefundHandleParam;
import com.sdsdiy.orderapi.enumeration.EnumProductCompensationStatus;
import com.sdsdiy.orderapi.enums.CompensationType;
import com.sdsdiy.orderdata.constant.order.SupplyChainTypeEnum;
import com.sdsdiy.orderdata.dto.OrderPaymentTypeDTO;
import com.sdsdiy.orderdata.enums.OrderStatus;
import com.sdsdiy.orderimpl.entity.po.Order;
import com.sdsdiy.orderimpl.entity.po.OrderItem;
import com.sdsdiy.orderimpl.entity.po.order.OrderItemSupplyChain;
import com.sdsdiy.orderimpl.feign.*;
import com.sdsdiy.orderimpl.feign.user.MerchantUserAccountFeign;
import com.sdsdiy.orderimpl.service.order.OrderItemSupplyChainService;
import com.sdsdiy.orderimpl.service.order.OrderRefundService;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentapi.dto.RefundDto;
import com.sdsdiy.paymentapi.dto.TenantWalletDto;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentapi.param.bill.MerchantBillCreateParam;
import com.sdsdiy.productapi.dto.ProductSimpleDto;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.UserAccountBalanceResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Log4j2
@DS("common")
@RequiredArgsConstructor
public class OrderCompensationService {
    private final OrderService orderService;
    private final OrderAmountService orderAmountService;
    private final MerchantFeign merchantFeign;
    private final RefundFeign refundFeign;
    private final MerchantBillFeign merchantBillFeign;
    private final MerchantUserAccountFeign merchantUserAccountFeign;
    private final TenantWalletFeign tenantWalletFeign;
    private final OrderPaymentTypeService orderPaymentTypeService;
    private final OrderRefundService orderRefundService;
    private final RocketMQTemplate rocketMQTemplate;

    @Resource
    OrderItemService orderItemService;
    @Resource
    OrderItemSupplyChainService orderItemSupplyChainService;

    @Resource
    ProductFeign productFeign;

    @Async
    public void asyncRefreshOrderCompensation(Long id) {
        this.refreshOrderCompensation(id);
    }

    private static Integer getProductionCycle(Map<Long, String> itemIdSupplyChainMap, Long orderItemId, ProductSimpleDto productSimpleDto) {
        String supplyChainType = itemIdSupplyChainMap.getOrDefault(orderItemId, SupplyChainTypeEnum.ONE_PIECE.name());
        return SupplyChainTypeEnum.isSmallOrder(supplyChainType) ? productSimpleDto.getSmallOrderProductionCycle() : productSimpleDto.getProductionCycle();
    }

    public void refreshOrderCompensation(Long id) {
        List<OrderItem> orderItems = this.orderItemService.findByOrderId(id);
        List<Long> productIds = orderItems.stream().map(OrderItem::getProductId).collect(Collectors.toList());
        log.info("refreshOrderCompensation id {}", id);
        if (CollectionUtil.isEmpty(productIds)) {
            log.info("refreshOrderCompensation return id {}", id);
            return;
        }
        List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());
        List<OrderItemSupplyChain> orderItemSupplyChains = this.orderItemSupplyChainService.listByIds(orderItemIds);
        Map<Long, String> itemIdSupplyChainMap = orderItemSupplyChains.stream().collect(Collectors.toMap(OrderItemSupplyChain::getId, OrderItemSupplyChain::getSupplyType, (a, b) -> b));

        List<ProductSimpleDto> productSimpleDtos = this.productFeign.findBySimpleIds(StringHelper.listConvertToString(productIds), null, "id,parent,productionCycle");
        Map<Long, ProductSimpleDto> productSimpleDtoMap = productSimpleDtos.stream().collect(Collectors.toMap(ProductSimpleDto::getId, Function.identity()));
        List<OrderItem> updateList = Lists.newArrayList();
        for (OrderItem orderItem : orderItems) {
            ProductSimpleDto productSimpleDto = productSimpleDtoMap.get(orderItem.getProductId());
            if (productSimpleDto != null && productSimpleDto.getParent() != null) {
                OrderItem updateParam = new OrderItem();
                updateParam.setId(orderItem.getId());
//                log.info("refreshOrderCompensation update data {}",productSimpleDto);

                updateParam.setCompensationStatus(productSimpleDto.getParent().getCompensationStatus());
                Integer productionCycle = getProductionCycle(itemIdSupplyChainMap, orderItem.getId(), productSimpleDto);
                updateParam.setProductionCycleMax(productionCycle);
                updateList.add(updateParam);
            }
        }
        if (CollectionUtil.isEmpty(updateList)) {
            return;
        }
        this.orderItemService.updateBatchById(updateList);
    }


    public void compensationCalculate(Long orderId, Long userId) {
        Order order = this.orderService.findById(orderId);
        log.info(orderId + " compensationCalculate in");
        if (order.getOriginType().equals(OrderOriginType.FBA.getValue())) {
            return;
        }
        Long calculateTime = null;
        //如果在质检之后上传
        if (order.getAccomplishTime() < order.getUploadLaberTime()) {
            calculateTime = order.getAccomplishTime();
        } else {
            calculateTime = System.currentTimeMillis();
        }
        Long beyondTime = calculateTime - order.getCompensationTime();
        double amount;
        Double productAmount = 0D;
        if (order.getStatus() == OrderStatus.FINISH.getStatus() || OrderConstant.ADVANCE.equals(order.getIsAdvance())) {
            List<OrderItem> orderItemList = this.orderItemService.findRealItemByOrderIds(Collections.singleton(order.getId()));
            for (OrderItem orderItem : orderItemList) {
                if (EnumProductCompensationStatus.OPEN.getStatus().equalsIgnoreCase(orderItem.getCompensationStatus())) {
                    productAmount = DoubleUtils.add(orderItem.getAmount(), productAmount);
                }
            }
        } else {
            Assert.wrong("订单结束才能计算赔付金额");
        }
        if (beyondTime < CompensationType.HOUR24.getConditionTime()) {
            amount = 0D;
        } else if (beyondTime < CompensationType.HOUR48.getConditionTime()) {
            amount = DoubleUtils.mul(productAmount, 0.2);
        } else if (beyondTime < CompensationType.HOUR72.getConditionTime()) {
            amount = DoubleUtils.mul(productAmount, 0.5);
        } else {
            amount = productAmount;
        }
        log.info(amount + " compensationCalculate amount");
        if (!CompensationStatus.COMMIT.getIntCode().equals(order.getCompensationStatus())) {
            return;
        }
        int status = 0 < amount ? CompensationStatus.SUCCESS.getIntCode() : CompensationStatus.REJECT.getIntCode();
        this.newCompensationFinish(order, calculateTime, amount, status, beyondTime);
        //消息发送
        this.sendMessageEndReminder(orderId, userId, beyondTime, amount);
    }


    /***
     *消息发送 催单结束
     */
    private void sendMessageEndReminder(Long id, Long userId, Long beyondTime, Double amount) {
        SystemEndReminderOrderMessage message = new SystemEndReminderOrderMessage();
        message.setOrderId(id);
        message.setOperatorUid(userId);
        message.setEid(id);
        message.setSendingTime(new Date());
        message.setCompensationAmount(amount);
        message.setReminderDuration(TimeUtil.getTimeHHMMSS(beyondTime));
//        this.mqTemplate.sendMessage(OrderProgressConstant.ORDER_PROGRESS_TOPIC
//                , OrderProgressConstant.SYSTEM_END_REMINDER_ORDER, message);
        this.rocketMQTemplate.sendNormal(
                RocketMqTopicConst.ORDER_PROGRESS_TOPIC,
                OrderProgressConstant.SYSTEM_END_REMINDER_ORDER,
                message);
    }

    public void newCompensationFinish(Order order, Long compensationFinishTime, Double amount, int status, Long time) {
        this.orderService.compensationFinish(order.getId(), compensationFinishTime, status, BigDecimal.valueOf(amount));
        if (!NumberUtils.greaterZero(amount)) {
            return;
        }
        String content = "订单号：%s，催单超时%s，经核算赔付%.2f元，已打入到您的账户余额中！";
        String title = String.format(content, order.getNo(), DateUtil.timeString(time), amount);
        OrderRefundHandleParam handleParam = new OrderRefundHandleParam();
        handleParam.setOrderId(order.getId())
                .setBalance(BigDecimal.valueOf(amount))
                .setTenantBalance(BigDecimal.ZERO)
                .setBonus(BigDecimal.ZERO)
                .setTenantBonus(BigDecimal.ZERO)
                .setSubject("赔付")
                .setTitle(title)
                .setRemark("")
                .setPurposeType(PurposeType.PAYOUT.getCode())
                .setDetailPurpose(DetailPurpose.SAAS_COMPENSATION_TO_MERCHANT.getCode())
                .setOperateRole(PaymentRoleEnum.SAAS.getCode())
                .setOperateRoleId(0L);
        orderRefundService.refundHandle(handleParam);
    }


//    public void compensationFinish(Order order, Long compensationFinishTime, Double amount, int status, Long time) {
//        this.orderService.compensationFinish(order.getId(), compensationFinishTime, status, BigDecimal.valueOf(amount));
//        if (!NumberUtils.greaterZero(amount)) {
//            return;
//        }
//        MerchantRespDto merchantRespDto = this.merchantFeign.getMerchantById(order.getMerchantId());
//        Long orderPaymentUserId = this.getOrderPaymentUserId(order.getId());
//
//        RefundParam refundParam = this.generateCompensationRefundDtoForMerchantPay(merchantRespDto, orderPaymentUserId, amount);
//
//        List<RefundDto> refundDtos = this.refundFeign.batchCreate(new RefundsCreateParam(Lists.newArrayList(refundParam)));
//
//        List<MerchantBillCreateParam> merchantBillCreateParams = Lists.newArrayList();
//        String content = "订单号：%s，催单超时%s，经核算赔付%.2f元，已打入到您的账户余额中！";
//        String object = String.format(content, order.getNo(), DateUtil.timeString(time), amount);
//        MerchantBillDisposeMoney merchantBillDisposeMoney = this.getDisposeMoney(merchantRespDto.getTenantId(), merchantRespDto.getId(), 0L);
//
//        for (RefundDto refundDto : refundDtos) {
//            merchantBillCreateParams.add(genMerchantCompensation(refundDto, merchantBillDisposeMoney, object, refundDto.getTotalAmount(), order.getNo()));
//
//        }
//        if (CollectionUtils.isNotEmpty(merchantBillCreateParams)) {
//            this.merchantBillFeign.batchCreate(new MerchantBillsCreateParam(merchantBillCreateParams));
//        }
//
//    }

    public Long getOrderPaymentUserId(Long orderId) {
        OrderPaymentTypeDTO orderPaymentTypeDTO = this.orderPaymentTypeService.findById(orderId);
        if (orderPaymentTypeDTO == null) {
            return 0L;
        } else {
            return orderPaymentTypeDTO.getPaymentUid();
        }
    }

    private RefundParam generateCompensationRefundDtoForMerchantPay(MerchantRespDto merchantRespDto, Long userId, Double balance) {
        RefundParam refundParam = new RefundParam();
//        PaymentParam paymentParam = new PaymentParam();
        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        //租户一定是通过余额付给商户
        refundParam.setBalance(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(0d, BigDecimal.ROUND_CEILING));
        refundParam.setPayMethod(PaymentMethodEnum.BALANCE.getCode());

        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        refundParam.setSubject("赔付");

        refundParam.setTotalAmount(com.sdsdiy.common.base.helper.DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setTargetTenantId(merchantRespDto.getTenantId());
        refundParam.setTargetMerchantId(merchantRespDto.getId());
        refundParam.setTargetUserId(userId);
        refundParam.setTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        //saas 退款给商户
        refundParam.setSourceMerchantId(0L);
        refundParam.setSourceTenantId(0L);
        refundParam.setSourceRole(PaymentRoleEnum.SAAS.getCode());
        refundParam.setTargetUserId(0L);
        refundParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.COMMON_BALANCE.getUsedType());
        refundParam.setDetailPurpose(DetailPurpose.SAAS_RECHARGE_TO_MERCHANT.getCode());
        return refundParam;
    }

    //产品租户推
    private RefundParam genCompensationRefundForMerchant(MerchantRespDto merchantRespDto,
                                                         Long orderPaymentUserId,
                                                         Long productTenantId,
                                                         Double balance) {


        RefundParam refundParam = new RefundParam();
        refundParam.setPayType(TransactionPayTypeEnum.MAIN.getValue())
                .setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus())
                .setOperateRole(PaymentRoleEnum.SYSTEM.getCode())
                .setOperateUserId(0L)
                .setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode())// 影响了谁
                .setOperateTargetRoleId(merchantRespDto.getId());


        refundParam.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        refundParam.setBalance(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setBonus(DoubleUtils.number2BigDecimal(0d, BigDecimal.ROUND_CEILING));
        refundParam.setPayMethod(PaymentMethodEnum.BALANCE.getCode());
        refundParam.setPurposeType(PurposeType.REFUND.getCode());
        refundParam.setSubject("赔付");

        refundParam.setTotalAmount(DoubleUtils.number2BigDecimal(balance, BigDecimal.ROUND_CEILING));
        refundParam.setTargetTenantId(merchantRespDto.getTenantId());
        refundParam.setTargetMerchantId(merchantRespDto.getId());
        refundParam.setTargetUserId(orderPaymentUserId);
        refundParam.setTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        //产品租户退款给商户
        refundParam.setSourceMerchantId(0L);
        refundParam.setSourceTenantId(productTenantId);
        refundParam.setSourceRole(PaymentRoleEnum.TENANT_SUP.getCode());
        refundParam.setMerchantBalanceUsedType(MerchantPayBalanceUsedType.COMMON_BALANCE.getUsedType());
        refundParam.setDetailPurpose(DetailPurpose.TENANT_COMPENSATION_TO_MERCHANT.getCode());
        return refundParam;
    }


    public MerchantBillDisposeMoney getDisposeMoney(Long tenantId, Long merchantId, Long userId) {
        UserAccountBalanceResp userAccountBalanceResp = this.merchantUserAccountFeign.getUserBalance(merchantId, userId);
        TenantWalletDto tenantWallet = this.tenantWalletFeign.get(tenantId);
        MerchantBillDisposeMoney merchantBillDisposeMoney = generateDisposeMonty(tenantWallet, userAccountBalanceResp);
        return merchantBillDisposeMoney;
    }

    public static MerchantBillDisposeMoney generateDisposeMonty(TenantWalletDto tenantWallet, UserAccountBalanceResp userAccountBalanceResp) {
        MerchantBillDisposeMoney disposeMoney = new MerchantBillDisposeMoney();
        if (ObjectUtil.isNotNull(tenantWallet)) {
            disposeMoney.setDisposeTenantGift(tenantWallet.getGiftMoney())
                    .setDisposeTenantBalance(tenantWallet.getBalance());
        }
        if (ObjectUtil.isNotNull(userAccountBalanceResp)) {
            disposeMoney.setDisposeMerchantGift(userAccountBalanceResp.getMerchantFreeGold())
                    .setDisposeMerchantBalance(userAccountBalanceResp.getMerchantBalance())
                    .setBalanceType(userAccountBalanceResp.getBalanceType())
            ;
        }
        return disposeMoney;
    }

    public static MerchantBillCreateParam genMerchantCompensation(RefundDto refundDto, MerchantBillDisposeMoney disposeMoney, String object, BigDecimal totalAmount, String no) {
        PurposeType purposeType = PurposeType.PAYOUT;
        String sourceRole = refundDto.getSourceRole();
        Long sourceTenantId = refundDto.getSourceTenantId();
        Long sourceMerchantId = refundDto.getSourceMerchantId();

        String targetRole = refundDto.getTargetRole();
        Long targetTenantId = refundDto.getTargetTenantId();
        Long targetMerchantId = refundDto.getTargetMerchantId();
        Long targetUserId = refundDto.getTargetUserId();

        return MerchantBillCreateParam.builder()
                .bizNo(no)
                .tradeNo(refundDto.getTradeNo())
                .orderName(object)
                .sourceRole(sourceRole)
                .sourceTenantId(sourceTenantId)
                .sourceMerchantId(sourceMerchantId)
                .sourceUserId(0L)
                .operateUserId(0L)
                .operateRole(PaymentRoleEnum.SAAS.getCode())
                .operateTargetRole(PaymentRoleEnum.MERCHANT.getCode())
                .operateTargetRoleId(targetMerchantId)
                .remarks("")
                .targetRole(targetRole)
                .targetTenantId(targetTenantId)
                .targetMerchantId(targetMerchantId)
                .targetUserId(targetUserId)
                .paymentMethod(PaymentMethodEnum.BALANCE.getCode())
                .totalMoney(disposeMoney.getDisposeTenantBalance())
                .freeGold(disposeMoney.getDisposeTenantGift())
                .changedBalance(totalAmount)
                .changedGift(BigDecimal.ZERO)
                .purposeType(purposeType.getCode())
                .detailPurpose(DetailPurpose.SAAS_COMPENSATION_TO_MERCHANT.getCode())
                .disposeTenantBalance(disposeMoney.getDisposeTenantBalance())
                .disposeTenantGift(disposeMoney.getDisposeTenantGift())
                .disposeMerchantBalance(disposeMoney.getDisposeMerchantBalance())
                .disposeMerchantGift(disposeMoney.getDisposeMerchantGift())
                .payChannel(PayChannelEnum.BALANCE.getCode())
                .build();
    }
}

