package com.sdsdiy.orderimpl.controller.offlinepay;


import com.alibaba.fastjson.JSON;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.entity.dto.PageResult;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.orderapi.api.OfflinePayRecordApi;
import com.sdsdiy.orderapi.constant.event.message.OfflinePayResetMessage;
import com.sdsdiy.orderapi.dto.adminprepaid.OfflinePayCommitResp;
import com.sdsdiy.orderapi.dto.offlinepay.*;
import com.sdsdiy.orderimpl.bo.BatchPaymentBo;
import com.sdsdiy.orderimpl.bo.OfflinePayPaymentGen;
import com.sdsdiy.orderimpl.entity.po.OfflinePayPayment;
import com.sdsdiy.orderimpl.feign.TenantWalletFeign;
import com.sdsdiy.orderimpl.service.offlinepay.*;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.dto.TenantWalletDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.sdsdiy.paymentapi.constant.PaymentMethodEnum.BALANCE;

/**
 * 线下付款记录表(OfflinePayRecord)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-24 17:43:46
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class OfflinePayRecordController implements OfflinePayRecordApi {

    private final OfflinePayRecordService offlinePayRecordService;
    private final OfflinePayRefundService offlinePayRefundService;
    private final OfflinePayRecordOrderService offlinePayRecordOrderService;
    private final OfflinePayRecordPaymentService offlinePayRecordPaymentService;
    private final OfflinePayPaymentService payPaymentService;
    private final RocketMQTemplate rocketMQTemplate;

    private final TenantWalletFeign tenantWalletFeign;

    @Override
    public PageResult<OfflinePayRecordPageResp> page(OfflinePayRecordPageParam param) {
        return offlinePayRecordService.page(param);
    }

    @Override
    public List<OfflinePayRecordExportResp> exportList(OfflinePayRecordPageParam param) {
        return offlinePayRecordService.exportList(param);
    }

    @Override
    public OfflinePayDetailResp batchPayDetail(OfflinePayBatchDetailParam param) {
        return payPaymentService.batchPayDetail(param);
    }

    @Override
    public List<OfflinePayPaymentDetailResp> listByTradeNo(String tradeNo) {
        return offlinePayRecordService.listByTradeNo(tradeNo);
    }

    @Override
    public OfflinePayRecordPageResp one(Long id, String fieldsName) {
        return offlinePayRecordService.one(id, fieldsName);
    }

    @Override
    public OfflinePayCommitResp save(OfflinePayParam param) {
        log.info("batchPay parma {}", JSON.toJSONString(param));
        if (!BALANCE.getCode().equalsIgnoreCase(param.getPaymentMethod())) {
            TenantWalletDto tenantWalletDto = tenantWalletFeign.get(param.getTenantId());
            Assert.validateBool(tenantWalletDto.getMerchantPayType().equalsIgnoreCase(param.getPaymentMethod()),"在线支付方式已变更，请刷新页面");
        }
        BatchPaymentBo batchPaymentBo = offlinePayRecordPaymentService.offlinePayParamPreview(param);
        OfflinePayPayment offlinePayPayment = offlinePayRecordPaymentService.batchPayments(batchPaymentBo);
        rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.TOP_EVENT_OFFLINE_PAY_RESET_SUMMARY, new OfflinePayResetMessage(param.getMerchantId()));
        return OfflinePayPaymentGen.generateOfflinePayCommitResp(offlinePayPayment, null);
    }
    @Override
    public PaymentDto refreshPayment(Long paymentId) {
        return offlinePayRecordService.refreshPayment(paymentId);
    }

    @Override
    public void refundByRecordId(Long recordId, String subject) {
        offlinePayRefundService.refundByRecordId(recordId, subject);
    }

    @Override
    public void offlinePayRecordOrderUpdate(Long offlinePayRecordId, Long businessId, String orderNo) {
        offlinePayRecordOrderService.offlinePayRecordOrderUpdate(offlinePayRecordId, businessId, orderNo);
    }

    @Override
    public void autoCreateOfflinePayRecode(AutoCreateOfflinePayRecodeParam param) {
        offlinePayRecordService.autoCreateOfflinePayRecode(param);
    }


    @Override
    public List<OfflinePayRecordReconciliationResp> reconciliationList(BaseListReqDto req) {
        return offlinePayRecordService.reconciliationList(req);
    }

    @Override
    public void offlinePayRecordOrderBatchUpdate(List<OfflinePayRecordOrderBatchUpdateParam> params) {
        offlinePayRecordOrderService.offlinePayRecordOrderBatchUpdate(params);
    }

    @Override
    public List<OfflinePayRecordPageResp> listNotFinishRecordsBySupAndDisTenantId(Long supTenantId, Long disTenantId) {
        return offlinePayRecordService.listNotFinishRecordsBySupAndDisTenantId(supTenantId, disTenantId);
    }

}