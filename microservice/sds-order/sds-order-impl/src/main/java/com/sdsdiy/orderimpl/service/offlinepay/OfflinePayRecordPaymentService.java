package com.sdsdiy.orderimpl.service.offlinepay;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.McContentHelper;
import com.sdsdiy.core.util.OrderCodeUtil;
import com.sdsdiy.logisticsdata.util.CarriageAmountUtil;
import com.sdsdiy.orderapi.constant.OfflinePayRecordConstant;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayBatchPayParam;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayParam;
import com.sdsdiy.orderimpl.bo.BatchPaymentBo;
import com.sdsdiy.orderimpl.bo.OfflinePayPaymentGen;
import com.sdsdiy.orderimpl.bo.OfflinePayRecordGen;
import com.sdsdiy.orderimpl.entity.po.OfflinePayAmountDetail;
import com.sdsdiy.orderimpl.entity.po.OfflinePayPayment;
import com.sdsdiy.orderimpl.entity.po.OfflinePayRecord;
import com.sdsdiy.orderimpl.entity.po.OfflinePayRecordOrder;
import com.sdsdiy.orderimpl.feign.MerchantFeign;
import com.sdsdiy.orderimpl.feign.payment.TransactionFeign;
import com.sdsdiy.orderimpl.feign.user.MerchantUserAccountFeign;
import com.sdsdiy.orderimpl.manager.OfflinePayAmountDetailMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayPaymentMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayRecordMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayRecordOrderMapperManager;
import com.sdsdiy.paymentapi.constant.PaymentMethodEnum;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import com.sdsdiy.paymentapi.constant.TransactionEntryTypeEnum;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.TransactionEntryParam;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.base.MerchantUserAccountRespDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.orderapi.constant.OfflinePayRecordConstant.StatusEnum.FAILED;
import static com.sdsdiy.orderapi.constant.OfflinePayRecordConstant.StatusEnum.UN_PAID;
import static com.sdsdiy.paymentapi.constant.PaymentMethodEnum.OFFLINE;
import static com.sdsdiy.paymentapi.constant.PaymentMethodEnum.balancePay;

/**
 * @author: bin_lin
 * @date: 2023/10/12 19:51
 * @desc:
 */
@Slf4j
@Service
@DS("master")
@RequiredArgsConstructor(onConstructor_ = {@Autowired, @Lazy})
public class OfflinePayRecordPaymentService {
    private final MerchantFeign merchantFeign;
    private final MerchantUserAccountFeign merchantUserAccountFeign;
    private final TransactionFeign transactionFeign;

    private final OfflinePayRecordMapperManager offlinePayRecordMapperManager;
    private final OfflinePayRecordOrderMapperManager offlinePayRecordOrderMapperManager;
    private final OfflinePayPaymentMapperManager offlinePayPaymentMapperManager;
    private final OfflinePayAmountDetailMapperManager offlinePayAmountDetailMapperManager;

    private final OfflinePayRecordService offlinePayRecordService;
    private final OfflinePaySaveService offlinePaySaveService;
    private final OfflinePayService offlinePayService;


    public BatchPaymentBo offlinePayParamPreview(OfflinePayParam param) {
        //参数校验
        offlinePayRecordService.offlineCheckParam(param);
        MerchantRespDto merchant = merchantFeign.getMerchantById(param.getMerchantId());
        Assert.validateNull(merchant, "商户异常");
        param.setBizNo(OrderCodeUtil.getOrderIdByUUId());
        OfflinePayRecord offlinePayRecord = OfflinePayRecordGen.generateOfflinePayPo(param, merchant);
        OfflinePayPayment offlinePayPayment = OfflinePayPaymentGen.generateOfflinePayPaymentPo(param, offlinePayRecord);
        List<OfflinePayRecordOrder> offlinePayRecordOrders = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(param.getOrderNos())) {
            param.getOrderNos().forEach(orderNo -> {
                OfflinePayRecordOrder offlinePayRecordOrder = new OfflinePayRecordOrder();
                offlinePayRecordOrder.setOfflinePayRecordId(offlinePayRecord.getId());
                offlinePayRecordOrder.setOrderNo(orderNo.getOrderNo());
                offlinePayRecordOrder.setPrice(orderNo.getPrice());
                offlinePayRecordOrders.add(offlinePayRecordOrder);
            });
        }
        try {
            BatchPaymentBo batchPaymentBo = new BatchPaymentBo();
            //创建支付单
            if (!OFFLINE.getCode().equals(param.getPaymentMethod())) {
                List<PaymentParam> paymentParams = Lists.newArrayList();
                List<TransactionEntryParam> transactionEntryList = Lists.newArrayList();
                if (param.getTenantCarriageCommissionRate() != null && param.getTenantCarriageCommissionRate().compareTo(BigDecimal.ZERO) > 0 && TenantCommonConstant.isSdsdiy(param.getTenantId())) {
                    BigDecimal tenantToSaasAmount = CarriageAmountUtil.getTenantCarriageAmount(param.getAmount(), param.getTenantCarriageCommissionRate());
                    PaymentParam tenantToSaasParam = OfflinePayRecordGen.genPaymentTenantToTenantParam(param, merchant, tenantToSaasAmount);
                    PaymentParam merchantToTenantParam = OfflinePayRecordGen.generatePaymentMerchantToTenantParam(param, merchant, merchantUserAccountFeign);
                    paymentParams.add(tenantToSaasParam);
                    paymentParams.add(merchantToTenantParam);
                } else {
                    PaymentParam paymentParam = OfflinePayRecordGen.generatePaymentMerchantToTenantParam(param, merchant, merchantUserAccountFeign);
                    paymentParams.add(paymentParam);
                }
                for (PaymentParam paymentParam : paymentParams) {
                    TransactionEntryParam transactionEntryParam = getTransactionParam(paymentParam, paymentParam.getBalance(), paymentParam.getBonus(), paymentParam.getTitle());
                    transactionEntryList.add(transactionEntryParam);
                }
                MultiTransactionCreateParam multiTransactionParam = new MultiTransactionCreateParam();
                multiTransactionParam.setPaymentList(paymentParams);
                multiTransactionParam.setTransactionEntryList(transactionEntryList);
                PaymentDto payment = transactionFeign.createPayment(multiTransactionParam);
                Assert.validateNull(payment, "支付异常，请重试");
                if (!PaymentMethodEnum.needWaitCustomerPaid(param.getPaymentMethod())) {
                    //余额支付
                    payment = transactionFeign.operateTransaction(payment.getId());
                }
                offlinePayPayment.setTradeNo(payment.getTradeNo());
                OfflinePayPaymentGen.formatOfflinePayPaymentPo(offlinePayPayment, payment);
                batchPaymentBo.setPreviewPayments(Lists.newArrayList(payment));
            }
            OfflinePayRecordGen.formatOfflinePayRecordsPo(Lists.newArrayList(offlinePayRecord), offlinePayPayment);
            ArrayList<OfflinePayAmountDetail> offlinePayAmountDetails = Lists.newArrayList();
            if (param.getTenantCarriageCommissionRate() != null && param.getTenantCarriageCommissionRate().compareTo(BigDecimal.ZERO) > 0 && TenantCommonConstant.isSdsdiy(param.getTenantId())) {
                BigDecimal tenantToSaasAmount = CarriageAmountUtil.getTenantCarriageAmount(param.getAmount(), param.getTenantCarriageCommissionRate());
                offlinePayAmountDetails.addAll(OfflinePayRecordGen.generateAmountDetails(offlinePayRecord, tenantToSaasAmount));
            } else {
                offlinePayAmountDetails.add(OfflinePayRecordGen.generateDefaultAmounts(offlinePayRecord));
            }
            offlinePayRecordMapperManager.save(offlinePayRecord);
            if (CollectionUtil.isNotEmpty(offlinePayRecordOrders)) {
                offlinePayRecordOrderMapperManager.saveBatch(offlinePayRecordOrders);
            }
            offlinePayPaymentMapperManager.save(offlinePayPayment);
            offlinePayAmountDetailMapperManager.saveBatch(offlinePayAmountDetails);
            offlinePayRecordService.createSendMessage(offlinePayRecord.getMerchantId(), offlinePayRecord.getId(), offlinePayRecord.getTradeType());
            batchPaymentBo.setOfflinePayPayment(offlinePayPayment);
            batchPaymentBo.setOfflinePayRecords(Lists.newArrayList(offlinePayRecord));
            return batchPaymentBo;
        } catch (Exception e) {
            log.error("offlinePayParamPreview error ", e);
            throw new BusinessException(e.getMessage());
        }
    }

    /***
     *支付处理
     */
    public BatchPaymentBo batchPreviewPayments(OfflinePayBatchPayParam param,
                                               List<OfflinePayRecord> offlinePayRecords,
                                               List<OfflinePayAmountDetail> offlinePayAmountDetails,
                                               Map<Long, List<OfflinePayRecordOrder>> offlinePayRecordOrderMap,
                                               Long belongUserId) {
        Map<Long, OfflinePayRecord> payRecordMap = offlinePayRecords.stream().collect(Collectors.toMap(OfflinePayRecord::getId, Function.identity()));
        OfflinePayPayment offlinePayPayment = OfflinePayPaymentGen.genOfflinePayPaymentPo(offlinePayRecords, param);
        Map<String, List<OfflinePayAmountDetail>> sourceRoleKeyMap = offlinePayAmountDetails.stream().collect(Collectors.groupingBy(OfflinePayAmountDetail::getSourceRole));
        List<PaymentParam> paymentParams = Lists.newArrayList();
        List<TransactionEntryParam> transactionEntryList = Lists.newArrayList();
        sourceRoleKeyMap.forEach((sourceRole, detailBos) -> {
            //商户只会付给商户的租户
            if (PaymentRoleEnum.MERCHANT.getCode().equals(sourceRole)) {
                PaymentParam paymentParam = OfflinePayPaymentGen.genMerchantToTenantParam(param, offlinePayPayment, detailBos, belongUserId);
                paymentParams.add(paymentParam);
                String title = offlinePayService.recordIdKeyTitleStr(offlinePayRecords, offlinePayRecordOrderMap);
                TransactionEntryParam transactionEntryParam = getTransactionParam(paymentParam, paymentParam.getBalance(), BigDecimal.ZERO, title);
                transactionEntryList.add(transactionEntryParam);
            } else if (PaymentRoleEnum.TENANT_DIS.getCode().equals(sourceRole)) {
                for (OfflinePayAmountDetail detailBo : detailBos) {
                    PaymentParam paymentParam = OfflinePayPaymentGen.genTenantToTenantParam(param, offlinePayPayment, detailBo, belongUserId);
                    TransactionEntryParam transactionEntryParam = getTransactionEntryParam(detailBo, payRecordMap, offlinePayRecordOrderMap, paymentParam);
                    paymentParams.add(paymentParam);
                    transactionEntryList.add(transactionEntryParam);
                }
            } else {
                log.error("错误类型");
                throw new BusinessException("错误类型");
            }
        });
        MultiTransactionCreateParam multiTransactionParam = new MultiTransactionCreateParam();
        multiTransactionParam.setPaymentList(paymentParams);
        multiTransactionParam.setTransactionEntryList(transactionEntryList);
        BatchPaymentBo batchPaymentBo = new BatchPaymentBo();
        try {
            log.info("预支付入参 {}", JSONUtil.toJsonStr(multiTransactionParam));
            PaymentDto payment = transactionFeign.createPayment(multiTransactionParam);
            Assert.validateNull(payment, "支付异常，请重试");
            if (!PaymentMethodEnum.needWaitCustomerPaid(param.getPaymentMethod())) {
                //余额支付
                payment = transactionFeign.operateTransaction(payment.getId());
            }
            batchPaymentBo.setPreviewPayments(Lists.newArrayList(payment));
            offlinePayPayment.setTradeNo(payment.getTradeNo());
            OfflinePayPaymentGen.formatOfflinePayPaymentPo(offlinePayPayment, payment);
        } catch (Exception e) {
            offlinePayPayment.setErrorLog(e.getMessage());
            offlinePayPayment.setStatus(FAILED.name());
            log.error("创建支付异常{}", e.getMessage());
            throw e;
        } finally {
            OfflinePayRecordGen.formatOfflinePayRecordsPo(offlinePayRecords, offlinePayPayment);
            offlinePaySaveService.batchSave(offlinePayRecords, offlinePayAmountDetails, offlinePayPayment);
            batchPaymentBo.setOfflinePayPayment(offlinePayPayment);
            batchPaymentBo.setOfflinePayRecords(offlinePayRecords);
        }
        return batchPaymentBo;
    }

    @NotNull
    private static TransactionEntryParam getTransactionParam(PaymentParam paymentParam,
                                                             BigDecimal balance, BigDecimal bonus, String title) {
        TransactionEntryParam transactionEntryParam = new TransactionEntryParam();
        transactionEntryParam.setPaymentBizNo(paymentParam.getBizNo());
        transactionEntryParam.setBizNoForBill(paymentParam.getBizNo());
        transactionEntryParam.setBalance(balance);
        transactionEntryParam.setBonus(bonus);
        transactionEntryParam.setType(TransactionEntryTypeEnum.PAY.getValue());
        transactionEntryParam.setTitle(title);
        return transactionEntryParam;
    }

    private TransactionEntryParam getTransactionEntryParam(OfflinePayAmountDetail detailBo,
                                                           Map<Long, OfflinePayRecord> offlinePayRecordMap,
                                                           Map<Long, List<OfflinePayRecordOrder>> offlinePayRecordOrderMap,
                                                           PaymentParam paymentParam) {
        List<OfflinePayRecordOrder> recordOrders = offlinePayRecordOrderMap.get(detailBo.getOfflinePayRecordId());
        OfflinePayRecord record = offlinePayRecordMap.get(detailBo.getOfflinePayRecordId());
        String title = offlinePayService.recordIdKeyTitleStr(record, recordOrders);
        return getTransactionParam(paymentParam, detailBo.getAmount(), BigDecimal.ZERO, title);
    }

    public OfflinePayPayment batchPayments(BatchPaymentBo batchPaymentBo) {
        if (!balancePay(batchPaymentBo.getOfflinePayPayment().getPaymentMethod())) {
            return batchPaymentBo.getOfflinePayPayment();
        }
        offlinePaySaveService.batchSave(batchPaymentBo);
        return batchPaymentBo.getOfflinePayPayment();
    }


    public void offlinePayRecordsCheck(String requestSource,
                                       List<OfflinePayRecord> offlinePayRecords,
                                       List<OfflinePayAmountDetail> offlinePayAmountDetails, Pair<Long, String> belongUserIdAndBalanceTypePair) {
        Assert.validateEmpty(offlinePayRecords, "未查的所选信息");
        Set<Long> merchantIds = offlinePayRecords.stream().map(OfflinePayRecord::getMerchantId).collect(Collectors.toSet());
        Assert.validateTrue(merchantIds.size() > 1, "仅支持对同一个商户的账单发起线下收款");
        long noUnPaidNum = offlinePayRecords.stream().filter(o -> !UN_PAID.name().equalsIgnoreCase(o.getStatus())).count();
        Assert.validateTrue(noUnPaidNum > 0, "当前选择的账单状态发生变更，请刷新后重试");
        Set<Integer> orderOffline = offlinePayRecords.stream().map(OfflinePayRecord::getIsOrderOffline).collect(Collectors.toSet());
        //同一商户 开通免密支付的账单与未开通免密支付的账单，批量创建线下收款拦截报错““
        Assert.validateTrue(orderOffline.size() > 1, "线下结算账单不可和线上支付账单同时付款！");
        if (OfflinePayRecordConstant.RequestSourceEnum.TENANT.name().equalsIgnoreCase(requestSource)) {
            Assert.validateTrue(!Objects.equals(McContentHelper.getCurrentTenantId(), offlinePayRecords.get(0).getTenantId()), "非当前租户数据！");
            long count = offlinePayRecords.stream().filter(o -> !o.getMerchantTenantId().equals(o.getTenantId())).count();
            Assert.validateTrue(count > 0, "租户端不支持支付");
        }
        if (OfflinePayRecordConstant.RequestSourceEnum.MERCHANT.name().equalsIgnoreCase(requestSource)) {
            Long currentUserId = McContentHelper.getCurrentUserId();
            Long merchantId = McContentHelper.getCurrentMerchantId();
            MerchantUserAccountRespDto merchantUserAccount = merchantUserAccountFeign.getOne(merchantId, currentUserId, "balanceType");
            Assert.validateNull(merchantUserAccount, "账号异常");
            String errorMsg = MerchantUserAccountConstant.ALLOCATION.equals(merchantUserAccount.getBalanceType()) ? "账单为公有账单，您无权操作" : "账单为私有账单，您无权操作";
            //当前账号余额类型与支付账单类型不一致的
            Assert.validateBool(belongUserIdAndBalanceTypePair.getSecond().equals(merchantUserAccount.getBalanceType()), errorMsg);
            //是分配，但支付账号不是同一个
            if (MerchantUserAccountConstant.ALLOCATION.equals(merchantUserAccount.getBalanceType()) && !Objects.equals(currentUserId, belongUserIdAndBalanceTypePair.getFirst())) {
                throw new BusinessException("当前账号不支持支付此批次账单！");
            }
        }
//        Set<String> tradeFlows = this.getTradeFlows(offlinePayRecords, isOpenOnlinePay);
//        Assert.validateTrue(tradeFlows.size() > 1, "所选账单不可批量支付");
//        List<OfflinePayAmountDetailBo> offlinePayAmountDetailBos = BeanUtil.copyToList(offlinePayAmountDetails, OfflinePayAmountDetailBo.class);
//        Map<OfflinePayAmountDetailBo, List<OfflinePayAmountDetailBo>> detailsGroup = offlinePayAmountDetailBos.stream().collect(Collectors.groupingBy(Function.identity()));
//        Assert.validateTrue(detailsGroup.size() > 2, "账单不可以合并支付!");
    }

    public Pair<Long, String> checkAndGetUserIdAndBalanceType(List<OfflinePayRecord> offlinePayRecords) {
        List<Long> belongUserIds = offlinePayRecords.stream().map(OfflinePayRecord::getBelongUserId).collect(Collectors.toList());
        if ((belongUserIds.contains(0L) && belongUserIds.size() == 1) || CollectionUtil.isEmpty(belongUserIds)) {
            return Pair.of(0L, MerchantUserAccountConstant.IN_COMMON);
        }
        List<MerchantUserAccountRespDto> accounts = merchantUserAccountFeign.getList(belongUserIds);
        Map<Long, String> userIdBalanceType = accounts.stream().collect(Collectors.toMap(MerchantUserAccountRespDto::getMerchantUserId, MerchantUserAccountRespDto::getBalanceType, (a, b) -> b));
        if (belongUserIds.contains(0L) || CollectionUtil.isEmpty(accounts)) {
            userIdBalanceType.put(0L, MerchantUserAccountConstant.IN_COMMON);
        }
        List<String> distinctBalanceType = userIdBalanceType.values().stream().distinct().collect(Collectors.toList());
        Assert.validateTrue(distinctBalanceType.size() > 1, "主商户和子账号分配余额的账单不可合并收款");
        //分配的只有一种情况
        Assert.validateTrue(distinctBalanceType.get(0).equalsIgnoreCase(MerchantUserAccountConstant.ALLOCATION) && userIdBalanceType.size() > 1, "不同子账号分配余额账单不可合并付款");
        if (distinctBalanceType.get(0).equalsIgnoreCase(MerchantUserAccountConstant.ALLOCATION)) {
            return Pair.of(belongUserIds.get(0), MerchantUserAccountConstant.ALLOCATION);
        }
        return Pair.of(0L, MerchantUserAccountConstant.IN_COMMON);
    }
}
