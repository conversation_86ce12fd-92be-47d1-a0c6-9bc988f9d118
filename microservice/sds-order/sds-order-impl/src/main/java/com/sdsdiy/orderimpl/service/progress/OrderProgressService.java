package com.sdsdiy.orderimpl.service.progress;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.enums.SdsPlatformEnum;
import com.sdsdiy.common.base.helper.McContentHelper;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.orderapi.constant.OrderOperatorConstant;
import com.sdsdiy.orderapi.constant.OrderProgressStatusConstant;
import com.sdsdiy.orderapi.constant.OrderProgressTypeConstant;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.dto.progress.*;
import com.sdsdiy.orderdata.enums.OrderStatus;
import com.sdsdiy.orderimpl.entity.po.*;
import com.sdsdiy.orderimpl.entity.po.progress.OrderItemProgress;
import com.sdsdiy.orderimpl.entity.po.progress.OrderProgress;
import com.sdsdiy.orderimpl.feign.FactorySysUserFeign;
import com.sdsdiy.orderimpl.feign.MerchantSysUserFeign;
import com.sdsdiy.orderimpl.feign.user.SaasSysUserFeign;
import com.sdsdiy.orderimpl.feign.user.TenantSysUserFeign;
import com.sdsdiy.orderimpl.mapper.OrderProgressMapper;
import com.sdsdiy.orderimpl.service.*;
import com.sdsdiy.userapi.dto.FactorySysUserRespDto;
import com.sdsdiy.userapi.dto.base.MerchantSysUserRespDto;
import com.sdsdiy.userapi.dto.saas.SaasSysUserDto;
import com.sdsdiy.userapi.dto.tenant.TenantSysUserDto;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

/***
 *
 *订单进度
 * <AUTHOR>
 * @date 2020/7/8 11:18
 */
@Service
@Log4j2
@DS("common")
public class OrderProgressService extends ServiceImpl<OrderProgressMapper, OrderProgress> {

    @Resource
    private OrderProgressMapper orderProgressMapper;


    @Resource
    private OrderItemProgressService orderItemProgressService;

    @Resource
    private FactoryOrderService factoryOrderService;
    @Resource
    private OrderItemService orderItemService;
    @Resource
    private MerchantSysUserFeign merchantSysUserFeign;

    @Resource
    private TenantSysUserFeign tenantSysUserFeign;

    @Resource
    private SaasSysUserFeign saasSysUserFeign;

    @Resource
    private FactorySysUserFeign factorySysUserFeign;

    @Resource
    private OrderService orderService;
    @Resource
    private OrderAmountService orderAmountService;

    @Resource
    private AfterServiceAuditsService afterServiceAuditsService;
    @Resource
    private AfterServiceAuditAdminImgService afterServiceAuditAdminImgService;

    public void test() {
        StringJoiner sj = new StringJoiner("<br />");
        sj.add("3款1件");
        sj.add("4款2件");
        String title = sj.toString();
        OrderProgress progress = new OrderProgress();
        progress.setOrderId(504755396991451136L);
        progress.setTitle(title);
        progress.setEtype("admin_pick_up");
        progress.setOrderStatus(OrderProgressStatusConstant.ADMIN_PICK_UP);
        progress.setOprateType(OrderOperatorConstant.OPERATOR_TYPE_ADMIN);
        progress.setCreateUid(1L);
        progress.setType(OrderProgressTypeConstant.ADMIN_PICK_UP);
        progress.setSendingTime(DateUtil.date());
        progress.setEid(504755396991451136L);
        progress.setOprateId(1L);
        progress.setEvidenceImgs("");
        progress.setLaberPdf("");
        this.save(progress);
    }

    public List<OrderProgress> findByEtypeAndOrderId(String eType, Long orderId) {
        if (StringUtils.isBlank(eType)) {
            return null;
        }
        LambdaQueryWrapper<OrderProgress> lambdaQueryWrapper = Wrappers.<OrderProgress>lambdaQuery()
                .eq(OrderProgress::getOrderId, orderId)
                .eq(OrderProgress::getEtype, eType);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public void saveApiFactoryGetLogisticsSuccess(Long orderId,String carriageNo,String carriagePdf){
        OrderProgress progress = new OrderProgress();
        progress.setOrderId(orderId);
        progress.setTitle("系统已将面单上传到工厂");
        progress.setEtype(OrderProgressConstant.API_FACTORY_GET_LOGISTICS);
        progress.setOrderStatus(OrderProgressStatusConstant.STATELESS);
        progress.setContent("运单号:" + carriageNo);
        progress.setOprateType(OrderOperatorConstant.OPERATOR_TYPE_ADMIN);
        progress.setType(OrderProgressTypeConstant.UPLOAD_NOODLE_LIST);
        progress.setSendingTime(DateUtil.date());
        progress.setEid(orderId);
        progress.setLaberPdf(carriagePdf);
        progress.setEvidenceImgs("");
        this.save(progress);
    }
    public void saveApiFactorySaveOrder(Long orderId){
        OrderProgress progress = new OrderProgress();
        progress.setOrderId(orderId);
        progress.setTitle("用户订单生产商已接单，在生产中");
        progress.setEtype(OrderProgressConstant.API_FACTORY_SAVE_ORDER);
        progress.setOrderStatus(OrderProgressStatusConstant.STATELESS);
        progress.setOprateType(OrderOperatorConstant.OPERATOR_TYPE_ADMIN);
        progress.setType(OrderProgressTypeConstant.MANUFACTURER_ORDERS);
        progress.setSendingTime(DateUtil.date());
        progress.setEid(orderId);
        progress.setEvidenceImgs("");
        progress.setLaberPdf("");
        this.save(progress);
    }

    //生产完成进度，content包含[生产单号]，要去除
    private static void formatFactoryProductionFinishContent(List<OrderProgressDto> orderItemDtoList) {
        for (OrderProgressDto orderProgressDto : orderItemDtoList) {
            if(orderProgressDto.getEtype().equalsIgnoreCase(OrderProgressConstant.FACTORY_PRODUCTION_FINISH)){
                String resultString = orderProgressDto.getContent().replaceAll("\\[.*?\\]", "");
                orderProgressDto.setContent(resultString);
            }
        }
    }

    public List<OrderProgress> findByEtypeAndOrderIds(String eType, List<Long> orderIds) {
        if (StringUtils.isBlank(eType)||CollUtil.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderProgress> lambdaQueryWrapper = Wrappers.<OrderProgress>lambdaQuery()
                .in(OrderProgress::getOrderId, orderIds)
                .eq(OrderProgress::getEtype, eType);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public static void main(String[] args) {
        List<Integer> orderItemDtoList = Lists.newArrayList(4,3,2,1);
        List<Integer> sub = CollUtil.sub(orderItemDtoList, 2, orderItemDtoList.size());

        System.out.println(sub);
    }

    /***
     *商户订单进度
     * <AUTHOR>
     * @date 2020/7/8 18:34
     */
    public List<OrderProgressDto> merchantProgresslist(Long orderId) {

        Order order = this.orderService.getById(orderId);

        List<String> hiddenProgresses = Lists.newArrayList(OrderProgressConstant.ADMIN_INTERCEPT_ORDER, OrderProgressConstant.ADMIN_CANCEL_INTERCEPT_ORDER);
        if (order.getIsAdvance() == 2) {
            List<String> merchantOrderProgressPreparePackingNotShowETypes = OrderProgressConstant.getMerchantOrderProgressPreparePackingNotShowETypes();
            hiddenProgresses.addAll(merchantOrderProgressPreparePackingNotShowETypes);
        }
        List<OrderProgress> orderProgresses = this.orderProgressMapper.selectList(Wrappers.<OrderProgress>lambdaQuery().eq(OrderProgress::getOrderId, orderId).
                ne(OrderProgress::getEtype, OrderProgressConstant.ADMIN_SEND_OUT_PREPARE_PACKING_ORDER).orderByDesc(OrderProgress::getSendingTime)
                .orderByDesc(OrderProgress::getId)
                .notIn(OrderProgress::getEtype, hiddenProgresses)
        );
        List<OrderProgressDto> orderItemDtoList = RelationsBinder.convertAndBind(orderProgresses, OrderProgressDto.class);


        for (OrderProgressDto orderProgressDto : orderItemDtoList) {
            String etype = orderProgressDto.getEtype();
            if (etype.equals(OrderProgressConstant.ADMIN_PREPARE_PACKING_PRODUCT)) {
                orderProgressDto.setTitle("平台已发货");
                continue;
            }
            if (etype.equals(OrderProgressConstant.ADMIN_SELF_SEND_OUT_FBA_ORDER)) {
                orderProgressDto.setTitle("货物已自提");
                continue;
            }
            if (etype.equals(OrderProgressConstant.ADMIN_ADOPT_REFUND_AFTER_SALE_ORDER)
                    || etype.equals(OrderProgressConstant.ADMIN_ADOPT_RESEND_AFTER_SALE_ORDER)
                    || etype.equals(OrderProgressConstant.ADMIN_REJECT_REFUND_AFTER_SALE_ORDER)
                    || etype.equals(OrderProgressConstant.ADMIN_REJECT_RESEND_AFTER_SALE_ORDER)) {
                this.getAfterServiceAudit(orderProgressDto);
            }
            if (orderProgressDto.getType() == OrderProgressTypeConstant.ADMIN_PACKAGE_SHIPPED) {
                String titleStr = StrUtil.format(orderProgressDto.getTitle(), "");
                orderProgressDto.setTitle(titleStr);
                continue;
            }

            orderProgressDto.setImgsArray(this.getImgs(orderProgressDto.getEvidenceImgs()));
            String title = this.getTitle(orderProgressDto.getTitle(), orderProgressDto.getOprateId(), orderProgressDto.getOprateType(), "商户");
            orderProgressDto.setTitle(title);

        }
        for (OrderProgressDto orderProgressDto : orderItemDtoList) {
            String etype = orderProgressDto.getEtype();
            if (etype.equals(OrderProgressConstant.ADMIN_SEND_OUT_PREPARE_PACKING_ORDER)
                || etype.equals(OrderProgressConstant.ADMIN_SEND_OUT_ORDER)) {
                orderProgressDto.setContent(orderProgressDto.getContent().replace("{notShowToMerchant}", "none"));
            }
            progressALabelHandel(orderProgressDto);
        }
        // 生产完成进度，content包含[生产单号]，要去除
        formatFactoryProductionFinishContent(orderItemDtoList);
        if(OrderStatus.isCancel(order.getStatus())){
            // 取消后的进度，商户不显示
            orderItemDtoList = filterProcess(orderItemDtoList);
        }

        return orderItemDtoList;
    }
    public static List<OrderProgressDto> filterProcess(List<OrderProgressDto> orderProgressDtoList) {
        // 找到取消角标
        int indexOfTypeCancel = CollUtil.indexOf(orderProgressDtoList, i -> i.getType()==OrderProgressTypeConstant.CANCELLATION_DURING_STOCK_PREPARATION);

        if (indexOfTypeCancel == -1) {
            // 没有取消角标，则返回所有
            return orderProgressDtoList;
        }

        // 有取消角标，则保留角标以及之前早期的进度（进度是倒序的）
        List<OrderProgressDto> result = CollUtil.sub(orderProgressDtoList, indexOfTypeCancel, orderProgressDtoList.size());

        // 取消之后，可能还有其他需要显示的进度，所以这里通用的type也特殊处理，让其显示
        for (int i = 0; i < indexOfTypeCancel; i++) {
            if (orderProgressDtoList.get(i).getType() == OrderProgressTypeConstant.COMMON) {
                result.add(0, orderProgressDtoList.get(i));
            }
        }

        return result;
    }

    private void progressALabelHandel(OrderProgressDto orderProgressDto) {
        if (orderProgressDto.getType() == OrderProgressTypeConstant.ORDER_END_OFFLINE_PAY || orderProgressDto.getType() == OrderProgressTypeConstant.JIT_ORDER_CARRIAGE_FREIGHT) {
            String content = orderProgressDto.getContent();
            String billId = ReUtil.getGroup1("<a>(\\w+)</a>", content);
            if (StrUtil.isNotBlank(billId)) {
                String aLabel = ReUtil.getGroup0("<a>(\\w+)</a>", content);
                String requestPlatform = McContentHelper.getRequestPlatform();
                if (SdsPlatformEnum.POD.equalsCode(requestPlatform)) {
                    content = content.replace(aLabel, "<a href=\"/home/<USER>" + billId + "\">" + billId + "</a>");
                } else if (SdsPlatformEnum.MERCHANT.equalsCode(requestPlatform)) {
                    content = content.replace(aLabel, "<a href=\"/admin/supplementary-bill?ids=" + billId + "\">" + billId + "</a>");
                }
                orderProgressDto.setContent(content);
            }
        }
    }

    /***
     *运营站
     * <AUTHOR>
     * @date 2020/7/8 18:34
     */
    public ProgressRespDto progresslist(Long orderId,String platformEnumCode) {
        ProgressRespDto progressRespDto = new ProgressRespDto();

        List<OrderProgress> orderProgresses = this.orderProgressMapper.selectList(
                Wrappers.<OrderProgress>lambdaQuery().eq(OrderProgress::getOrderId, orderId).
                        ne(OrderProgress::getEtype, OrderProgressConstant.ADMIN_ALL_CHECK_PASSED_ADVANCE_PRODUCT).
                        ne(OrderProgress::getEtype, OrderProgressConstant.ADMIN_PRODUCTION_FINALLY_ORDER).
                        orderByDesc(OrderProgress::getSendingTime)
                        .orderByDesc(OrderProgress::getId)
        );
        boolean isDistribution=false;
        boolean isPodSdsTenant=false;
        boolean otherTenantMerchant=false;
        if(platformEnumCode.equals(SdsPlatformEnum.POD.getCode())){
            OrderAmount orderAmount = orderAmountService.getById(orderId);
            isPodSdsTenant= TenantCommonConstant.isSdsdiy(McContentHelper.getCurrentTenantId());
            if(null!=orderAmount){
                Long tenantId = orderAmount.getTenantId();
                Long productTenantId = orderAmount.getProductTenantId();
                boolean tenantIdValid = NumberUtils.greaterZero(tenantId) && NumberUtils.greaterZero(productTenantId);
                isDistribution = tenantIdValid&&!tenantId.equals(productTenantId);
                otherTenantMerchant=!tenantId.equals(McContentHelper.getCurrentTenantId());
            }
        }
        List<OrderProgressDto> orderItemDtoList = RelationsBinder.convertAndBind(orderProgresses, OrderProgressDto.class);

        Map<Long, OrderProgressDto> eidProgress = Maps.newHashMap();

        for (OrderProgressDto orderProgressDto : orderItemDtoList) {

            String etype = orderProgressDto.getEtype();
            String accountName = this.getAccountNameByOpenType(orderProgressDto.getOprateType(),orderProgressDto.getOprateId(), "平台");
            accountName=otherTenantMerchant&&!isPodSdsTenant?"****":accountName;
            String titleStr = StrUtil.format(orderProgressDto.getTitle(), accountName);
            orderProgressDto.setTitle(titleStr);
            orderProgressDto.setImgsArray(this.getImgs(orderProgressDto.getEvidenceImgs()));
            if (etype.equals(OrderProgressConstant.ADMIN_ADOPT_REFUND_AFTER_SALE_ORDER)
                    || etype.equals(OrderProgressConstant.ADMIN_ADOPT_RESEND_AFTER_SALE_ORDER)
                    || etype.equals(OrderProgressConstant.ADMIN_REJECT_REFUND_AFTER_SALE_ORDER)
                    || etype.equals(OrderProgressConstant.ADMIN_REJECT_RESEND_AFTER_SALE_ORDER)) {
                this.getAfterServiceAudit(orderProgressDto);
                eidProgress.put(orderProgressDto.getEid(), orderProgressDto);
            }
            //特殊处理 运营站这几个流程的您要换成用户
            if (orderProgressDto.getEtype().equals(OrderProgressConstant.ADMIN_REJECT_RESEND_AFTER_SALE_ORDER)) {
                orderProgressDto.setTitle(orderProgressDto.getTitle().replaceAll("您", "用户"));
                continue;
            }
            if (orderProgressDto.getEtype().equals(OrderProgressConstant.ADMIN_ADOPT_RESEND_AFTER_SALE_ORDER)) {
                orderProgressDto.setTitle(orderProgressDto.getTitle().replaceAll("您", "用户"));
                continue;
            }
            if (orderProgressDto.getEtype().equals(OrderProgressConstant.ADMIN_REJECT_REFUND_AFTER_SALE_ORDER)) {
                orderProgressDto.setTitle(orderProgressDto.getTitle().replaceAll("您", "用户"));
                continue;
            }
            if (orderProgressDto.getEtype().equals(OrderProgressConstant.ADMIN_ADOPT_REFUND_AFTER_SALE_ORDER)) {
                orderProgressDto.setTitle(orderProgressDto.getTitle().replaceAll("您", "用户"));
                continue;
            }

            if (etype.equals(OrderProgressConstant.ADMIN_SEND_OUT_PREPARE_PACKING_ORDER)
                    || etype.equals(OrderProgressConstant.ADMIN_SEND_OUT_ORDER)) {
                orderProgressDto.setContent(orderProgressDto.getContent().replace("{notShowToPod}", "none"));
            }
            progressALabelHandel(orderProgressDto);
            if(orderProgressDto.getEtype().equalsIgnoreCase(OrderProgressConstant.ADMIN_PREPAID_SHIPPED)){
                String requestPlatform = McContentHelper.getRequestPlatform();
                if (SdsPlatformEnum.POD.equalsCode(requestPlatform)) {
                    orderProgressDto.setTitle(removeALabel(orderProgressDto.getTitle()));
                }
            }
        }

        formatFactoryProductionFinishContent(orderItemDtoList);
        progressRespDto.setOrderProgress(orderItemDtoList);

        /****
         *工厂订单进度
         * <AUTHOR>
         * @date 2020/7/8 17:14
         */
        List<OrderItemProgress> orderItemProgresses = this.orderItemProgressService.getBaseMapper().selectList(Wrappers.<OrderItemProgress>lambdaQuery().eq(OrderItemProgress::getOrderId, orderId)
                .orderByDesc(OrderItemProgress::getSendingTime)
        );
        List<ItemProgressDto> itemProgressDtos = RelationsBinder.convertAndBind(orderItemProgresses, ItemProgressDto.class);

        List<OrderItem> orderItemList = this.orderItemService.getOrderItemByOrderId(orderId);
        List<Long> orderItemIds = Lists.newArrayList();

        Map<Long, Boolean> isReissueMap = Maps.newConcurrentMap();
        for (OrderItem orderItem : orderItemList) {
            orderItemIds.add(orderItem.getId());
            //判断是否为补件
            if (orderItem.getBeResendForLose() > 0) {
                isReissueMap.put(orderItem.getId(), true);
            } else {
                isReissueMap.put(orderItem.getId(), false);
            }

        }
        List<FactoryOrder> factoryOrderList = this.factoryOrderService.findByOrderItemId(orderItemIds);
        Map<Long, List<ItemProgressDto>> map1 = Maps.newConcurrentMap();


        List<FactoryOrderDto> factoryOrderDtoList = Lists.newArrayList();
        for (ItemProgressDto orderItemProgress : itemProgressDtos) {


            if (null != orderItemProgress.getFactoryOrderId()) {
                if (map1.containsKey(orderItemProgress.getFactoryOrderId())) {
                    List<ItemProgressDto> progressDtoList = map1.get(orderItemProgress.getFactoryOrderId());
                    progressDtoList.add(orderItemProgress);
                    map1.put(orderItemProgress.getFactoryOrderId(), progressDtoList);
                } else {
                    List<ItemProgressDto> progressDtoList = Lists.newArrayList();
                    progressDtoList.add(orderItemProgress);
                    map1.put(orderItemProgress.getFactoryOrderId(), progressDtoList);
                }

            }
        }
        for (FactoryOrder factoryOrder : factoryOrderList) {
            if (map1.containsKey(factoryOrder.getId())) {
                List<ItemProgressDto> itemProgressDtosList = map1.get(factoryOrder.getId());
                FactoryOrderDto factoryOrderDto = new FactoryOrderDto();
                List<ItemProgressDto> itemProgressDtoList = Lists.newArrayList();
                factoryOrderDto.setIsReissue(isReissueMap.get(factoryOrder.getOrderItemId()));
                for (ItemProgressDto orderItemProgress : itemProgressDtosList) {

                    String etype = orderItemProgress.getEtype();
                    orderItemProgress.setImgsArray(this.getImgs(orderItemProgress.getEvidenceImgs()));
                    String title = this.getTitle(orderItemProgress.getTitle(), orderItemProgress.getOprateId(), orderItemProgress.getOprateType(), "平台工厂");
                    orderItemProgress.setTitle(title);
                    factoryOrderDto.setId(factoryOrder.getId());
                    factoryOrderDto.setProductName(factoryOrder.getProductName());
                    factoryOrderDto.setTextureName(factoryOrder.getTextureName());
                    factoryOrderDto.setProductSize(factoryOrder.getProductSize());
                    factoryOrderDto.setProductColorName(factoryOrder.getProductColorName());
                    ProductColor productColor = JSONArray.parseObject(factoryOrder.getProdutColorBlock(), ProductColor.class);
                    factoryOrderDto.setProductColor(productColor);
                    if (orderItemProgress.getEtype().equals(OrderProgressConstant.ADMIN_CHECK_TRANSFER_ORDER)) {
                        //驳回转移单
                        factoryOrderDto.setProductName("[驳回转移]" + factoryOrderDto.getProductName());
                    }
                    if (etype.equals(OrderProgressConstant.ADMIN_ADOPT_REFUND_AFTER_SALE_ORDER)
                            || etype.equals(OrderProgressConstant.ADMIN_ADOPT_RESEND_AFTER_SALE_ORDER)
                            || etype.equals(OrderProgressConstant.ADMIN_REJECT_REFUND_AFTER_SALE_ORDER)
                            || etype.equals(OrderProgressConstant.ADMIN_REJECT_RESEND_AFTER_SALE_ORDER)) {
                        if (eidProgress.containsKey(orderItemProgress.getEid())) {
                            OrderProgressDto orderProgressDto = eidProgress.get(orderItemProgress.getEid());
                            orderItemProgress.setAdditionalContent(orderProgressDto.getAdditionalContent());
                            orderItemProgress.setAdditionalImgs(orderProgressDto.getAdditionalImgs());
                        }
                    }
                    itemProgressDtoList.add(orderItemProgress);

                }
                factoryOrderDto.setProgress(itemProgressDtoList);

                factoryOrderDtoList.add(factoryOrderDto);

            }

        }

        progressRespDto.setFactoryOrderItemProgress(factoryOrderDtoList);

        return progressRespDto;
    }

    public static String removeALabel(String input) {
        try {
            // 定义正则表达式
            String regex = "<a[^>]*>(.*?)</a>";
            // 移除a标签
            return input.replaceAll(regex, "$1");
        } catch (Exception e) {
            return input;
        }
    }

    /***
     *批量保存
     * <AUTHOR>
     * @date 2020/7/31 11:25
     */
    public boolean savePoBatch(List<OrderProgress> list) {
        if (CollUtil.isEmpty(list)) {
            return false;
        }
        return this.saveBatch(list);
    }

    public void savePo(OrderProgress po) {
        this.save(po);
    }

    //平台售后重发售后退款的情况下查询平台审核原因以及凭证  v5.6  shanbin_sun 2021年1月5日17:01:29
    private void getAfterServiceAudit(OrderProgressDto orderProgressDto) {
        Long eid = orderProgressDto.getEid();
        if (null != eid) {
            AfterServiceAudit afterServiceAudit = this.afterServiceAuditsService.getById(eid);
            if (null != afterServiceAudit) {
                orderProgressDto.setAdditionalContent(afterServiceAudit.getAuditRemark());
                List<String> list = this.afterServiceAuditAdminImgService.findImages(eid);
                if (CollectionUtils.isNotEmpty(list)) {
                    orderProgressDto.setAdditionalImgs(list);
                }
            }
        }
    }

    public List<String> getImgs(String evidenceImgs) {
        List<String> list = null;
        if (!StrUtil.hasBlank(evidenceImgs)) {
            JSONArray evidenceImgsArr = JSONArray.parseArray(evidenceImgs);
            list = evidenceImgsArr.toJavaList(String.class);
        }
        return list;
    }

    public String getTitle(String title, Long oprateId, int opratetype, String riseBranch) {
        String accountName = this.getAccountNameByOpenType(opratetype, oprateId, riseBranch);
        String titleStr = StrUtil.format(title, accountName);
        return titleStr;
    }

    private String getAccountNameByOpenType(int oprateType, Long oprateId, String riseBranch) {
        String accountName = "";
        if (oprateType == OrderOperatorConstant.OPERATOR_TYPE_USER) {
            String rise = this.getRise(riseBranch);
            MerchantSysUserRespDto merchant = this.merchantSysUserFeign.getMerchantSysUserById(oprateId);
            String name = merchant == null ? "" : merchant.getUsername();
            return StrUtil.format(rise, name);
        }
        if (oprateType == OrderOperatorConstant.OPERATOR_TYPE_MERCHANT) {
            String rise = this.getRise(riseBranch);
            MerchantSysUserRespDto merchant = this.merchantSysUserFeign.getMerchantSysUserById(oprateId);
            String name = merchant == null ? "" : merchant.getUsername();
            return StrUtil.format(rise, name);
        }
        if (oprateType == OrderOperatorConstant.OPERATOR_TYPE_ADMIN) {
            String rise = this.getRiseAdmin(riseBranch);
            if (riseBranch.equals("商户")) {
                return "平台";
            }
            TenantSysUserDto tenantSysUserDto = this.tenantSysUserFeign.getById(oprateId);
            String name = tenantSysUserDto == null ? "" : tenantSysUserDto.getUserName();
            return StrUtil.format(rise, name);
        }

        if (oprateType == OrderOperatorConstant.OPERATOR_TYPE_SAAS) {
            String rise = this.getRiseAdmin(riseBranch);
            if (riseBranch.equals("商户")) {
                return "平台";
            }
            SaasSysUserDto saasSysUserDto = this.saasSysUserFeign.getById(oprateId);
            String name = saasSysUserDto == null ? "" : saasSysUserDto.getUserName();
            return StrUtil.format(rise, name);
        }

        if (oprateType == OrderOperatorConstant.OPERATOR_TYPE_FACTORY) {
            String rise = this.getRiseFactory(riseBranch);
            if (riseBranch.equals("商户") || riseBranch.equals("平台")) {
                return rise;
            }
            FactorySysUserRespDto factorySysUserRespDto = this.factorySysUserFeign.getFactorySysUserById(oprateId);
            String name = factorySysUserRespDto == null ? "" : factorySysUserRespDto.getUsername();
            return StrUtil.format(rise, name);
        }
        if (oprateType == OrderOperatorConstant.OPERATOR_TYPE_SYSTEM) {
            return "";
        }

        return accountName;
    }

    private String getRise(String riseBranch) {
        String rise = "";
        if (riseBranch.equals("平台")) {
            rise = "用户（账号：{}）";
        }
        if (riseBranch.equals("商户")) {
            rise = "您（账号：{}）";
        }
        if (riseBranch.equals("工厂商")) {
            rise = "客户";
        }
        if (riseBranch.equals("平台工厂")) {
            rise = "客户（账号：{}）";
        }
        return rise;
    }

    private String getRiseAdmin(String riseBranch) {
        String rise = "";
        if (riseBranch.equals("平台")) {
            rise = "平台（账号：{}）";
        }
        if (riseBranch.equals("商户")) {
            rise = "平台";
        }
        if (riseBranch.equals("工厂商")) {
            rise = "平台";
        }
        if (riseBranch.equals("平台工厂")) {
            rise = "平台（账号：{}）";
        }
        return rise;
    }

    private String getRiseFactory(String riseBranch) {
        String rise = "";
        if (riseBranch.equals("平台")) {
            rise = "用户";
        }
        if (riseBranch.equals("商户")) {
            rise = "您";
        }
        if (riseBranch.equals("工厂商")) {
            rise = "您（账号：{}）";
        }
        if (riseBranch.equals("平台工厂")) {
            rise = "生产商（账号：{}）";
        }
        return rise;
    }

}
