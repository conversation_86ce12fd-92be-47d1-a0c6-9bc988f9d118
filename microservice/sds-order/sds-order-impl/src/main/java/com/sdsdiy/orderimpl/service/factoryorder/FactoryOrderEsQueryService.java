package com.sdsdiy.orderimpl.service.factoryorder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.es.core.chain.EsChainQueryWrapper;
import com.es.core.wrapper.EsQueryWrapper;
import com.es.pojo.EsResponse;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.core.base.util.StringUtils;
import com.sdsdiy.designproductdata.dto.es.DesignProductEsDTO;
import com.sdsdiy.logisticsapi.constant.CountryExpressInfoNewConstant;
import com.sdsdiy.orderapi.constant.OrderOriginType;
import com.sdsdiy.orderapi.constant.UrgeType;
import com.sdsdiy.orderapi.constant.productionLine.FactoryOrderFilterTypeConstant;
import com.sdsdiy.orderapi.dto.FactoryOrderQueryParameters;
import com.sdsdiy.orderapi.dto.base.FactoryOrderLogisticsRespDto;
import com.sdsdiy.orderapi.enumeration.EnumExpressType;
import com.sdsdiy.orderapi.enums.FactoryOrderStatusEnum;
import com.sdsdiy.orderapi.enums.orderitem.OrderItemProductionStatusEnum;
import com.sdsdiy.orderdata.dto.factory.order.FactoryOrdersStaticRespDto;
import com.sdsdiy.orderdata.dto.factoryOrder.es.FactoryShippingTimeReqEsDTO;
import com.sdsdiy.orderdata.dto.factoryOrder.es.FactoryShippingTimeRespEsDTO;
import com.sdsdiy.orderdata.dto.factoryOrder.es.IssuingBayThroughputReqEsDTO;
import com.sdsdiy.orderdata.dto.factoryOrder.es.IssuingBayThroughputRespEsDTO;
import com.sdsdiy.orderdata.enums.OrderStatus;
import com.sdsdiy.orderdata.enums.QcStatusEnum;
import com.sdsdiy.orderimpl.entity.po.factoryorder.FactoryOrderEsPO;
import com.sdsdiy.orderimpl.feign.ProductFeign;
import com.sdsdiy.orderimpl.feign.designproduct.DesignProductEsFeign;
import com.sdsdiy.orderimpl.feign.user.IssuingBayUserFeign;
import com.sdsdiy.orderimpl.feign.user.TenantSysUserFeign;
import com.sdsdiy.orderimpl.service.FactoryOrderLogisticsService;
import com.sdsdiy.statdata.dto.issuing.bay.IssuingBayDealProgressDetailReqDTO;
import com.sdsdiy.statdata.dto.material.MerchantMaterialOrderAnalyzeReqDTO;
import com.sdsdiy.statdata.dto.material.MerchantMaterialOrderAnalyzeRespDTO;
import com.sdsdiy.statdata.dto.material.MerchantMaterialOrderAnalyzeRespDTO.Store;
import com.sdsdiy.userapi.dto.user.UserFilterMerchantDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.Filters;
import org.elasticsearch.search.aggregations.bucket.filter.FiltersAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.filter.FiltersAggregator;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.AvgAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedAvg;
import org.elasticsearch.search.aggregations.metrics.ParsedValueCount;
import org.elasticsearch.search.aggregations.metrics.ValueCountAggregationBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 查询放这边
 *
 * <AUTHOR>
 * @date 2023/8/7
 */
@Log4j2
@RequiredArgsConstructor
@Service
public class FactoryOrderEsQueryService {
    private static final Integer MANUSCRIPTE_OVER_YES = 1;
    private final FactoryOrderEsService factoryOrderEsService;
    private final ProductFeign productFeign;
    private final DesignProductEsFeign designProductEsFeign;
    private final TenantSysUserFeign tenantSysUserFeign;
    private final IssuingBayUserFeign issuingBayUserFeign;
    private final FactoryOrderLogisticsService factoryOrderLogisticsService;

    public EsResponse<FactoryOrderEsPO> pageIssuingBayDealProgress(IssuingBayDealProgressDetailReqDTO reqDTO) {
        EsChainQueryWrapper<FactoryOrderEsPO> queryWrapper = this.factoryOrderEsService.esChainQueryWrapper().must();
        queryWrapper.term(FactoryOrderEsPO::getIssuingBayId, reqDTO.getIssuingBayId())
                .terms(FactoryOrderEsPO::getStatus, DEAL_PROGRESS_FACTORY_ORDER_STATUS)
                // 未全部发货
                .lt(reqDTO.getItemProductionStatus() != null && !OrderItemProductionStatusEnum.ALL_SHIPPED.equalsCode(reqDTO.getItemProductionStatus())
                        , FactoryOrderEsPO::getItemProductionStatus, OrderItemProductionStatusEnum.ALL_SHIPPED.code)
                // 已全部发货
                .ge(reqDTO.getItemProductionStatus() != null && OrderItemProductionStatusEnum.ALL_SHIPPED.equalsCode(reqDTO.getItemProductionStatus())
                        , FactoryOrderEsPO::getItemProductionStatus, OrderItemProductionStatusEnum.ALL_SHIPPED.code)
                .terms(CollUtil.isNotEmpty(reqDTO.getOrderStatusList()), FactoryOrderEsPO::getOrderStatus, reqDTO.getOrderStatusList())
        ;
        if (reqDTO.getShipTimeStart() != null || reqDTO.getShipTimeEnd() != null) {
            queryWrapper.between(FactoryOrderEsPO::getShipTime, reqDTO.getShipTimeStart(), reqDTO.getShipTimeEnd(), true, true);
        }
        if (reqDTO.getReceiveTimeStart() != null || reqDTO.getReceiveTimeEnd() != null) {
            queryWrapper.between(FactoryOrderEsPO::getIssuingBayReceiveTime, reqDTO.getReceiveTimeStart(), reqDTO.getReceiveTimeEnd(), true, true);
        }
        if (reqDTO.getIsReceive() != null) {
            queryWrapper.term(BasePoConstant.NO.equals(reqDTO.getIsReceive()), FactoryOrderEsPO::getIssuingBayReceiveTime, 0)
                    .gt(BasePoConstant.YES.equals(reqDTO.getIsReceive()), FactoryOrderEsPO::getIssuingBayReceiveTime, 0);
        }
        if (reqDTO.getIsQc() != null) {
            queryWrapper.term(BasePoConstant.NO.equals(reqDTO.getIsQc()), FactoryOrderEsPO::getFinishedTime, 0)
                    .gt(BasePoConstant.YES.equals(reqDTO.getIsQc()), FactoryOrderEsPO::getFinishedTime, 0);
        }
        if (reqDTO.getIsCarriagePrint() != null) {
            queryWrapper.term(BasePoConstant.NO.equals(reqDTO.getIsCarriagePrint()), FactoryOrderEsPO::getCarriagePrintTime, 0)
                    .gt(BasePoConstant.YES.equals(reqDTO.getIsCarriagePrint()), FactoryOrderEsPO::getCarriagePrintTime, 0);
        }
        if (reqDTO.getIsClassify() != null) {
            queryWrapper.term(BasePoConstant.NO.equals(reqDTO.getIsClassify()), FactoryOrderEsPO::getClassifyTime, 0)
                    .gt(BasePoConstant.YES.equals(reqDTO.getIsClassify()), FactoryOrderEsPO::getClassifyTime, 0)
                    // 无需分拣=2，分拣时间=-1
                    .term(reqDTO.getIsClassify() == 2, FactoryOrderEsPO::getClassifyTime, -1);
        }

        queryWrapper.orderByAsc("shipTime");
        return queryWrapper.page(reqDTO.getPage(), reqDTO.getSize());
    }

    private static final List<Integer> DEAL_PROGRESS_FACTORY_ORDER_STATUS = Arrays.asList(
            FactoryOrderStatusEnum.RECEIVE_THINGS.status
            , FactoryOrderStatusEnum.PASS_GC.status
            , FactoryOrderStatusEnum.ACCOMPLISH.status
    );

    public Aggregations statForIssuingBayDealProgress(Long issuingBayId, Integer days) {
        long now = System.currentTimeMillis();
        // 包括今天，所以n-1天前
        long beforeDay = TimeUtil.beginOfDay(now, -(days - 1));
        EsChainQueryWrapper<FactoryOrderEsPO> queryWrapper = this.factoryOrderEsService.esChainQueryWrapper().must();
        queryWrapper.term(FactoryOrderEsPO::getIssuingBayId, issuingBayId)
                .between(FactoryOrderEsPO::getShipTime, beforeDay, now, true, true)
                .terms(FactoryOrderEsPO::getStatus, DEAL_PROGRESS_FACTORY_ORDER_STATUS);

        // 按 发货日期 分组
        DateHistogramAggregationBuilder shipTimeBuilder = AggregationBuilders.dateHistogram("shipTime")
                .field("shipTime").fixedInterval(DateHistogramInterval.DAY)
                .minDocCount(0).format("").offset("-8h");

        RangeQueryBuilder issuingBayReceived = QueryBuilders.rangeQuery("issuingBayReceiveTime").gte(1);
        RangeQueryBuilder finished = QueryBuilders.rangeQuery("finishedTime").gte(1);
        RangeQueryBuilder unAllShipped = QueryBuilders.rangeQuery("itemProductionStatus").lt(OrderItemProductionStatusEnum.ALL_SHIPPED.code);
        RangeQueryBuilder allShipped = QueryBuilders.rangeQuery("itemProductionStatus").gte(OrderItemProductionStatusEnum.ALL_SHIPPED.code);
        TermsQueryBuilder cancelOrder = QueryBuilders.termsQuery("orderStatus", OrderStatus.CANCEL_STATUS_LIST);
        RangeQueryBuilder canClassify = QueryBuilders.rangeQuery("classifyTime").gte(0);
        // 数量统计
        FiltersAggregationBuilder countNumBuilder = AggregationBuilders.filters("countNum"
                , new FiltersAggregator.KeyedFilter("shipNum", QueryBuilders.rangeQuery("shipTime").gte(1))
                // 收货数（包括没收货直接质检的）
                , new FiltersAggregator.KeyedFilter("receiveNum", QueryBuilders.boolQuery().should(issuingBayReceived).should(finished))
                , new FiltersAggregator.KeyedFilter("qcNum", finished)
                , new FiltersAggregator.KeyedFilter("carriagePrintNum", QueryBuilders.rangeQuery("carriagePrintTime").gte(1))
                , new FiltersAggregator.KeyedFilter("canCarriagePrintNum", QueryBuilders.boolQuery().must(allShipped).mustNot(cancelOrder))
                , new FiltersAggregator.KeyedFilter("classifyNum", QueryBuilders.rangeQuery("classifyTime").gte(1))
                , new FiltersAggregator.KeyedFilter("canClassifyNum", QueryBuilders.boolQuery().must(canClassify).must(allShipped).mustNot(cancelOrder))
                // 未配齐
                , new FiltersAggregator.KeyedFilter("unAllShippedNum", QueryBuilders.boolQuery().must(unAllShipped).mustNot(cancelOrder))
        );
        shipTimeBuilder.subAggregation(countNumBuilder);

        queryWrapper.addAggregationBuilder(shipTimeBuilder);
        return queryWrapper.aggregation();
    }

    public List<FactoryShippingTimeRespEsDTO> statForFactoryShippingTime(FactoryShippingTimeReqEsDTO reqEsDTO) {
        EsChainQueryWrapper<FactoryOrderEsPO> queryWrapper = this.factoryOrderEsService.esChainQueryWrapper().must();
        queryWrapper.between(FactoryOrderEsPO::getShipTime, reqEsDTO.getShipTimeStart(), reqEsDTO.getShipTimeEnd(), true, false)
                .gt(FactoryOrderEsPO::getIssuingBayReceiveTime, 0)
                .terms(CollUtil.isNotEmpty(reqEsDTO.getFactoryIds()), FactoryOrderEsPO::getFactoryId, reqEsDTO.getFactoryIds());
        // 平均值（收货时间-发货时间）
        AvgAggregationBuilder avgTimeBuilder = AggregationBuilders.avg("avgTime")
                .script(new Script("doc['issuingBayReceiveTime'].value-doc['shipTime'].value"));
        // 按 工厂 分组
        TermsAggregationBuilder factoryBuilder = AggregationBuilders.terms("factoryId")
                .field("factoryId").size(BasePoConstant.ES_MAX_QUERY)
                .subAggregation(avgTimeBuilder);
        queryWrapper.addAggregationBuilder(factoryBuilder);
        Aggregations aggregation = queryWrapper.aggregation();
        List<FactoryShippingTimeRespEsDTO> esList = new ArrayList<>();

        Terms factoryTerms = aggregation.get("factoryId");
        for (Terms.Bucket factoryBucket : factoryTerms.getBuckets()) {
            FactoryShippingTimeRespEsDTO respEsDTO = new FactoryShippingTimeRespEsDTO();
            respEsDTO.setFactoryId(Long.parseLong(factoryBucket.getKeyAsString()));
            ParsedAvg avgTimeTerms = factoryBucket.getAggregations().get("avgTime");
            respEsDTO.setAvgShippingTime(BigDecimal.valueOf(avgTimeTerms.getValue()).setScale(0, RoundingMode.UP).longValue());
            esList.add(respEsDTO);
        }
        return esList;
    }

    public List<IssuingBayThroughputRespEsDTO> statForIssuingBayThroughput(IssuingBayThroughputReqEsDTO reqEsDTO) {
        if (CollUtil.isEmpty(reqEsDTO.getIssuingBayIds())) {
            return Collections.emptyList();
        }
        EsChainQueryWrapper<FactoryOrderEsPO> queryWrapper = this.factoryOrderEsService.esChainQueryWrapper().must();
        queryWrapper.between(FactoryOrderEsPO::getShipTime, reqEsDTO.getShipTimeStart(), reqEsDTO.getShipTimeEnd(), true, false)
                .ge(FactoryOrderEsPO::getItemProductionStatus, OrderItemProductionStatusEnum.ALL_SHIPPED.code)
                .and(a -> a.mustNot().terms(FactoryOrderEsPO::getOrderStatus, OrderStatus.CANCEL_STATUS_LIST))
                .terms(FactoryOrderEsPO::getIssuingBayId, reqEsDTO.getIssuingBayIds());
        // 按 是否打印面单 分组，carriagePrintTime>0表示已打印
        FiltersAggregationBuilder carriagePrintTimeBuilder = AggregationBuilders.filters("carriagePrintTime"
                , new FiltersAggregator.KeyedFilter("printNum", QueryBuilders.rangeQuery("carriagePrintTime").from(1))
                , new FiltersAggregator.KeyedFilter("unPrintNum", QueryBuilders.termQuery("carriagePrintTime", 0)));
        // 按 发货日期 分组
        DateHistogramAggregationBuilder shipTimeBuilder = AggregationBuilders.dateHistogram("shipTime")
                .field("shipTime").fixedInterval(DateHistogramInterval.DAY)
                .minDocCount(0).format("").offset("-8h")
                .subAggregation(carriagePrintTimeBuilder);
        // 按 发货仓 分组
        TermsAggregationBuilder issuingBayBuilder = AggregationBuilders.terms("issuingBayId")
                .field("issuingBayId").size(reqEsDTO.getIssuingBayIds().size())
                .subAggregation(shipTimeBuilder);
        queryWrapper.addAggregationBuilder(issuingBayBuilder);
        Aggregations aggregation = queryWrapper.aggregation();

        List<IssuingBayThroughputRespEsDTO> respEsDtoList = new ArrayList<>();
        Terms issuingBayTerms = aggregation.get("issuingBayId");
        for (Terms.Bucket issuingBayBucket : issuingBayTerms.getBuckets()) {
            IssuingBayThroughputRespEsDTO respEsDTO = new IssuingBayThroughputRespEsDTO();
            respEsDTO.setIssuingBayId(Long.parseLong(issuingBayBucket.getKeyAsString()));

            List<IssuingBayThroughputRespEsDTO.DayDTO> dayList = new ArrayList<>();
            Histogram shipTimeTerms = issuingBayBucket.getAggregations().get("shipTime");
            for (Histogram.Bucket shipTimeBucket : shipTimeTerms.getBuckets()) {
                IssuingBayThroughputRespEsDTO.DayDTO dayDTO = new IssuingBayThroughputRespEsDTO.DayDTO();
                Filters carriagePrintTerms = shipTimeBucket.getAggregations().get("carriagePrintTime");
                for (Filters.Bucket carriagePrintBucket : carriagePrintTerms.getBuckets()) {
                    if ("printNum".equals(carriagePrintBucket.getKeyAsString())) {
                        dayDTO.setPrintNum((int) carriagePrintBucket.getDocCount());
                    } else if ("unPrintNum".equals(carriagePrintBucket.getKeyAsString())) {
                        dayDTO.setUnPrintNum((int) carriagePrintBucket.getDocCount());
                    }
                }
                if (!NumberUtils.greaterZero(dayDTO.getPrintNum()) && !NumberUtils.greaterZero(dayDTO.getUnPrintNum())) {
                    continue;
                }
                ZonedDateTime timeKey = (ZonedDateTime) shipTimeBucket.getKey();
                dayDTO.setDayTime(LocalDateTimeUtil.toEpochMilli(timeKey));
                dayList.add(dayDTO);
            }
            respEsDTO.setDayList(dayList);
            respEsDtoList.add(respEsDTO);
        }
        return respEsDtoList;
    }

    public EsChainQueryWrapper<FactoryOrderEsPO> materialOrderAnalyzeQueryWrapper(MerchantMaterialOrderAnalyzeReqDTO reqDTO) {
        List<Long> inProductParentIds = null;
        if (StrUtil.isNotBlank(reqDTO.getProductName())) {
            inProductParentIds = this.productFeign.findParentIdsByCategoryOrNameOrSku(BaseListDto.of(null), reqDTO.getProductName());
            if (CollUtil.isEmpty(inProductParentIds)) {
                return null;
            }
        }
        EsChainQueryWrapper<FactoryOrderEsPO> queryWrapper = this.factoryOrderEsService.esChainQueryWrapper().must();
        queryWrapper.term(FactoryOrderEsPO::getMerchantId, McContentHelper.getCurrentMerchantId())
                .terms(FactoryOrderEsPO::getMaterialIds, reqDTO.getMaterialId())
                .terms(CollUtil.isNotEmpty(inProductParentIds), FactoryOrderEsPO::getProductParentId, inProductParentIds);
        if (StrUtil.isNotBlank(reqDTO.getYearMoth())) {
            DateTime yearMoth = DateUtil.parse(reqDTO.getYearMoth(), "yyyy-MM");
            long beginMoth = DateUtil.beginOfMonth(yearMoth).getTime();
            long endMoth = DateUtil.endOfMonth(yearMoth).getTime();
            queryWrapper.between(FactoryOrderEsPO::getOrderPayTime, beginMoth, endMoth);
        }
        return queryWrapper;
    }

    public List<String> materialOrderAnalyzeStoreGroup(MerchantMaterialOrderAnalyzeReqDTO reqDTO) {
        EsChainQueryWrapper<FactoryOrderEsPO> queryWrapper = this.materialOrderAnalyzeQueryWrapper(reqDTO);
        if (queryWrapper == null) {
            return Collections.emptyList();
        }
        TermsAggregationBuilder merchantStoreSiteGroup = AggregationBuilders
                .terms("merchantStoreSite").field("merchantStoreSite.keyword").size(BasePoConstant.ES_MAX_QUERY);
        TermsAggregationBuilder merchantStoreGroup = AggregationBuilders
                .terms("merchantStoreId").field("merchantStoreId").size(BasePoConstant.ES_MAX_QUERY)
                .subAggregation(merchantStoreSiteGroup);
        Aggregations aggregation = queryWrapper.addAggregationBuilder(merchantStoreGroup).aggregation();
        Terms terms = aggregation.get("merchantStoreId");
        List<String> storeList = new ArrayList<>();
        for (Terms.Bucket bucket : terms.getBuckets()) {
            String storeId = bucket.getKeyAsString();
            storeList.add(storeId);
            Terms siteTerms = bucket.getAggregations().get("merchantStoreSite");
            for (Terms.Bucket siteBucket : siteTerms.getBuckets()) {
                String site = siteBucket.getKeyAsString();
                if (StrUtil.isNotBlank(site)) {
                    storeList.add(storeId + "-" + site);
                }
            }
        }
        return storeList;
    }

    public PageResultDto<MerchantMaterialOrderAnalyzeRespDTO> materialOrderAnalyze(MerchantMaterialOrderAnalyzeReqDTO reqDTO) {
        EsChainQueryWrapper<FactoryOrderEsPO> queryWrapper = this.materialOrderAnalyzeQueryWrapper(reqDTO);
        if (queryWrapper == null) {
            return PageResultDto.empty();
        }
        // 店铺
        if (StrUtil.isNotBlank(reqDTO.getStoreId())) {
            // 格式：1234-US
            String[] storeArr = reqDTO.getStoreId().split("-");
            queryWrapper.term(FactoryOrderEsPO::getMerchantStoreId, storeArr[0]);
            if (storeArr.length > 1) {
                queryWrapper.termKeyword(FactoryOrderEsPO::getMerchantStoreSite, storeArr[1]);
            }
        }
        EsResponse<FactoryOrderEsPO> esResponse = queryWrapper.orderByDesc("orderPayTime", "id")
                .page(reqDTO.getPage(), reqDTO.getSize());
        if (CollUtil.isEmpty(esResponse.getList())) {
            return PageResultDto.empty();
        }
        List<MerchantMaterialOrderAnalyzeRespDTO> respList = ListUtil.copyProperties(esResponse.getList(), MerchantMaterialOrderAnalyzeRespDTO.class
                , (s, t) -> {
                    t.setKeyId(s.getDesignProductKeyId())
                            .setPayTime(com.sdsdiy.core.base.util.DateUtil.longToString(s.getOrderPayTime()))
                            .setStore(new Store().setId(s.getMerchantStoreId()).setSite(s.getMerchantStoreSite()));
                });
        return PageResultDto.success(esResponse.getTotal(), respList);
    }

    /**
     * 工厂端 生产单列表、待排产订单列表、产能线、产品分组 wrapper
     */
    public EsChainQueryWrapper<FactoryOrderEsPO> genFactoryEndEsQueryWrapper(FactoryOrderQueryParameters params) {

        EsChainQueryWrapper<FactoryOrderEsPO> esQueryParam = this.factoryOrderEsService.esChainQueryWrapper().must();
        esQueryParam.term(FactoryOrderEsPO::getIsManuscriptOver, MANUSCRIPTE_OVER_YES);
        esQueryParam.term(Objects.nonNull(params.getIsDownMaterrial()), FactoryOrderEsPO::getIsDownMaterial, params.getIsDownMaterrial());
        esQueryParam.term(NumberUtils.greaterZero(params.getProductId()), FactoryOrderEsPO::getProductParentId, params.getProductId());
        esQueryParam.term(BasePoConstant.INTEGER_ZERO.equals(params.getIsJoinTask()), FactoryOrderEsPO::getTaskId, BasePoConstant.INTEGER_ZERO);
        if (StrUtil.isNotEmpty(params.getStartTime())) {
            Long startTime = TimeUtil.str2Long(params.getStartTime());
            esQueryParam.ge(FactoryOrderEsPO::getCreateTime, startTime);
        }
        if (StrUtil.isNotEmpty(params.getEndTime())) {
            Long endTime = TimeUtil.str2Long(params.getEndTime());
            esQueryParam.le(FactoryOrderEsPO::getCreateTime, endTime);
        }
        if (StrUtil.isNotEmpty(params.getDeliverStartTime())) {
            Long startTime = TimeUtil.str2Long(params.getDeliverStartTime());
            esQueryParam.ge(FactoryOrderEsPO::getShipTime, startTime);
        }
        if (StrUtil.isNotEmpty(params.getDeliverEndTime())) {
            Long endTime = TimeUtil.str2Long(params.getDeliverEndTime());
            esQueryParam.le(FactoryOrderEsPO::getShipTime, endTime);
        }
        if (StrUtil.isNotEmpty(params.getProduceStartTime())) {
            Long startTime = TimeUtil.str2Long(params.getProduceStartTime());
            esQueryParam.ge(FactoryOrderEsPO::getProductTime, startTime);
        }
        if (StrUtil.isNotEmpty(params.getProduceEndTime())) {
            Long endTime = TimeUtil.str2Long(params.getProduceEndTime());
            esQueryParam.le(FactoryOrderEsPO::getProductTime, endTime);
        }

        String originType = params.getOriginType();
        if (StrUtil.isNotEmpty(originType)) {
            OrderOriginType orderOriginType = OrderOriginType.check(originType);
            if (orderOriginType.equals(OrderOriginType.NORMAL)) {
                esQueryParam.and(a -> a.mustNot().term("originType.keyword", OrderOriginType.FBA.getValue()));
            } else {
                esQueryParam.term("originType.keyword", originType);
            }
        }
        if (CollUtil.isNotEmpty(params.getNos())) {

            esQueryParam.and(a -> a.should().terms(FactoryOrderEsPO::getOrderNo, params.getNos())
                    .terms(FactoryOrderEsPO::getNo, params.getNos()));
        }

        if (StrUtil.isNotEmpty(params.getKeyword())) {
            params.setKeyword(params.getKeyword().trim());
            DesignProductEsDTO esDTO = this.designProductEsFeign.findByKeyId(params.getKeyword());
            if (Objects.nonNull(esDTO) && StrUtil.isNotEmpty(esDTO.getKeyId())) {
                esQueryParam.and(a -> a.should().wildcard("productName.keyword", this.addWildcardPredixAndSuffix(params.getKeyword()))
                        .wildcard("no.keyword", this.addWildcardPredixAndSuffix(params.getKeyword()))
                        .term("designProductKeyId.keyword", esDTO.getKeyId())
                        .wildcard("originalAsNo.keyword", this.addWildcardPredixAndSuffix(params.getKeyword())));
            } else {
                esQueryParam.and(a -> a.should().wildcard("productName.keyword", this.addWildcardPredixAndSuffix(params.getKeyword()))
                        .wildcard("no.keyword", this.addWildcardPredixAndSuffix(params.getKeyword()))
                        .wildcard("originalAsNo.keyword", this.addWildcardPredixAndSuffix(params.getKeyword())));
            }
        }

        if (StrUtil.isNotEmpty(params.getStatus())) {
            List<Integer> statusList = Arrays.stream(params.getStatus().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            esQueryParam.terms(FactoryOrderEsPO::getStatus, statusList);
        }
        esQueryParam.term(StrUtil.isNotEmpty(params.getProductSize()), "productSize.keyword", params.getProductSize());
        esQueryParam.wildcard(StrUtil.isNotEmpty(params.getProductColorName()), "productColorName.keyword", this.addWildcardPredixAndSuffix(params.getProductColorName()));
        esQueryParam.term(NumberUtils.greaterZero(params.getIssuingBayAreaId()), FactoryOrderEsPO::getIssuingBayAreaId, params.getIssuingBayAreaId());
        esQueryParam.term(NumberUtils.greaterZero(params.getIssuingBayId()), FactoryOrderEsPO::getIssuingBayId, params.getIssuingBayId());
        esQueryParam.term(StrUtil.isNotEmpty(params.getMerchantNo()), "merchantNo.keyword", params.getMerchantNo());
        if (StrUtil.isNotEmpty(params.getAfterServiceRefuseType())) {
            List<Long> filters = StringUtils.stringToLongList(params.getAfterServiceRefuseType());

            if (filters.contains(FactoryOrderFilterTypeConstant.FILTER_AFTER_SERVICE)) {
                esQueryParam.term(FactoryOrderEsPO::getBeAfterServiceOrder, FactoryOrderFilterTypeConstant.FILTER_AFTER_SERVICE.intValue());
            }
            if (filters.contains(FactoryOrderFilterTypeConstant.FILTER_RESEND)) {
                esQueryParam.term(FactoryOrderEsPO::getRefuseType, FactoryOrderFilterTypeConstant.FILTER_RESEND.intValue());

            }
            if (filters.contains(FactoryOrderFilterTypeConstant.FILTER_REJECT)) {
                esQueryParam.term(FactoryOrderEsPO::getRefuseType, FactoryOrderFilterTypeConstant.FILTER_REJECT.intValue());
            }
        }
        esQueryParam.term(NumberUtils.greaterZero(params.getProductionLineId()), FactoryOrderEsPO::getProductionLineId, params.getProductionLineId());
        esQueryParam.term(Objects.nonNull(params.getManuscriptFeedbackStatus()), FactoryOrderEsPO::getManuscriptFeedbackStatus, params.getManuscriptFeedbackStatus());
        esQueryParam.and(a -> a.mustNot().term(Objects.nonNull(params.getNoManuscriptFeedbackStatus()),
                FactoryOrderEsPO::getManuscriptFeedbackStatus, params.getNoManuscriptFeedbackStatus()));

        esQueryParam.term(StrUtil.isNotEmpty(params.getDownStatus()), "downStatus.keyword", params.getDownStatus());
        esQueryParam.term(StrUtil.isNotEmpty(params.getRequireManuscript()), "requireManuscript.keyword", params.getRequireManuscript());

        esQueryParam.term(NumberUtils.greaterZero(params.getTaskId()), FactoryOrderEsPO::getTaskId, params.getTaskId());
        esQueryParam.term(StrUtil.isNotEmpty(params.getSerialNumber()), "serialNumber.keyword", params.getSerialNumber());

        esQueryParam.term(StrUtil.isNotEmpty(params.getOrderAllocationType()), "orderAllocationType.keyword", params.getOrderAllocationType());

        esQueryParam.term(NumberUtils.greaterZero(params.getFactoryId()), FactoryOrderEsPO::getFactoryId, params.getFactoryId());
        Consumer<EsQueryWrapper<FactoryOrderEsPO>> earlyWarningQueryCondition =
                this.genEarlyWarningQueryCondition(params.getEarlyWarningIds(), params.getEarlyWarningId());
        if (Objects.nonNull(earlyWarningQueryCondition)) {
            esQueryParam.and(earlyWarningQueryCondition);
        }
        esQueryParam.term(StrUtil.isNotEmpty(params.getAccurateProductColorName()), FactoryOrderEsPO::getProductColorName, params.getAccurateProductColorName());
        if (StrUtil.isNotEmpty(params.getDistributorTenantIds())) {
            List<Long> distributorTenantIds = Arrays.stream(params.getDistributorTenantIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
            esQueryParam.terms(FactoryOrderEsPO::getMerchantTenantId, distributorTenantIds);
        }
        String sort = params.getSort();

        if (StringUtils.isNotBlank(sort)) {

            List<SortRule> sortRuleList = SortUtil.getSortRuleListUnderLineToCamel(sort);

            for (SortRule sortRule : sortRuleList) {
                //字符串类型的排序 得后面加.keyword
                if (!"id".equals(sortRule.getColumnName()) && !sortRule.getColumnName().endsWith("time")) {
                    sortRule.setColumnName(sortRule.getColumnName() + ".keyword");
                }
                if (sortRule.getOrderByDesc().equals(true)) {
                    esQueryParam.orderByDesc(sortRule.getColumnName());
                } else {
                    esQueryParam.orderByAsc(sortRule.getColumnName());
                }
            }
        }
        return esQueryParam;
    }

    private Consumer<EsQueryWrapper<FactoryOrderEsPO>> genEarlyWarningQueryCondition(List<Long> earlyWarningIds, Long earlyWarning) {

        long now = System.currentTimeMillis();
        Consumer<EsQueryWrapper<FactoryOrderEsPO>> consumer;

        if (CollUtil.isEmpty(earlyWarningIds) && !NumberUtils.greaterZero(earlyWarning)) {
            return null;
        }

        if (CollUtil.isEmpty(earlyWarningIds)) {

            earlyWarningIds = new ArrayList<>();

            earlyWarningIds.add(earlyWarning);
        }

        List<Long> finalEarlyWarningIds = earlyWarningIds;

        consumer = a ->
                a.should()
                        .or(b -> b.terms(FactoryOrderEsPO::getOrderEarlyWarningIds, finalEarlyWarningIds))
                        .or(finalEarlyWarningIds.contains(UrgeType.FACTORY_PRODUCE_TIMEOUT.getId()),
                                containsProductTimeOut -> {
                                    containsProductTimeOut.lt(FactoryOrderEsPO::getOutDate, now);
                                    this.statusLtAccomplishCondition(containsProductTimeOut);
                                })
                        .or(finalEarlyWarningIds.contains(UrgeType.WARING.getId()), containsWarning -> {
                            containsWarning
                                    .should()
                                    .term(FactoryOrderEsPO::getOrderEarlyWarningIds, 10L);
                            this.urgenWarningAddCondition(containsWarning);
                            this.resendWarningAddCondition(containsWarning);
                        })
                        .or(finalEarlyWarningIds.contains(UrgeType.URGEN_EXPRESS.getId()), this::urgenWarningAddCondition)
                        .or(finalEarlyWarningIds.contains(UrgeType.RESEND.getId()), this::resendWarningAddCondition)
                        .or(finalEarlyWarningIds.contains(UrgeType.REJECT.getId()), this::rejectWarningAddCondition)
                        .or(finalEarlyWarningIds.contains(UrgeType.LOSE.getId()), this::loseWarningAddCondition)
                        .or(finalEarlyWarningIds.contains(UrgeType.NO_TAKE.getId()), noTakeWarning -> {
                            noTakeWarning.lt(FactoryOrderEsPO::getOutPaymentDate, now)
                                    .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.UNCONFIRMED.getStatus());
                            this.isNotOverTimeAddCondition(noTakeWarning, now);
                        })
                        .or(finalEarlyWarningIds.contains(UrgeType.NO_SHIP.getId()),
                                noShip -> noShip.lt(FactoryOrderEsPO::getOutExpressDate, now)
                                        .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.SHIPMENTS.getStatus()))
                        .or(finalEarlyWarningIds.contains(UrgeType.NO_HARVEST.getId()),
                                noHarvest -> noHarvest.lt(FactoryOrderEsPO::getOutHarvestDate, now)
                                        .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.RECEIVE_THINGS.getStatus()))
                        .or(finalEarlyWarningIds.contains(UrgeType.NO_QUALITY_TESTING.getId()),
                                noQualityTest -> noQualityTest.lt(FactoryOrderEsPO::getOutConfirmDate, now)
                                        .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.PASS_GC.getStatus()))
                        .or(finalEarlyWarningIds.contains(UrgeType.COLSEER_24.getId()),
                                close24 -> close24
                                        .and(close24Or ->
                                                close24Or.should()
                                                        .or(outPayment ->
                                                                outPayment.gt(FactoryOrderEsPO::getOutPaymentDate, now)
                                                                        .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.UNCONFIRMED.getStatus()))
                                                        .or(outExpressDate -> outExpressDate.gt(FactoryOrderEsPO::getOutExpressDate, now)
                                                                .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.SHIPMENTS.getStatus()))
                                                        .or(accomplishProduction -> accomplishProduction
                                                                .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.ACCOMPLISH_PRODUCTION.getStatus()))
                                                        .or(harvestDate -> harvestDate.gt(FactoryOrderEsPO::getOutHarvestDate, now)
                                                                .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.RECEIVE_THINGS.getStatus()))
                                                        .or(outConfirmDate -> outConfirmDate.gt(FactoryOrderEsPO::getOutConfirmDate, now)
                                                                .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.PASS_GC.getStatus())))
                                        .lt(FactoryOrderEsPO::getOutCycleDate, now + 86400000L)
                                        .gt(FactoryOrderEsPO::getOutCycleDate, now)
                                        .lt(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.ACCOMPLISH.getStatus())
                                        .gt(FactoryOrderEsPO::getOutDate, now));
        return consumer;
    }

    private void loseWarningAddCondition(EsQueryWrapper<FactoryOrderEsPO> wrapper) {
        wrapper.term(FactoryOrderEsPO::getRefuseType, QcStatusEnum.LESS.getCode());
        this.statusLtAccomplishCondition(wrapper);
    }

    private void isNotOverTimeAddCondition(EsQueryWrapper<FactoryOrderEsPO> wrapper, Long time) {
        wrapper.gt(FactoryOrderEsPO::getOutCycleDate, time);
    }

    private void rejectWarningAddCondition(EsQueryWrapper<FactoryOrderEsPO> wrapper) {
        wrapper.term(FactoryOrderEsPO::getRefuseType, BasePoConstant.YES);
        this.statusLtAccomplishCondition(wrapper);
    }

    private void resendWarningAddCondition(EsQueryWrapper<FactoryOrderEsPO> wrapper) {
        wrapper.term(FactoryOrderEsPO::getBeSendForLose, BasePoConstant.YES);
        this.statusLtAccomplishCondition(wrapper);
    }

    private void urgenWarningAddCondition(EsQueryWrapper<FactoryOrderEsPO> wrapper) {
        wrapper.term(FactoryOrderEsPO::getOrderExpressType, EnumExpressType.URGENT.getValue());
        this.statusLtAccomplishCondition(wrapper);
    }

    private void statusLtAccomplishCondition(EsQueryWrapper<FactoryOrderEsPO> wrapper) {
        wrapper.lt(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.ACCOMPLISH.status);
    }

    public EsChainQueryWrapper<FactoryOrderEsPO> genPodEndQueryWrapper(Long tenantId, String lastThreeMonths,
                                                                       Long userId, String startTime, String endTime,
                                                                       Long factoryId, String keyword, String status,
                                                                       String merchantOrProductOrCrmUserName,
                                                                       Integer isManuscriptOver, Long earlyWarningId,
                                                                       Long issuingBayAreaId, Long issuingBayId,
                                                                       String originType, String finishedStartTime,
                                                                       String finishedEndTime,
                                                                       String orderItemSupplyChainType,
                                                                       String logisticsNo,
                                                                       Long beginExpressTime,
                                                                       Long endExpressTime,
                                                                       Long distributorTenantId) {

        EsChainQueryWrapper<FactoryOrderEsPO> esQueryParam = this.factoryOrderEsService.esChainQueryWrapper().must();
        esQueryParam.term(NumberUtils.greaterZero(factoryId), FactoryOrderEsPO::getFactoryId, factoryId);
        esQueryParam.term(Objects.nonNull(isManuscriptOver), FactoryOrderEsPO::getIsManuscriptOver, isManuscriptOver);
        if (StringUtils.isNotEmpty(logisticsNo)) {
            List<FactoryOrderLogisticsRespDto> factoryOrderLogisticsRespDtos = this.factoryOrderLogisticsService.getByLikeLogisticsNo(logisticsNo);
            List<Long> factoryOrderIds = Lists.newArrayList(-1L);
            if (CollUtil.isNotEmpty(factoryOrderLogisticsRespDtos)) {
                factoryOrderIds = factoryOrderLogisticsRespDtos.stream().map(FactoryOrderLogisticsRespDto::getFactoryOrderId).distinct().collect(Collectors.toList());
            }
            esQueryParam.terms(FactoryOrderEsPO::getId, factoryOrderIds);
        }
        if (NumberUtils.greaterZero(beginExpressTime)) {
            esQueryParam.ge(FactoryOrderEsPO::getExpressTime, beginExpressTime);
        }
        if (NumberUtils.greaterZero(endExpressTime)) {
            esQueryParam.le(FactoryOrderEsPO::getExpressTime, endExpressTime);
        }
        //新增分销商筛选
        esQueryParam.term(NumberUtils.greaterZero(distributorTenantId), FactoryOrderEsPO::getMerchantTenantId, distributorTenantId);
        if (StrUtil.isNotEmpty(merchantOrProductOrCrmUserName)) {
            esQueryParam.and(a -> a.should()
                    .wildcard("productSku.keyword", this.addWildcardPredixAndSuffix(merchantOrProductOrCrmUserName))
                    .wildcard("productName.keyword", this.addWildcardPredixAndSuffix(merchantOrProductOrCrmUserName))
                    .wildcard("merchantName.keyword", this.addWildcardPredixAndSuffix(merchantOrProductOrCrmUserName))
                    .wildcard("crmUserName.keyword", this.addWildcardPredixAndSuffix(merchantOrProductOrCrmUserName))
            );
        }
        // 业务员对应的商户ids
        UserFilterMerchantDto userFilterMerchantDto = new UserFilterMerchantDto();
        userFilterMerchantDto.setUserId(userId);
        userFilterMerchantDto.setMerchantIds(Lists.newArrayList());
        List<Long> finalFilterMerchantIds = this.tenantSysUserFeign.getFinalFilterMerchantIds(userFilterMerchantDto);
        if (CollUtil.isNotEmpty(finalFilterMerchantIds)) {
            esQueryParam.terms(FactoryOrderEsPO::getMerchantId, finalFilterMerchantIds);
        }
        if (StringUtils.isNotEmpty(keyword)) {
            List<String> list = Arrays.asList(keyword.split(" "));
            esQueryParam.and(a -> a.should()
                    .terms("no.keyword", list)
                    .terms(FactoryOrderEsPO::getOrderNo, list));
        }
        if (NumberUtils.greaterZero(userId)) {
            List<Long> issuingBayIds = this.issuingBayUserFeign.permissionList(tenantId, userId, issuingBayAreaId, issuingBayId);
            esQueryParam.terms(FactoryOrderEsPO::getIssuingBayId, issuingBayIds);
        }
        if (StrUtil.isNotEmpty(status)) {
            List<Long> statuses = Arrays.stream(status.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            esQueryParam.terms(FactoryOrderEsPO::getStatus, statuses);
            if (Objects.isNull(isManuscriptOver) && statuses.size() == 1 && FactoryOrderStatusEnum.CANCEL.status != statuses.get(0).intValue()) {
                esQueryParam.term(FactoryOrderEsPO::getIsManuscriptOver, MANUSCRIPTE_OVER_YES);
            }
        } else {
            esQueryParam.and(a -> a.mustNot().term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.SHELVE.getStatus()));
        }

        if (StrUtil.isNotEmpty(lastThreeMonths)) {

            DateTime lastThreeMonthsTime = DateUtil.offsetMonth(DateUtil.date(), -3);
            if (lastThreeMonths.equals(BasePoConstant.YES_STRING)) {
                // 近三个月
                if (null != lastThreeMonthsTime) {
                    esQueryParam.ge(FactoryOrderEsPO::getCreateTime, lastThreeMonthsTime.getTime());
                }

            } else {
                // 三个月前
                if (null != lastThreeMonthsTime) {
                    esQueryParam.lt(FactoryOrderEsPO::getCreateTime, lastThreeMonthsTime.getTime());

                }
            }
        }

        if (StrUtil.isNotEmpty(startTime)) {
            Long startTimeLong = TimeUtil.str2Long(startTime);
            esQueryParam.ge(FactoryOrderEsPO::getCreateTime, startTimeLong);
        }
        if (StrUtil.isNotEmpty(endTime)) {
            Long endTimeLong = TimeUtil.str2Long(endTime);
            esQueryParam.le(FactoryOrderEsPO::getCreateTime, endTimeLong);
        }
        if (StrUtil.isNotEmpty(originType)) {
            OrderOriginType orderOriginType = OrderOriginType.check(originType);
            if (orderOriginType.equals(OrderOriginType.NORMAL)) {
                esQueryParam.and(a -> a.mustNot().term("originType.keyword", OrderOriginType.FBA.getValue()));
            } else {
                esQueryParam.term("originType.keyword", originType);
            }
        }
        if (StrUtil.isNotEmpty(finishedStartTime)) {
            Long finishedStartTimeLong = TimeUtil.str2Long(finishedStartTime);
            esQueryParam.ge(FactoryOrderEsPO::getFinishedTime, finishedStartTimeLong);
        }
        if (StrUtil.isNotEmpty(finishedEndTime)) {
            Long finishedEndTimeLong = TimeUtil.str2Long(finishedEndTime);
            esQueryParam.le(FactoryOrderEsPO::getFinishedTime, finishedEndTimeLong);
        }


        Consumer<EsQueryWrapper<FactoryOrderEsPO>> earlyWarningQueryCondition =
                this.genEarlyWarningQueryCondition(null, earlyWarningId);
        if (Objects.nonNull(earlyWarningQueryCondition)) {
            esQueryParam.and(earlyWarningQueryCondition);
        }
        if (StrUtil.isNotEmpty(orderItemSupplyChainType)) {
            esQueryParam.term("orderItemSupplyChainType.keyword", orderItemSupplyChainType);
        }
        return esQueryParam;
    }

    public FactoryOrdersStaticRespDto earlyWarningStatistics(Long factoryId, Integer status, Long taskId) {

        EsChainQueryWrapper<FactoryOrderEsPO> urgentQueryWrapper = this.countWarningNumWrapper(factoryId, status, taskId);
        ValueCountAggregationBuilder idCountBuilder = AggregationBuilders.count("idCount").field("id");
        urgentQueryWrapper.addAggregationBuilder(idCountBuilder);
        Aggregations aggregations = urgentQueryWrapper.aggregation();
        ParsedValueCount idCount = aggregations.get("idCount");
        long idTotal = idCount.getValue();
        FactoryOrdersStaticRespDto res = new FactoryOrdersStaticRespDto();
        res.setUrgent((int) idTotal);
        long now = System.currentTimeMillis();

        if (status == null || FactoryOrderStatusEnum.UNCONFIRMED.getStatus() == status) {
            EsChainQueryWrapper<FactoryOrderEsPO> confirmWarningNumWrapper = this.confirmWarningNumWrapper(factoryId, taskId, now);
            confirmWarningNumWrapper.addAggregationBuilder(idCountBuilder);
            Aggregations confirmAggregation = confirmWarningNumWrapper.aggregation();
            ParsedValueCount confirmWarningIdCount = confirmAggregation.get("idCount");
            long confirmWarningIdTotal = confirmWarningIdCount.getValue();
            res.setConfirmWaring((int) confirmWarningIdTotal);
        }

        if (status == null || FactoryOrderStatusEnum.SHIPMENTS.getStatus() == status) {
            EsChainQueryWrapper<FactoryOrderEsPO> shipWarningNumWrapper = this.shipWarningNumWrapper(factoryId, taskId, now);
            shipWarningNumWrapper.addAggregationBuilder(idCountBuilder);
            Aggregations shipAggregation = shipWarningNumWrapper.aggregation();
            ParsedValueCount shipWarningIdCount = shipAggregation.get("idCount");
            long shipWarningIdTotal = shipWarningIdCount.getValue();
            res.setShipWaring((int) shipWarningIdTotal);
        }

        EsChainQueryWrapper<FactoryOrderEsPO> willOverTimeWarningNumWrapper = this.willOverWarningNumWrapper(factoryId, status, now, now + 86400000L, taskId);
        willOverTimeWarningNumWrapper.addAggregationBuilder(idCountBuilder);
        Aggregations willOverTimeWarningAggregation = willOverTimeWarningNumWrapper.aggregation();
        ParsedValueCount wilLOverTimeWarningCount = willOverTimeWarningAggregation.get("idCount");
        long wilLOverTimeWarningTotal = wilLOverTimeWarningCount.getValue();
        res.setWillOverTimeWaring((int) wilLOverTimeWarningTotal);

        EsChainQueryWrapper<FactoryOrderEsPO> outDatgNumWrapper = this.outDateNumWrapper(factoryId, status, now, taskId);
        outDatgNumWrapper.addAggregationBuilder(idCountBuilder);
        Aggregations outDatgNumAggregation = outDatgNumWrapper.aggregation();
        ParsedValueCount outDetaCount = outDatgNumAggregation.get("idCount");
        long outDetaTotal = outDetaCount.getValue();
        res.setOverdue((int) outDetaTotal);
        return res;
    }

    private EsChainQueryWrapper<FactoryOrderEsPO> outDateNumWrapper(Long factoryId, Integer status, Long now, Long taskId) {
        EsChainQueryWrapper<FactoryOrderEsPO> esQueryParam = this.factoryOrderEsService.esChainQueryWrapper().must();
        esQueryParam
                .lt(FactoryOrderEsPO::getOutDate, now)
                .term(FactoryOrderEsPO::getFactoryId, factoryId)
                .term(FactoryOrderEsPO::getIsManuscriptOver, MANUSCRIPTE_OVER_YES)
                .term(NumberUtils.greaterZero(status), FactoryOrderEsPO::getStatus, status)
                .terms(FactoryOrderEsPO::getStatus,
                        Arrays.asList(FactoryOrderStatusEnum.UNCONFIRMED.getStatus(),
                                FactoryOrderStatusEnum.ACCOMPLISH_PRODUCTION.getStatus(),
                                FactoryOrderStatusEnum.SHIPMENTS.getStatus(),
                                FactoryOrderStatusEnum.RECEIVE_THINGS.getStatus(),
                                FactoryOrderStatusEnum.PASS_GC.getStatus()))
                .term(Objects.nonNull(taskId), FactoryOrderEsPO::getTaskId, taskId);
        return esQueryParam;
    }

    private EsChainQueryWrapper<FactoryOrderEsPO> willOverWarningNumWrapper(Long factoryId, Integer status, Long startTime, Long endTime, Long taskId) {
        EsChainQueryWrapper<FactoryOrderEsPO> esQueryParam = this.factoryOrderEsService.esChainQueryWrapper().must();
        esQueryParam.term(FactoryOrderEsPO::getFactoryId, factoryId)
                .term(FactoryOrderEsPO::getIsManuscriptOver, MANUSCRIPTE_OVER_YES)
                .term(NumberUtils.greaterZero(status), FactoryOrderEsPO::getStatus, status)
                .terms(FactoryOrderEsPO::getStatus,
                        Arrays.asList(FactoryOrderStatusEnum.UNCONFIRMED.getStatus(),
                                FactoryOrderStatusEnum.ACCOMPLISH_PRODUCTION.getStatus(),
                                FactoryOrderStatusEnum.SHIPMENTS.getStatus()))
                .and(willOverTime ->
                        willOverTime.should()
                                .or(outPayment ->
                                        outPayment.gt(FactoryOrderEsPO::getOutPaymentDate, startTime)
                                                .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.UNCONFIRMED.getStatus()))
                                .or(outExpressDate -> outExpressDate.gt(FactoryOrderEsPO::getOutExpressDate, startTime)
                                        .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.SHIPMENTS.getStatus()))
                                .or(accomplishProduction -> accomplishProduction
                                        .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.ACCOMPLISH_PRODUCTION.getStatus())))
                .lt(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.ACCOMPLISH.getStatus())
                .lt(FactoryOrderEsPO::getOutDate, endTime)
                .gt(FactoryOrderEsPO::getOutDate, startTime);
        return esQueryParam;
    }

    private EsChainQueryWrapper<FactoryOrderEsPO> confirmWarningNumWrapper(Long factoryId, Long taskId, Long now) {
        EsChainQueryWrapper<FactoryOrderEsPO> esQueryParam = this.factoryOrderEsService.esChainQueryWrapper().must();
        esQueryParam.term(FactoryOrderEsPO::getFactoryId, factoryId)
                .term(FactoryOrderEsPO::getIsManuscriptOver, MANUSCRIPTE_OVER_YES)
                .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.UNCONFIRMED.getStatus())
                .term(Objects.nonNull(taskId), FactoryOrderEsPO::getTaskId, taskId)
                .lt(FactoryOrderEsPO::getOutPaymentDate, now)
                .gt(FactoryOrderEsPO::getOutCycleDate, now);
        return esQueryParam;
    }

    private EsChainQueryWrapper<FactoryOrderEsPO> shipWarningNumWrapper(Long factoryId, Long taskId, Long now) {
        EsChainQueryWrapper<FactoryOrderEsPO> esQueryParam = this.factoryOrderEsService.esChainQueryWrapper().must();
        esQueryParam.term(FactoryOrderEsPO::getFactoryId, factoryId)
                .term(FactoryOrderEsPO::getIsManuscriptOver, MANUSCRIPTE_OVER_YES)
                .term(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.SHIPMENTS.getStatus())
                .term(Objects.nonNull(taskId), FactoryOrderEsPO::getTaskId, taskId)
                .lt(FactoryOrderEsPO::getOutExpressDate, now);
        return esQueryParam;
    }

    private EsChainQueryWrapper<FactoryOrderEsPO> countWarningNumWrapper(Long factoryId, Integer status, Long taskId) {
        EsChainQueryWrapper<FactoryOrderEsPO> esQueryParam = this.factoryOrderEsService.esChainQueryWrapper().must();
        esQueryParam.term(FactoryOrderEsPO::getFactoryId, factoryId)
                .term(FactoryOrderEsPO::getIsManuscriptOver, MANUSCRIPTE_OVER_YES)
                .term(NumberUtils.greaterZero(status), FactoryOrderEsPO::getStatus, status)
                .term(Objects.nonNull(taskId), FactoryOrderEsPO::getTaskId, taskId)
                .lt(FactoryOrderEsPO::getStatus, FactoryOrderStatusEnum.ACCOMPLISH.status)
                .and(a -> a.should()
                        .or(expressType -> expressType.term(FactoryOrderEsPO::getOrderExpressType, CountryExpressInfoNewConstant.YES_EXPRESS_TYPE))
                        .or(beResendForLose -> beResendForLose.term(FactoryOrderEsPO::getBeSendForLose, BasePoConstant.YES))
                        // earlyWarningTypeId = 10 人工加急
                        .or(orderEarlyWarning -> orderEarlyWarning.terms(FactoryOrderEsPO::getOrderEarlyWarningIds, Collections.singletonList(10L))));
        return esQueryParam;
    }

    /**
     * wildcard模糊搜索 value需要"*" 通配符
     */
    private String addWildcardPredixAndSuffix(String s) {

        return "*" + s + "*";
    }
}
