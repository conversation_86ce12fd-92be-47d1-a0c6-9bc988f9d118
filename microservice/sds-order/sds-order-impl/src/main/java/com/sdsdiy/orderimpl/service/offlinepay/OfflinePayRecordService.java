package com.sdsdiy.orderimpl.service.offlinepay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.entity.dto.PageResult;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.IdGenerator;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.orderapi.constant.AdminPrepaidConstant;
import com.sdsdiy.orderapi.constant.OfflinePayRecordConstant;
import com.sdsdiy.orderapi.constant.event.message.OfflinePayResetMessage;
import com.sdsdiy.orderapi.dto.offlinepay.*;
import com.sdsdiy.orderimpl.bo.OfflinePayRecordGen;
import com.sdsdiy.orderimpl.entity.po.*;
import com.sdsdiy.orderimpl.feign.*;
import com.sdsdiy.orderimpl.feign.payment.TenantDistributionWalletFeign;
import com.sdsdiy.orderimpl.feign.payment.TransactionFeign;
import com.sdsdiy.orderimpl.feign.user.MerchantUserAccountFeign;
import com.sdsdiy.orderimpl.feign.user.TenantSysUserFeign;
import com.sdsdiy.orderimpl.manager.*;
import com.sdsdiy.orderimpl.service.AdminPrepaidService;
import com.sdsdiy.orderimpl.service.OrderService;
import com.sdsdiy.paymentapi.constant.PaymentMethodEnum;
import com.sdsdiy.paymentapi.constant.PaymentStatusEnum;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import com.sdsdiy.paymentapi.dto.TenantWalletDto;
import com.sdsdiy.paymentapi.param.TenantDistributionQueryParam;
import com.sdsdiy.userapi.constant.EnumNotificationTitle;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.constant.MsgModule;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.MerchantSimpleDto;
import com.sdsdiy.userapi.dto.NotificationDTO;
import com.sdsdiy.userapi.dto.base.MerchantUserAccountRespDto;
import com.sdsdiy.userapi.dto.merchant.MerchantSysUserSimpleDto;
import com.sdsdiy.userapi.dto.tenant.TenantSysUserDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.common.base.constant.MybatisPlusConstant.LIMIT_ONE;
import static com.sdsdiy.orderapi.constant.OfflinePayRecordConstant.StatusEnum.UN_BILLED;
import static com.sdsdiy.orderapi.constant.OfflinePayRecordConstant.StatusEnum.UN_PAID;
import static com.sdsdiy.orderapi.constant.OfflinePayRecordConstant.TradeTypeEnum.*;


/**
 * 线下付款记录表(OfflinePayRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-24 17:43:46
 */
@Slf4j
@Service
@DS("master")
@RequiredArgsConstructor(onConstructor_ = {@Autowired, @Lazy})
public class OfflinePayRecordService {

    private final OfflinePayRecordMapperManager offlinePayRecordMapperManager;
    private final AdminPrepaidPaymentHistoryMapperManager adminPrepaidPaymentHistoryMapperManager;
    private final OfflinePayRecordPaymentHandleService offlinePayRecordPaymentHandleService;
    private final OfflinePayRecordOrderMapperManager offlinePayRecordOrderMapperManager;
    private final OfflinePayAmountDetailMapperManager offlinePayAmountDetailMapperManager;

    private final OrderService orderService;

    private final PaymentFeign paymentFeign;
    private final MerchantFeign merchantFeign;
    private final TenantWalletFeign tenantWalletFeign;
    private final NotificationFeign notificationFeign;
    private final TenantSysUserFeign tenantSysUserFeign;
    private final MerchantSysUserFeign merchantSysUserFeign;
    private final MerchantUserAccountFeign merchantUserAccountFeign;
    private final TenantDistributionWalletFeign tenantDistributionWalletFeign;
    private final TransactionFeign transactionFeign;

    private final RocketMQTemplate rocketMQTemplate;
    @Resource
    @Lazy
    private OfflinePayRecordService offlinePayRecordService;
    @Resource
    @Lazy
    private OfflinePayPaymentMapperManager offlinePayPaymentMapperManager;
    @Resource
    @Lazy
    private AdminPrepaidService adminPrepaidService;

    public PageResult<OfflinePayRecordPageResp> page(OfflinePayRecordPageParam param) {
        boolean isContinue = this.formatParamByKeyword(param);
        if (!isContinue) {
            return new PageResult<>();
        }
        Page<OfflinePayRecord> page = this.pageWrapper(param).page(new Page<>(param.getPage(), param.getSize()));
        List<OfflinePayRecordPageResp> offlinePayRecords = RelationsBinder.convertAndBind(page.getRecords(), OfflinePayRecordPageResp.class);
        this.formatPageResp(offlinePayRecords, param);
        PageResult<OfflinePayRecordPageResp> pageResult = new PageResult<>();
        pageResult.setPage(page.getPages());
        pageResult.setSize(page.getSize());
        pageResult.setTotal(page.getTotal());
        pageResult.setRecords(offlinePayRecords);
        return pageResult;
    }

    public List<OfflinePayRecordExportResp> exportList(OfflinePayRecordPageParam param) {
        boolean isContinue = this.formatParamByKeyword(param);
        if (!isContinue) {
            return Lists.newArrayList();
        }
        List<OfflinePayRecord> offlinePayRecords = this.pageWrapper(param).list();
        List<OfflinePayRecordPageResp> offlinePayRecordRespList = RelationsBinder.convertAndBind(offlinePayRecords, OfflinePayRecordPageResp.class);
        this.formatPageResp(offlinePayRecordRespList, param);
        return BeanUtil.copyToList(offlinePayRecordRespList, OfflinePayRecordExportResp.class);
    }

    private LambdaQueryChainWrapper<OfflinePayRecord> pageWrapper(OfflinePayRecordPageParam param) {
        LambdaQueryChainWrapper<OfflinePayRecord> wrapper = offlinePayRecordMapperManager.lambdaQuery("", OfflinePayRecordPageResp.class);
        if (!StringUtils.isEmpty(param.getKeyword())) {
            wrapper.and(childWrapper -> childWrapper.in(CollectionUtil.isNotEmpty(param.getMerchantIds()), OfflinePayRecord::getMerchantId, param.getMerchantIds())
                    .or().in(CollectionUtil.isNotEmpty(param.getIds()), OfflinePayRecord::getId, param.getIds()));
        }
        if (param.getTenantId() != null) {
            if (TenantCommonConstant.isSdsdiy(param.getTenantId())) {
                wrapper.eq(OfflinePayRecord::getTenantId, param.getTenantId());
            } else {
                //判断租户是否开启线上收款 开启可以到商户归属的，未开启则展示数据归属
                TenantWalletDto tenantWalletDto = tenantWalletFeign.get(param.getTenantId());
                if (YES.equals(tenantWalletDto.getOpenOnlinePay())) {
                    wrapper.eq(OfflinePayRecord::getMerchantTenantId, param.getTenantId());
                } else {
                    wrapper.eq(OfflinePayRecord::getTenantId, param.getTenantId());
                }
//                wrapper.and(child -> child.eq(OfflinePayRecord::getTenantId, param.getTenantId()).or(childTwo -> childTwo.eq(OfflinePayRecord::getMerchantTenantId, param.getTenantId())
//                        .in(OfflinePayRecord::getTradeFlow, Lists.newArrayList(EnumOrderPayType.TENANT.getValue(), EnumOrderPayType.TENANT_SAAS.getValue()))));
            }
        }
        List<String> tradeTypes = new ArrayList<>();
        if (StrUtil.isNotBlank(param.getTradeType())) {
            tradeTypes.add(param.getTradeType());
            if (ALIEXPRESS_JIT.equalsCode(param.getTradeType())) {
                tradeTypes.add(TEMU_FULLY.getCode());
            }
        }

        return wrapper
                .eq(!StringUtils.isEmpty(param.getPaymentMethod()), OfflinePayRecord::getPaymentMethod, param.getPaymentMethod())
                .in(!tradeTypes.isEmpty(), OfflinePayRecord::getTradeType, tradeTypes)
                .eq(!StringUtils.isEmpty(param.getStatus()), OfflinePayRecord::getStatus, param.getStatus())
                .in(OfflinePayRecord::getStatus, OfflinePayRecordConstant.StatusEnum.showStatusList())
                .in(CollectionUtil.isNotEmpty(param.getBelongUserIds()), OfflinePayRecord::getBelongUserId, param.getBelongUserIds())
                .ge(param.getStartDate() != null, OfflinePayRecord::getCreateTime, param.getStartDate())
                .lt(param.getEndDate() != null, OfflinePayRecord::getCreateTime, param.getEndDate())
                .eq(param.getMerchantId() != null, OfflinePayRecord::getMerchantId, param.getMerchantId())
                .orderByDesc(OfflinePayRecord::getId);
    }

    public OfflinePayRecordPageResp one(Long id, String fieldsName) {
        OfflinePayRecord one = offlinePayRecordMapperManager.lambdaQuery(fieldsName, OfflinePayRecordPageResp.class)
                .eq(OfflinePayRecord::getId, id)
                .in(OfflinePayRecord::getStatus, OfflinePayRecordConstant.StatusEnum.showStatusList())
                .one();
        return RelationsBinder.convertAndBind(one, OfflinePayRecordPageResp.class);
    }

    public Long autoCreateOfflinePayRecode(AutoCreateOfflinePayRecodeParam param) {
        Assert.validateBool(OfflinePayRecordConstant.TradeTypeEnum.isAutoCreateEnable(param.getTradeType()), "类型异常！");
        MerchantRespDto merchant = merchantFeign.getMerchantById(param.getMerchantId());
        OfflinePayRecord offlinePayRecord = new OfflinePayRecord();
        offlinePayRecord.setId(IdGenerator.nextId());
        offlinePayRecord.setMerchantId(param.getMerchantId());
        offlinePayRecord.setMerchantTenantId(merchant.getTenantId());
        offlinePayRecord.setTradeType(param.getTradeType());
        offlinePayRecord.setAmount(param.getAmount());
        offlinePayRecord.setDetail(param.getDetail());
        offlinePayRecord.setCreateUid(param.getCreateUid());
        offlinePayRecord.setUpdateUid(param.getCreateUid());
        offlinePayRecord.setOrigin(OfflinePayRecordConstant.SourceEnum.AUTO.getCode());

        AdminPrepaidPaymentHistory prepaidPaymentHistory = null;
        List<OfflinePayAmountDetail> amountDetails = Lists.newArrayList();
        ArrayList<OfflinePayRecordOrder> offlinePayRecordOrders = Lists.newArrayList();

        if (ADMIN_PREPAY_SHIPPING.getName().equals(param.getTradeType())) {
            Assert.validateNull(param.getAdminPrepaidId(), "寄付id必填");
            offlinePayRecord.setTenantId(TenantCommonConstant.SDSDIY_TENANT_ID);
            offlinePayRecord.setStatus(UN_BILLED.name());
            prepaidPaymentHistory = new AdminPrepaidPaymentHistory();
            prepaidPaymentHistory.setAdminPrepaidId(param.getAdminPrepaidId());
            prepaidPaymentHistory.setOfflinePayRecordId(offlinePayRecord.getId());
            prepaidPaymentHistory.setStatus(UN_BILLED.name());
            prepaidPaymentHistory.setPrepaidCost(new BigDecimal("0"));
        } else if (ALIEXPRESS_JIT.getName().equals(param.getTradeType()) || TEMU_FULLY.getName().equals(param.getTradeType())) {
            Assert.validateNull(param.getTenantId(), "jit/发仓费用，租户ID必填");
            offlinePayRecord.setTenantId(param.getTenantId());
            offlinePayRecord.setStatus(UN_PAID.name());
            if (!TenantCommonConstant.isSdsdiy(param.getTenantId())) {
                Boolean onlineOpen = tenantWalletFeign.isOnlineOpen(param.getTenantId());
                if (!onlineOpen) {
                    offlinePayRecord.setIsOrderOffline(YES);
                    offlinePayRecord.setPaymentMethod(PaymentMethodEnum.OFFLINE.getCode());
                }
            }
        } else if (FINISHED_DIFF.getName().equals(param.getTradeType())) {
            Assert.validateNull(param.getTradeFlow(), "补款，交易流向必填");
            Assert.validateNull(param.getBelongUserId(), "补款，归属用户id必填");
            Assert.validateNull(param.getIsOrderOffline(), "补款，是否线下付款必填");
            Assert.validateEmpty(param.getAmountDetails(), "补款，资金流向必填");
            Assert.validateEmpty(param.getOrderNos(), "补款，订单明细必填");
            Assert.validateNull(param.getTenantId(), "补款，补款对应必填");
            offlinePayRecord.setTenantId(param.getTenantId());
            if (YES.equals(param.getIsOrderOffline())) {
                offlinePayRecord.setPaymentMethod(PaymentMethodEnum.OFFLINE.getCode());
            }
            offlinePayRecord.setStatus(UN_PAID.name());
            offlinePayRecord.setBelongUserId(param.getBelongUserId());
            offlinePayRecord.setIsOrderOffline(param.getIsOrderOffline());
            amountDetails = BeanUtil.copyToList(param.getAmountDetails(), OfflinePayAmountDetail.class);
            amountDetails.forEach(a -> a.setOfflinePayRecordId(offlinePayRecord.getId()));
        } else {
            throw new BusinessException("暂不支持其他收款类型自动创建");
        }
        if (CollectionUtil.isNotEmpty(param.getOrderNos())) {
            param.getOrderNos().forEach(orderNo -> {
                OfflinePayRecordOrder offlinePayRecordOrder = new OfflinePayRecordOrder();
                offlinePayRecordOrder.setOfflinePayRecordId(offlinePayRecord.getId());
                offlinePayRecordOrder.setOrderNo(orderNo.getOrderNo());
                offlinePayRecordOrder.setPrice(param.getAmount());
                offlinePayRecordOrders.add(offlinePayRecordOrder);
            });
        }
        offlinePayRecordService.saveRecord(offlinePayRecord, offlinePayRecordOrders, amountDetails, prepaidPaymentHistory);
        rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.TOP_EVENT_OFFLINE_PAY_RESET_SUMMARY, new OfflinePayResetMessage(param.getMerchantId()));
        return offlinePayRecord.getId();
    }

    public void updateAdminPrepaidAmount(Map<Long, BigDecimal> adminPrepaidIdKeyAmountValueMap) {
        if (CollectionUtil.isEmpty(adminPrepaidIdKeyAmountValueMap)) {
            return;
        }
        Map<Long, Long> adminPrepaidIdKeyRecordIdValue = adminPrepaidPaymentHistoryMapperManager.lambdaQuery()
                .in(AdminPrepaidPaymentHistory::getAdminPrepaidId, adminPrepaidIdKeyAmountValueMap.keySet())
                .in(AdminPrepaidPaymentHistory::getStatus, Lists.newArrayList(UN_PAID.name(), UN_BILLED.name()))
                .list().stream()
                .collect(Collectors.toMap(AdminPrepaidPaymentHistory::getAdminPrepaidId, AdminPrepaidPaymentHistory::getOfflinePayRecordId, (a, b) -> b));

        ArrayList<OfflinePayRecord> updateRecords = Lists.newArrayList();
        adminPrepaidIdKeyAmountValueMap.forEach((adminPrepaidId, amount) -> {
            Long offlinePayRecordId = adminPrepaidIdKeyRecordIdValue.get(adminPrepaidId);
            Assert.validateNull(offlinePayRecordId, "支付状态非修改状态,请刷新后重试！");
            OfflinePayRecord offlinePayRecord = new OfflinePayRecord();
            offlinePayRecord.setId(offlinePayRecordId);
            offlinePayRecord.setAmount(amount);
            offlinePayRecord.setStatus(UN_PAID.name());
            updateRecords.add(offlinePayRecord);
        });
        if (CollectionUtil.isEmpty(updateRecords)) {
            return;
        }
        List<Long> recordIds = updateRecords.stream().map(OfflinePayRecord::getId).collect(Collectors.toList());
        offlinePayRecordMapperManager.updateBatchById(updateRecords);
        adminPrepaidPaymentHistoryMapperManager.lambdaUpdate()
                .in(AdminPrepaidPaymentHistory::getOfflinePayRecordId, recordIds)
                .in(AdminPrepaidPaymentHistory::getStatus, Lists.newArrayList(UN_PAID.name(), UN_BILLED.name()))
                .set(AdminPrepaidPaymentHistory::getStatus, UN_PAID.name())
                .update();

        Set<Long> merchantIds = offlinePayRecordMapperManager.listByIds(recordIds).stream().map(OfflinePayRecord::getMerchantId).collect(Collectors.toSet());
        for (Long merchantId : merchantIds) {
            rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.TOP_EVENT_OFFLINE_PAY_RESET_SUMMARY, new OfflinePayResetMessage(merchantId));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveRecord(OfflinePayRecord offlinePayRecord, ArrayList<OfflinePayRecordOrder> offlinePayRecordOrders, List<OfflinePayAmountDetail> amountDetails, AdminPrepaidPaymentHistory prepaidPaymentHistory) {
        offlinePayRecordMapperManager.save(offlinePayRecord);
        if (prepaidPaymentHistory != null) {
            adminPrepaidPaymentHistoryMapperManager.save(prepaidPaymentHistory);
        }
        if (CollectionUtil.isNotEmpty(amountDetails)) {
            offlinePayAmountDetailMapperManager.saveBatch(amountDetails);
        }
        if (CollectionUtil.isNotEmpty(offlinePayRecordOrders)) {
            offlinePayRecordOrderMapperManager.saveBatch(offlinePayRecordOrders);
        }
        this.createSendMessage(offlinePayRecord.getMerchantId(), offlinePayRecord.getId(), offlinePayRecord.getTradeType());
    }

    public void offlineCheckParam(OfflinePayParam param) {
        ArrayList<String> purposeTypes = Lists.newArrayList(FINISHED_DIFF.getName(), CHANGE_WAYBILL.getName(), CAL_VOLUME.getName(), CUSTOMS_DUTY.getName(), OTHER.getName());
        Assert.validateBool(purposeTypes.contains(param.getTradeType()), "交易用途错误，请重新填写");
        ArrayList<String> orderNoCheckTypes = Lists.newArrayList(FINISHED_DIFF.getName(), CHANGE_WAYBILL.getName(), CAL_VOLUME.getName(), CUSTOMS_DUTY.getName());
        Assert.validateTrue(orderNoCheckTypes.contains(param.getTradeType()) && CollectionUtil.isEmpty(param.getOrderNos()), "订单信息不能为空！");
        if (CollectionUtil.isNotEmpty(param.getOrderNos())) {
            //订单匹配
            List<String> orderNos = param.getOrderNos().stream().map(OfflinePayRecordOrderParam::getOrderNo).distinct().collect(Collectors.toList());
            Assert.validateTrue(param.getOrderNos().size() != orderNos.size(), "存在重复订单号,请核对后重试！");
            List<Order> orders = orderService.getOrderByOrderNos(orderNos);
            List<String> orderNosData = orders.stream().map(Order::getNo).collect(Collectors.toList());
            orderNos.removeAll(orderNosData);
            Assert.validateTrue(CollectionUtil.isNotEmpty(orderNos), "以下订单未查询到订单信息：" + orderNos.toString());
            //商户匹配
            List<String> noMatchMerchantOrderNo = orders.stream().filter(o -> !o.getMerchantId().equals(param.getMerchantId())).map(Order::getNo).collect(Collectors.toList());
            Assert.validateTrue(CollectionUtil.isNotEmpty(noMatchMerchantOrderNo), "非该商户订单：" + noMatchMerchantOrderNo.toString());
            BigDecimal totalAmount = param.getOrderNos().stream().map(OfflinePayRecordOrderParam::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            Assert.validateTrue(param.getAmount().compareTo(totalAmount) != 0, "订单金额之和与总金额不等，请核对后重试！");
        }
        Optional<PaymentMethodEnum> optional = PaymentMethodEnum.getByCode(param.getPaymentMethod());
        Assert.validateBool(optional.isPresent(), "支付用途错误，请重新填写");
    }

    private boolean formatParamByKeyword(OfflinePayRecordPageParam param) {
        if (!StringUtils.isEmpty(param.getMerchantNo())) {
            Long merchantIdByNo = merchantFeign.getMerchantIdByNo(param.getMerchantNo());
            if (merchantIdByNo == null) {
                return false;
            }
            param.setMerchantId(merchantIdByNo);
        }
        if (StringUtils.isEmpty(param.getKeyword())) {
            return true;
        }
        List<Long> merchantIds = Lists.newArrayList();
        if (OfflinePayRecordConstant.RequestSourceEnum.TENANT.name().equals(param.getRequestSource())) {
            merchantIds = merchantFeign.getMerchantIdByNameOrNo(param.getKeyword());
        }
        ArrayList<Long> ids = Lists.newArrayList();
        if (OfflinePayRecordConstant.RequestSourceEnum.MERCHANT.name().equals(param.getRequestSource()) && param.getMerchantId() != null) {
            List<Long> idsALL = offlinePayRecordMapperManager.lambdaQuery()
                    .eq(OfflinePayRecord::getMerchantId, param.getMerchantId())
                    .like(OfflinePayRecord::getDetail, param.getKeyword())
                    .select(OfflinePayRecord::getId).list().stream()
                    .map(OfflinePayRecord::getId)
                    .collect(Collectors.toList());
            ids.addAll(idsALL);
        }
        List<String> keywordSplit = com.sdsdiy.core.base.util.StringUtils.stringToStringList(param.getKeyword());
        if (CollectionUtil.isNotEmpty(keywordSplit)) {
            for (String key : keywordSplit) {
                if (com.sdsdiy.core.base.util.StringUtils.isNumber(key)) {
                    ids.add(Long.valueOf(key));
                }
            }
        }
        List<OfflinePayRecordOrder> offlinePayRecordOrders = offlinePayRecordOrderMapperManager.lambdaQuery().eq(OfflinePayRecordOrder::getOrderNo, param.getKeyword()).list();
        if (CollectionUtil.isNotEmpty(offlinePayRecordOrders)) {
            ids.addAll(offlinePayRecordOrders.stream().map(OfflinePayRecordOrder::getOfflinePayRecordId).collect(Collectors.toList()));
        }
        if (CollectionUtil.isEmpty(ids) && CollectionUtil.isEmpty(merchantIds)) {
            return false;
        }
        param.setMerchantIds(merchantIds);
        param.setIds(ids);
        return true;
    }

    private void formatPageResp(List<OfflinePayRecordPageResp> offlinePayRecords, OfflinePayRecordPageParam reqParam) {
        if (CollectionUtil.isEmpty(offlinePayRecords)) {
            return;
        }
        Set<Long> merchantIds = offlinePayRecords.stream().map(OfflinePayRecordPageResp::getMerchantId).collect(Collectors.toSet());
        Set<Long> userIds = offlinePayRecords.stream().map(OfflinePayRecordPageResp::getCreateUid).collect(Collectors.toSet());
        Set<Long> belongUserId = offlinePayRecords.stream().map(OfflinePayRecordPageResp::getBelongUserId).filter(userId -> userId != 0).collect(Collectors.toSet());
        Set<Long> supplierTenantIds = offlinePayRecords.stream().map(OfflinePayRecordPageResp::getTenantId).collect(Collectors.toSet());
        Set<Long> distMerchantTenantIds = offlinePayRecords.stream().map(OfflinePayRecordPageResp::getMerchantTenantId).collect(Collectors.toSet());
        Map<String, TenantDistributorWalletDto> supTenantIdAndDistKeyWalletMap = this.getSupTenantIdWalletMap(distMerchantTenantIds, supplierTenantIds);
        Map<Long, String> merchantUserIdKeyTypeMap = Maps.newHashMap();
        Map<Long, MerchantSysUserSimpleDto> merchantUserSimpleMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(belongUserId)) {
            merchantUserIdKeyTypeMap = merchantUserAccountFeign.getList(Lists.newArrayList(belongUserId)).stream().collect(Collectors.toMap(MerchantUserAccountRespDto::getMerchantUserId, MerchantUserAccountRespDto::getBalanceType, (a, b) -> b));
            merchantUserSimpleMap = merchantSysUserFeign.mapMerchantSysUserSimpleDtoByIds(Lists.newArrayList(belongUserId));
        }
        Map<Long, MerchantSimpleDto> merchantSimpleMap = merchantFeign.mapMerchantSimpleDtoByIds(merchantIds);
        List<TenantSysUserDto> tenants = tenantSysUserFeign.findByIds("userName", userIds);
        Map<Long, String> userNameMap = tenants.stream().collect(Collectors.toMap(TenantSysUserDto::getId, TenantSysUserDto::getUserName));

        if (OfflinePayRecordConstant.RequestSourceEnum.MERCHANT.getCode().equals(reqParam.getRequestSource())) {
            MerchantSimpleDto merchantSimpleDto = merchantSimpleMap.get(reqParam.getMerchantId());
            TenantWalletDto tenantWalletDto = tenantWalletFeign.get(merchantSimpleDto.getTenantId());
            offlinePayRecords.forEach(r -> r.setIsOpenOnlinePay(tenantWalletDto == null ? 0 : tenantWalletDto.getOpenOnlinePay()));
        }
        for (OfflinePayRecordPageResp r : offlinePayRecords) {
            String supAndDisTenantIdKey = getSupAndDisTenantIdKey(r.getTenantId(), r.getMerchantTenantId());
            TenantDistributorWalletDto tenantDistributorWalletDto = supTenantIdAndDistKeyWalletMap.get(supAndDisTenantIdKey);
            r.setMerchantName(Optional.of(merchantSimpleMap.get(r.getMerchantId())).orElse(new MerchantSimpleDto()).getName());
            r.setMerchantNo(Optional.of(merchantSimpleMap.get(r.getMerchantId())).orElse(new MerchantSimpleDto()).getMerchantNo());
            r.setCreateName(userNameMap.get(r.getCreateUid()));
            r.setUserBalanceType(merchantUserIdKeyTypeMap.getOrDefault(r.getBelongUserId(), MerchantUserAccountConstant.IN_COMMON));
            r.setBelongUserName(merchantUserSimpleMap.getOrDefault(r.getBelongUserId(), new MerchantSysUserSimpleDto()).getUsername());
            r.setTradeFlow(OfflinePayRecordGen.getTradeFlow(r.getMerchantTenantId(), r.getTenantId(), tenantDistributorWalletDto));
        }
    }

    public void overdueTimePayHandler() {
        List<OfflinePayRecord> overdueTimeRecords = offlinePayRecordMapperManager.lambdaQuery()
                .in(OfflinePayRecord::getStatus, Lists.newArrayList(OfflinePayRecordConstant.StatusEnum.DURING.name(), OfflinePayRecordConstant.StatusEnum.FAILED.name()))
                .in(OfflinePayRecord::getPaymentMethod, Lists.newArrayList(PaymentMethodEnum.ALI_PAY.getCode(), PaymentMethodEnum.LAKALA.getCode()))
                .le(OfflinePayRecord::getPaymentOverdueTime, System.currentTimeMillis())
                .list();
        if (CollectionUtil.isEmpty(overdueTimeRecords)) {
            return;
        }
        List<Long> overdueTimeRecordIds = overdueTimeRecords.stream().map(OfflinePayRecord::getId).collect(Collectors.toList());
        //寄付中超时关闭处理
        List<AdminPrepaidPaymentHistory> prepaidPaymentHistories = adminPrepaidPaymentHistoryMapperManager.lambdaQuery()
                .in(AdminPrepaidPaymentHistory::getOfflinePayRecordId, overdueTimeRecordIds)
                .eq(AdminPrepaidPaymentHistory::getStatus, OfflinePayRecordConstant.StatusEnum.DURING.name())
                .list();
        List<Long> prepaidPaymentHistoryIds = Lists.newArrayList();
        Set<Long> adminPrepaidIds = Sets.newHashSet();
        if (CollectionUtil.isNotEmpty(prepaidPaymentHistories)) {
            prepaidPaymentHistoryIds = prepaidPaymentHistories.stream().map(AdminPrepaidPaymentHistory::getId).collect(Collectors.toList());
            adminPrepaidIds = prepaidPaymentHistories.stream().map(AdminPrepaidPaymentHistory::getAdminPrepaidId).collect(Collectors.toSet());
            List<Long> haveOtherRecordHistoryAdminPrepaidIds = adminPrepaidPaymentHistoryMapperManager.lambdaQuery()
                    .notIn(AdminPrepaidPaymentHistory::getOfflinePayRecordId, overdueTimeRecordIds)
                    .in(AdminPrepaidPaymentHistory::getAdminPrepaidId, adminPrepaidIds)
                    .eq(AdminPrepaidPaymentHistory::getStatus, OfflinePayRecordConstant.StatusEnum.DURING.name())
                    .list().stream().map(AdminPrepaidPaymentHistory::getAdminPrepaidId)
                    .collect(Collectors.toList());
            haveOtherRecordHistoryAdminPrepaidIds.forEach(adminPrepaidIds::remove);
        }
        offlinePayRecordService.updateRecordStatusByOverdueTime(overdueTimeRecordIds, prepaidPaymentHistoryIds, adminPrepaidIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRecordStatusByOverdueTime(List<Long> overdueTimeRecordIds, List<Long> prepaidPaymentHistoryIds, Set<Long> adminPrepaidIds) {
        offlinePayRecordMapperManager.lambdaUpdate()
                .set(OfflinePayRecord::getStatus, OfflinePayRecordConstant.StatusEnum.CLOSED.name())
                .set(OfflinePayRecord::getCloseTime, new Date())
                .in(OfflinePayRecord::getId, overdueTimeRecordIds)
                .eq(OfflinePayRecord::getStatus, OfflinePayRecordConstant.StatusEnum.DURING.name())
                .update();
        if (CollectionUtil.isNotEmpty(prepaidPaymentHistoryIds)) {
            adminPrepaidPaymentHistoryMapperManager.lambdaUpdate()
                    .set(AdminPrepaidPaymentHistory::getStatus, OfflinePayRecordConstant.StatusEnum.CLOSED.name())
                    .in(AdminPrepaidPaymentHistory::getId, prepaidPaymentHistoryIds)
                    .eq(AdminPrepaidPaymentHistory::getStatus, OfflinePayRecordConstant.StatusEnum.DURING.name())
                    .update();
        }
        if (CollectionUtil.isNotEmpty(adminPrepaidIds)) {
            adminPrepaidService.lambdaUpdate()
                    .set(AdminPrepaid::getPaymentStatus, AdminPrepaidConstant.PaymentStatusEnum.PENDING_PAYMENT.name())
                    .in(AdminPrepaid::getId, adminPrepaidIds)
                    .eq(AdminPrepaid::getPaymentStatus, AdminPrepaidConstant.PaymentStatusEnum.PAYMENT_OFFLINE.name())
                    .update();
        }
    }

    public PaymentDto refreshPayment(Long paymentId) {
        //刷新支付结果
        PaymentDto paymentDto = transactionFeign.operateTransaction(paymentId);
        if (PaymentStatusEnum.INVALID.getStatus().equals(paymentDto.getStatus())) {
            throw new BusinessException("该二维码已失效，请重新购买");
        }
        if (paymentDto.getMethod().equals(PaymentMethodEnum.ALI_PAY.getCode()) && PaymentStatusEnum.WAIT_PAY.getStatus().equals(paymentDto.getStatus())) {
            return paymentDto;
        }
        if (paymentDto.getMethod().equals(PaymentMethodEnum.LAKALA.getCode()) && PaymentStatusEnum.WAIT_PAY.getStatus().equals(paymentDto.getStatus())) {
            return paymentDto;
        }
        OfflinePayRecord one = offlinePayRecordMapperManager.lambdaQuery()
                .eq(OfflinePayRecord::getPaymentId, paymentId)
                .orderByDesc(OfflinePayRecord::getId)
                .last(LIMIT_ONE)
                .one();
        if (one == null) {
            throw new BusinessException("支付异常!请重试");
        }
        offlinePayRecordPaymentHandleService.pay(paymentId, one.getMerchantId());
        return paymentDto;
    }

    public List<OfflinePayRecordReconciliationResp> reconciliationList(BaseListReqDto req) {
        if (CollectionUtil.isEmpty(req.getIdList())) {
            return Lists.newArrayList();
        }
        List<OfflinePayRecord> offlinePayRecords = offlinePayRecordMapperManager.lambdaQuery()
                .in(OfflinePayRecord::getId, req.getIdList())
                .list();
        if (CollectionUtil.isEmpty(offlinePayRecords)) {
            return Lists.newArrayList();
        }
        return RelationsBinder.convertAndBind(offlinePayRecords, OfflinePayRecordReconciliationResp.class);
    }

    public List<OfflinePayPaymentDetailResp> listByTradeNo(String tradeNo) {
        OfflinePayPayment one = offlinePayPaymentMapperManager.lambdaQuery()
                .eq(OfflinePayPayment::getTradeNo, tradeNo)
                .one();
        if (one == null) {
            return Lists.newArrayList();
        }
        List<OfflinePayRecord> offlinePayRecords = offlinePayRecordMapperManager.lambdaQuery()
                .eq(OfflinePayRecord::getOfflinePayPaymentId, one.getId())
                .list();
        if (CollectionUtil.isEmpty(offlinePayRecords)) {
            return Lists.newArrayList();
        }
        Map<Long, OfflinePayRecord> recordMap = offlinePayRecords.stream().collect(Collectors.toMap(OfflinePayRecord::getId, Function.identity()));
        List<OfflinePayPaymentDetailResp> offlinePayPaymentDetails = RelationsBinder.convertAndBind(offlinePayRecords, OfflinePayPaymentDetailResp.class);
        for (OfflinePayPaymentDetailResp detail : offlinePayPaymentDetails) {
            if (CollectionUtil.isNotEmpty(detail.getTradeDirectionAmountDetails())) {
                continue;
            }
            OfflinePayRecord offlinePayRecord = recordMap.get(detail.getId());
            List<OfflinePayAmountDetail> amountDetails = Lists.newArrayList(OfflinePayRecordGen.generateDefaultAmounts(offlinePayRecord));
            detail.setTradeDirectionAmountDetails(BeanUtil.copyToList(amountDetails, OfflinePayAmountDetailResp.class));
        }
        return offlinePayPaymentDetails;
    }

    public void createSendMessage(Long merchantId, Long offlinePayRecordId, String tradeType) {
        NotificationDTO notificationDTO = new NotificationDTO();
        String content;
        if (FINISHED_DIFF.getName().equals(tradeType)) {
            content = String.format(EnumNotificationTitle.ORDER_END_OFFLINE_PAY.getContent(), offlinePayRecordId);
            notificationDTO.setNotificationTitle(EnumNotificationTitle.ORDER_END_OFFLINE_PAY);
        } else if (CHANGE_WAYBILL.getName().equals(tradeType)) {
            content = String.format(EnumNotificationTitle.CHANGE_WAYBILL_OFFLINE_PAY.getContent(), offlinePayRecordId);
            notificationDTO.setNotificationTitle(EnumNotificationTitle.CHANGE_WAYBILL_OFFLINE_PAY);
        } else if (CAL_VOLUME.getName().equals(tradeType)) {
            content = String.format(EnumNotificationTitle.CAL_VOLUME_OFFLINE_PAY.getContent(), offlinePayRecordId);
            notificationDTO.setNotificationTitle(EnumNotificationTitle.CAL_VOLUME_OFFLINE_PAY);
        } else if (CUSTOMS_DUTY.getName().equals(tradeType)) {
            content = String.format(EnumNotificationTitle.CUSTOMS_DUTY_OFFLINE_PAY.getContent(), offlinePayRecordId);
            notificationDTO.setNotificationTitle(EnumNotificationTitle.CUSTOMS_DUTY_OFFLINE_PAY);
        } else if (OTHER.getName().equals(tradeType)) {
            content = String.format(EnumNotificationTitle.OTHER_OFFLINE_PAY.getContent(), offlinePayRecordId);
            notificationDTO.setNotificationTitle(EnumNotificationTitle.OTHER_OFFLINE_PAY);
        } else {
            return;
        }
        notificationDTO.setMerchantId(merchantId);
        notificationDTO.setContent(content);
        notificationDTO.setMsgModule(MsgModule.SEND_NOTIFICATION);
        this.notificationFeign.saveNotificationDTO(notificationDTO);
    }

    public void adminPrepayShippingInit(AutoCreateOfflinePayRecodeParam param) {
        Assert.validateBool(OfflinePayRecordConstant.TradeTypeEnum.isAutoCreateEnable(param.getTradeType()), "类型异常！");
        Assert.validateNull(param.getAdminPrepaidId(), "寄付id必填");
        Integer count = offlinePayRecordMapperManager.lambdaQuery().eq(OfflinePayRecord::getDetail, param.getDetail()).count();
        if (count > 0) {
            log.info("寄付补款账单初始化重复插入数据:{}", JSONUtil.toJsonStr(param));
            return;
        }
        MerchantRespDto merchant = merchantFeign.getMerchantById(param.getMerchantId());
        OfflinePayRecord offlinePayRecord = new OfflinePayRecord();
        offlinePayRecord.setId(IdGenerator.nextId());
        offlinePayRecord.setMerchantId(param.getMerchantId());
        offlinePayRecord.setMerchantTenantId(merchant.getTenantId());
        offlinePayRecord.setTradeType(param.getTradeType());
        offlinePayRecord.setAmount(param.getAmount());
        offlinePayRecord.setDetail(param.getDetail());
        offlinePayRecord.setOrigin(OfflinePayRecordConstant.SourceEnum.AUTO.getCode());
        offlinePayRecord.setTenantId(TenantCommonConstant.SDSDIY_TENANT_ID);
        offlinePayRecord.setStatus(param.getStatus());

        AdminPrepaidPaymentHistory prepaidPaymentHistory = new AdminPrepaidPaymentHistory();
        prepaidPaymentHistory.setAdminPrepaidId(param.getAdminPrepaidId());
        prepaidPaymentHistory.setOfflinePayRecordId(offlinePayRecord.getId());
        prepaidPaymentHistory.setStatus(param.getStatus());
        prepaidPaymentHistory.setPrepaidCost(param.getAmount());
        offlinePayRecordService.saveRecord(offlinePayRecord, Lists.newArrayList(), Lists.newArrayList(), prepaidPaymentHistory);

    }

    public List<OfflinePayRecordPageResp> listNotFinishRecordsBySupAndDisTenantId(Long supTenantId, Long disTenantId) {
        List<OfflinePayRecord> list = offlinePayRecordMapperManager.lambdaQuery()
                .eq(OfflinePayRecord::getTenantId, supTenantId)
                .eq(OfflinePayRecord::getMerchantTenantId, disTenantId)
                .notIn(OfflinePayRecord::getStatus, OfflinePayRecordConstant.StatusEnum.finishStatusList())
                .list();
        return CollUtil.isEmpty(list) ? Collections.emptyList() : BeanUtil.copyToList(list, OfflinePayRecordPageResp.class);
    }

    private Map<String, TenantDistributorWalletDto> getSupTenantIdWalletMap(Collection<Long> merchantTenantIds, Collection<Long> supplierTenantIds) {
        TenantDistributionQueryParam tenantDistributionQueryParam = new TenantDistributionQueryParam();
        tenantDistributionQueryParam.setDisTenantIds(Lists.newArrayList(merchantTenantIds));
        tenantDistributionQueryParam.setSupTenantId(Lists.newArrayList(supplierTenantIds));
        List<TenantDistributorWalletDto> tenantDistributorWalletDtos = tenantDistributionWalletFeign.queryWallets(tenantDistributionQueryParam);
        return ListUtil.toMap(t -> getSupAndDisTenantIdKey(t.getSupTenantId(), t.getDisTenantId()), tenantDistributorWalletDtos);
    }

    private String getSupAndDisTenantIdKey(Long supTenantId, Long disTenantId) {
        return supTenantId + "_" + disTenantId;
    }

//    public Map<Long, String> recordIdKeyTitleMap(List<OfflinePayRecord> offlinePayRecords,
//                                                 List<OfflinePayRecordOrderResp> orders) {
//        Map<Long, String> idKeyTitleMap = new HashMap<>();
//        for (OfflinePayRecord record : offlinePayRecords) {
//            StringBuilder title = new StringBuilder("补款账期id:" + record.getId());
//            if (OfflinePayRecordConstant.TradeTypeEnum.ALIEXPRESS_JIT.getName().equals(record.getTradeType())) {
//                if (!StrUtil.isBlank(record.getDetail())) {
//                    String replaced = record.getDetail().replaceAll("\n\n", ", ");
//                    title.append(",速卖通JIT订单自寄单号：").append(replaced);
//                    idKeyTitleMap.put(record.getId(), title.toString());
//                    continue;
//                }
//                title.append(",速卖通JIT订单自寄单号：").append(record.getDetail());
//                idKeyTitleMap.put(record.getId(), title.toString());
//                continue;
//            } else if (OfflinePayRecordConstant.TradeTypeEnum.TEMU_FULLY.getName().equals(record.getTradeType())) {
//                if (!StrUtil.isBlank(record.getDetail())) {
//                    String replaced = record.getDetail().replaceAll("\n\n", ", ");
//                    title.append(",temu全托管发仓订单自寄发货单号：").append(replaced);
//                    idKeyTitleMap.put(record.getId(), title.toString());
//                    continue;
//                }
//                title.append(",temu全托管发仓订单自寄发货单号：").append(record.getDetail());
//                idKeyTitleMap.put(record.getId(), title.toString());
//                continue;
//            }
//            if (CollectionUtil.isEmpty(orders)) {
//                idKeyTitleMap.put(record.getId(), title.toString());
//                continue;
//            }
//            List<String> orderNosStr = orders.stream()
//                    .map(OfflinePayRecordOrderResp::getOrderNo)
//                    .collect(Collectors.toList());
//            String ordersStr = CollectionUtil.join(orderNosStr, ",");
//            title.append(",订单号:").append(ordersStr);
//            idKeyTitleMap.put(record.getId(), title.toString());
//        }
//        return idKeyTitleMap;
//    }


}