package com.sdsdiy.orderimpl.service.offlinepay;


import cn.hutool.core.collection.CollectionUtil;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.sdsdiy.orderimpl.bo.OfflinePayRecordGen;
import com.sdsdiy.orderimpl.entity.po.OfflinePayAmountDetail;
import com.sdsdiy.orderimpl.entity.po.OfflinePayRecord;
import com.sdsdiy.orderimpl.manager.OfflinePayAmountDetailMapperManager;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 线下付款金额明细(OfflinePayAmountDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-10 18:35:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OfflinePayAmountDetailService {
    private final OfflinePayAmountDetailMapperManager offlinePayAmountDetailMapperManager;

//    public List<OfflinePayAmountDetail> getOfflinePayAmountDetails(List<OfflinePayRecord> offlinePayRecords) {
//        if (CollectionUtil.isEmpty(offlinePayRecords)) {
//            return Lists.newArrayList();
//        }
//        List<Long> offlinePayRecordIds = offlinePayRecords.stream().map(OfflinePayRecord::getId).collect(Collectors.toList());
//        Map<Long, List<OfflinePayAmountDetail>> offlinePayAmountGroup = offlinePayAmountDetailMapperManager.lambdaQuery()
//                .in(OfflinePayAmountDetail::getOfflinePayRecordId, offlinePayRecordIds)
//                .list()
//                .stream().collect(Collectors.groupingBy(OfflinePayAmountDetail::getOfflinePayRecordId));
//        List<OfflinePayAmountDetail> offlinePayAmountDetails = Lists.newArrayList();
//        for (OfflinePayRecord record : offlinePayRecords) {
//            List<OfflinePayAmountDetail> details = offlinePayAmountGroup.get(record.getId());
//            if (CollectionUtil.isNotEmpty(details)) {
//                offlinePayAmountDetails.addAll(details);
//            } else {
//                offlinePayAmountDetails.addAll(OfflinePayRecordGen.generateDefaultAmounts(record));
//            }
//        }
//        return offlinePayAmountDetails;
//    }


    public List<OfflinePayAmountDetail> getOfflinePayAmountDetails(List<OfflinePayRecord> offlinePayRecords,
                                                                   Map<Long, TenantDistributorWalletDto> supTenantIdWalletMap) {
        if (CollectionUtil.isEmpty(offlinePayRecords)) {
            return Lists.newArrayList();
        }
        Map<Long, String> offlinePayRecordIdKeyTrade = Maps.newHashMap();
        for (OfflinePayRecord offlinePayRecord : offlinePayRecords) {
            String tradeFlow = OfflinePayRecordGen.getTradeFlow(offlinePayRecord.getMerchantTenantId(), offlinePayRecord.getTenantId(), supTenantIdWalletMap);
            offlinePayRecordIdKeyTrade.put(offlinePayRecord.getId(), tradeFlow);
        }
        List<Long> offlinePayRecordIds = offlinePayRecords.stream().map(OfflinePayRecord::getId).collect(Collectors.toList());
        Map<Long, List<OfflinePayAmountDetail>> offlinePayAmountGroup = offlinePayAmountDetailMapperManager.lambdaQuery()
                .in(OfflinePayAmountDetail::getOfflinePayRecordId, offlinePayRecordIds)
                .list().stream()
                .collect(Collectors.groupingBy(OfflinePayAmountDetail::getOfflinePayRecordId));

        List<OfflinePayAmountDetail> offlinePayAmountDetails = Lists.newArrayList();
        for (OfflinePayRecord record : offlinePayRecords) {
            List<OfflinePayAmountDetail> details = offlinePayAmountGroup.get(record.getId());
            String tradeFlow = offlinePayRecordIdKeyTrade.get(record.getId());
            if (CollectionUtil.isEmpty(details)) {
                offlinePayAmountDetails.addAll(OfflinePayRecordGen.generateDefaultAmounts(record, tradeFlow));
                continue;
            }
            offlinePayAmountDetails.addAll(details);
//            List<OfflinePayAmountDetail> errorDetails = details.stream().filter(d -> d.getSourceRole().equals(PaymentRoleEnum.MERCHANT.getCode())
//                    && d.getTargetRole().equals(PaymentRoleEnum.SAAS.getCode())).collect(Collectors.toList());
//            //交易方式为TENANT_SAAS的 资金流向为商户到saas的需要重置
//            if (!TenantCommonConstant.isSdsdiy(record.getMerchantTenantId())
//                    && EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.getValue().equals(tradeFlow)
//                    && CollectionUtil.isNotEmpty(errorDetails)) {
//                offlinePayAmountDetails.addAll(OfflinePayRecordGen.generateDefaultAmounts(record, tradeFlow));
//            } else {
//                offlinePayAmountDetails.addAll(details);
//            }
        }
        return offlinePayAmountDetails;
    }

    /**
     * 重置生成
     */
    public void reSave(List<OfflinePayAmountDetail> offlinePayAmountDetails) {
        if (CollectionUtil.isEmpty(offlinePayAmountDetails)) {
            return;
        }
        List<Long> offlinePayRecordIds = offlinePayAmountDetails.stream().map(OfflinePayAmountDetail::getOfflinePayRecordId).collect(Collectors.toList());
        offlinePayAmountDetailMapperManager.lambdaUpdate()
                .in(OfflinePayAmountDetail::getOfflinePayRecordId, offlinePayRecordIds)
                .remove();
        offlinePayAmountDetailMapperManager.saveBatch(offlinePayAmountDetails);
    }
}
