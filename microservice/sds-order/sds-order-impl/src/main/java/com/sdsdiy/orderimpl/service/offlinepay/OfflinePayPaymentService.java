package com.sdsdiy.orderimpl.service.offlinepay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.redis.lock.annotation.DistributedLock;
import com.sdsdiy.orderapi.constant.EnumOrderPayType;
import com.sdsdiy.orderapi.constant.OfflinePayRecordConstant;
import com.sdsdiy.orderapi.constant.event.message.OfflinePayResetMessage;
import com.sdsdiy.orderapi.dto.adminprepaid.OfflinePayCommitResp;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayBatchDetailParam;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayBatchPayParam;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayDetailResp;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayPaymentResp;
import com.sdsdiy.orderimpl.bo.BatchPaymentBo;
import com.sdsdiy.orderimpl.bo.OfflinePayPaymentGen;
import com.sdsdiy.orderimpl.bo.OfflinePayRecordGen;
import com.sdsdiy.orderimpl.entity.po.*;
import com.sdsdiy.orderimpl.feign.MerchantFeign;
import com.sdsdiy.orderimpl.feign.payment.TenantDistributionWalletFeign;
import com.sdsdiy.orderimpl.feign.user.TenantFeign;
import com.sdsdiy.orderimpl.manager.AdminPrepaidPaymentHistoryMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayPaymentMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayRecordMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayRecordOrderMapperManager;
import com.sdsdiy.paymentapi.constant.PaymentMethodEnum;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import com.sdsdiy.paymentapi.param.TenantDistributionQueryParam;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.tenant.TenantListReq;
import com.sdsdiy.userapi.dto.tenant.TenantRespDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.NO;
import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.common.base.constant.MybatisPlusConstant.LIMIT_ONE;

/**
 * 线下付款(OfflinePayPayment)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-22 14:25:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OfflinePayPaymentService {

    private final OfflinePayPaymentMapperManager offlinePayPaymentMapperManager;
    private final OfflinePayRecordMapperManager offlinePayRecordMapperManager;
    private final OfflinePayRecordOrderMapperManager offlinePayRecordOrderMapperManager;

    private final AdminPrepaidPaymentHistoryMapperManager adminPrepaidPaymentHistoryMapperManager;

    private final OfflinePayRecordPaymentService offlinePayRecordPaymentService;
    private final OfflinePayAmountDetailService offlinePayAmountDetailService;
    private final OfflinePaySaveService offlinePaySaveService;
    private final OfflinePayService offlinePayService;

    private final RocketMQTemplate rocketMQTemplate;
    private final MerchantFeign merchantFeign;
    private final TenantFeign tenantFeign;
    private final TenantDistributionWalletFeign tenantDistributionWalletFeign;

    @DistributedLock(value = "#param.merchantId")
    public OfflinePayCommitResp batchPay(OfflinePayBatchPayParam param) {
        List<OfflinePayRecord> offlinePayRecords = offlinePayRecordMapperManager.listByIds(param.getOfflinePayRecordIds());
        List<OfflinePayRecordOrder> recordOrders = offlinePayRecordOrderMapperManager.lambdaQuery()
                .in(OfflinePayRecordOrder::getOfflinePayRecordId, param.getOfflinePayRecordIds())
                .list();
        Map<Long, List<OfflinePayRecordOrder>> offlinePayRecordMap = recordOrders.stream().collect(Collectors.groupingBy(OfflinePayRecordOrder::getOfflinePayRecordId));
        List<Long> supplierTenantIds = offlinePayRecords.stream().map(OfflinePayRecord::getTenantId).distinct().collect(Collectors.toList());
        Map<Long, TenantDistributorWalletDto> supTenantIdWalletMap = this.getSupTenantIdWalletMap(offlinePayRecords.get(0).getMerchantTenantId(), supplierTenantIds);
        List<OfflinePayAmountDetail> offlinePayAmountDetails = offlinePayAmountDetailService.getOfflinePayAmountDetails(offlinePayRecords, supTenantIdWalletMap);//校验
        BigDecimal totalAmount = offlinePayRecords.stream().map(OfflinePayRecord::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        Assert.validateTrue(param.getAmount().compareTo(totalAmount) != 0, "金额发生变动，请重试！");
        Pair<Long, String> belongUserIdAndBalanceTypePair = offlinePayRecordPaymentService.checkAndGetUserIdAndBalanceType(offlinePayRecords);
        offlinePayRecordPaymentService.offlinePayRecordsCheck(param.getOrigin(), offlinePayRecords, offlinePayAmountDetails, belongUserIdAndBalanceTypePair);
        //付款 余额或者二维码
        BatchPaymentBo batchPaymentBo = offlinePayRecordPaymentService.batchPreviewPayments(param, offlinePayRecords, offlinePayAmountDetails, offlinePayRecordMap, belongUserIdAndBalanceTypePair.getFirst());
        //返回值付款处理
        OfflinePayPayment offlinePayPayment = offlinePayRecordPaymentService.batchPayments(batchPaymentBo);
        rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.TOP_EVENT_OFFLINE_PAY_RESET_SUMMARY, new OfflinePayResetMessage(param.getMerchantId()));
        return OfflinePayPaymentGen.generateOfflinePayCommitResp(offlinePayPayment, offlinePayRecords);
    }


    public OfflinePayDetailResp batchPayDetail(OfflinePayBatchDetailParam param) {
        List<OfflinePayRecord> offlinePayRecords = offlinePayRecordMapperManager.listByIds(param.getIdList());
        Assert.validateTrue(offlinePayRecords.size() > 101, "批量支付数量过多，请少于100条！！");
        Pair<Long, String> belongUserIdAndBalanceTypePair = offlinePayRecordPaymentService.checkAndGetUserIdAndBalanceType(offlinePayRecords);
        //租户的钱包
        List<Long> supplierTenantIds = offlinePayRecords.stream().map(OfflinePayRecord::getTenantId).distinct().collect(Collectors.toList());
        Map<Long, TenantDistributorWalletDto> tenantWalletMap = this.getSupTenantIdWalletMap(offlinePayRecords.get(0).getMerchantTenantId(), supplierTenantIds);
        List<OfflinePayAmountDetail> offlinePayAmountDetails = offlinePayAmountDetailService.getOfflinePayAmountDetails(offlinePayRecords, tenantWalletMap);
        offlinePayRecordPaymentService.offlinePayRecordsCheck(param.getRequestSource(), offlinePayRecords, offlinePayAmountDetails, belongUserIdAndBalanceTypePair);
        //租户信息
        TenantListReq req = new TenantListReq();
        req.setInIds(supplierTenantIds);
        Map<Long, String> tenantIdToNoMap = tenantFeign.listDto(req).stream().collect(Collectors.toMap(TenantRespDto::getId, TenantRespDto::getTenantNo));
        //找出不可支付的租户id
        List<Long> nonPayableTenantIds = this.findNonPayableTenantIds(tenantWalletMap, offlinePayAmountDetails);
        //获取详情信息
        List<OfflinePayDetailResp.OfflinePayDetailDto> offlinePayDetails = this.buildOfflinePayDetails(offlinePayRecords, tenantWalletMap, nonPayableTenantIds, tenantIdToNoMap);

        OfflinePayDetailResp offlinePayDetailResp = new OfflinePayDetailResp();
        List<Long> merchantIds = offlinePayRecords.stream().map(OfflinePayRecord::getMerchantId).collect(Collectors.toList());
        MerchantRespDto merchant = merchantFeign.getMerchantById(merchantIds.get(0));
        BigDecimal totalAmount = offlinePayRecords.stream().map(OfflinePayRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        offlinePayDetailResp.setMerchantId(merchant.getId());
        offlinePayDetailResp.setMerchantName(merchant.getName());
        offlinePayDetailResp.setTotalAmount(totalAmount);
        offlinePayDetailResp.setDetails(offlinePayDetails);
        return offlinePayDetailResp;
    }

    /**
     * 支付回调处理
     */
    @DistributedLock(value = "#paymentParam.tradeNo")
    public PaymentDto paymentCallback(PaymentDto paymentParam) {
        log.info("offline payment handle begin tradeNo:{} ", paymentParam);
        OfflinePayPayment payment = offlinePayPaymentMapperManager.lambdaQuery()
                .eq(OfflinePayPayment::getPaymentId, paymentParam.getId())
                .orderByDesc(OfflinePayPayment::getId)
                .last(LIMIT_ONE)
                .one();
        Assert.validateNull(payment, "线上付款记录数据异常");
        //异常状态 一般不存在
        if (OfflinePayRecordConstant.StatusEnum.NONE.name().equals(payment.getStatus()) || OfflinePayRecordConstant.StatusEnum.FAILED.name().equals(payment.getStatus())) {
            log.error("线上付款记录状态异常 offlinePayPaymentId:{}", payment.getId());
            return null;
        }
        //支付已完成或者已退款的不处理
        if (OfflinePayRecordConstant.StatusEnum.PAID.name().equals(payment.getStatus()) || OfflinePayRecordConstant.StatusEnum.RETURN.name().equals(payment.getStatus())) {
            return null;
        }
        List<OfflinePayRecord> offlinePayRecords = offlinePayRecordMapperManager.lambdaQuery().eq(OfflinePayRecord::getOfflinePayPaymentId, payment.getId()).list();
        return offlinePaySaveService.updatePaidStatus(paymentParam, payment, offlinePayRecords);
    }

    public void overdueTimePayHandler() {
        List<OfflinePayPayment> list = offlinePayPaymentMapperManager.lambdaQuery()
                .in(OfflinePayPayment::getStatus, Lists.newArrayList(OfflinePayRecordConstant.StatusEnum.DURING.name(), OfflinePayRecordConstant.StatusEnum.FAILED.name()))
                .in(OfflinePayPayment::getPaymentMethod, Lists.newArrayList(PaymentMethodEnum.ALI_PAY.getCode(), PaymentMethodEnum.LAKALA.getCode())).le(OfflinePayPayment::getPaymentOverdueTime, System.currentTimeMillis()).list();
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<Long> offlinePayPaymentIds = list.stream().map(OfflinePayPayment::getId).collect(Collectors.toList());
        List<OfflinePayRecord> overdueTimeRecords = offlinePayRecordMapperManager.lambdaQuery().in(OfflinePayRecord::getOfflinePayPaymentId, offlinePayPaymentIds).list();
        List<Long> overdueTimeRecordIds = overdueTimeRecords.stream().map(OfflinePayRecord::getId).collect(Collectors.toList());
        //寄付中超时关闭处理
        List<AdminPrepaidPaymentHistory> prepaidPaymentHistories = adminPrepaidPaymentHistoryMapperManager.lambdaQuery().in(AdminPrepaidPaymentHistory::getOfflinePayRecordId, overdueTimeRecordIds).eq(AdminPrepaidPaymentHistory::getStatus, OfflinePayRecordConstant.StatusEnum.DURING.name()).list();
        List<Long> prepaidPaymentHistoryIds = Lists.newArrayList();
        Set<Long> adminPrepaidIds = Sets.newHashSet();
        if (CollectionUtil.isNotEmpty(prepaidPaymentHistories)) {
            prepaidPaymentHistoryIds = prepaidPaymentHistories.stream().map(AdminPrepaidPaymentHistory::getId).collect(Collectors.toList());
            adminPrepaidIds = prepaidPaymentHistories.stream().map(AdminPrepaidPaymentHistory::getAdminPrepaidId).collect(Collectors.toSet());
            List<Long> haveOtherRecordHistoryAdminPrepaidIds = adminPrepaidPaymentHistoryMapperManager.lambdaQuery().notIn(AdminPrepaidPaymentHistory::getOfflinePayRecordId, overdueTimeRecordIds).in(AdminPrepaidPaymentHistory::getAdminPrepaidId, adminPrepaidIds).eq(AdminPrepaidPaymentHistory::getStatus, OfflinePayRecordConstant.StatusEnum.DURING.name()).list().stream().map(AdminPrepaidPaymentHistory::getAdminPrepaidId).collect(Collectors.toList());
            haveOtherRecordHistoryAdminPrepaidIds.forEach(adminPrepaidIds::remove);
        }
        offlinePaySaveService.updateRecordStatusByOverdueTime(offlinePayPaymentIds, overdueTimeRecordIds, prepaidPaymentHistoryIds, adminPrepaidIds);
    }

    public OfflinePayPaymentResp getOne(Long offlinePayPaymentId) {
        OfflinePayPayment payment = offlinePayPaymentMapperManager.getById(offlinePayPaymentId);
        return BeanUtil.toBean(payment, OfflinePayPaymentResp.class);
    }

    private List<OfflinePayDetailResp.OfflinePayDetailDto> buildOfflinePayDetails(
            List<OfflinePayRecord> offlinePayRecords,
            Map<Long, TenantDistributorWalletDto> tenantWalletMap,
            List<Long> nonPayableTenantIds,
            Map<Long, String> tenantIdToNoMap) {

        return offlinePayRecords.stream()
                .map(record -> {
                    OfflinePayDetailResp.OfflinePayDetailDto dto = BeanUtil.toBean(record, OfflinePayDetailResp.OfflinePayDetailDto.class);
                    // 交易流水
                    String tradeFlow = OfflinePayRecordGen.getTradeFlow(record.getMerchantTenantId(), record.getTenantId(), tenantWalletMap);
                    dto.setTradeFlow(tradeFlow);
                    dto.setIsCanPay(YES); // 默认可支付
                    // 处理不可支付情况
                    if (EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.getValue().equals(tradeFlow)
                            && nonPayableTenantIds.contains(record.getTenantId())) {
                        String tenantNo = tenantIdToNoMap.getOrDefault(record.getTenantId(), "");
                        dto.setIsCanPay(NO);
                        dto.setErrorMsg("租户(" + tenantNo + ")授信余额不足，请联系客服");
                    }
                    return dto;
                }).collect(Collectors.toList());
    }


    private List<Long> findNonPayableTenantIds(Map<Long, TenantDistributorWalletDto> tenantWalletMap, List<OfflinePayAmountDetail> offlinePayAmountDetails) {
        Map<Long, BigDecimal> supperTenantIdKeyAmountMap = offlinePayAmountDetails.stream()
                .filter(o -> PaymentRoleEnum.TENANT_DIS.getCode().equals(o.getSourceRole())
                        && PaymentRoleEnum.TENANT_SUP.getCode().equals(o.getTargetRole()))
                .collect(Collectors.groupingBy(
                        OfflinePayAmountDetail::getTargetTenantId,
                        Collectors.reducing(BigDecimal.ZERO, OfflinePayAmountDetail::getAmount, BigDecimal::add)
                ));

        List<Long> nonPayableTenantIds = Lists.newArrayList();
        for (Map.Entry<Long, BigDecimal> entry : supperTenantIdKeyAmountMap.entrySet()) {
            BigDecimal balance = tenantWalletMap.get(entry.getKey()) == null ? BigDecimal.ZERO : tenantWalletMap.get(entry.getKey()).getBalance();
            if (balance.compareTo(entry.getValue()) >= 0) {
                continue;
            }
            nonPayableTenantIds.add(entry.getKey());
        }
        return nonPayableTenantIds;
    }

    private Map<Long, TenantDistributorWalletDto> getSupTenantIdWalletMap(Long merchantTenantId, List<Long> supplierTenantIds) {
        TenantDistributionQueryParam tenantDistributionQueryParam = new TenantDistributionQueryParam();
        tenantDistributionQueryParam.setDisTenantIds(Collections.singletonList(merchantTenantId));
        tenantDistributionQueryParam.setSupTenantId(supplierTenantIds);
        List<TenantDistributorWalletDto> tenantDistributorWalletDtos = tenantDistributionWalletFeign.queryWallets(tenantDistributionQueryParam);
        return ListUtil.toMap(TenantDistributorWalletDto::getSupTenantId, tenantDistributorWalletDtos);
    }
}
