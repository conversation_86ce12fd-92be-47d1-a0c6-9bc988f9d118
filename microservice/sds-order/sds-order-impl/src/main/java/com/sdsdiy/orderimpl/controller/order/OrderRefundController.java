package com.sdsdiy.orderimpl.controller.order;

import com.sdsdiy.orderapi.api.order.OrderRefundApi;
import com.sdsdiy.orderapi.dto.order.OrderRefundHandleParam;
import com.sdsdiy.orderimpl.service.order.OrderRefundService;
import com.sdsdiy.paymentapi.dto.RefundDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/8/18 9:43)
 */
@RestController
@RequiredArgsConstructor
public class OrderRefundController implements OrderRefundApi {

    private final OrderRefundService orderRefundService;

    @Override
    public RefundDto refundHandle(OrderRefundHandleParam param) {
        return orderRefundService.refundHandle(param);
    }

}
