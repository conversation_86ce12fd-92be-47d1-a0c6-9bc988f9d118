package com.sdsdiy.orderimpl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.helper.mybatis.MybatisQueryUtil;
import com.sdsdiy.core.base.service.BaseServiceImpl;
import com.sdsdiy.core.redis.util.RedisUtil;
import com.sdsdiy.orderapi.constant.EnumOrderPayType;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderdata.dto.OrderAmountAfterOrderDto;
import com.sdsdiy.orderdata.dto.create.OrderAmountCreateDTO;
import com.sdsdiy.orderdata.util.OrderAmountCalUtil;
import com.sdsdiy.orderimpl.entity.po.OrderAmount;
import com.sdsdiy.orderimpl.mapper.OrderAmountMapper;
import com.sdsdiy.paymentapi.constant.PaymentMethodEnum;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.compress.utils.Lists;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Log4j2
@DS("master")
public class OrderAmountService extends BaseServiceImpl<OrderAmountMapper, OrderAmount> {
    @Resource
    private RedisUtil redisUtil;

    public List<OrderAmount> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return MybatisQueryUtil.inSplitForR(ids, null, this::listByIds);
    }

    public void increaseTenantRefundAmount(OrderAmount orderAmount) {
        this.baseMapper.increaseTenantRefundAmount(orderAmount);
    }

    public Map<Long, OrderAmountRespDTO> findMapByIds(Collection<Long> ids) {
        List<OrderAmount> orderAmounts = this.findByIds(ids);
        List<OrderAmountRespDTO> list = BeanUtil.copyToList(orderAmounts, OrderAmountRespDTO.class);
        return list.stream().collect(Collectors.toMap(OrderAmountRespDTO::getId, Function.identity()));
    }
    public Map<Long, OrderAmount> mapByIds(Collection<Long> ids) {
        List<OrderAmount> orderAmounts = this.findByIds(ids);
        return orderAmounts.stream().collect(Collectors.toMap(OrderAmount::getId, Function.identity()));
    }

    public OrderAmount findById(Long id) {
        return this.baseMapper.selectById(id);
    }

    public OrderAmount findById(Long id, String fields) {
        return this.lambdaQuery(fields, null).eq(OrderAmount::getId, id).one();
    }

    public void savePo(OrderAmount po) {
        if (po.getId() == null) {
            return;
        }
        po.setUpdatedTime(System.currentTimeMillis());
        this.saveOrUpdate(po);
    }

    /**
     * 会考虑已退款，进行差值更新
     */
    public void updateOrderPaidAmount(Long id, OrderAmountCreateDTO createDTO) {
        OrderAmount originalAmount = this.findById(id, "tenantRefundAmount,tenantRefundProductAmount,tenantRefundServiceAmount,tenantRefundCarriageAmount");
        if (originalAmount == null) {
            return;
        }
        OrderAmount orderAmount = new OrderAmount();
        orderAmount.setTenantTotalAmount(OrderAmountCalUtil
                .calPaidAmount(createDTO.getTenantTotalAmount(), originalAmount.getTenantRefundAmount()).doubleValue());
        orderAmount.setTenantProductAmount(OrderAmountCalUtil
                .calPaidAmount(createDTO.getTenantProductAmount(), originalAmount.getTenantRefundProductAmount()).doubleValue());
        orderAmount.setTenantServiceAmount(OrderAmountCalUtil
                .calPaidAmount(createDTO.getTenantServiceAmount(), originalAmount.getTenantRefundServiceAmount()).doubleValue());
        orderAmount.setTenantCarriageAmount(OrderAmountCalUtil
                .calPaidAmount(createDTO.getTenantCarriageAmount(), originalAmount.getTenantRefundCarriageAmount()).doubleValue());
        orderAmount.setTenantUsedFreeGold(createDTO.getTenantUsedFreeGold());
        orderAmount.setProductTenantId(createDTO.getProductTenantId());
        orderAmount.setPaymentType(createDTO.getPaymentType());
        orderAmount.setId(id);

        orderAmount.setServiceAmount(createDTO.getServiceAmount());
        orderAmount.setTotalAmount(createDTO.getTotalAmount());
        orderAmount.setMaterialServiceAmount(createDTO.getMaterialServiceAmount());
        orderAmount.setProductAmount(createDTO.getProductAmount());
        orderAmount.setCarriageAmount(createDTO.getCarriageAmount());
        this.updateById(orderAmount);
    }

    /**
     * 这个方法会直接进行覆盖更新
     */
    public void updateProductAmount(Long id, OrderAmountCreateDTO orderAmountCreateDTO) {
        OrderAmount orderAmount = new OrderAmount();
        orderAmount.setTenantTotalAmount(orderAmountCreateDTO.getTenantTotalAmount());
        orderAmount.setTenantProductAmount(orderAmountCreateDTO.getTenantProductAmount());
        orderAmount.setTenantServiceAmount(orderAmountCreateDTO.getTenantServiceAmount());
        orderAmount.setTenantCarriageAmount(orderAmountCreateDTO.getTenantCarriageAmount());
        orderAmount.setTenantUsedFreeGold(orderAmountCreateDTO.getTenantUsedFreeGold());
        orderAmount.setProductTenantId(orderAmountCreateDTO.getProductTenantId());
        orderAmount.setPaymentType(orderAmountCreateDTO.getPaymentType());
        orderAmount.setId(id);

        orderAmount.setServiceAmount(orderAmountCreateDTO.getServiceAmount());
        orderAmount.setTotalAmount(orderAmountCreateDTO.getTotalAmount());
        orderAmount.setMaterialServiceAmount(orderAmountCreateDTO.getMaterialServiceAmount());
        orderAmount.setProductAmount(orderAmountCreateDTO.getProductAmount());
        orderAmount.setCarriageAmount(orderAmountCreateDTO.getCarriageAmount());

        this.updateById(orderAmount);
    }

    public void updateTenantCarriageAmount(Long id, BigDecimal tenantCarriageAmount) {
        OrderAmount orderAmount = new OrderAmount();
        orderAmount.setTenantCarriageAmount(tenantCarriageAmount.doubleValue());
        orderAmount.setId(id);
        this.updateById(orderAmount);
    }

    public void increaseAmount(Long id, Double totalAmount) {
        this.baseMapper.increaseAmount(id, totalAmount);

    }

    //    public void increaseAmount(Long id, Double totalAmount) {
//        this.baseMapper.increaseAmount(id, totalAmount);
//
//    }
    public void decreaseTenantUsedFreeGold(Long id, Double totalAmount) {
        this.baseMapper.decreaseTenantUsedFreeGold(id, totalAmount);

    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean transactionSave(OrderAmount entity) {
        return this.save(entity);
    }

    public void createAfterServiceAmount(Long id,Long tenantId,Long productTenantId) {
        OrderAmount orderAmount = this.findById(id);
        if (orderAmount != null) {
            return;
        }
        String paymentType = tenantId.equals(productTenantId) ? EnumOrderPayType.MERCHANT_OWNTENANT.getValue() : EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.getValue();
        orderAmount = new OrderAmount();
        orderAmount.setId(id);
        orderAmount.setTenantId(tenantId);
        orderAmount.setProductTenantId(productTenantId);
        orderAmount.setPaymentType(paymentType);
        orderAmount.setPaymentMethod(PaymentMethodEnum.OFFLINE.getCode());
        this.save(orderAmount);

    }

    public void createOrUpdateAfterServiceAmount(OrderAmountAfterOrderDto orderAmountAfterOrderDto) {
        Long id = orderAmountAfterOrderDto.getId();
        Double tenantProductAmount = orderAmountAfterOrderDto.getTenantProductAmount();
        Long tenantId = orderAmountAfterOrderDto.getTenantId();
        Long productTenantId = orderAmountAfterOrderDto.getProductTenantId();

        OrderAmount orderAmount = this.findById(id);
        if (orderAmount != null) {
            orderAmount.setTenantProductAmount(tenantProductAmount);
            this.updateById(orderAmount);
            return;
        }
        orderAmount = new OrderAmount();
        orderAmount.setId(id);
        orderAmount.setTenantProductAmount(tenantProductAmount);
        orderAmount.setProductTenantId(productTenantId);
        orderAmount.setTenantId(tenantId);
        orderAmount.setPaymentType(EnumOrderPayType.getPaymentType(tenantId, productTenantId));
        orderAmount.setPaymentMethod(PaymentMethodEnum.OFFLINE.getCode());
        this.save(orderAmount);

    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean transactionUpdate(OrderAmount entity) {
        return this.updateById(entity);
    }


    public List<Long> filterOrderIdForPod(Long tenantId, List<Long> orderIds) {
        if(CollUtil.isEmpty(orderIds)){
            return Lists.newArrayList();
        }
        LambdaQueryChainWrapper<OrderAmount> lambdaQueryChainWrapper = this.lambdaQuery()
                .in(OrderAmount::getId, orderIds)
                .and(wrapper -> {
                    wrapper.eq(OrderAmount::getTenantId, tenantId);
                    if (TenantCommonConstant.SDSDIY_TENANT_ID.equals(tenantId)) {
                        wrapper.or().eq(OrderAmount::getProductTenantId, tenantId);
                    } else {
                        wrapper.eq(OrderAmount::getProductTenantId, tenantId);
                    }
                });


        return lambdaQueryChainWrapper
                .select(OrderAmount::getId)
                .list()
                .stream()
                .map(OrderAmount::getId)
                .collect(Collectors.toList());
    }

    private static final int BATCH_SIZE = 500; // 每批更新条数

    public List<OrderAmount> selectBatchAfterId(long lastId, int limit) {
        List<OrderAmount> list = this.lambdaQuery()
            .lt(OrderAmount::getId, lastId)
            .in(OrderAmount::getPaymentType, EnumOrderPayType.SAAS.getValue(), EnumOrderPayType.TENANT_SAAS.getValue())
            .orderByDesc(OrderAmount::getId)
            .last("LIMIT " + limit)
            .list();
        return list;
    }

    public static void main(String[] args) {
        System.out.println(Long.MAX_VALUE);
    }
    /**
     * 判断是否未处理过
     */
    private boolean isUnprocessed(OrderAmount orderAmount) {
        return EnumOrderPayType.SAAS.getValue().equals(orderAmount.getPaymentType())
            || EnumOrderPayType.TENANT_SAAS.getValue().equals(orderAmount.getPaymentType());
    }

    /**orderAmount paymentType旧数据处理*/
    @Async
    public void oldDataUpdateOrderAmountPaymentType(String orderId,String lastIdStr,String stopIdStr) {
        // 如果传了 id，就只处理这条
        if (StrUtil.isNotBlank(orderId)) {
            long id = Long.parseLong(orderId);
            OrderAmount orderAmount = this.findById(id);
            if (orderAmount != null && isUnprocessed(orderAmount)) {
                OrderAmount update=getUpdateAmount(orderAmount);
                updateById(update);
            }
            log.info("orderAmount:update:status:finished");
            return; // 结束
        }
        redisUtil.set("orderAmount:update:status", "running", Duration.ofDays(3).getSeconds());

        Long stopId = StrUtil.isNotBlank(stopIdStr) ? Long.parseLong(stopIdStr) : null;
        // 读取上次的游标（支持断点续跑）
        long lastId = (lastIdStr != null) ? Long.parseLong(lastIdStr) : Long.MAX_VALUE;
        List<OrderAmount> batch;

        do {
            // 分页读取（用主键递增避免大表 offset 慢）
            batch = selectBatchAfterId(lastId, BATCH_SIZE);
            if (batch.isEmpty()) {
                redisUtil.set("orderAmount:update:status", "finished");
                break;
            }
            List<OrderAmount> updateList = new ArrayList<>();
            for (OrderAmount orderAmount : batch) {
                OrderAmount update=getUpdateAmount(orderAmount);
                updateList.add(update);
            }

            // 批量更新
            if (CollUtil.isNotEmpty(updateList)) {
                this.updateBatchById(updateList);
            }

            // 更新 lastId
            lastId = batch.get(batch.size() - 1).getId();
            redisUtil.set("orderAmount:update:lastId", String.valueOf(lastId), Duration.ofDays(3).getSeconds());

            log.info("lastId: {}", lastId);
            if(null!=stopId&&stopId>=lastId){
                break;
            }
        } while (batch.size() == BATCH_SIZE);
    }

    private static OrderAmount getUpdateAmount(OrderAmount orderAmount) {
        String oldType = orderAmount.getPaymentType();
        Long tenantId = orderAmount.getTenantId();

        OrderAmount update=new OrderAmount();
        update.setId(orderAmount.getId());
        if (EnumOrderPayType.SAAS.getValue().equals(oldType)) {
            if (TenantCommonConstant.isSdsdiy(tenantId)) {
                update.setPaymentType(EnumOrderPayType.MERCHANT_OWNTENANT.getValue());
            } else if (tenantId > TenantCommonConstant.SDSDIY_TENANT_ID) {
                update.setPaymentType(EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.getValue());
            }
        } else if (EnumOrderPayType.TENANT_SAAS.getValue().equals(oldType)) {
            update.setPaymentType(EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.getValue());
        }
        return update;
    }
}

