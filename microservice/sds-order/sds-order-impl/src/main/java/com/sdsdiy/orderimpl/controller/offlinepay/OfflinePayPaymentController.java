package com.sdsdiy.orderimpl.controller.offlinepay;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.orderapi.api.OfflinePayPaymentApi;
import com.sdsdiy.orderapi.dto.adminprepaid.OfflinePayCommitResp;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayBatchPayParam;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayPaymentResp;
import com.sdsdiy.orderimpl.service.offlinepay.OfflinePayPaymentService;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 线下付款(OfflinePayPayment)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-22 14:25:27
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class OfflinePayPaymentController implements OfflinePayPaymentApi {

    private final OfflinePayPaymentService offlinePayPaymentService;

    @Override
    public OfflinePayCommitResp batchPay(OfflinePayBatchPayParam param) {
        log.info("batchPay parma {}", JSON.toJSONString(param));
        return offlinePayPaymentService.batchPay(param);
    }

    @Override
    public PaymentDto paymentCallback(PaymentDto paymentDto) {
        log.info("paymentCallback parma {}", JSON.toJSONString(paymentDto));
        return offlinePayPaymentService.paymentCallback(paymentDto);
    }

    @Override
    public OfflinePayPaymentResp one(Long offlinePayPaymentId) {
        return offlinePayPaymentService.getOne(offlinePayPaymentId);
    }
}
