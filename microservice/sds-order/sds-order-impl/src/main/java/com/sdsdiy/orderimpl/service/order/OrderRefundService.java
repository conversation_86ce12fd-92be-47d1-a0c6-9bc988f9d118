package com.sdsdiy.orderimpl.service.order;

import cn.hutool.json.JSONUtil;
import com.beust.jcommander.internal.Lists;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.McContentHelper;
import com.sdsdiy.core.util.OrderCodeUtil;
import com.sdsdiy.orderapi.constant.EnumOrderPayType;
import com.sdsdiy.orderapi.dto.order.OrderRefundHandleParam;
import com.sdsdiy.orderapi.dto.ordercarriagereturnrecode.OrderCarriageNoReturnHandleParam;
import com.sdsdiy.orderdata.dto.OrderPaymentTypeDTO;
import com.sdsdiy.orderimpl.entity.po.Order;
import com.sdsdiy.orderimpl.entity.po.OrderAmount;
import com.sdsdiy.orderimpl.feign.payment.TransactionFeign;
import com.sdsdiy.orderimpl.feign.user.MerchantUserAccountFeign;
import com.sdsdiy.orderimpl.service.OrderAmountService;
import com.sdsdiy.orderimpl.service.OrderPaymentTypeService;
import com.sdsdiy.orderimpl.service.OrderService;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.RefundDto;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentapi.param.TransactionEntryParam;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.dto.UserAccountBalanceResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.sdsdiy.orderapi.constant.OrderCarriageReturnRecodeConstant.TYPE_MANUALLY;

/**
 * <AUTHOR>
 * @date 2025/8/18 9:45)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderRefundService {
    private final OrderAmountService orderAmountService;
    private final OrderService orderService;
    private final TransactionFeign transactionFeign;
    private final OrderPaymentTypeService orderPaymentTypeService;
    private final MerchantUserAccountFeign merchantUserAccountFeign;

    /**
     * pod 端已完成换单退款处理
     */
    public void podRefundCompletedOrderChangePrice(OrderCarriageNoReturnHandleParam param, String logisticsName, Order order) {
        BigDecimal differencePrice = param.getDifferentialPrice().abs();
        BigDecimal tenantDifferentialPrice = param.getTenantDifferentialPrice().abs();
        String subject = "已完成换单";
        String remark = logisticsName;
        if (param.getType() == TYPE_MANUALLY) {
            remark = remark + "：" + param.getNewCarriageNo();
        }
        OrderRefundHandleParam handleParam = new OrderRefundHandleParam();
        handleParam.setOrderId(order.getId())
                .setBalance(differencePrice)
                .setTenantBalance(tenantDifferentialPrice)
                .setBonus(BigDecimal.ZERO)
                .setTenantBonus(BigDecimal.ZERO)
                .setPurposeType(PurposeType.REFUND.getCode())
                .setTitle(subject)
                .setSubject(subject)
                .setRemark(remark)
                .setDetailPurpose(DetailPurpose.ADMIN_CHANGE_ORDER_REFUND.getCode())
                .setOperateRole(PaymentRoleEnum.TENANT.getCode())
                .setOperateRoleId(McContentHelper.getCurrentUserId());
        this.refundHandle(handleParam);
    }


    public RefundDto refundHandle(OrderRefundHandleParam param) {
        log.info("orderRefundHandle param {}", JSONUtil.toJsonStr(param));
        Order order = orderService.findById(param.getOrderId());
        OrderAmount orderAmount = orderAmountService.findById(param.getOrderId());
        Assert.validateNull(orderAmount, "订单金额不存在");
        UserAccountBalanceResp userAccountBalance = this.getUserAccountBalance(orderAmount.getMerchantId(), orderAmount.getId());
        List<RefundParam> refundList = new ArrayList<>();
        // 租户退给商户
        PaymentMethodEnum payMethod = PaymentMethodEnum.isOffline(orderAmount.getPaymentMethod())
                ? PaymentMethodEnum.OFFLINE : PaymentMethodEnum.BALANCE;

        RefundParam toMerchant = new RefundParam();
        refundList.add(toMerchant);

        toMerchant.setPurposeType(param.getPurposeType())
                .setDetailPurpose(param.getDetailPurpose())
                .setPayType(TransactionPayTypeEnum.MAIN.getValue())
                .setPayMethod(payMethod.getCode())
                .setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus())
                .setSubject(param.getSubject())
                .setBizNo(OrderCodeUtil.getOrderIdByUUId())
                .setBalance(param.getBalance())
                .setRemark(param.getRemark())
                .setBonus(param.getBonus());

        toMerchant.setOperateRole(param.getOperateRole())
                .setOperateUserId(param.getOperateRoleId())
                .setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode())
                .setOperateTargetRoleId(orderAmount.getMerchantId());

        toMerchant.setSourceTenantId(orderAmount.getTenantId())
                .setSourceMerchantId(0L)
                .setSourceUserId(0L)
                .setSourceRole(PaymentRoleEnum.TENANT.getCode());

        toMerchant.setTargetTenantId(orderAmount.getTenantId())
                .setTargetMerchantId(orderAmount.getMerchantId())
                .setTargetUserId(userAccountBalance.getId())
                .setTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        if (MerchantUserAccountConstant.ALLOCATION.equalsIgnoreCase(userAccountBalance.getBalanceType())) {
            toMerchant.setBalanceType(BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType());
        } else {
            toMerchant.setBalanceType(BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType());
        }
        // 供应租户 退给 分销租户
        if (EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.equalsCode(orderAmount.getPaymentType())) {
            RefundParam toTenant = new RefundParam();
            refundList.add(toTenant);
            toTenant.setPurposeType(param.getPurposeType())
                    .setDetailPurpose(param.getPurposeType())
                    .setPayType(TransactionPayTypeEnum.SUB.getValue())
                    .setPayMethod(PaymentMethodEnum.BALANCE.getCode())
                    .setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus())
                    .setSubject(param.getSubject())
                    .setBizNo(OrderCodeUtil.getOrderIdByUUId())
                    .setBalanceType(BalanceUsedType.TENANT_DISTRIBUTION_BALANCE.getUsedType())
                    .setBalance(param.getTenantBalance())
                    .setBonus(param.getTenantBonus());

            toTenant.setOperateRole(PaymentRoleEnum.SYSTEM.getCode())
                    .setOperateUserId(0L)
                    .setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode())
                    .setOperateTargetRoleId(orderAmount.getMerchantId());
            if (!PaymentRoleEnum.MERCHANT.getCode().equals(param.getOperateRole())) {
                toTenant.setOperateRole(param.getOperateRole())
                        .setOperateUserId(param.getOperateRoleId());
            }
            toTenant.setSourceTenantId(orderAmount.getProductTenantId())
                    .setSourceMerchantId(0L)
                    .setSourceUserId(0L)
                    .setSourceRole(PaymentRoleEnum.TENANT_SUP.getCode());

            toTenant.setTargetTenantId(orderAmount.getTenantId())
                    .setTargetMerchantId(0L)
                    .setTargetUserId(0L)
                    .setTargetRole(PaymentRoleEnum.TENANT_DIS.getCode());
        }
        List<TransactionEntryParam> transactionEntryList = Lists.newArrayList();
        for (RefundParam refundParam : refundList) {
            TransactionEntryParam transactionEntryParam = new TransactionEntryParam();
            transactionEntryParam.setRefundBizNo(refundParam.getBizNo());
            transactionEntryParam.setBalance(refundParam.getBalance().abs());
            transactionEntryParam.setBonus(refundParam.getBonus().abs());
            transactionEntryParam.setBizNoForBill(order.getNo());
            transactionEntryParam.setType(TransactionEntryTypeEnum.REFUND.getValue());
            transactionEntryParam.setTitle(param.getTitle());
            transactionEntryList.add(transactionEntryParam);
        }
        MultiTransactionCreateParam createParam = new MultiTransactionCreateParam();
        createParam.setRefundList(refundList);
        createParam.setTransactionEntryList(transactionEntryList);
        log.info("orderRefundHandle createRefund param {}", JSONUtil.toJsonStr(createParam));
        RefundDto refundDto = this.transactionFeign.createRefund(createParam);
        this.transactionFeign.operateRefund(refundDto.getId());
        return refundDto;
    }

    public UserAccountBalanceResp getUserAccountBalance(Long merchantId, Long orderId) {
        OrderPaymentTypeDTO orderPaymentTypeDTO = orderPaymentTypeService.findById(orderId);
        if (orderPaymentTypeDTO == null) {
            UserAccountBalanceResp userAccountBalanceResp = new UserAccountBalanceResp();
            userAccountBalanceResp.setId(0L);
            userAccountBalanceResp.setBalanceType(MerchantUserAccountConstant.IN_COMMON);
            return userAccountBalanceResp;
        } else {
            return merchantUserAccountFeign.getUserBalance(merchantId, orderPaymentTypeDTO.getPaymentUid());
        }

    }

}
