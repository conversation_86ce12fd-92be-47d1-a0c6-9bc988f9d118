package com.sdsdiy.orderimpl.service.offlinepay;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.orderapi.constant.OfflinePayRecordConstant;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayBatchPayParam;
import com.sdsdiy.orderimpl.bo.OfflinePayRecordGen;
import com.sdsdiy.orderimpl.entity.po.OfflinePayAmountDetail;
import com.sdsdiy.orderimpl.entity.po.OfflinePayRecord;
import com.sdsdiy.orderimpl.entity.po.OfflinePayRecordOrder;
import com.sdsdiy.orderimpl.feign.payment.TenantDistributionWalletFeign;
import com.sdsdiy.orderimpl.manager.OfflinePayAmountDetailMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayPaymentMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayRecordMapperManager;
import com.sdsdiy.orderimpl.manager.OfflinePayRecordOrderMapperManager;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import com.sdsdiy.paymentapi.param.TenantDistributionQueryParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 线下付款(OfflinePayPayment)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-22 14:25:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OfflinePayService {

    private final OfflinePayPaymentMapperManager offlinePayPaymentMapperManager;
    private final OfflinePayAmountDetailMapperManager offlinePayAmountDetailMapperManager;
    private final OfflinePayRecordMapperManager offlinePayRecordMapperManager;
    private final OfflinePayRecordOrderMapperManager offlinePayRecordOrderMapperManager;
    private final TenantDistributionWalletFeign tenantDistributionWalletFeign;


    public void buildBatchPayBo(OfflinePayBatchPayParam param) {
        List<OfflinePayRecord> offlinePayRecords = offlinePayRecordMapperManager.listByIds(param.getOfflinePayRecordIds());
        List<OfflinePayRecordOrder> recordOrders = offlinePayRecordOrderMapperManager.lambdaQuery()
                .in(OfflinePayRecordOrder::getOfflinePayRecordId, param.getOfflinePayRecordIds())
                .list();
        Map<Long, String> idKeyTitleMap = recordIdKeyTitleMap(offlinePayRecords, recordOrders);
        List<Long> supplierTenantIds = offlinePayRecords.stream().map(OfflinePayRecord::getTenantId).distinct().collect(Collectors.toList());
        Map<Long, TenantDistributorWalletDto> supTenantIdWalletMap = this.getSupTenantIdWalletMap(offlinePayRecords.get(0).getMerchantTenantId(), supplierTenantIds);
        Map<Long, List<OfflinePayAmountDetail>> offlinePayAmountGroup = offlinePayAmountDetailMapperManager.lambdaQuery()
                .in(OfflinePayAmountDetail::getOfflinePayRecordId, param.getOfflinePayRecordIds())
                .list().stream()
                .collect(Collectors.groupingBy(OfflinePayAmountDetail::getOfflinePayRecordId));


    }


    public String recordIdKeyTitleStr(List<OfflinePayRecord> records,
                                      Map<Long, List<OfflinePayRecordOrder>> orderMap) {
        StringBuilder title = new StringBuilder();
        // 处理订单号字符串
        for (int i = 0; i < records.size(); i++) {
            OfflinePayRecord record = records.get(i);
            List<OfflinePayRecordOrder> orders = orderMap.get(record.getId());
            String str = recordIdKeyTitleStr(record, orders);
            if (i == 0) {
                title.append(str);
            } else {
                title.append(";").append(str);
            }
        }
        return title.toString();
    }


    public String recordIdKeyTitleStr(OfflinePayRecord record,
                                      List<OfflinePayRecordOrder> orders) {
        // 处理订单号字符串
        String orderNumbers = buildOrderNumbers(orders);
        StringBuilder title = new StringBuilder("补款账期id:" + record.getId());
        String tradeType = record.getTradeType();
        // 根据交易类型处理不同的后缀
        String suffix = getTradeTypeSuffix(tradeType, record.getDetail());
        if (!StringUtils.isEmpty(suffix)) {
            title.append(suffix);
            return title.toString();
        }
        // 非特定交易类型，拼接订单号
        if (orderNumbers != null) {
            title.append(",订单号:").append(orderNumbers);
        }
        return title.toString();
    }


    public Map<Long, String> recordIdKeyTitleMap(List<OfflinePayRecord> offlinePayRecords,
                                                 List<OfflinePayRecordOrder> orders) {
        // 处理订单号字符串
        String orderNumbers = buildOrderNumbers(orders);
        Map<Long, String> idKeyTitleMap = new HashMap<>((int) (offlinePayRecords.size() / 0.75) + 1);

        for (OfflinePayRecord record : offlinePayRecords) {
            StringBuilder title = new StringBuilder("补款账期id:" + record.getId());
            String tradeType = record.getTradeType();
            // 根据交易类型处理不同的后缀
            String suffix = getTradeTypeSuffix(tradeType, record.getDetail());
            if (!StringUtils.isEmpty(suffix)) {
                title.append(suffix);
                idKeyTitleMap.put(record.getId(), title.toString());
                continue;
            }
            // 非特定交易类型，拼接订单号
            if (orderNumbers != null) {
                title.append(",订单号:").append(orderNumbers);
            }
            idKeyTitleMap.put(record.getId(), title.toString());
        }
        return idKeyTitleMap;
    }

    /**
     * 构建订单号拼接字符串
     */
    private String buildOrderNumbers(List<OfflinePayRecordOrder> orders) {
        if (CollectionUtil.isEmpty(orders)) {
            return "";
        }
        List<String> orderNos = orders.stream()
                .map(OfflinePayRecordOrder::getOrderNo)
                .collect(Collectors.toList());
        return CollectionUtil.join(orderNos, ",");
    }

    /**
     * 根据交易类型获取对应的标题后缀
     */
    private String getTradeTypeSuffix(String tradeType, String detail) {
        // 统一处理detail中的换行符
        String processedDetail = StrUtil.isBlank(detail) ? "" : detail.replaceAll("\n\n", ", ");
        //处理不同交易类型
        if (OfflinePayRecordConstant.TradeTypeEnum.ALIEXPRESS_JIT.getName().equals(tradeType)) {
            return ",速卖通JIT订单自寄单号：" + processedDetail;
        }
        if (OfflinePayRecordConstant.TradeTypeEnum.TEMU_FULLY.getName().equals(tradeType)) {
            return ",temu全托管发仓订单自寄发货单号：" + processedDetail;
        }
        return "";
    }

    private Map<Long, TenantDistributorWalletDto> getSupTenantIdWalletMap(Long merchantTenantId, List<Long> supplierTenantIds) {
        TenantDistributionQueryParam tenantDistributionQueryParam = new TenantDistributionQueryParam();
        tenantDistributionQueryParam.setDisTenantIds(Collections.singletonList(merchantTenantId));
        tenantDistributionQueryParam.setSupTenantId(supplierTenantIds);
        List<TenantDistributorWalletDto> tenantDistributorWalletDtos = tenantDistributionWalletFeign.queryWallets(tenantDistributionQueryParam);
        return ListUtil.toMap(TenantDistributorWalletDto::getSupTenantId, tenantDistributorWalletDtos);
    }

    public List<OfflinePayAmountDetail> getOfflinePayAmountDetails(List<OfflinePayRecord> offlinePayRecords,
                                                                   Map<Long, TenantDistributorWalletDto> supTenantIdWalletMap,
                                                                   Map<Long, List<OfflinePayAmountDetail>> offlinePayAmountGroup) {
        if (CollectionUtil.isEmpty(offlinePayRecords)) {
            return Lists.newArrayList();
        }
        Map<Long, String> offlinePayRecordIdKeyTrade = Maps.newHashMap();
        for (OfflinePayRecord offlinePayRecord : offlinePayRecords) {
            String tradeFlow = OfflinePayRecordGen.getTradeFlow(offlinePayRecord.getMerchantTenantId(), offlinePayRecord.getTenantId(), supTenantIdWalletMap);
            offlinePayRecordIdKeyTrade.put(offlinePayRecord.getId(), tradeFlow);
        }
        List<OfflinePayAmountDetail> offlinePayAmountDetails = Lists.newArrayList();
        for (OfflinePayRecord record : offlinePayRecords) {
            List<OfflinePayAmountDetail> details = offlinePayAmountGroup.get(record.getId());
            String tradeFlow = offlinePayRecordIdKeyTrade.get(record.getId());
            if (CollectionUtil.isEmpty(details)) {
                offlinePayAmountDetails.addAll(OfflinePayRecordGen.generateDefaultAmounts(record, tradeFlow));
                continue;
            }
            offlinePayAmountDetails.addAll(details);
        }
        return offlinePayAmountDetails;
    }
}
