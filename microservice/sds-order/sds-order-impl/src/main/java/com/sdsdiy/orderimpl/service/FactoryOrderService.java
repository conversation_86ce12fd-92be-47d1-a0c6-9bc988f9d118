package com.sdsdiy.orderimpl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.es.core.chain.EsChainQueryWrapper;
import com.es.pojo.EsResponse;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.CommonConstant;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.*;
import com.sdsdiy.common.base.enums.SdsPlatformEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.core.base.service.BaseServiceImpl;
import com.sdsdiy.core.base.util.DateUtil;
import com.sdsdiy.core.base.util.StringUtils;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.util.BarcodeUtil;
import com.sdsdiy.designproductapi.dto.base.DesignProductRespDto;
import com.sdsdiy.designproductdata.constant.DesignProductTypeConstant;
import com.sdsdiy.designproductdata.dto.customImage.DesignProductCustomInfoReqDto;
import com.sdsdiy.designproductdata.dto.customImage.DesignProductCustomInfoRespDto;
import com.sdsdiy.designproductdata.dto.customImage.DesignProductCustomLayerInfoRespDto;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayRespDto;
import com.sdsdiy.logisticsapi.constant.TenantLogisticsConstant;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsapi.enums.LogisticsCodeIdEnum;
import com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum;
import com.sdsdiy.materialdata.dto.material.MaterialRespDto;
import com.sdsdiy.orderapi.constant.*;
import com.sdsdiy.orderapi.constant.earlywarning.EarlyWarningTypeConstant;
import com.sdsdiy.orderapi.constant.earlywarning.OrderEarlyWarningConstant;
import com.sdsdiy.orderapi.constant.event.message.factory.FactoryBatchDownloadOrderMessage;
import com.sdsdiy.orderapi.constant.manuscript.IsDownMaterrialConstant;
import com.sdsdiy.orderapi.constant.manuscript.ManuscriptFeedbackStatusConstant;
import com.sdsdiy.orderapi.constant.productionLine.FactoryOrderFilterTypeConstant;
import com.sdsdiy.orderapi.dto.FactoryOrderQueryParameters;
import com.sdsdiy.orderapi.dto.ImportedPlatformOrderFbaItemRespDto;
import com.sdsdiy.orderapi.dto.base.FactoryOrderExtraResp;
import com.sdsdiy.orderapi.dto.factoryassistant.*;
import com.sdsdiy.orderapi.dto.factoryorder.*;
import com.sdsdiy.orderapi.dto.factoryorder.FactoryOrderPrintInfoRespDto.FactoryOrdersBean.*;
import com.sdsdiy.orderapi.dto.manuscript.ManuscriptRefreshDto;
import com.sdsdiy.orderapi.dto.manuscript.ManuscriptRefreshGroupingRspDto;
import com.sdsdiy.orderapi.dto.manuscript.ManuscriptRefreshSearchRspDto;
import com.sdsdiy.orderapi.dto.order.OrderItemTransferHistoryDto;
import com.sdsdiy.orderapi.dto.productionLine.*;
import com.sdsdiy.orderapi.dto.tenant.TenantLogisticsOrderRespDto;
import com.sdsdiy.orderapi.enumeration.EnumExpressType;
import com.sdsdiy.orderapi.enums.*;
import com.sdsdiy.orderapi.param.FactoryOrderExportExcelParam;
import com.sdsdiy.orderdata.bo.factory.order.ProductionLineGroupBo;
import com.sdsdiy.orderdata.dto.*;
import com.sdsdiy.orderdata.dto.factory.order.FactoryOrderRespDto;
import com.sdsdiy.orderdata.dto.factory.order.*;
import com.sdsdiy.orderdata.dto.factory.task.FactoryOrderLittleHelperStaticDto;
import com.sdsdiy.orderdata.dto.factory.task.FactoryOrderTaskReqDto;
import com.sdsdiy.orderdata.dto.factory.task.FactoryTaskRespDto;
import com.sdsdiy.orderdata.dto.factoryOrder.*;
import com.sdsdiy.orderdata.dto.order.OrderItemSupplyChainDTO;
import com.sdsdiy.orderdata.dto.print.fo.FoProductionPrintTemplateConfigDTO;
import com.sdsdiy.orderdata.dto.print.fo.FoProductionPrintTemplateQueryDTO;
import com.sdsdiy.orderdata.dto.print.fo.FoProductionPrintTemplateRespDTO;
import com.sdsdiy.orderdata.dto.warning.EarlyWarningTypeDto;
import com.sdsdiy.orderdata.dto.warning.OrderEarlyWarningDto;
import com.sdsdiy.orderdata.enums.QcStatusEnum;
import com.sdsdiy.orderimpl.config.FactoryTaskSqsConfig;
import com.sdsdiy.orderimpl.entity.po.OrderItem;
import com.sdsdiy.orderimpl.entity.po.*;
import com.sdsdiy.orderimpl.entity.po.factoryorder.FactoryOrderDelivery;
import com.sdsdiy.orderimpl.entity.po.factoryorder.FactoryOrderEsPO;
import com.sdsdiy.orderimpl.entity.po.task.FactoryTask;
import com.sdsdiy.orderimpl.entity.po.task.FactoryTaskOrderRel;
import com.sdsdiy.orderimpl.entity.po.warning.OrderEarlyWarning;
import com.sdsdiy.orderimpl.feign.*;
import com.sdsdiy.orderimpl.feign.product.ColorFeign;
import com.sdsdiy.orderimpl.feign.product.ProductSupplyFeign;
import com.sdsdiy.orderimpl.feign.user.TenantFeign;
import com.sdsdiy.orderimpl.manager.EarlyWarningTypeMapperManager;
import com.sdsdiy.orderimpl.manager.FactoryOrderMapperManager;
import com.sdsdiy.orderimpl.manager.print.FoProductionPrintTemplateManager;
import com.sdsdiy.orderimpl.mapper.FactoryOrderMapper;
import com.sdsdiy.orderimpl.service.factoryorder.FactoryOrderDeliveryService;
import com.sdsdiy.orderimpl.service.factoryorder.FactoryOrderEsQueryService;
import com.sdsdiy.orderimpl.service.factoryorder.FactoryOrderSerialService;
import com.sdsdiy.orderimpl.service.order.OrderItemSupplyChainService;
import com.sdsdiy.orderimpl.service.task.FactoryTaskOrderRelService;
import com.sdsdiy.orderimpl.service.task.FactoryTaskService;
import com.sdsdiy.orderimpl.service.tenant.TenantLogisticsOrderService;
import com.sdsdiy.productapi.dto.ProductDto;
import com.sdsdiy.productapi.dto.ProductSupplyDTO;
import com.sdsdiy.productdata.dto.ProductRespDto;
import com.sdsdiy.productdata.dto.ProductSupplyFactoryIdsProductIdsDTO;
import com.sdsdiy.productdata.enums.supply.SupplyChainTypeEnum;
import com.sdsdiy.userapi.constant.enums.DistributionProductLogisticsSourceEnum;
import com.sdsdiy.userapi.dto.tenant.TenantListReq;
import com.sdsdiy.userapi.dto.tenant.TenantRespDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.ParsedValueCount;
import org.elasticsearch.search.aggregations.metrics.Sum;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.MybatisPlusConstant.LIMIT_ONE;
import static com.sdsdiy.materialapi.constant.MaterialSourceTypeConstant.OFFICIAL_MATERIAL;
import static com.sdsdiy.productapi.constant.EmbroideryConstant.S3_OFFICE_IMG_PREVIEW;
import static com.sdsdiy.productapi.constant.EmbroideryConstant.S3_PREVIEW;

@Slf4j
@Service
@DS("common")
@RequiredArgsConstructor(onConstructor_ = {@Autowired, @Lazy})
public class FactoryOrderService extends BaseServiceImpl<FactoryOrderMapper, FactoryOrder> {
    public static final String HTTPS_S3_CN_NORTH_URL = "https://static-photo-center-prov.oss-cn-hangzhou.aliyuncs.com/";

    private final OrderItemImportProductService orderItemImportProductService;
    private final FactoryTaskService factoryTaskService;
    private final FactoryOrderStatusService factoryOrderStatusService;

    @Resource
    private FactoryOrderSerialService factoryOrderSerialService;

    @Resource
    private FactoryTaskOrderRelService factoryTaskOrderRelService;
    @Resource
    private OrderEarlyWarningService orderEarlyWarningService;
    @Resource
    private OrderRemarkService orderRemarkService;
    @Resource
    private FactoryOrderOperateRecordService operateRecordService;
    private final OrderService orderService;
    private final FactoryOrderMapperManager factoryOrderMapperManager;
    private final FactoryOrderMapper factoryOrderMapper;
    private final ProductFeign productFeign;
    private final IssuingBayFeign issuingBayFeign;
    private final ProductionLineService productionLineService;
    private final OrderItemService orderItemService;

    private final FactoryOrderLogisticsService factoryOrderLogisticsService;
    private final OrderItemTransferHistoryService orderItemTransferHistoryService;
    private final DesignProductFeign designProductFeign;
    private final AfterServiceAuditItemService afterServiceAuditService;

    private final EarlyWarningTypeMapperManager earlywarningTypeService;
    private final TenantLogisticsOrderService tenantLogisticsOrderService;
    private final OrderAmountService orderAmountService;
    private final TenantFeign tenantFeign;
    private final ProductSupplyFeign productSupplyFeign;
    private final OrderItemSupplyChainService orderItemSupplyChainService;
    private final FactoryOrderDeliveryService factoryOrderDeliveryService;
    private final MaterialFeign materialFeign;
    private final TenantLogisticsFeign tenantLogisticsFeign;
    private final FactoryOrderExtraService factoryOrderExtraService;
    private final DesignProductImageFeign designProductImageFeign;
    private final S3ServiceV2 s3ServiceV2;
    private final FoProductionPrintTemplateManager foProductionPrintTemplateManager;
    private final ColorFeign colorFeign;
    private final FactoryOrderEsQueryService factoryOrderEsQueryService;
    private final RocketMQTemplate rocketMQTemplate;
    public static final List<Integer> factoryOrderAvailableStatus = Lists.newArrayList();

    static {
        factoryOrderAvailableStatus.add(FactoryOrderStatusEnum.UNCONFIRMED.getStatus());
        factoryOrderAvailableStatus.add(FactoryOrderStatusEnum.ACCOMPLISH_PRODUCTION.getStatus());
        factoryOrderAvailableStatus.add(FactoryOrderStatusEnum.SHIPMENTS.getStatus());
        factoryOrderAvailableStatus.add(FactoryOrderStatusEnum.RECEIVE_THINGS.getStatus());
        factoryOrderAvailableStatus.add(FactoryOrderStatusEnum.PASS_GC.getStatus());
        factoryOrderAvailableStatus.add(FactoryOrderStatusEnum.ACCOMPLISH.getStatus());
    }


    public FactoryOrder findByNo(String no) {
        if (StringUtils.isBlank(no)) {
            return null;
        }
        return this.lambdaQuery().eq(FactoryOrder::getNo, no).one();
    }

    public List<FactoryOrder> findAllByExportParam(FactoryOrderExportExcelParam param) {
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getId, param.getFactoryOrderIds())
                .eq(FactoryOrder::getFactoryId, param.getFactoryId())
                .select(
                        FactoryOrder::getId,
                        FactoryOrder::getOrderItemId,
                        FactoryOrder::getImgUrl,
                        FactoryOrder::getNo,
                        FactoryOrder::getEndProductId,
                        FactoryOrder::getProductName,
                        FactoryOrder::getProductSku,
                        FactoryOrder::getTextureName,
                        FactoryOrder::getProductSize,
                        FactoryOrder::getProductColorName,
                        FactoryOrder::getNum,
                        FactoryOrder::getBeginTime,
                        FactoryOrder::getIssuingBayId,
                        FactoryOrder::getOriginType,
                        FactoryOrder::getProductId,
                        FactoryOrder::getShapeCodeUrl,
                        FactoryOrder::getTenantId
                );
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }


    public List<FactoryOrder> findByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return null;
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .eq(FactoryOrder::getMerchantOrderNo, orderNo);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public List<FactoryOrder> findByOrderNos(Long factoryId, Integer status, List<String> nos) {
        if (CollectionUtils.isEmpty(nos)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getNo, nos)
                .eq(status != null, FactoryOrder::getStatus, status)
                .eq(FactoryOrder::getFactoryId, factoryId);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public List<FactoryOrder> findByOrderNos(List<String> nos) {
        if (CollectionUtils.isEmpty(nos)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getNo, nos);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public List<FactoryOrder> findIdsByOrderNos(Long factoryId, List<String> nos) {
        if (CollectionUtils.isEmpty(nos)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .select(FactoryOrder::getId, FactoryOrder::getNo, FactoryOrder::getStatus, FactoryOrder::getIsManuscriptOver, FactoryOrder::getOrderItemId, FactoryOrder::getRequireManuscript)
                .in(FactoryOrder::getNo, nos)
                .eq(FactoryOrder::getFactoryId, factoryId);
        List<FactoryOrder> factoryOrders = this.baseMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(factoryOrders)) {
            return Lists.newArrayList();
        }

        return factoryOrders;
    }


    public List<FactoryOrder> findByTaskId(Long factoryId, Long taskId) {
        if (taskId == null) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .eq(FactoryOrder::getTaskId, taskId)
                .eq(FactoryOrder::getFactoryId, factoryId);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public List<FactoryOrder> findByVariantProductIds(Long factoryId, Collection<Long> variantProductIds) {
        if (CollUtil.isEmpty(variantProductIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .select(FactoryOrder::getId, FactoryOrder::getProductionLineId)
                .in(FactoryOrder::getProductId, variantProductIds)
                .in(FactoryOrder::getStatus, Lists.newArrayList(1, 2, 3, 4, 5))
                .eq(FactoryOrder::getFactoryId, factoryId);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }


    public List<FactoryOrder> findByTaskIdsAndStatus(Long factoryId, Collection<Long> taskIds, List<Integer> status) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getTaskId, taskIds)
                .in(CollUtil.isNotEmpty(status), FactoryOrder::getStatus, status)
                .eq(FactoryOrder::getFactoryId, factoryId);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }


    public List<FactoryOrder> findByTaskIdsAndSort(Long factoryId, Long taskId, Collection<Integer> status, Collection<String> operStatus, String type, String sort) {
        if (ObjectUtil.isNull(taskId)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .eq(FactoryOrder::getTaskId, taskId)
                .in(PrintTypeEnum.PRINT.getValue().equals(type), FactoryOrder::getPrintStatus, operStatus)
                .in(DownTypeEnum.DOWN.getValue().equals(type), FactoryOrder::getDownStatus, operStatus)
                .in(CollectionUtil.isNotEmpty(status), FactoryOrder::getStatus, status)
                .eq(FactoryOrder::getFactoryId, factoryId)
                .orderByDesc(StrUtil.isNotEmpty(sort), FactoryOrder::getProductName, FactoryOrder::getProductColorName, FactoryOrder::getProductSize, FactoryOrder::getId);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }


    public List<FactoryOrder> findByFactoryOrderNos(Collection<String> nos) {
        if (CollectionUtils.isEmpty(nos)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery().in(FactoryOrder::getNo, nos).list();
    }

    public List<FactoryOrder> findByFactoryOrderNos(BaseListQueryDTO<String> dto) {
        if (CollectionUtils.isEmpty(dto.getList())) {
            return Collections.emptyList();
        }
        return this.lambdaQuery(dto.getFields(), null).in(FactoryOrder::getNo, dto.getList()).list();
    }

    public List<FactoryOrderDto> findDtoByFactoryOrderNos(BaseListQueryDTO<String> dto) {
        if (CollectionUtils.isEmpty(dto.getList())) {
            return Collections.emptyList();
        }
        List<FactoryOrder> list = this.lambdaQuery(dto.getFields(), FactoryOrderDto.class)
                .in(FactoryOrder::getNo, dto.getList()).list();
        return ListUtil.copyProperties(list, FactoryOrderDto.class);
    }

    public List<FactoryOrderDto> findDtoByFactoryOrderIds(BaseListQueryDTO<Long> dto) {
        if (CollectionUtils.isEmpty(dto.getList())) {
            return Collections.emptyList();
        }
        List<FactoryOrder> list = this.lambdaQuery(dto.getFields(), FactoryOrderDto.class)
                .in(FactoryOrder::getId, dto.getList()).list();
        return ListUtil.copyProperties(list, FactoryOrderDto.class);
    }

    public List<FactoryOrder> findOutDateByNos(Collection<String> nos, Long factoryId, List<Integer> statusList) {
        if (CollUtil.isEmpty(nos)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().in(FactoryOrder::getNo, nos)
                .eq(factoryId != null, FactoryOrder::getFactoryId, factoryId)
                .in(FactoryOrder::getStatus, statusList)
                .le(FactoryOrder::getOutDate, System.currentTimeMillis()).list();
    }

    public List<FactoryOrder> findByMerchantOrderNos(Collection<String> nos) {
        if (CollectionUtils.isEmpty(nos)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getMerchantOrderNo, nos)
                ;
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }
    public List<FactoryOrder> findUnconfirmByMerchantOrderNos(Collection<String> nos) {
        if (CollectionUtils.isEmpty(nos)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getMerchantOrderNo, nos)
                .eq(FactoryOrder::getStatus, FactoryOrderStatusEnum.UNCONFIRMED.getStatus())
                ;
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public static Boolean isTransferType(String type) {
        if (EnumOrderItemTransferType.REJECT.getValue().equalsIgnoreCase(type)) {
            return true;
        }
        if (EnumOrderItemTransferType.TRANSFER.getValue().equalsIgnoreCase(type)) {
            return true;
        }
        if (EnumOrderItemTransferType.LOSE_RESEND.getValue().equalsIgnoreCase(type)) {
            return true;
        }
        return false;
    }

    public List<FactoryOrder> findNoCancelsByOrderNos(List<String> orderNos) {
        if (CollUtil.isEmpty(orderNos)) {
            return Lists.newArrayList();
        }
        List<FactoryOrder> factoryOrders = this.findByMerchantOrderNos(orderNos);
        List<FactoryOrder> noCancelFactoryOrders = Lists.newArrayList();

        for (FactoryOrder factoryOrder : factoryOrders) {
            if (factoryOrder.getBeResendForLose()) {
                //过滤漏件的
            } else if (isTransferType(factoryOrder.getTransferType())) {
                //过滤驳回转移的
            } else if (!FactoryOrderStatusConstant.cancel(factoryOrder.getStatus())) {
                noCancelFactoryOrders.add(factoryOrder);
            }
        }
        return noCancelFactoryOrders;

    }

    public List<FactoryOrder> findByMerchantOrderNo(String no) {
        return this.lambdaQuery()
                .eq(FactoryOrder::getMerchantOrderNo, no).list();
    }

    public List<FactoryOrder> findByMerchantOrderNo(String no, String fields) {
        return this.lambdaQuery(fields, null)
                .eq(FactoryOrder::getMerchantOrderNo, no).list();
    }


    public List<FactoryOrder> findByOrderItemId(Collection<Long> orderItemIds) {
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getOrderItemId, orderItemIds);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public List<FactoryOrder> findByOrderItemIds(BaseListQueryDTO<Long> dto) {
        if (CollUtil.isEmpty(dto.getList())) {
            return Collections.emptyList();
        }
        return this.lambdaQuery(dto.getFields(), null)
                .in(FactoryOrder::getOrderItemId, dto.getList()).list();
    }


    public List<FactoryOrder> findByMerchantOrderNoOrderByconfirmTime(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getMerchantOrderNo, orderNos)
                .orderByDesc(FactoryOrder::getConfirmTime);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public List<FactoryOrder> findByMerchantOrderNoOrderByshipTime(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getMerchantOrderNo, orderNos)
                .orderByDesc(FactoryOrder::getShipTime);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public List<FactoryOrder> findByMerchantOrderNoOrderByFinishedTime(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getMerchantOrderNo, orderNos)
                .orderByDesc(FactoryOrder::getFinishedTime);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }


    /***
     *稿件反馈更新状态
     * <AUTHOR>
     * @date 2020/8/28 15:35
     */
    public void feedbackManuscript(Long id, int manuscriptFeedbackStatus, Date manuscriptFeedbackTime, Long outDate, Date outCycleDate) {
        LambdaUpdateWrapper<FactoryOrder> lambdaUpdateWrapper = Wrappers.<FactoryOrder>lambdaUpdate()
                .set(FactoryOrder::getManuscriptFeedbackStatus, manuscriptFeedbackStatus)
                .set(outDate != null, FactoryOrder::getOutDate, outDate)
                .set(outCycleDate != null, FactoryOrder::getOutCycleDate, outCycleDate)
                .set(manuscriptFeedbackTime != null, FactoryOrder::getManuscriptFeedbackTime, manuscriptFeedbackTime)
                .set(FactoryOrder::getIsDownMaterrial, IsDownMaterrialConstant.NO)
                .eq(FactoryOrder::getId, id);
        int count = this.baseMapper.update(null, lambdaUpdateWrapper);
        Assert.validateBool(count > 0, "工厂订单更新失败！！");

    }


    public FactoryOrderDto findDtoById(Long factoryOrderId) {
        FactoryOrder factoryOrder = this.lambdaQuery(null, FactoryOrderDto.class)
                .eq(FactoryOrder::getId, factoryOrderId).one();
        return RelationsBinder.convertAndBind(factoryOrder, FactoryOrderDto.class);
    }

    public List<FactoryOrder> findAllByOrderItemIds(List<Long> orderItemIds) {
        if (CollUtil.isEmpty(orderItemIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .in(FactoryOrder::getOrderItemId, orderItemIds);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    public List<FactoryOrderDto> findByFactoryOrderIds(String ids) {
        List<FactoryOrder> factoryOrderList = Lists.newArrayList();
        List<String> orderIdStrArr = new ArrayList<>();
        if (StrUtil.isNotBlank(ids)) {
            orderIdStrArr = StrUtil.split(ids, ",");
        }
        if (ArrayUtil.isNotEmpty(orderIdStrArr)) {
            LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                    .in(FactoryOrder::getId, orderIdStrArr);
            factoryOrderList = this.baseMapper.selectList(lambdaQueryWrapper);
        }

        return RelationsBinder.convertAndBind(factoryOrderList, FactoryOrderDto.class, null);

    }

    public List<FactoryOrder> findByFactoryOrderNos(List<String> nos, Long factoryId, Collection<Integer> inStatus, Collection<Integer> notInStatus) {
        if (CollUtil.isEmpty(nos)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(FactoryOrder::getNo, nos)
                .eq(factoryId != null, FactoryOrder::getFactoryId, factoryId)
                .in(CollUtil.isNotEmpty(inStatus), FactoryOrder::getStatus, inStatus)
                .notIn(CollUtil.isNotEmpty(notInStatus), FactoryOrder::getStatus, notInStatus)
                .list();
    }

    public List<FactoryOrderRespDto> findDtoByFactoryOrderNos(List<String> nos, Long factoryId, Integer status, List<Integer> notInStatus) {
        List<FactoryOrder> factoryOrders = this.findByFactoryOrderNos(nos, factoryId, status == null ? null : Collections.singleton(status), notInStatus);
        if (CollectionUtil.isEmpty(factoryOrders)) {
            return Collections.emptyList();
        }
        return this.packageFactoryOrders(factoryOrders, factoryId);
    }

    public List<OrderItemRespDto> findOrderItemsByFactoryOrderIds(List<Long> ids, String fields) {
        List<FactoryOrder> factoryOrders = this.lambdaQuery().select(FactoryOrder::getOrderItemId)
                .in(FactoryOrder::getId, ids).list();
        List<Long> orderItemIds = factoryOrders.stream().map(FactoryOrder::getOrderItemId).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(orderItemIds)) {
            return Lists.newArrayList();
        }
        IdsSearchHelper idsSearchHelper = new IdsSearchHelper();
        idsSearchHelper.setIds(orderItemIds);
        idsSearchHelper.setFields(fields);
        return this.orderItemService.findByIds(idsSearchHelper);

    }

    public List<FactoryOrder> findByIds(BaseListQueryDTO<Long> queryDTO) {
        if (CollUtil.isEmpty(queryDTO.getList())) {
            return Collections.emptyList();
        }
        return this.lambdaQuery(queryDTO.getFields(), null)
                .in(FactoryOrder::getId, queryDTO.getList()).list();
    }


    public List<FactoryOrder> findByIds(Collection<Long> ids) {
        return this.listByIds(ids);
    }

    public List<FactoryOrder> findByIds(List<Long> ids, List<Integer> status) {
        return this.getBaseLambdaQuery().in(FactoryOrder::getStatus, status).in(FactoryOrder::getId, ids).list();
    }


    public List<FactoryOrder> findNotDownCacheByFactoryId(Long factoryId, String printStatus, String downStatus) {
        return this.getBaseLambdaQuery()
                .eq(FactoryOrder::getFactoryId, factoryId)
                .eq(StrUtil.isNotEmpty(printStatus), FactoryOrder::getPrintStatus, printStatus)
                .eq(StrUtil.isNotEmpty(downStatus), FactoryOrder::getDownStatus, downStatus)
                .in(FactoryOrder::getStatus, Arrays.asList(1, 2, 3, 4, 5))
                .list();
    }


    public List<FactoryOrder> findNotComplete(Long factoryId) {
        return this.lambdaQuery()
                .select(FactoryOrder::getId, FactoryOrder::getNo)
                .eq(FactoryOrder::getFactoryId, factoryId)
                .eq(FactoryOrder::getStatus, FactoryOrderStatusEnum.ACCOMPLISH_PRODUCTION.getStatus())
                .gt(FactoryOrder::getCreateTime, new Date(System.currentTimeMillis() - 60 * DateUtil.dayCurrentTimeMillis()))
                .lt(FactoryOrder::getCreateTime, new Date(System.currentTimeMillis() - 5 * DateUtil.dayCurrentTimeMillis()))
                .list();
    }

    public FactoryOrder findById(Long id, String fields) {
        return this.lambdaQuery(fields, null).eq(FactoryOrder::getId, id).one();
    }

    public List<FactoryOrder> findByIds(Long factoryId, Collection<Long> ids) {
        return this.lambdaQuery().in(FactoryOrder::getId, ids).eq(FactoryOrder::getFactoryId, factoryId).list();
    }

    public List<FactoryOrderByProductGroupingDto> productGroupingByEs(FactoryOrderQueryParameters factoryOrderQueryParameters) {

        EsChainQueryWrapper<FactoryOrderEsPO> wrapper = this.factoryOrderEsQueryService.genFactoryEndEsQueryWrapper(factoryOrderQueryParameters);

        TermsAggregationBuilder productColorBlockBuilder = AggregationBuilders
                .terms("productColorBlock")
                .field("productColorBlock.keyword")
                .size(Integer.MAX_VALUE);

        TermsAggregationBuilder productSizeBuilder = AggregationBuilders
                .terms("productSize")
                .field("productSize.keyword")
                .size(Integer.MAX_VALUE)
                .subAggregation(productColorBlockBuilder);

        TermsAggregationBuilder productColorNameBuilder = AggregationBuilders
                .terms("productColorName")
                .field("productColorName.keyword")
                .size(Integer.MAX_VALUE)
                .subAggregation(productSizeBuilder);

        TermsAggregationBuilder productIdBuilder = AggregationBuilders.terms("productId")
                .field("productId")
                .size(Integer.MAX_VALUE)
                .subAggregation(productColorNameBuilder);
        wrapper.addAggregationBuilder(productIdBuilder);

        Aggregations aggregation = wrapper.aggregation();
        Terms productIdTerms = aggregation.get("productId");
        List<String> stringList = new ArrayList<>();
        for (Terms.Bucket productIdBucket : productIdTerms.getBuckets()) {
            String productId = productIdBucket.getKeyAsString();
            stringList.add(productId);
        }
        String productIds = String.join(",", stringList);

        //查询所有产品
        List<ProductDto> productDtoList = this.productFeign.findByIdsAndStatus(productIds, null, null);
        //根据母体分组
        List<FactoryOrderByProductGroupingDto> productGroupingDtoList;

        List<Long> parentIdsList = Lists.newArrayList();
        Map<Long, FactoryOrderByProductGroupingDto> productDtoMap = Maps.newConcurrentMap();
        Map<Long, FactoryOrderByProductGroupingDto> productDtoParMap = Maps.newConcurrentMap();

        for (Terms.Bucket productIdBucket : productIdTerms.getBuckets()) {

            long productId = productIdBucket.getKeyAsNumber().longValue();

            Terms productColorNameTerms = productIdBucket.getAggregations().get("productColorName");
            for (Terms.Bucket colorNameBucket : productColorNameTerms.getBuckets()) {
                String colorName = colorNameBucket.getKeyAsString();

                Terms productSizeTerms = colorNameBucket.getAggregations().get("productSize");
                for (Terms.Bucket sizeBucket : productSizeTerms.getBuckets()) {
                    String productSize = sizeBucket.getKeyAsString();

                    Terms productColorBlockTerms = sizeBucket.getAggregations().get("productColorBlock");
                    for (Terms.Bucket colorBlockBucket : productColorBlockTerms.getBuckets()) {
                        String productColorBlock = colorBlockBucket.getKeyAsString();
                        long count = colorBlockBucket.getDocCount();

                        if (productDtoMap.containsKey(productId)) {
                            FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto = productDtoMap.get(productId);
                            this.assemblyProductionLineEs(productDtoMap, productId, colorName, productSize, productColorBlock, count, factoryOrderByProductGroupingDto);
                        } else {
                            FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto = new FactoryOrderByProductGroupingDto();
                            ProductItem productItem = new ProductItem();
                            productItem.setColorList(Lists.newArrayList());
                            productItem.setSizeList(Lists.newArrayList());
                            productItem.setColorMap(Maps.newLinkedHashMap());
                            productItem.setSizeMap(Maps.newLinkedHashMap());
                            ColorDto colorAll = new ColorDto();
                            colorAll.setColorName("全部");
                            colorAll.setNum(0);
                            productItem.getColorMap().put("全部", colorAll);
                            SizeDto sizeDto = new SizeDto();
                            sizeDto.setSizeName("全部");
                            sizeDto.setNum(0);
                            productItem.getSizeMap().put("全部", sizeDto);
                            factoryOrderByProductGroupingDto.setProductItem(productItem);
                            factoryOrderByProductGroupingDto.setNum(0);
                            this.assemblyProductionLineEs(productDtoMap, productId, colorName, productSize, productColorBlock, count, factoryOrderByProductGroupingDto);
                            productDtoMap.put(productId, factoryOrderByProductGroupingDto);
                        }
                    }
                }
            }
        }

        for (ProductDto productDto : productDtoList) {
            if (productDtoParMap.containsKey(productDto.getParentId())) {
                FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDtoPar = productDtoParMap.get(productDto.getParentId());
                ProductItem productItemPar = factoryOrderByProductGroupingDtoPar.getProductItem();
                Map<String, ColorDto> colorDtoMapPar = productItemPar.getColorMap();
                Map<String, SizeDto> sizeDtoMapPar = productItemPar.getSizeMap();
                FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto = productDtoMap.get(productDto.getId());
                //聚合
                ProductItem productItem = factoryOrderByProductGroupingDto.getProductItem();
                Map<String, ColorDto> colorDtoMap = productItem.getColorMap();
                for (String key : colorDtoMap.keySet()) {
                    ColorDto colorDto = colorDtoMap.get(key);
                    if (colorDtoMapPar.containsKey(key)) {
                        ColorDto colorDtoPar = colorDtoMapPar.get(key);
                        colorDtoPar.setNum(colorDtoPar.getNum() + colorDto.getNum());
                        colorDtoMapPar.put(colorDtoPar.getColorName(), colorDtoPar);
                    } else {
                        colorDtoMapPar.put(colorDto.getColorName(), colorDto);
                    }

                }
                Map<String, SizeDto> sizeDtoMap = productItem.getSizeMap();
                for (String key : sizeDtoMap.keySet()) {
                    SizeDto sizeDto = sizeDtoMap.get(key);
                    if (sizeDtoMapPar.containsKey(key)) {
                        SizeDto sizeDtoPar = sizeDtoMapPar.get(key);
                        sizeDtoPar.setNum(sizeDtoPar.getNum() + sizeDto.getNum());
                        sizeDtoMapPar.put(sizeDtoPar.getSizeName(), sizeDtoPar);
                    } else {
                        sizeDtoMapPar.put(sizeDto.getSizeName(), sizeDto);
                    }
                }

                factoryOrderByProductGroupingDtoPar.setNum(factoryOrderByProductGroupingDtoPar.getNum() + factoryOrderByProductGroupingDto.getNum());
                productDtoParMap.put(productDto.getParentId(), factoryOrderByProductGroupingDtoPar);
            } else {
                FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto = productDtoMap.get(productDto.getId());
                factoryOrderByProductGroupingDto.setId(productDto.getParentId());
                productDtoParMap.put(productDto.getParentId(), factoryOrderByProductGroupingDto);
            }
            parentIdsList.add(productDto.getParentId());
        }
        //查询母体名称
        List<String> parentIdsStrList = parentIdsList.stream().map(String::valueOf).collect(Collectors.toList());
        String parentIds = String.join(",", parentIdsStrList);
        List<ProductDto> productParDtoList = this.productFeign.findByIdsAndStatus(parentIds, null, null);
        Map<Long, ProductDto> productParDtoMap = productParDtoList.stream().collect(Collectors.toMap(ProductDto::getId, ProductDto -> ProductDto));
        productGroupingDtoList = new ArrayList<>(productDtoParMap.values());

        for (FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto : productGroupingDtoList) {
            ProductDto dto = productParDtoMap.get(factoryOrderByProductGroupingDto.getId());
            factoryOrderByProductGroupingDto.setProductName(dto.getName());
            ProductItem productItem = factoryOrderByProductGroupingDto.getProductItem();
            productItem.setColorList(new ArrayList<>(productItem.getColorMap().values()));
            productItem.setSizeList(new ArrayList<>(productItem.getSizeMap().values()));
        }

        return productGroupingDtoList;
    }

    private void assemblyProductionLineEs(Map<Long, FactoryOrderByProductGroupingDto> productDtoMap, Long productId, String colorName, String productSize, String productColorBlock, Long count, FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto) {
        ProductItem productItem = factoryOrderByProductGroupingDto.getProductItem();
        Map<String, ColorDto> colorDtoMap = productItem.getColorMap();
        Map<String, SizeDto> sizeDtoMap = productItem.getSizeMap();

        //颜色
        if (colorDtoMap.containsKey(colorName)) {
            ColorDto colorDto = colorDtoMap.get(colorName);
            colorDto.setNum(colorDto.getNum() + count.intValue());
            colorDtoMap.put(colorName, colorDto);
            ColorDto colorDtoAll = colorDtoMap.get("全部");
            colorDtoAll.setNum(colorDtoAll.getNum() + count.intValue());
            colorDtoMap.put("全部", colorDtoAll);
        } else {
            ColorDto colorDto = new ColorDto();
            colorDto.setColorName(colorName);
            colorDto.setColorBlock(productColorBlock);
            colorDto.setNum(count.intValue());
            colorDto.setProductId(productId);
            colorDtoMap.put(colorName, colorDto);
            ColorDto colorDtoAll = colorDtoMap.get("全部");
            colorDtoAll.setNum(colorDtoAll.getNum() + count.intValue());
            colorDtoMap.put("全部", colorDtoAll);
        }
        //尺寸
        if (sizeDtoMap.containsKey(productSize)) {
            SizeDto sizeDto = sizeDtoMap.get(productSize);
            sizeDto.setNum(sizeDto.getNum() + count.intValue());
            sizeDtoMap.put(productSize, sizeDto);
            SizeDto sizeDtoAll = sizeDtoMap.get("全部");
            sizeDtoAll.setNum(sizeDtoAll.getNum() + count.intValue());
            sizeDtoMap.put("全部", sizeDtoAll);
        } else {
            SizeDto sizeDto = new SizeDto();
            sizeDto.setSizeName(productSize);
            sizeDto.setNum(count.intValue());
            sizeDto.setProductId(productId);
            sizeDtoMap.put(productSize, sizeDto);
            SizeDto sizeDtoAll = sizeDtoMap.get("全部");
            sizeDtoAll.setNum(sizeDtoAll.getNum() +  count.intValue());
            sizeDtoMap.put("全部", sizeDtoAll);
        }
        productItem.setColorMap(colorDtoMap);
        productItem.setSizeMap(sizeDtoMap);
        factoryOrderByProductGroupingDto.setNum(factoryOrderByProductGroupingDto.getNum() +  count.intValue());
        productDtoMap.put(productId, factoryOrderByProductGroupingDto);
    }

    /***
     *按产品归类
     * <AUTHOR>
     * @date 2020/9/22 17:07
     */
    @Deprecated
    public List<FactoryOrderByProductGroupingDto> productGrouping(FactoryOrderQueryParameters factoryOrderQueryParameters) {

        //时间处理
        this.timeToLongProcessing(factoryOrderQueryParameters);
        //预警条件处理
        if (CollectionUtil.isNotEmpty(factoryOrderQueryParameters.getEarlyWarningIds())) {
            this.getOrEarlyWarning(factoryOrderQueryParameters);
        } else {
            this.getEarlyWarning(factoryOrderQueryParameters);
        }
        appendSerialNumberLeftJoinSql(factoryOrderQueryParameters);
        //类型
        this.getFilter(factoryOrderQueryParameters);

        List<FactoryOrder> factoryOrderList = this.factoryOrderMapper.groupingProduct(factoryOrderQueryParameters);
        if (CollectionUtil.isEmpty(factoryOrderList)) {
            return Lists.newArrayList();
        }


        List<String> stringList = factoryOrderList.stream().map(e -> String.valueOf(e.getProductId())).collect(Collectors.toList());
        String productIds = String.join(",", stringList);
        //查询所有产品
        List<ProductDto> productDtoList = this.productFeign.findByIdsAndStatus(productIds, null, null);
        //根据母体分组
        List<FactoryOrderByProductGroupingDto> productGroupingDtoList = Lists.newArrayList();

        List<Long> parentIdsList = Lists.newArrayList();
        Map<Long, FactoryOrderByProductGroupingDto> productDtoMap = Maps.newConcurrentMap();
        Map<Long, FactoryOrderByProductGroupingDto> productDtoParMap = Maps.newConcurrentMap();

        for (FactoryOrder factoryOrder : factoryOrderList) {
            if (productDtoMap.containsKey(factoryOrder.getProductId())) {
                FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto = productDtoMap.get(factoryOrder.getProductId());
                this.assemblyProductionLine(productDtoMap, factoryOrder, factoryOrderByProductGroupingDto);
            } else {
                FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto = new FactoryOrderByProductGroupingDto();
                ProductItem productItem = new ProductItem();
                productItem.setColorList(Lists.newArrayList());
                productItem.setSizeList(Lists.newArrayList());
                productItem.setColorMap(Maps.newLinkedHashMap());
                productItem.setSizeMap(Maps.newLinkedHashMap());
                ColorDto colorAll = new ColorDto();
                colorAll.setColorName("全部");
                colorAll.setNum(0);
                productItem.getColorMap().put("全部", colorAll);
                SizeDto sizeDto = new SizeDto();
                sizeDto.setSizeName("全部");
                sizeDto.setNum(0);
                productItem.getSizeMap().put("全部", sizeDto);
                factoryOrderByProductGroupingDto.setProductItem(productItem);
                factoryOrderByProductGroupingDto.setNum(0);
                this.assemblyProductionLine(productDtoMap, factoryOrder, factoryOrderByProductGroupingDto);
                productDtoMap.put(factoryOrder.getProductId(), factoryOrderByProductGroupingDto);
            }

        }

        for (ProductDto productDto : productDtoList) {
            if (productDtoParMap.containsKey(productDto.getParentId())) {
                FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDtoPar = productDtoParMap.get(productDto.getParentId());
                ProductItem productItemPar = factoryOrderByProductGroupingDtoPar.getProductItem();
                Map<String, ColorDto> colorDtoMapPar = productItemPar.getColorMap();
                Map<String, SizeDto> sizeDtoMapPar = productItemPar.getSizeMap();
                FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto = productDtoMap.get(productDto.getId());
                //聚合
                ProductItem productItem = factoryOrderByProductGroupingDto.getProductItem();
                Map<String, ColorDto> colorDtoMap = productItem.getColorMap();
                for (String key : colorDtoMap.keySet()) {
                    ColorDto colorDto = colorDtoMap.get(key);
                    if (colorDtoMapPar.containsKey(key)) {
                        ColorDto colorDtoPar = colorDtoMapPar.get(key);
                        colorDtoPar.setNum(colorDtoPar.getNum() + colorDto.getNum());
                        colorDtoMapPar.put(colorDtoPar.getColorName(), colorDtoPar);
                    } else {
                        colorDtoMapPar.put(colorDto.getColorName(), colorDto);
                    }

                }
                Map<String, SizeDto> sizeDtoMap = productItem.getSizeMap();
                for (String key : sizeDtoMap.keySet()) {
                    SizeDto sizeDto = sizeDtoMap.get(key);
                    if (sizeDtoMapPar.containsKey(key)) {
                        SizeDto sizeDtoPar = sizeDtoMapPar.get(key);
                        sizeDtoPar.setNum(sizeDtoPar.getNum() + sizeDto.getNum());
                        sizeDtoMapPar.put(sizeDtoPar.getSizeName(), sizeDtoPar);
                    } else {
                        sizeDtoMapPar.put(sizeDto.getSizeName(), sizeDto);
                    }
                }

                factoryOrderByProductGroupingDtoPar.setNum(factoryOrderByProductGroupingDtoPar.getNum() + factoryOrderByProductGroupingDto.getNum());
                productDtoParMap.put(productDto.getParentId(), factoryOrderByProductGroupingDtoPar);
            } else {
                FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto = productDtoMap.get(productDto.getId());
                factoryOrderByProductGroupingDto.setId(productDto.getParentId());
                productDtoParMap.put(productDto.getParentId(), factoryOrderByProductGroupingDto);
            }
            parentIdsList.add(productDto.getParentId());
        }
        //查询母体名称
        List<String> parentIdsStrList = parentIdsList.stream().map(String::valueOf).collect(Collectors.toList());
        String parentIds = String.join(",", parentIdsStrList);
        List<ProductDto> productParDtoList = this.productFeign.findByIdsAndStatus(parentIds, null, null);
        Map<Long, ProductDto> productParDtoMap = productParDtoList.stream().collect(Collectors.toMap(ProductDto::getId, ProductDto -> ProductDto));
        productGroupingDtoList = new ArrayList<FactoryOrderByProductGroupingDto>(productDtoParMap.values());

        for (FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto : productGroupingDtoList) {
            ProductDto dto = productParDtoMap.get(factoryOrderByProductGroupingDto.getId());
            factoryOrderByProductGroupingDto.setProductName(dto.getName());
            ProductItem productItem = factoryOrderByProductGroupingDto.getProductItem();
            productItem.setColorList(new ArrayList<ColorDto>(productItem.getColorMap().values()));
            productItem.setSizeList(new ArrayList<SizeDto>(productItem.getSizeMap().values()));
        }

        return productGroupingDtoList;
    }

    private void timeToLongProcessing(FactoryOrderQueryParameters factoryOrderQueryParameters) {
        //确认时间
        if (StringUtils.isNotEmpty(factoryOrderQueryParameters.getConfirmStartTime())) {
            factoryOrderQueryParameters.setConfirmStartTimeLong(DateUtil.StringToLong(factoryOrderQueryParameters.getConfirmStartTime()));
        }
        if (StringUtils.isNotEmpty(factoryOrderQueryParameters.getConfirmEndTime())) {
            factoryOrderQueryParameters.setConfirmEndTimeLong(DateUtil.StringToLong(factoryOrderQueryParameters.getConfirmEndTime()));
        }
        //生产时间
        if (StringUtils.isNotEmpty(factoryOrderQueryParameters.getProduceStartTime())) {
            factoryOrderQueryParameters.setProduceStartTimeLong(DateUtil.StringToLong(factoryOrderQueryParameters.getProduceStartTime()));
        }
        if (StringUtils.isNotEmpty(factoryOrderQueryParameters.getProduceEndTime())) {
            factoryOrderQueryParameters.setProduceEndTimeLong(DateUtil.StringToLong(factoryOrderQueryParameters.getProduceEndTime()));
        }
        //发货时间
        if (StringUtils.isNotEmpty(factoryOrderQueryParameters.getDeliverStartTime())) {
            factoryOrderQueryParameters.setDeliverStartTimeLong(DateUtil.StringToLong(factoryOrderQueryParameters.getDeliverStartTime()));
        }
        if (StringUtils.isNotEmpty(factoryOrderQueryParameters.getDeliverEndTime())) {
            factoryOrderQueryParameters.setDeliverEndTimeLong(DateUtil.StringToLong(factoryOrderQueryParameters.getDeliverEndTime()));
        }
    }


    private void assemblyProductionLine(Map<Long, FactoryOrderByProductGroupingDto> productDtoMap, FactoryOrder factoryOrder, FactoryOrderByProductGroupingDto factoryOrderByProductGroupingDto) {

        ProductItem productItem = factoryOrderByProductGroupingDto.getProductItem();
        Map<String, ColorDto> colorDtoMap = productItem.getColorMap();
        Map<String, SizeDto> sizeDtoMap = productItem.getSizeMap();

        String colorName = factoryOrder.getProductColorName();
        String sizeName = factoryOrder.getProductSize();
        //统计数
        int num = factoryOrder.getNum();
        //颜色
        if (colorDtoMap.containsKey(colorName)) {
            ColorDto colorDto = colorDtoMap.get(colorName);
            colorDto.setNum(colorDto.getNum() + num);
            colorDtoMap.put(colorName, colorDto);
            ColorDto colorDtoAll = colorDtoMap.get("全部");
            colorDtoAll.setNum(colorDtoAll.getNum() + num);
            colorDtoMap.put("全部", colorDtoAll);
        } else {
            ColorDto colorDto = new ColorDto();
            colorDto.setColorName(colorName);
            colorDto.setColorBlock(factoryOrder.getProdutColorBlock());
            colorDto.setNum(num);
            colorDto.setProductId(factoryOrder.getProductId());
            colorDtoMap.put(colorName, colorDto);
            ColorDto colorDtoAll = colorDtoMap.get("全部");
            colorDtoAll.setNum(colorDtoAll.getNum() + num);
            colorDtoMap.put("全部", colorDtoAll);
        }
        //尺寸
        if (sizeDtoMap.containsKey(sizeName)) {
            SizeDto sizeDto = sizeDtoMap.get(sizeName);
            sizeDto.setNum(sizeDto.getNum() + num);
            sizeDtoMap.put(sizeName, sizeDto);
            SizeDto sizeDtoAll = sizeDtoMap.get("全部");
            sizeDtoAll.setNum(sizeDtoAll.getNum() + num);
            sizeDtoMap.put("全部", sizeDtoAll);
        } else {
            SizeDto sizeDto = new SizeDto();
            sizeDto.setSizeName(sizeName);
            sizeDto.setNum(num);
            sizeDto.setProductId(factoryOrder.getProductId());
            sizeDtoMap.put(sizeName, sizeDto);
            SizeDto sizeDtoAll = sizeDtoMap.get("全部");
            sizeDtoAll.setNum(sizeDtoAll.getNum() + num);
            sizeDtoMap.put("全部", sizeDtoAll);
        }
        productItem.setColorMap(colorDtoMap);
        productItem.setSizeMap(sizeDtoMap);
        factoryOrderByProductGroupingDto.setNum(factoryOrderByProductGroupingDto.getNum() + num);
        productDtoMap.put(factoryOrder.getProductId(), factoryOrderByProductGroupingDto);

    }

    /***
     *按生产线归类
     * <AUTHOR>
     * @date 2020/9/22 17:07
     */
    @Deprecated
    public List<FactoryOrderByProductionLineGroupingDto> productionLineGrouping(FactoryOrderQueryParameters factoryOrderQueryParameters) {
        List<FactoryOrderByProductionLineGroupingDto> productGroupingDtoList = Lists.newArrayList();
        Long factoryId = factoryOrderQueryParameters.getFactoryId();

        //时间处理
        this.timeToLongProcessing(factoryOrderQueryParameters);
        //预警条件处理
        this.getEarlyWarning(factoryOrderQueryParameters);
        //预警条件处理
        if (CollectionUtil.isNotEmpty(factoryOrderQueryParameters.getEarlyWarningIds())) {
            this.getOrEarlyWarning(factoryOrderQueryParameters);
        } else {
            this.getEarlyWarning(factoryOrderQueryParameters);
        }
        appendSerialNumberLeftJoinSql(factoryOrderQueryParameters);
        //类型
        this.getFilter(factoryOrderQueryParameters);

        List<ProductionLineGroupBo> factoryOrderList = this.factoryOrderMapper.groupingProductionLine(factoryOrderQueryParameters);
        if (CollectionUtil.isEmpty(factoryOrderList)) {
            return Lists.newArrayList();
        }

        List<Long> productionLineIds = factoryOrderList.stream().map(ProductionLineGroupBo::getProductionLineId).collect(Collectors.toList());
        List<ProductionLineRespDto> lineRespDtos = this.productionLineService.listByIds(factoryId, productionLineIds);
        Map<Long, ProductionLineRespDto> productLineMap = lineRespDtos.stream().collect(Collectors.toMap(ProductionLineRespDto::getId, ProductionLineItem -> ProductionLineItem));
        //默认生产线
        ProductionLineRespDto productionLineDto = this.productionLineService.defaultProductionLine(factoryId);

        Map<Long, FactoryOrderByProductionLineGroupingDto> groupingDtoMap = Maps.newConcurrentMap();
        for (ProductionLineGroupBo factoryOrder : factoryOrderList) {
            FactoryOrderByProductionLineGroupingDto factoryOrderByProductionLineGroupingDto = new FactoryOrderByProductionLineGroupingDto();
            if (productLineMap.containsKey(factoryOrder.getProductionLineId())) {
                ProductionLineRespDto productionLineRespDto = productLineMap.get(factoryOrder.getProductionLineId());
                this.assemblyProductionLineGrouping(groupingDtoMap, productionLineRespDto, factoryOrder, factoryOrderByProductionLineGroupingDto, productionLineRespDto.getId());
            } else {
                this.assemblyProductionLineGrouping(groupingDtoMap, productionLineDto, factoryOrder, factoryOrderByProductionLineGroupingDto, productionLineDto.getId());
            }
        }

        productGroupingDtoList = new ArrayList<FactoryOrderByProductionLineGroupingDto>(groupingDtoMap.values());

        return productGroupingDtoList;
    }


    public FactoryOrderListDto<FactoryOrderRespDto> page(FactoryOrderQueryParameters params) {
        FactoryOrderListDto<FactoryOrderRespDto> result = new FactoryOrderListDto<>();
        result.setPage(params.getPage());
        result.setSize(params.getSize());

        //时间处理
        this.timeToLongProcessing(params);
        //预警条件处理
        if (CollectionUtil.isNotEmpty(params.getEarlyWarningIds())) {
            this.getOrEarlyWarning(params);
        } else {
            this.getEarlyWarning(params);
        }
        appendSerialNumberLeftJoinSql(params);

        //类型
        this.getFilter(params);


        int start = (params.getPage() - 1) * params.getSize();
        params.setStart(start);
        long total = this.factoryOrderMapper.count(params);
        result.setTotal(total);
        if (total < 1) {
            return result;
        }
        long totalProduct = this.factoryOrderMapper.sumProductNum(params);

        String sort = params.getSort();

        if (StringUtils.isNotBlank(sort)) {
            List<SortRule> sortRuleList = SortUtil.getSortRuleList(sort);
            List<String> sortArray = Lists.newArrayList();
            for (SortRule sortRule : sortRuleList) {
                String sortColumen = sortRule.getColumnName();
                // id+0处理
                if (sortColumen.equals("id")) {
                    sortColumen += "+0";
                }

                if (sortRule.getOrderByDesc().equals(true)) {
                    sortArray.add("t." + sortColumen + " DESC");
                } else {
                    sortArray.add("t." + sortColumen + " ASC");
                }
            }

            params.setSort(sortArray.stream().collect(Collectors.joining(",")));
        }

        List<FactoryOrder> factoryOrderList = this.factoryOrderMapper.page(params);
        if (CollectionUtil.isNotEmpty(factoryOrderList)) {
            List<FactoryOrderRespDto> records = this.packageFactoryOrders(factoryOrderList, params.getFactoryId());
            this.formatRejectOrLoseCancel(records);
            result.setRecords(records);
        }
        result.setCountOrder(total);
        result.setCountProduct(totalProduct);

        return result;
    }

    public FactoryOrderListDto<FactoryOrderRespDto> esPage(FactoryOrderQueryParameters params) {
        FactoryOrderListDto<FactoryOrderRespDto> result = new FactoryOrderListDto<>();
        EsChainQueryWrapper<FactoryOrderEsPO> wrapper = this.factoryOrderEsQueryService.genFactoryEndEsQueryWrapper(params);
        EsResponse<FactoryOrderEsPO> page = wrapper.page(params.getPage(), params.getSize());
        wrapper.addAggregationBuilder(AggregationBuilders.sum("num").field("num"));
        Aggregations aggregation = wrapper.aggregation();
        Aggregation num = aggregation.get("num");

        if (num != null) {
            double numValue = ((Sum) num).getValue();
            result.setCountProduct((long) numValue);
        }

        List<FactoryOrderEsPO> list = page.getList();
        if (CollectionUtil.isNotEmpty(list)) {
            //es查出来之后 需要根据es的排序
            List<Long> factoryOrderIdList = list.stream().map(FactoryOrderEsPO::getId).collect(Collectors.toList());
            List<FactoryOrder> unSortFactoryList = this.listByIds(factoryOrderIdList);
            List<FactoryOrder> factoryOrderList = this.factoryOrderListSortByEsList(factoryOrderIdList, unSortFactoryList);
            List<FactoryOrderRespDto> records = this.packageFactoryOrders(factoryOrderList, params.getFactoryId());
            this.formatDistributorTenantName(params.getTenantId(), records);
            this.formatRejectOrLoseCancel(records);
            result.setRecords(records);
        }
        result.setCountOrder(page.getTotal());
        result.setTotal(page.getTotal());
        return result;
    }

    private static void appendSerialNumberLeftJoinSql(FactoryOrderQueryParameters params) {
        if (StrUtil.isNotBlank(params.getSerialNumber())) {
            String leftJoinOrderSql = StrUtil.isNotBlank(params.getLeftJoinOrderSql()) ? params.getLeftJoinOrderSql() : "";
            String serialNumberLeftJoinSql = " left join sds_mc_order.`factory_order_serial` fos on fos.factory_order_id = t.id ";
            leftJoinOrderSql = leftJoinOrderSql + serialNumberLeftJoinSql;
            params.setLeftJoinOrderSql(leftJoinOrderSql);
        }
    }

    private void formatRejectOrLoseCancel(List<FactoryOrderRespDto> records) {
        for (FactoryOrderRespDto resp : records) {
            Integer refuseType = resp.getRefuseType();
            if (NumberUtils.greaterZero(refuseType)) {
                if (refuseType.equals(QcStatusEnum.REJECT_REFUND.code)) {
                    resp.setRejectCancel(resp.getRefuseNum());
                    //如果是驳回取消或者漏件取消的话，总数 == num + refuseNum
                    //驳回 漏件则总数 == num
                    resp.setNum(resp.getNum() + resp.getRefuseNum());
                } else if (refuseType.equals(QcStatusEnum.LESS_REFUND.code)) {
                    resp.setLoseCancel(resp.getRefuseNum());
                    resp.setNum(resp.getNum() + resp.getRefuseNum());
                }
            }
        }
    }

    private static String buildAfterServiceRefuseTypeSql(String afterServiceRefuseType) {
        if (CharSequenceUtil.isBlank(afterServiceRefuseType)) {
            return "";
        }
        List<Long> filters = StringUtils.stringToLongList(afterServiceRefuseType);
        List<String> filterSqls = Lists.newArrayList();
        if (filters.contains(FactoryOrderFilterTypeConstant.FILTER_AFTER_SERVICE)) {
            filterSqls.add("  t.be_after_service_order = 1");
        }
        if (filters.contains(FactoryOrderFilterTypeConstant.FILTER_RESEND)) {
            filterSqls.add("  t.refuse_type = 2");
        }
        if (filters.contains(FactoryOrderFilterTypeConstant.FILTER_REJECT)) {
            filterSqls.add("  t.refuse_type = 1");
        }
        String filterSql = "";
        if (CollUtil.isNotEmpty(filterSqls)) {
            filterSql = StringUtils.join(filterSqls, " or ");
        }
        return filterSql;
    }


    public FactoryOrderRespDto detail(Long id, Long factoryId) {
        FactoryOrder factoryOrder = this.factoryOrderMapper.selectById(id);
        Assert.validateNull(factoryOrder, "订单不存在");
        Assert.validateBool(factoryOrder.getFactoryId().equals(factoryId), "订单不存在");
        return this.packageFactoryOrders(Lists.newArrayList(factoryOrder), factoryOrder.getFactoryId()).get(0);
    }


    public List<FactoryOrderRespDto> allList(FactoryOrderQueryParameters params) {
        // 或 预警
        this.getOrEarlyWarning(params);
        // 且 售后类型、预警
        getFilter(params);
        appendSerialNumberLeftJoinSql(params);
        List<FactoryOrder> factoryOrderList = this.factoryOrderMapper.all(params);
        List<FactoryOrderRespDto> records = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(factoryOrderList)) {
            records = RelationsBinder.convertAndBind(factoryOrderList, FactoryOrderRespDto.class
                    , "orderItem,orderEarlyWarnings,outPaymentDate,outExpressDate,outHarvestDate,outConfirmDate,outDate,manuscriptFeedbackTime,manuscriptFeedbackStatus");
        }
        return records;
    }

    public void formatShipmentBelongingTenantName(List<OrderRespDto> orders) {
        if (CollUtil.isEmpty(orders)) {
            return;
        }
        Set<Long> tenantIds = Sets.newHashSet();
        List<Long> orderIds = orders.stream().map(OrderRespDto::getId).collect(Collectors.toList());

        List<OrderAmount> orderAmounts = this.orderAmountService.findByIds(orderIds);
        tenantIds.addAll(orderAmounts.stream().map(OrderAmount::getProductTenantId).collect(Collectors.toSet()));
        tenantIds.addAll(orderAmounts.stream().map(OrderAmount::getTenantId).collect(Collectors.toSet()));

        Map<Long, OrderAmount> amountMap = orderAmounts.stream().collect(Collectors.toMap(OrderAmount::getId, Function.identity()));
        Map<Long, TenantLogisticsOrderRespDto> tenantLogisticsOrderMap = com.beust.jcommander.internal.Maps.newHashMap();
        //分销订单
        List<OrderRespDto> orderOrderTenants = orders.stream().filter(o -> DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(o.getLogisticsSource())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(orderOrderTenants)) {
            List<Long> orderOrderTenantIds = orderOrderTenants.stream().map(OrderRespDto::getId).collect(Collectors.toList());
            List<TenantLogisticsOrderRespDto> tenantLogisticsOrders = this.tenantLogisticsOrderService.getByIds(orderOrderTenantIds);
            tenantLogisticsOrderMap = tenantLogisticsOrders.stream().collect(Collectors.toMap(TenantLogisticsOrderRespDto::getId, Function.identity()));
        }
        List<TenantRespDto> tenants = this.tenantFeign.getByIds(new ArrayList<>(tenantIds));
        Map<Long, String> tenantIdKeyNameMap = tenants.stream().collect(Collectors.toMap(TenantRespDto::getId, TenantRespDto::getName));
        for (OrderRespDto order : orders) {
            OrderAmount amountAmount = amountMap.get(order.getId());
            if (amountAmount == null) {
                continue;
            }
            order.setShipmentBelongingTenantName(tenantIdKeyNameMap.get(amountAmount.getProductTenantId()));
            if (DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(order.getLogisticsSource())) {
                TenantLogisticsOrderRespDto logisticsOrder = tenantLogisticsOrderMap.get(order.getId());
                if (logisticsOrder != null && TenantLogisticsConstant.ShipmentPlaceTypeEnum.TENANT.name().equals(logisticsOrder.getShipmentPlaceType())) {
                    order.setShipmentBelongingTenantName(tenantIdKeyNameMap.get(amountAmount.getTenantId()));
                }
            }
        }
    }


    public List<FactoryOrderRespDto> packageFactoryOrders(List<FactoryOrder> factoryOrderList, Long factoryId) {
        // 查询产品
        List<Long> productIds = factoryOrderList.stream().distinct().map(FactoryOrder::getProductId).collect(Collectors.toList());
        List<ProductDto> productDtos = this.productFeign.findByIds(productIds, "texture,prototypeType");
        Map<Long, ProductDto> productMap = productDtos.stream().collect(Collectors.toMap(ProductDto::getId, entity -> entity));
        // 查询发货仓信息
        Set<Long> issuingBayIds = factoryOrderList.stream().map(FactoryOrder::getIssuingBayId).collect(Collectors.toSet());
        List<IssuingBayRespDto> issuingbays = this.issuingBayFeign.findByIds("", issuingBayIds);
        Map<Long, IssuingBayRespDto> issuingbayMap = issuingbays.stream().collect(Collectors.toMap(IssuingBayRespDto::getId, entity -> entity));
        // 转移单检索封装
        List<Long> factoryOrderIds = factoryOrderList.stream().map(FactoryOrder::getId).collect(Collectors.toList());
        List<OrderItemTransferHistoryDto> orderItemTransferHistoryDtos = this.orderItemTransferHistoryService.findByNewFactoryOrderIds(factoryOrderIds, "newFactoryOrderId,num,no,transferType");
        Map<Long, OrderItemTransferHistoryDto> orderItemTransferHistoryDtoMap = orderItemTransferHistoryDtos.stream().collect(Collectors.toMap(OrderItemTransferHistoryDto::getNewFactoryOrderId, Function.identity()));

        //供应关系和子单产能类型
        Set<Long> orderItemIds = factoryOrderList.stream().map(FactoryOrder::getOrderItemId).collect(Collectors.toSet());
        List<ProductSupplyDTO> productSupplies = this.productSupplyFeign.findProductSupplyListByFactoryId(factoryId);

        Map<String, ProductSupplyDTO> productSupplyMaps = productSupplies.stream()
                .collect(Collectors.toMap(p -> ProductSupplyDTO.getVariantIdAndSupplyChainTypeKey(p.getProductId(), p.getSupplyChainType()), Function.identity(), (a, b) -> a));
        List<OrderItemSupplyChainDTO> orderItemSupplyChainServiceByIds = this.orderItemSupplyChainService.findByIds(orderItemIds);
        Map<Long, String> orderIteKeymSupplyChainMap = orderItemSupplyChainServiceByIds.stream().collect(Collectors.toMap(OrderItemSupplyChainDTO::getId, OrderItemSupplyChainDTO::getSupplyType));

        List<FactoryOrderRespDto> records = RelationsBinder.convertAndBind(factoryOrderList, FactoryOrderRespDto.class, "totalPrice,price,printStatus,downStatus,printDate,downDate,manuscriptFeedbackTime,confirmTime,status,productName,orderItem,remarks,orderEarlyWarnings,outPaymentDate,outExpressDate,outHarvestDate,outConfirmDate,outDate,manuscriptFeedbackTime,order,requireManuscript,tenantId");
        // 查询供销产品
        List<Long> factoryOrders = records.stream().map(FactoryOrderRespDto::getId).collect(Collectors.toList());
        List<OrderRemark> remarks = this.orderRemarkService.getSortedListByOrderIdsAndType(factoryOrders, OrderRemarkTypeEnum.FACTORY_ORDER_TYPE);
        Map<Long, List<OrderRemark>> remarkMaps = remarks.stream().collect(Collectors.groupingBy(OrderRemark::getOrderId));
        Set<String> merchantOrderNos = factoryOrderList.stream().map(FactoryOrder::getMerchantOrderNo).collect(Collectors.toSet());
        List<FactoryOrder> allFactoryOrderList = this.findByMerchantOrderOrNos(merchantOrderNos);
        Map<String, List<FactoryOrder>> allFactoryOrderMap = allFactoryOrderList.stream().collect(Collectors.groupingBy(FactoryOrder::getMerchantOrderNo));

        records.forEach(factoryOrderInfo -> {
            ProductDto product = productMap.get(factoryOrderInfo.getProductId());
            factoryOrderInfo.setProduct(product);
            factoryOrderInfo.setCategories(product.getCategorys());
            factoryOrderInfo.setIssuingBay(issuingbayMap.get(factoryOrderInfo.getIssuingBayId()));
            if (StringUtils.isNotBlank(factoryOrderInfo.getOrderItem().getImgs())) {
                factoryOrderInfo.setImageUrls(JSON.parseArray(factoryOrderInfo.getOrderItem().getImgs(), String.class));
            }
            if (CollectionUtil.isNotEmpty(orderItemTransferHistoryDtoMap)) {
                OrderItemTransferHistoryDto orderItemTransferHistoryDto = orderItemTransferHistoryDtoMap.get(factoryOrderInfo.getId());
                if (orderItemTransferHistoryDto != null) {
                    factoryOrderInfo.setTransferType(orderItemTransferHistoryDto.getTransferType());
                    factoryOrderInfo.setLastRefoundOrderProductNum(orderItemTransferHistoryDto.getNum());
                    factoryOrderInfo.setLastRefoundOrderOriginalAsNo(orderItemTransferHistoryDto.getNo());
                }
            }

            List<OrderRemark> factoryOrderRemarks = remarkMaps.get(factoryOrderInfo.getId());
            if (CollectionUtil.isNotEmpty(factoryOrderRemarks)) {
                factoryOrderInfo.setRemarks(RelationsBinder.convertAndBind(factoryOrderRemarks, OrderRemarkRespDto.class));
                for (OrderRemarkRespDto remark : factoryOrderInfo.getRemarks()) {
                    remark.setGmtCreatedTime(DateUtil.longToString(remark.getCreatedTime()));
                }
            }

            if (CollectionUtil.isNotEmpty(productSupplyMaps)) {
                String chainSupplyType = orderIteKeymSupplyChainMap.get(factoryOrderInfo.getOrderItem().getId());
                chainSupplyType = chainSupplyType == null ? SupplyChainTypeEnum.ONE_PIECE.name() : chainSupplyType;
                ProductSupplyDTO productSupplyDTO = productSupplyMaps.get(ProductSupplyDTO.getVariantIdAndSupplyChainTypeKey(factoryOrderInfo.getProductId(), chainSupplyType));
                if (productSupplyDTO != null) {
                    factoryOrderInfo.setProductSupplyCode(productSupplyDTO.getCode());
                }
            }

            List<FactoryOrder> allFactoryOrders = allFactoryOrderMap.get(factoryOrderInfo.getMerchantOrderNo());
            if (CollectionUtils.isNotEmpty(allFactoryOrders)) {
                int validNum = 0;
                for (FactoryOrder factoryOrder : allFactoryOrders) {
                    if (!factoryOrder.getStatus().equals(FactoryOrderStatusEnum.CANCEL.getStatus())) {
                        validNum++;
                    }
                }
                factoryOrderInfo.setSameOrderValidNum(validNum);
            }


            factoryOrderInfo.setOrderHistorys(this.getStatusHistory(factoryOrderInfo));

            factoryOrderInfo.setPrintStatus(factoryOrderInfo.getPrintStatus().equals(PrintTypeEnum.PRINT.getValue()) ? AutoTypeEnum.AUTO_Y.getValue() : AutoTypeEnum.AUTO_N.getValue());
            factoryOrderInfo.setDownStatus(factoryOrderInfo.getDownStatus().equals(DownTypeEnum.DOWN.getValue()) ? AutoTypeEnum.AUTO_Y.getValue() : AutoTypeEnum.AUTO_N.getValue());


        });

        // 封装任务相关功能
        this.packageFactoryTask(factoryId, records);
        // 告警信息封装
        this.addWarning(records);
        // 休假时间封装
//        packageProductHolidayByProductIds(productIds, records);


        return records;
    }


    private void packageFactoryTask(Long factoryId, List<FactoryOrderRespDto> records) {
        Map<Long, FactoryOrderRespDto> factoryOrderMap = records.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        List<FactoryTaskOrderRel> factoryTaskOrderRels = this.factoryTaskOrderRelService.findByOrderIds(factoryId, factoryOrderMap.keySet());
        Set<Long> factoryTaskOrderRelIds = factoryTaskOrderRels.stream().map(FactoryTaskOrderRel::getTaskId).collect(Collectors.toSet());

        // 计划id获取计划任务
        List<FactoryTask> factoryTasks = this.factoryTaskService.findByIds(factoryId, factoryTaskOrderRelIds);
        Map<Long, FactoryTask> factoryTaskMap = factoryTasks.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        factoryTaskOrderRels.forEach(relInfo -> {
            FactoryOrderRespDto factoryOrder = factoryOrderMap.get(relInfo.getFactoryOrderId());
            FactoryTask factoryTask = factoryTaskMap.get(relInfo.getTaskId());
            if (factoryOrder != null && factoryTask != null) {
                factoryOrder.setFactoryTaskName(factoryTask.getName());
                factoryOrder.setFactoryTaskNo(factoryTask.getNo());
            }
        });


    }
    public Map<Long, FactoryTaskRespDto> getFactoryTask(FactoryOrderTaskReqDto dto) {
        if(CollUtil.isEmpty(dto.getFactoryOrderIds())){
            return Collections.emptyMap();
        }
        List<FactoryTaskOrderRel> factoryTaskOrderRels = this.factoryTaskOrderRelService.findByOrderIds(dto.getFactoryId(), dto.getFactoryOrderIds());
        if(CollUtil.isEmpty(factoryTaskOrderRels)){
            return Collections.emptyMap();
        }
        Map<Long, Long> factoryOrderIdTaskIdMap = ListUtil.toMap(FactoryTaskOrderRel::getFactoryOrderId, FactoryTaskOrderRel::getTaskId, factoryTaskOrderRels);
        Set<Long> taskIds = factoryTaskOrderRels.stream().map(FactoryTaskOrderRel::getTaskId).collect(Collectors.toSet());

        // 计划id获取计划任务
        List<FactoryTask> factoryTasks = this.factoryTaskService.findByIds(dto.getFactoryId(), taskIds);
        if(CollUtil.isEmpty(factoryTasks)){
            return Collections.emptyMap();
        }
        Map<Long, FactoryTask> factoryTaskMap = ListUtil.toMap(FactoryTask::getId, factoryTasks);

        Map<Long,FactoryTaskRespDto> map=Maps.newHashMap();
        for (Long factoryOrderId : dto.getFactoryOrderIds()) {
            Long taskId = factoryOrderIdTaskIdMap.get(factoryOrderId);
            if(!NumberUtils.greaterZero(taskId)){
                continue;
            }
            FactoryTask factoryTask = factoryTaskMap.get(taskId);
            if(null==factoryTask){
                continue;
            }
            FactoryTaskRespDto factoryTaskRespDto = BeanUtil.copyProperties(factoryTask, FactoryTaskRespDto.class);
            map.put(factoryOrderId,factoryTaskRespDto);
        }
        return map;
    }

    // 添加告警信息
    public void addWarning(List<FactoryOrderRespDto> orders) {
        List<OrderEarlyWarning> orderEarlyWarnings = this.orderEarlyWarningService.findListByOrderItemIds(orders
                .stream()
                .map(FactoryOrderRespDto::getOrderItemId)
                .collect(Collectors.toList()));

        Map<Long, EarlyWarningTypeDto> earlyWarningTypeMap = this.earlywarningTypeService.getAllEarlyWarningTypeMap(orderEarlyWarnings
                .stream()
                .map(OrderEarlyWarning::getEarlyWarningId)
                .collect(Collectors.toList()));

        for (FactoryOrderRespDto factoryOrder : orders) {
            List<OrderEarlyWarningDto> warnings = factoryOrder.getOrderEarlyWarnings();
            if (warnings == null) {
                warnings = Lists.newArrayList();
            }
            OrderEarlyWarningDto warning;
            // 订单状态判断
            if (!Arrays.asList(1, 2, 3, 4, 5).contains(factoryOrder.getStatus())) {
                continue;
            }
            long now = System.currentTimeMillis();

            if (factoryOrder.getStatus() == FactoryOrderStatusEnum.UNCONFIRMED.getStatus() && now > factoryOrder.getOutPaymentDate().getTime()) {
                LocalDateTime outDate = LocalDateTimeUtil.of(factoryOrder.getOutPaymentDate().getTime()).minusSeconds(OrderEarlyWarningConstant.ONE_DAY_SEC);
                warning = this.generateOrderEarlyWarning(OutTypeEnum.NO_ANSWER_ORDER.getCode(), outDate, EarlyWarningTypeConstant.EarlyWarningType.NO_ANSWER_24);
                warnings.add(0, warning);
            }
            if (factoryOrder.getStatus() == FactoryOrderStatusEnum.SHIPMENTS.getStatus() && now > factoryOrder.getOutExpressDate().getTime()) {
                LocalDateTime outDate = LocalDateTimeUtil.of(factoryOrder.getOutExpressDate().getTime()).minusSeconds(OrderEarlyWarningConstant.ONE_DAY_SEC);
                warning = this.generateOrderEarlyWarning(OutTypeEnum.DELIVERY_OUT_DATE.getCode(), outDate, EarlyWarningTypeConstant.EarlyWarningType.UNSHIPPED_24);
                warnings.add(0, warning);
            }

            if (factoryOrder.getStatus() == FactoryOrderStatusEnum.RECEIVE_THINGS.getStatus() && now > factoryOrder.getOutHarvestDate().getTime()) {
                LocalDateTime outDate = LocalDateTimeUtil.of(factoryOrder.getOutHarvestDate().getTime()).minusSeconds(OrderEarlyWarningConstant.HOUR_36H_SEC);
                warning = this.generateOrderEarlyWarning(OutTypeEnum.TAKE_DELIVERY_OUT_DATE.getCode(), outDate, EarlyWarningTypeConstant.EarlyWarningType.NO_RECEIVE_24);
                warnings.add(0, warning);
            }

            if (factoryOrder.getStatus() == FactoryOrderStatusEnum.PASS_GC.getStatus() && now > factoryOrder.getOutConfirmDate().getTime()) {
                LocalDateTime outDate = LocalDateTimeUtil.of(factoryOrder.getOutConfirmDate().getTime()).minusSeconds(OrderEarlyWarningConstant.HOUR_18H_SEC);
                warning = this.generateOrderEarlyWarning(OutTypeEnum.NOT_THE_QUALITY.getCode(), outDate, EarlyWarningTypeConstant.EarlyWarningType.NO_QUALITY_24);
                warnings.add(0, warning);
            }

            Long outCycleMsec = null;
            if (factoryOrder.getOutCycleDate() != null) {
                outCycleMsec = factoryOrder.getOutCycleDate().getTime();
            }

            if (factoryOrder.getStatus() < FactoryOrderStatusEnum.ACCOMPLISH.getStatus()
                    && outCycleMsec != null
                    && now > outCycleMsec - OrderEarlyWarningConstant.ONE_DAY_MSEC) {
                boolean isAdd = true;
                if (warnings.size() > 0) {
                    for (OrderEarlyWarningDto orderWaring : warnings) {
                        if (Arrays.asList(3L, 4L, 5L, 6L, 7L).contains(orderWaring.getEarlyWarningId())) {
                            isAdd = false;
                            break;
                        }
                    }
                }

                if (isAdd) {
                    if (outCycleMsec - OrderEarlyWarningConstant.ONE_DAY_MSEC < now && now < outCycleMsec) {
                        isAdd = false;
                        warning = this.generateOrderEarlyWarning(OutTypeEnum.CLOSE_PRODUCTION_CYCLE.getCode(), LocalDateTimeUtil.of(outCycleMsec), EarlyWarningTypeConstant.EarlyWarningType.COLESE_24);
                        warnings.add(0, warning);
                    }
                }

                if (now > factoryOrder.getOutDate() && factoryOrder.getStatus() < FactoryOrderStatusEnum.SHIPMENTS.getStatus()) {
                    if (!isAdd) {
                        warnings.remove(0);
                    }
                    warning = this.generateOrderEarlyWarning(OutTypeEnum.OUT_PRODUCTION_CYCLE_NO_DELIVERY.getCode(), LocalDateTimeUtil.of(outCycleMsec), EarlyWarningTypeConstant.EarlyWarningType.OUT_DATE);
                    warnings.add(0, warning);
                }

            }
            for (OrderEarlyWarningDto orderEarlyWarning : warnings) {
                EarlyWarningTypeDto warningType = earlyWarningTypeMap.get(orderEarlyWarning.getEarlyWarningId());
                if (Objects.nonNull(warningType) && Arrays.asList(1L, 3L, 4L, 5L, 6L, 7L).contains(orderEarlyWarning.getEarlyWarningId())) {
                    warningType.setName(warningType.getRemark());
                }
                if (Objects.isNull(warningType)) {
                    throw new BusinessException("没找到预警类型 id = " + orderEarlyWarning.getEarlyWarningId());
                }
                orderEarlyWarning.setEarlyWarningType(warningType);
                if (factoryOrder.getManuscriptFeedbackStatus() == ManuscriptFeedbackStatusConstant.YES) {
                    orderEarlyWarning.setOriginDate(factoryOrder.getManuscriptFeedbackTime());
                    orderEarlyWarning.setFrozen(true);
                }
            }
            factoryOrder.setOrderEarlyWarnings(warnings);

        }
    }

    public OrderEarlyWarningDto generateOrderEarlyWarning(int outType, LocalDateTime outDate, Long earlyWarningTypeId) {
        OrderEarlyWarningDto orderEarlyWarning = new OrderEarlyWarningDto();
        orderEarlyWarning.setEarlyWarningId(earlyWarningTypeId);
        orderEarlyWarning.setOutType(outType);
        orderEarlyWarning.setType(OrderEarlyWarningConstant.AUTO_WARNING);
        orderEarlyWarning.setOutDate(outDate);
        return orderEarlyWarning;
    }

    private void getFilter(FactoryOrderQueryParameters parameters) {
        String afterServiceRefuseTypeSql = buildAfterServiceRefuseTypeSql(parameters.getAfterServiceRefuseType());
        parameters.addFilterSql(afterServiceRefuseTypeSql);

        String andEarlyWarningSql = getEarlyWarningSql(parameters.getAndEarlyWarningIds(), parameters);
        parameters.addFilterSql(andEarlyWarningSql);
    }

    private void getOrEarlyWarning(FactoryOrderQueryParameters parameters) {
        // 或 预警
        String orEarlyWarningSql = getEarlyWarningSql(parameters.getEarlyWarningIds(), parameters);
        if (StrUtil.isNotBlank(orEarlyWarningSql) && Boolean.TRUE.equals(parameters.getEarlyWarnIncludeAfterSale())) {
            // 增加售后类型
            String filterSql = buildAfterServiceRefuseTypeSql("1,2,3");
            if (CharSequenceUtil.isNotBlank(filterSql)) {
                orEarlyWarningSql += " OR ( " + filterSql + " )";
            }
        }
        parameters.setEarlyWarningSql(orEarlyWarningSql);
    }

    private static String getEarlyWarningSql(List<Long> earlyWarningIds, FactoryOrderQueryParameters queryParameters) {
        if (CollectionUtil.isEmpty(earlyWarningIds)) {
            return "";
        }
        String urgenWarningSql = " (o.express_type = 1 and t.status < 6)";
        String resendWarningSql = " (t.be_resend_for_lose = 1 and t.status < 6)";
        String rejectWarningSql = " (t.refuse_type = 1 and t.status < 6)";
        String handleWarningSql = " (t.order_item_id in (select o.id from order_item o LEFT JOIN order_early_warning oew on oew.order_item_id =o.id   where oew.early_warning_id = 10)) ";
        String leftJoinOrder = " left join  `order` o on o.no = t.merchant_order_no ";
        String isNotOverTime = " AND t.out_cycle_date > NOW() "; // 未超期
        String loseWarningSql = " (t.refuse_type = 2 and t.status < 6)";

        String earlyWarningSql = "";
        if (earlyWarningIds.contains(UrgeType.FACTORY_PRODUCE_TIMEOUT.getId())) {
            earlyWarningSql += " OR (UNIX_TIMESTAMP(NOW())*1000> t.out_date and  t.status<6) ";
        }

        if (earlyWarningIds.contains(8L)) {
            earlyWarningSql += " OR (t.out_urge_date< UNIX_TIMESTAMP(NOW())*1000 and UNIX_TIMESTAMP(NOW())*1000<out_date and status<6) OR (t.order_item_id IN (SELECT o.id from order_item o LEFT JOIN order_early_warning oew on oew.order_item_id =o.id   where oew.early_warning_id = 2))";
        }

        if (earlyWarningIds.contains(UrgeType.WARING.getId())) {
            queryParameters.setLeftJoinOrderSql(leftJoinOrder);
            earlyWarningSql += "  OR ( " + urgenWarningSql + " OR " + resendWarningSql + " OR " + handleWarningSql + " ) ";
        }

        if (earlyWarningIds.contains(UrgeType.URGEN_EXPRESS.getId())) {
            queryParameters.setLeftJoinOrderSql(leftJoinOrder);
            earlyWarningSql += "  OR ( " + urgenWarningSql + " ) ";
        }

        if (earlyWarningIds.contains(UrgeType.RESEND.getId())) {
            earlyWarningSql += "  OR ( " + resendWarningSql + " ) ";
        }

        if (earlyWarningIds.contains(UrgeType.REJECT.getId())) {
            earlyWarningSql += "  OR ( " + rejectWarningSql + " ) ";
        }

        if (earlyWarningIds.contains(UrgeType.LOSE.getId())) {
            earlyWarningSql += "  OR ( " + loseWarningSql + " ) ";
        }

        if (earlyWarningIds.contains(UrgeType.NO_TAKE.getId())) {
            earlyWarningSql += " OR ( UNIX_TIMESTAMP(t.out_payment_date)*1000<" + System.currentTimeMillis() + " and t.status =1  ) ";
            earlyWarningSql += isNotOverTime;
        }

        if (earlyWarningIds.contains(UrgeType.NO_SHIP.getId())) {
            earlyWarningSql += " OR (UNIX_TIMESTAMP(t.out_express_date)*1000<" + System.currentTimeMillis() + " and t.status =3  ) ";
        }

        if (earlyWarningIds.contains(UrgeType.NO_HARVEST.getId())) {
            earlyWarningSql += " OR (UNIX_TIMESTAMP( t.out_harvest_date)*1000<" + System.currentTimeMillis() + " and t.status =4) ";
        }
        if (earlyWarningIds.contains(UrgeType.NO_QUALITY_TESTING.getId())) {
            earlyWarningSql += " OR (UNIX_TIMESTAMP(t.out_confirm_date)*1000<" + System.currentTimeMillis() + " and t.status=5) ";
        }

        if (earlyWarningIds.contains(UrgeType.COLSEER_24.getId())) {
            earlyWarningSql += " OR (((UNIX_TIMESTAMP(t.out_payment_date)*1000>UNIX_TIMESTAMP(NOW())*1000 and  t.status=1 ) " +
                    " or (UNIX_TIMESTAMP(t.out_express_date)*1000>UNIX_TIMESTAMP(NOW())*1000 and t.status =3)" +
                    " or (t.status=2)" +
                    " or (UNIX_TIMESTAMP(t.out_harvest_date)*1000>UNIX_TIMESTAMP(NOW())*1000 and t.status =4)" +
                    " or (UNIX_TIMESTAMP(t.out_confirm_date)*1000>UNIX_TIMESTAMP(NOW())*1000 and t.status =5)) " +
                    " and UNIX_TIMESTAMP(t.out_cycle_date)*1000< UNIX_TIMESTAMP(NOW())*1000+" + "1000 * 60 * 60 * 24" +
                    " and UNIX_TIMESTAMP(t.out_cycle_date)*1000>UNIX_TIMESTAMP(NOW())*1000  " +
                    " and t.status<6" +
                    " and t.out_date>UNIX_TIMESTAMP(NOW())*1000)";
        }

        String strEarlyWarningIds = earlyWarningIds.stream().map(Object::toString).collect(Collectors.joining(","));
        earlyWarningSql += " OR (t.order_item_id IN (select o.id from order_item o LEFT JOIN order_early_warning oew on oew.order_item_id =o.id   WHERE oew.early_warning_id in (" + strEarlyWarningIds + "))) ";
        earlyWarningSql = "1!=1  " + earlyWarningSql;
        return earlyWarningSql;
    }

    private void getEarlyWarning(FactoryOrderQueryParameters factoryOrderQueryParameters) {
        String earlyWarningSql = "";
        String leftJoinOrderSql = "";
        Long earlyWarningId = factoryOrderQueryParameters.getEarlyWarningId();
        if (earlyWarningId != null) {
            String urgenWarningSql = " (o.express_type = 1 and t.status < 6)";
            String resendWarningSql = " (be_resend_for_lose = 1 and t.status < 6)";
            String rejectWarningSql = " (refuse_type = 1 and t.status < 6)";
            String loseWarningSql = " (refuse_type = 2 and t.status < 6)";
            String handleWarningSql = " (order_item_id in (select o.id from order_item o left join order_early_warning oew on oew.order_item_id =o.id   where oew.early_warning_id = 10)) ";
            String leftJoinOrder = " left join  `order` o on o.no = t.merchant_order_no ";
            String isNotOverTime = " AND out_cycle_date > NOW() "; // 未超期
            if (earlyWarningId == 1L) {
                earlyWarningSql = "UNIX_TIMESTAMP(NOW())*1000>out_date and  status<6";
            } else if (earlyWarningId == 8L) {
                earlyWarningSql = "(out_urge_date< UNIX_TIMESTAMP(NOW())*1000 and UNIX_TIMESTAMP(NOW())*1000<out_date and status<6) or (order_item_id in (select o.id from order_item o left join order_early_warning oew on oew.order_item_id =o.id   where oew.early_warning_id = 2))";
            } else if (UrgeType.WARING.getId().equals(earlyWarningId)) {
                leftJoinOrderSql = leftJoinOrder;
                earlyWarningSql = urgenWarningSql + " or " + resendWarningSql + " or " + handleWarningSql;
            } else if (UrgeType.URGEN_EXPRESS.getId().equals(earlyWarningId)) {
                leftJoinOrderSql = leftJoinOrder;
                earlyWarningSql = urgenWarningSql;
            } else if (UrgeType.RESEND.getId().equals(earlyWarningId)) {
                earlyWarningSql = resendWarningSql;
            } else if (UrgeType.REJECT.getId().equals(earlyWarningId)) {
                earlyWarningSql = rejectWarningSql;
            } else if (UrgeType.LOSE.getId().equals(earlyWarningId)) {
                earlyWarningSql = loseWarningSql;
            } else if (UrgeType.NO_TAKE.getId().equals(earlyWarningId)) {
                earlyWarningSql = "UNIX_TIMESTAMP(out_payment_date)*1000<" + System.currentTimeMillis() + " and status =1 ";
                earlyWarningSql += isNotOverTime;
            } else if (UrgeType.NO_SHIP.getId().equals(earlyWarningId)) {
                earlyWarningSql = "UNIX_TIMESTAMP(out_express_date)*1000<" + System.currentTimeMillis() + " and status =3 ";
//                earlyWarningSql += isNotOverTime;
            } else if (UrgeType.NO_HARVEST.getId().equals(earlyWarningId)) {
                earlyWarningSql = "UNIX_TIMESTAMP( out_harvest_date)*1000<" + System.currentTimeMillis() + " and status =4 ";
//                earlyWarningSql +=isNotOverTime;
            } else if (UrgeType.NO_QUALITY_TESTING.getId().equals(earlyWarningId)) {
                earlyWarningSql = " UNIX_TIMESTAMP(out_confirm_date)*1000<" + System.currentTimeMillis() + " and status=5 ";
//                earlyWarningSql +=isNotOverTime;
            } else if (UrgeType.COLSEER_24.getId().equals(earlyWarningId)) {
                earlyWarningSql = " ((UNIX_TIMESTAMP(out_payment_date)*1000>UNIX_TIMESTAMP(NOW())*1000 and  status=1 ) " +
                        " or (UNIX_TIMESTAMP(out_express_date)*1000>UNIX_TIMESTAMP(NOW())*1000 and status =3)" +
                        " or (status=2)" +
                        " or (UNIX_TIMESTAMP(out_harvest_date)*1000>UNIX_TIMESTAMP(NOW())*1000 and status =4)" +
                        " or (UNIX_TIMESTAMP(out_confirm_date)*1000>UNIX_TIMESTAMP(NOW())*1000 and status =5)) " +
                        " and UNIX_TIMESTAMP(out_cycle_date)*1000< UNIX_TIMESTAMP(NOW())*1000+" + "1000 * 60 * 60 * 24" +
                        " and UNIX_TIMESTAMP(out_cycle_date)*1000>UNIX_TIMESTAMP(NOW())*1000  " +
                        " and status<6" +
                        " and out_date>UNIX_TIMESTAMP(NOW())*1000";
            } else {
                earlyWarningSql = " (order_item_id in (select o.id from order_i" +
                        "tem o left join order_early_warning oew on oew.order_item_id =o.id   where oew.early_warning_id = " + earlyWarningId + ")) ";
            }
        }
        factoryOrderQueryParameters.setEarlyWarningSql(earlyWarningSql);
        factoryOrderQueryParameters.setLeftJoinOrderSql(leftJoinOrderSql);
    }

    private List<OrderHistoryDto> getStatusHistory(FactoryOrderRespDto factoryOrder) {
        List<OrderHistoryDto> list = Lists.newArrayList();
        if (factoryOrder.getCreateTime() != null) {
            list.add(new OrderHistoryDto(FactoryOrderStatusEnum.UNCONFIRMED.getStatus(), factoryOrder.getCreateTime()));
        }
        if (factoryOrder.getConfirmTime() != null && factoryOrder.getConfirmTime() > 0) {
            list.add(new OrderHistoryDto(FactoryOrderStatusEnum.ACCOMPLISH_PRODUCTION.getStatus(), new Date(factoryOrder.getConfirmTime())));
        }
        if (factoryOrder.getProductTime() != null && factoryOrder.getProductTime() > 0) {
            list.add(new OrderHistoryDto(FactoryOrderStatusEnum.SHIPMENTS.getStatus(), new Date(factoryOrder.getProductTime())));
        }
        if (factoryOrder.getShipTime() != null && factoryOrder.getShipTime() > 0) {
            list.add(new OrderHistoryDto(FactoryOrderStatusEnum.RECEIVE_THINGS.getStatus(), new Date(factoryOrder.getShipTime())));
        }
        if (factoryOrder.getCancelTime() != null && factoryOrder.getCancelTime() > 0) {
            list.add(new OrderHistoryDto(FactoryOrderStatusEnum.CANCEL.getStatus(), new Date(factoryOrder.getCancelTime())));
        }
        if (factoryOrder.getFinishedTime() != null && factoryOrder.getFinishedTime() > 0) {
            list.add(new OrderHistoryDto(FactoryOrderStatusEnum.ACCOMPLISH.getStatus(), new Date(factoryOrder.getFinishedTime())));
        }

        return list;
    }

    @Deprecated
    private void assemblyProductionLineGrouping(Map<Long, FactoryOrderByProductionLineGroupingDto> map, ProductionLineRespDto productionLineDto, ProductionLineGroupBo factoryOrder, FactoryOrderByProductionLineGroupingDto factoryOrderByProductionLineGroupingDto, Long id) {
        if (map.containsKey(id)) {
            FactoryOrderByProductionLineGroupingDto dto = map.get(id);
            dto.setNum(dto.getNum() + factoryOrder.getNum());
            dto.setProductionLineName(productionLineDto.getName());

            dto.setProductionLineId(id);
            map.put(id, dto);
        } else {
            factoryOrderByProductionLineGroupingDto.setNum(factoryOrder.getNum());
            factoryOrderByProductionLineGroupingDto.setProductionLineName(productionLineDto.getName());

            factoryOrderByProductionLineGroupingDto.setProductionLineId(id);
            map.put(id, factoryOrderByProductionLineGroupingDto);
        }

    }

    public int factoryOrdersNoFinishNum(List<Long> orderItemIds) {
        return this.baseMapper.factoryOrdersNoFinishNum(orderItemIds);
    }

    public int factoryOrdersHaveFinishNum(List<Long> orderItemIds) {
        return this.baseMapper.factoryOrdersHaveFinishNum(orderItemIds);
    }

    public void cancelByOrderItemIds(List<Long> orderItemIds) {
        if (CollectionUtil.isEmpty(orderItemIds)) {
            return;
        }
        List<FactoryOrder> factoryOrders = this.findByOrderItemId(orderItemIds);
        if (CollUtil.isEmpty(factoryOrders)) {
            return;
        }
        List<Long> ids = ListUtil.toValueList(FactoryOrder::getId, factoryOrders);
        lambdaUpdate().in(FactoryOrder::getId, ids)
                .ne(FactoryOrder::getStatus, FactoryOrderStatusEnum.CANCEL.status)
                .set(FactoryOrder::getCancelTime, System.currentTimeMillis())
                .set(FactoryOrder::getStatus, FactoryOrderStatusEnum.CANCEL.status).update();
        // 计划取消重新统计
        Set<Long> taskIds = factoryOrders.stream().map(FactoryOrder::getTaskId).
                filter(NumberUtils::greaterZero).collect(Collectors.toSet());
        this.factoryTaskService.sendUpdateStaticMsg(FactoryTaskSqsConfig.FACTORY_TASK_UPDATE_TYPE_CANCEL, taskIds);
    }

    public void updateBayId(Long bayId, Long bayAreaId, String orderNo) {
        this.lambdaUpdate().eq(FactoryOrder::getMerchantOrderNo, orderNo)
                .set(FactoryOrder::getIssuingBayId, bayId)
                .set(FactoryOrder::getIssuingBayAreaId, bayAreaId).update();
    }

    public void updatePo(FactoryOrder po) {
        po.setUpdateTime(new Date());
        this.updateById(po);
    }

    public void updatePoBatch(List<FactoryOrder> poList) {
        if (CollUtil.isEmpty(poList)) {
            return;
        }
        Date date = new Date();
        poList.forEach(i -> i.setUpdateTime(date));
        this.updateBatchById(poList);
    }

    public void batchUpdate(List<FactoryOrderReqDto> dtos) {
        if (CollUtil.isEmpty(dtos)) {
            return;
        }
        List<FactoryOrder> pos = BeanUtil.copyToList(dtos, FactoryOrder.class);
        this.updatePoBatch(pos);
    }

    public int factoryOrdersNum(List<Long> orderItemIds) {
        return this.baseMapper.factoryOrdersNum(orderItemIds);
    }

    public FactoryOrder findByOrderItemId(Long orderItemId) {
        return this.lambdaQuery().eq(FactoryOrder::getOrderItemId, orderItemId)
                .notIn(FactoryOrder::getStatus, Arrays.asList(7, 8, 98))
                .orderByAsc(FactoryOrder::getId)
                .last(CommonConstant.LIMIT_1).one();
    }

    public void updateFnsku(FactoryOrder factoryOrder) {
        if (StrUtil.isBlank(factoryOrder.getFnsku())) {
            log.info("FactoryOrder Fnsku is null,param is {}", JSON.toJSONString(factoryOrder));
            return;
        }
        LambdaUpdateWrapper<FactoryOrder> lambdaUpdateWrapper = Wrappers.<FactoryOrder>lambdaUpdate()
                .set(StrUtil.isNotBlank(factoryOrder.getFnsku()), FactoryOrder::getFnsku, factoryOrder.getFnsku())
                .eq(FactoryOrder::getOrderItemId, factoryOrder.getOrderItemId());
        this.update(lambdaUpdateWrapper);
    }

    public List<FactoryAssistantRespItemDto> printAssistant(FactoryAssistantDto factoryAssistantDto) {
        return this.factoryOrderMapper.printAssistant(factoryAssistantDto);
    }

    public FactoryAssistantRespDto printAssistantCount(FactoryAssistantDto factoryAssistantDto) {
        return this.factoryOrderMapper.printAssistantCount(factoryAssistantDto);
    }


    public FactoryAssistantRecordDto record(FactoryAssistantRecordRespDto factoryAssistantRecordRespDto) {
        Page<FactoryOrder> pagination = new Page<>(factoryAssistantRecordRespDto.getPage(), factoryAssistantRecordRespDto.getSize());
        FactoryAssistantRecordDto factoryAssistantRecordDto = new FactoryAssistantRecordDto();
        IPage<FactoryOrder> templatePage = this.factoryOrderMapper.record(pagination, factoryAssistantRecordRespDto.getFactoryId()
                , factoryAssistantRecordRespDto.getStatus(), factoryAssistantRecordRespDto.getFactoryOrderNo(), factoryAssistantRecordRespDto.getConfirmEndTime(), factoryAssistantRecordRespDto.getConfirmStartTime());
        if (templatePage == null) {
            return factoryAssistantRecordDto;
        }
        List<FactoryAssistantRecordItemDto> records = RelationsBinder.convertAndBind(templatePage.getRecords(), FactoryAssistantRecordItemDto.class);
        if (CollectionUtils.isEmpty(records)) {
            return factoryAssistantRecordDto;
        }
        factoryAssistantRecordDto.setItems(records);
        factoryAssistantRecordDto.setTotalCount(templatePage.getTotal());
        return factoryAssistantRecordDto;
    }

    public List<FactoryAssistantRePrintRespDto> rePrint(FactoryAssistantRePrintDto factoryAssistantRePrintDto) {
        Long factoryOrderId = factoryAssistantRePrintDto.getFactoryOrderId();
        FactoryOrder factoryOrder = this.getById(factoryOrderId);
        if (ObjectUtil.isNull(factoryOrder)) {
            return Lists.newArrayList();
        }
        Long confirmTime = factoryOrder.getConfirmTime();
        factoryAssistantRePrintDto.setConfirmTime(confirmTime);


        return this.factoryOrderMapper.rePrint(factoryAssistantRePrintDto);
    }

    /**
     * v6.11 稿件刷新列表
     */
    public IPage<ManuscriptRefreshSearchRspDto> manuscriptRefreshSearch(Page<ManuscriptRefreshSearchRspDto> pagination, ManuscriptRefreshDto refreshDto) {

        return this.factoryOrderMapper.manuscriptRefreshSearch(pagination, refreshDto);
    }

    public List<ManuscriptRefreshSearchRspDto> manuscriptRefreshRefresh(ManuscriptRefreshDto refreshDto) {

        return this.factoryOrderMapper.manuscriptRefreshRefresh(refreshDto);
    }


    public ManuscriptRefreshGroupingRspDto grouping(ManuscriptRefreshDto refreshDto) {
        ManuscriptRefreshGroupingRspDto manuscriptRefreshGroupingRspDto = new ManuscriptRefreshGroupingRspDto();
        if (CollectionUtil.isEmpty(refreshDto.getFactoryOrderNos()) && CollectionUtil.isEmpty(refreshDto.getPrototypeIdList())) {
            manuscriptRefreshGroupingRspDto.setColors(null);
            manuscriptRefreshGroupingRspDto.setSizes(null);
            manuscriptRefreshGroupingRspDto.setStatusList(null);
            manuscriptRefreshGroupingRspDto.setCount(0L);
            return manuscriptRefreshGroupingRspDto;
        }
        manuscriptRefreshGroupingRspDto.setColors(this.factoryOrderMapper.manuscriptRefreshGroupingColor(refreshDto));
        manuscriptRefreshGroupingRspDto.setSizes(this.factoryOrderMapper.manuscriptRefreshGroupingSize(refreshDto));
        manuscriptRefreshGroupingRspDto.setStatusList(this.factoryOrderMapper.manuscriptRefreshGroupingFactoryOrderStatus(refreshDto));
        manuscriptRefreshGroupingRspDto.setCount(this.factoryOrderMapper.manuscriptRefreshGroupingSum(refreshDto));
        return manuscriptRefreshGroupingRspDto;
    }

    public List<FactoryOrderDto> findNotPutInFactoryByOrderNo(List<String> nos) {
        return this.factoryOrderMapper.findNotPutInFactoryByOrderNo(nos);
    }

    public List<FactoryOrderDto> findNotCancelFactoryByOrderNo(List<String> nos) {
        List<FactoryOrder> factoryOrders = this.factoryOrderMapper.findNotCancelFactoryByOrderNo(nos);
        return BeanUtil.copyToList(factoryOrders, FactoryOrderDto.class);
    }


    @NotNull
    private static ArrayList<FactoryOrderLogistics> getFactoryOrderLogistics(List<FactoryOrderUpdateLogisticsAndDeliveryParam> factoryOrderParams, Map<Long, FactoryOrderLogistics> factoryOrderLogisticsMap, FactoryOrderDeliveryLabelDto factoryOrderDeliveryLabelDto) {
        ArrayList<FactoryOrderLogistics> orderLogistics = Lists.newArrayList();
        factoryOrderParams.forEach(p -> {
            FactoryOrderLogistics factoryOrderLogistics = new FactoryOrderLogistics();
            FactoryOrderLogistics one = factoryOrderLogisticsMap.get(p.getId());
            if (one != null) {
                factoryOrderLogistics.setId(one.getId());
            }
            factoryOrderLogistics.setLogisticsName(factoryOrderDeliveryLabelDto.getLogisticsName());
            factoryOrderLogistics.setLogisticsNo(factoryOrderDeliveryLabelDto.getLogisticsNo());
            factoryOrderLogistics.setFactoryOrderId(p.getId());
            factoryOrderLogistics.setFactoryOrderDeliveryId(factoryOrderDeliveryLabelDto.getId());
            orderLogistics.add(factoryOrderLogistics);
        });
        return orderLogistics;
    }

    public FactoryOrderDeliveryLabelDto updateLogisticsByNos(Long factoryId, FactoryOrderDeliveryParam dto) {
        List<FactoryOrderUpdateLogisticsAndDeliveryParam> factoryOrderParams = dto.getFactoryOrderParams();
        log.info("updateLogisticsByNos params size {}", factoryOrderParams.size());
        List<FactoryOrder> factoryOrders = BeanUtil.copyToList(factoryOrderParams, FactoryOrder.class);
        this.updateBatchById(factoryOrders);
        List<Long> factoryOrderIds = factoryOrderParams.stream().map(FactoryOrderUpdateLogisticsAndDeliveryParam::getId).collect(Collectors.toList());
        List<FactoryOrderLogistics> factoryOrderLogisticsOld = this.factoryOrderLogisticsService.getByFactoryOrderIds(factoryOrderIds);
        Map<Long, FactoryOrderLogistics> factoryOrderLogisticsMap = factoryOrderLogisticsOld.stream().collect(Collectors.toMap(FactoryOrderLogistics::getFactoryOrderId, f -> f));

        List<FactoryOrder> fos = this.factoryOrderStatusService.listByIds(factoryOrderIds);
        Map<Long, FactoryOrder> factoryOrderMap = fos.stream().collect(Collectors.toMap(i -> i.getId(), f -> f, (a, b) -> b));
        List<FactoryOrderOperateRecord> records = Lists.newArrayList();
        factoryOrderParams.forEach(p -> {
            FactoryOrder factoryOrder = factoryOrderMap.get(p.getId());
            FactoryOrderOperateRecord shipRecord = this.factoryOrderStatusService.addOperate(p.getFactoryOrderNo(), SdsPlatformEnum.FACTORY, p.getOpUserId(),
                    p.getOpUserName(),
                    FactoryOperateEnum.intoStatusValueOf(FactoryOrderStatusEnum.RECEIVE_THINGS.getStatus()), null != factoryOrder ? factoryOrder.getNum() : 0);
            records.add(shipRecord);
        });
        FactoryOrderDeliveryLabelDto factoryOrderDeliveryLabelDto = this.saveDelivery(factoryId, fos, dto.getDeliveryBatch(), dto.getOperatorId());
        ArrayList<FactoryOrderLogistics> orderLogistics = getFactoryOrderLogistics(factoryOrderParams, factoryOrderLogisticsMap, factoryOrderDeliveryLabelDto);
        this.factoryOrderLogisticsService.addOrSaveBatch(orderLogistics);
        log.info("updateLogisticsByNos end ");
        // 进行任务数计算
        this.factoryTaskService.modifyFactoryTaskExpressNumByFactoryOrderIds(factoryId, factoryOrderIds);

        this.operateRecordService.saveBatch(records);

        return factoryOrderDeliveryLabelDto;
    }

    public FactoryOrderDeliveryLabelDto completAndShipByNos(Long factoryId, FactoryOrderDeliveryParam dto) {
        List<FactoryOrderUpdateLogisticsAndDeliveryParam> factoryOrderParams = dto.getFactoryOrderParams();
        log.info("completAndShipByNos params size {}", factoryOrderParams.size());
        List<FactoryOrder> factoryOrders = BeanUtil.copyToList(factoryOrderParams, FactoryOrder.class);
        this.updateBatchById(factoryOrders);
        List<Long> factoryOrderIds = factoryOrderParams.stream().map(FactoryOrderUpdateLogisticsAndDeliveryParam::getId).collect(Collectors.toList());
        List<FactoryOrderLogistics> factoryOrderLogisticsOld = this.factoryOrderLogisticsService.getByFactoryOrderIds(factoryOrderIds);
        Map<Long, FactoryOrderLogistics> factoryOrderLogisticsMap = factoryOrderLogisticsOld.stream().collect(Collectors.toMap(FactoryOrderLogistics::getFactoryOrderId, f -> f));
        // 计划关系
//        List<FactoryTaskOrderRel> taskOrderRels = factoryTaskOrderRelService.findByOrderIds(factoryId, factoryOrderIds);
//        Map<Long, FactoryTaskOrderRel> taskOrderRelMap = taskOrderRels.stream().collect(Collectors.toMap(FactoryTaskOrderRel::getFactoryOrderId, Function.identity()));
        List<FactoryOrder> fos = this.factoryOrderStatusService.listByIds(factoryOrderIds);
        Map<Long, FactoryOrder> factoryOrderMap = fos.stream().collect(Collectors.toMap(i -> i.getId(), f -> f, (a, b) -> b));

        Set<String> merchantOrderIds = fos.stream().map(FactoryOrder::getMerchantOrderNo).collect(Collectors.toSet());
        List<Order> merchantOrders = this.orderService.findByNos(merchantOrderIds);

        Map<String, Order> merchantOrderNoMap = merchantOrders.stream().collect(Collectors.toMap(Order::getNo, entity -> entity));

        List<FactoryOrderOperateRecord> records = Lists.newArrayList();
        factoryOrderParams.forEach(p -> {
            FactoryOrder factoryOrder = factoryOrderMap.get(p.getId());

            FactoryOrderOperateRecord completRecord = this.factoryOrderStatusService.addOperate(p.getFactoryOrderNo(), SdsPlatformEnum.FACTORY, p.getOpUserId(),
                    p.getOpUserName(),
                    FactoryOperateEnum.intoStatusValueOf(FactoryOrderStatusEnum.SHIPMENTS.getStatus()),
                    null != factoryOrder ? factoryOrder.getNum() : 0);
            records.add(completRecord);

            FactoryOrderOperateRecord shipRecord = this.factoryOrderStatusService.addOperate(p.getFactoryOrderNo(), SdsPlatformEnum.FACTORY, p.getOpUserId(),
                    p.getOpUserName(),
                    FactoryOperateEnum.intoStatusValueOf(FactoryOrderStatusEnum.RECEIVE_THINGS.getStatus()),
                    null != factoryOrder ? factoryOrder.getNum() : 0);
            records.add(shipRecord);
        });
        FactoryOrderDeliveryLabelDto factoryOrderDeliveryLabelDto = this.saveDelivery(factoryId, fos, dto.getDeliveryBatch(), dto.getOperatorId());
        ArrayList<FactoryOrderLogistics> orderLogistics = getFactoryOrderLogistics(factoryOrderParams, factoryOrderLogisticsMap, factoryOrderDeliveryLabelDto);
        this.factoryOrderLogisticsService.addOrSaveBatch(orderLogistics);
        log.info("completAndShipByNos end ");
        // 进行任务数计算
        this.factoryTaskService.modifyCompleteAndShipByFactoryOrderIds(factoryId, factoryOrderIds);
//        factoryTaskOrderRelService.updateBatchById(taskOrderRels);
        this.factoryOrderStatusService.sendFactoryShipOrderMessage(dto.getOperatorId(), fos, merchantOrderNoMap, factoryId);

        this.operateRecordService.saveBatch(records);
        return factoryOrderDeliveryLabelDto;
    }

    public FactoryOrderDeliveryLabelDto saveDelivery(Long factoryId, List<FactoryOrder> factoryOrders, FactoryOrderDeliveryReqDto dto, Long userId) {
        //保存批次
        FactoryOrderDelivery factoryOrderDelivery = this.saveFactoryOrderDelivery(factoryId, factoryOrders, dto, userId);
        //封装返回值
        return this.factoryOrderDeliveryService.getBatchLabelDto(factoryOrderDelivery);
    }

    private FactoryOrderDelivery saveFactoryOrderDelivery(Long factoryId, List<FactoryOrder> factoryOrders, FactoryOrderDeliveryReqDto dto, Long userId) {
        int productNum = factoryOrders.stream().mapToInt(FactoryOrder::getNum).sum();
        FactoryOrderDeliverySaveReqDto factoryOrderDeliverySaveReqDto = new FactoryOrderDeliverySaveReqDto();
        factoryOrderDeliverySaveReqDto.setBoxNum(dto.getBoxNum());
        factoryOrderDeliverySaveReqDto.setLogisticsName(dto.getLogisticsName());
        factoryOrderDeliverySaveReqDto.setLogisticsNo(dto.getLogisticsNo());
        factoryOrderDeliverySaveReqDto.setNum(factoryOrders.size());
        factoryOrderDeliverySaveReqDto.setProductNum(productNum);
        factoryOrderDeliverySaveReqDto.setIssuingBayId(factoryOrders.get(0).getIssuingBayId());
        factoryOrderDeliverySaveReqDto.setFactoryId(factoryId);
        factoryOrderDeliverySaveReqDto.setCreateUid(userId);
        factoryOrderDeliverySaveReqDto.setUpdateUid(userId);
        return this.factoryOrderDeliveryService.save(factoryOrderDeliverySaveReqDto);
    }

    public void oldDataAddPrototypeId(Date startTime, Date endTime) {
        List<FactoryOrder> updateFactoryOrderList = Lists.newArrayList();
        //查询要刷新时间内的工厂订单列表
        List<Integer> status = Lists.newArrayList();
        status.add(FactoryOrderStatusConstant.CANCELLED);
        status.add(FactoryOrderStatusConstant.DELETE);
        LambdaQueryWrapper<FactoryOrder> lambdaQueryWrapper = Wrappers.<FactoryOrder>lambdaQuery()
                .ge(FactoryOrder::getCreateTime, startTime)
                .le(FactoryOrder::getCreateTime, endTime)
                .notIn(FactoryOrder::getStatus, status)
                .eq(FactoryOrder::getPrototypeId, 0L);
        List<FactoryOrder> factoryOrderList = this.baseMapper.selectList(lambdaQueryWrapper);
        if (CollUtil.isEmpty(factoryOrderList)) {
            return;
        }
        Map<Long, List<FactoryOrder>> factoryOrderMap = factoryOrderList.stream().collect(Collectors.groupingBy(FactoryOrder::getMerchantId));
        factoryOrderMap.forEach((merchantId, subFactoryOrderList) -> {
            List<Long> designProductIdList = subFactoryOrderList.stream().map(FactoryOrder::getEndProductId).distinct().collect(Collectors.toList());
            String designProductIdsStr = Joiner.on(",").skipNulls().join(designProductIdList);
            List<DesignProductRespDto> designProductRespDtoList = this.designProductFeign.findByIds(merchantId, designProductIdsStr, "prototypeId");
            if (CollUtil.isEmpty(designProductRespDtoList)) {
                return;
            }
            Map<Long, DesignProductRespDto> designProductRespDtoMap = designProductRespDtoList.stream().collect(Collectors.toMap(DesignProductRespDto::getId, s -> s, (a, b) -> a));
            subFactoryOrderList.stream().forEach(factoryOrder -> {
                DesignProductRespDto designProductRespDto = designProductRespDtoMap.get(factoryOrder.getEndProductId());
                if (designProductRespDto == null || designProductRespDto.getPrototypeId() == null) {
                    return;
                }
                factoryOrder.setPrototypeId(designProductRespDto.getPrototypeId());
                updateFactoryOrderList.add(factoryOrder);
            });
        });

        if (CollUtil.isNotEmpty(updateFactoryOrderList)) {
            for (FactoryOrder factoryOrder : updateFactoryOrderList) {
                LambdaUpdateWrapper<FactoryOrder> updateWrapper = Wrappers.<FactoryOrder>lambdaUpdate()
                        .set(FactoryOrder::getPrototypeId, factoryOrder.getPrototypeId())
                        .eq(FactoryOrder::getId, factoryOrder.getId());
                this.update(updateWrapper);
            }
        }
    }

    public LambdaQueryChainWrapper<FactoryOrder> getBaseLambdaQuery() {
        List<Integer> status = Lists.newArrayList();
        status.add(FactoryOrderStatusConstant.CANCELLED);
        status.add(FactoryOrderStatusConstant.DELETE);
        return this.lambdaQuery().notIn(FactoryOrder::getStatus, status);
    }

    public FactoryOrderRespDto getFactoryOrder(FactoryOrderReqDto factoryOrderReqDto) {
        if (factoryOrderReqDto == null) {
            return null;
        }
        List<Integer> statusList = factoryOrderReqDto.getStatusList();
        LambdaQueryWrapper<FactoryOrder> wrapper = Wrappers.lambdaQuery(FactoryOrder.class)
                .eq(FactoryOrder::getNo, factoryOrderReqDto.getNo())
                .in(CollectionUtil.isNotEmpty(statusList), FactoryOrder::getStatus, statusList)
                .last(LIMIT_ONE);
        FactoryOrder factoryOrder = this.baseMapper.selectOne(wrapper);
        return RelationsBinder.convertAndBind(factoryOrder, FactoryOrderRespDto.class, "");
    }

    public List<FactoryOrderRespDto> getFactoryOrderList(FactoryOrderReqDto factoryOrderReqDto) {
        if (factoryOrderReqDto == null) {
            return null;
        }
        List<Integer> statusList = factoryOrderReqDto.getStatusList();
        LambdaQueryWrapper<FactoryOrder> wrapper = Wrappers.lambdaQuery(FactoryOrder.class)
                .eq(FactoryOrder::getMerchantOrderNo, factoryOrderReqDto.getMerchantOrderNo())
                .in(CollUtil.isNotEmpty(factoryOrderReqDto.getNoList()), FactoryOrder::getNo, factoryOrderReqDto.getNoList())
                .in(CollUtil.isNotEmpty(statusList), FactoryOrder::getStatus, statusList)
                .eq(factoryOrderReqDto.getCancalStatus() != null, FactoryOrder::getCancalStatus, factoryOrderReqDto.getCancalStatus());
        List<FactoryOrder> factoryOrderList = this.baseMapper.selectList(wrapper);
        return RelationsBinder.convertAndBind(factoryOrderList, FactoryOrderRespDto.class, "");
    }

    public void updateAllocationByIds(List<Long> factoryOrderIds) {
        this.lambdaUpdate().set(FactoryOrder::getIsAllocation, 0)
                .in(FactoryOrder::getId, factoryOrderIds)
                .update();
    }

    public boolean allocation(List<FactoryOrder> factoryOrders, List<FactoryOrder> commitFactoryOrders) {
        List<Long> orderItemId = factoryOrders.stream().map(FactoryOrder::getOrderItemId).collect(Collectors.toList());
        List<AfterServiceAuditItem> list = this.afterServiceAuditService.findByOrderImteIds(orderItemId, null);
        Map<Long, AfterServiceAuditItem> afterServiceAuditHashMap = Maps.newHashMap();
        for (AfterServiceAuditItem item : list) {
            afterServiceAuditHashMap.put(item.getOrderItemId(), item);
        }
        int cancelCount = 0;
        List<Long> ids = new ArrayList<>();
        for (FactoryOrder factoryOrder : factoryOrders) {
            ids.add(factoryOrder.getId());
            if (afterServiceAuditHashMap.get(factoryOrder.getOrderItemId()) != null) {
                AfterServiceAuditItem afterServiceAuditItem = afterServiceAuditHashMap.get(factoryOrder.getOrderItemId());
                factoryOrder.setCancalStatus(afterServiceAuditItem.getStatus());
                if (afterServiceAuditItem.getStatus() == 2) {
                    cancelCount++;
                }
            }
        }
        for (FactoryOrder factoryOrder : commitFactoryOrders) {
            if (afterServiceAuditHashMap.get(factoryOrder.getOrderItemId()) != null) {
                AfterServiceAuditItem afterServiceAuditItem = afterServiceAuditHashMap.get(factoryOrder.getOrderItemId());
                if (afterServiceAuditItem.getStatus() == 2) {
                    return false;
                }
            }
        }

        //只有一个商品不需要分配
        boolean isUpdate = true;
        if (factoryOrders.size() <= 1) {
            isUpdate = false;
        } else if (cancelCount == factoryOrders.size()) {
            //订单中只有一个产品但有多件数量，不需要分配仓位号
            isUpdate = false;
        } else if (cancelCount == factoryOrders.size() - 1) {
            //订单中有两件及以上不同的产品，其他产品都已取消，只剩一件产品时，不需要分配仓位号
            isUpdate = false;
        }

        if (!isUpdate) {
            this.updateAllocationByIds(ids);
        }

        return isUpdate;
    }


    public void updatePrintStatus(List<Long> factoryOrderIds, String status, Long opUserId) {
        if (CollUtil.isEmpty(factoryOrderIds)) {
            return;
        }

        Set<Long> modifyFactoryOrderIds = Sets.newHashSet();
        List<FactoryOrder> factoryOrders = this.findByIds(factoryOrderIds);
        Map<Long, FactoryOrder> factoryOrderMap = factoryOrders.stream().collect(Collectors.toMap(FactoryOrder::getId, Function.identity()));

        List<OrderItemRespDto> orderItems = this.findOrderItemsByFactoryOrderIds(factoryOrderIds, "id,orderId");
        Map<Long, OrderItemRespDto> orderItemRespDtoMap = orderItems.stream().collect(Collectors.toMap(OrderItemRespDto::getId, Function.identity()));

        for (Long factoryOrderId : factoryOrderIds) {
            FactoryOrder factoryOrder = factoryOrderMap.get(factoryOrderId);
            if (factoryOrder == null) {
                continue;
            }

            if (DownTypeEnum.CACHE.getValue().equals(status) && !factoryOrder.getPrintStatus().equals(DownTypeEnum.NOT.getValue())) {
                continue;
            }

            OrderItemRespDto orderItemRespDto = orderItemRespDtoMap.get(factoryOrder.getOrderItemId());
            if (orderItemRespDto == null) {
                continue;
            }

            modifyFactoryOrderIds.add(factoryOrder.getId());
        }

        if (CollUtil.isNotEmpty(modifyFactoryOrderIds)) {
            this.factoryOrderMapperManager.updatePrintStatus(modifyFactoryOrderIds, status);
        }
    }

    public void updateDownStatus(List<Long> factoryOrderIds, String status) {
        Set<Long> modifyFactoryOrderIds = Sets.newHashSet();
        List<FactoryOrder> factoryOrders = this.findByIds(factoryOrderIds);
        Map<Long, FactoryOrder> factoryOrderMap = factoryOrders.stream().collect(Collectors.toMap(FactoryOrder::getId, Function.identity()));

        List<OrderItemRespDto> orderItems = this.findOrderItemsByFactoryOrderIds(factoryOrderIds, "id,orderId");
        Map<Long, OrderItemRespDto> orderItemRespDtoMap = orderItems.stream().collect(Collectors.toMap(OrderItemRespDto::getId, Function.identity()));

        for (Long factoryOrderId : factoryOrderIds) {
            FactoryOrder factoryOrder = factoryOrderMap.get(factoryOrderId);
            if (factoryOrder == null) {
                continue;
            }
            // 避免cache 修改掉已下载的数据
            if (DownTypeEnum.CACHE.getValue().equals(status) && !factoryOrder.getDownStatus().equals(DownTypeEnum.NOT.getValue())) {
                continue;
            }

            OrderItemRespDto orderItemRespDto = orderItemRespDtoMap.get(factoryOrder.getOrderItemId());
            if (orderItemRespDto == null) {
                continue;
            }

            modifyFactoryOrderIds.add(factoryOrder.getId());
        }

        if (CollUtil.isNotEmpty(factoryOrderIds)) {
            this.factoryOrderMapperManager.updateDownStatus(modifyFactoryOrderIds, status);
            this.rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER,
                    OrderTagConst.FACTORY_ORDER_ES_TOPIC,
                    FactoryOrderEsMessageDTO.ofIds(factoryOrderIds));
        }
    }

    public void updateDownStatusAndSendMessage(List<Long> factoryOrderIds, String status, Long opUserId) {
        Set<Long> modifyFactoryOrderIds = Sets.newHashSet();
        List<FactoryOrder> factoryOrders = this.findByIds(factoryOrderIds);
        Map<Long, FactoryOrder> factoryOrderMap = factoryOrders.stream().collect(Collectors.toMap(FactoryOrder::getId, Function.identity()));

        List<OrderItemRespDto> orderItems = this.findOrderItemsByFactoryOrderIds(factoryOrderIds, "id,orderId");
        Map<Long, OrderItemRespDto> orderItemRespDtoMap = orderItems.stream().collect(Collectors.toMap(OrderItemRespDto::getId, Function.identity()));

        for (Long factoryOrderId : factoryOrderIds) {
            FactoryOrder factoryOrder = factoryOrderMap.get(factoryOrderId);
            if (factoryOrder == null) {
                continue;
            }
            // 避免cache 修改掉已下载的数据
            if (DownTypeEnum.CACHE.getValue().equals(status) && !factoryOrder.getDownStatus().equals(DownTypeEnum.NOT.getValue())) {
                continue;
            }

            OrderItemRespDto orderItemRespDto = orderItemRespDtoMap.get(factoryOrder.getOrderItemId());
            if (orderItemRespDto == null) {
                continue;
            }


            modifyFactoryOrderIds.add(factoryOrder.getId());
        }

        if (CollUtil.isNotEmpty(factoryOrderIds)) {
            this.factoryOrderMapperManager.updateDownStatus(modifyFactoryOrderIds, status);
        }

        // 下載才進行消息發送
        if (CollectionUtil.isNotEmpty(modifyFactoryOrderIds) && DownTypeEnum.DOWN.getValue().equals(status)) {
            FactoryBatchDownloadOrderMessage messages = new FactoryBatchDownloadOrderMessage();
            messages.setSendingTime(new Date());
            messages.setOperatorUid(opUserId);
            messages.setFactoryOrderIds(modifyFactoryOrderIds);

            this.factoryTaskOrderRelService.sendFactoryDownManuscriptOrderMessage(messages);
        }

    }

    public FactoryOrderLittleHelperStaticDto unconfirmFactoryOrdersDayStatic(Long factoryId) {
        List<FactoryStatusStaticRespDto> statiInfo = this.factoryOrderMapper.unconfirmedFactoryDayStatic(factoryId);
        FactoryOrderLittleHelperStaticDto result = new FactoryOrderLittleHelperStaticDto();
        int totalOrderNum = 0;
        int moreOrderNum = 0;
        if (CollUtil.isEmpty(statiInfo)) {
            result.setMoreOrderNum(moreOrderNum);
            result.setUnconfimOrderNum(totalOrderNum);
            return result;
        }
        int skip = 3;
        Map<String, Integer> topStaticInfos = Maps.newLinkedHashMap();
        for (int i = 0; i < statiInfo.size(); i++) {
            FactoryStatusStaticRespDto info = statiInfo.get(i);
            totalOrderNum += info.getOrderNum();
            if (i < skip) {
                topStaticInfos.put(info.getDay(), info.getOrderNum());
            } else {
                moreOrderNum += info.getOrderNum();
            }

        }
        result.setMoreOrderNum(moreOrderNum);
        result.setUnconfimOrderNum(totalOrderNum);
        result.setTopOrderNun(topStaticInfos);
        return result;
    }

    public List<FactoryOrder> findByNosAndFinishedTime(Collection<String> merchantOrderOrNos) {
        if (CollectionUtil.isEmpty(merchantOrderOrNos)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .gt(FactoryOrder::getFinishedTime, 0)
                .in(FactoryOrder::getMerchantOrderNo, merchantOrderOrNos)
                .or()
                .in(FactoryOrder::getNo, merchantOrderOrNos)
                .list();
    }

    public List<FactoryOrder> findByMerchantOrderOrNos(Collection<String> merchantOrderOrNos) {
        if (CollUtil.isEmpty(merchantOrderOrNos)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(FactoryOrder::getMerchantOrderNo, merchantOrderOrNos)
                .list();
    }

    public List<FactoryOrder> findByFactoryAndFinishedTime(Long factoryId, Long finishBeginTime, Long finishEndTime) {
        if (factoryId == null) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .eq(FactoryOrder::getFactoryId, factoryId)
                .eq(FactoryOrder::getStatus, FactoryOrderStatusConstant.COMPLETED)
                .between(FactoryOrder::getFinishedTime, finishBeginTime, finishEndTime)
                .orderByDesc(FactoryOrder::getFinishedTime)
                .list();
    }

    public List<Long> findIdByFactoryAndFinishedTime(Long factoryId, Long finishBeginTime, Long finishEndTime) {
        if (factoryId == null) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .select(FactoryOrder::getId)
                .eq(FactoryOrder::getFactoryId, factoryId)
                .eq(FactoryOrder::getStatus, FactoryOrderStatusConstant.COMPLETED)
                .between(FactoryOrder::getFinishedTime, finishBeginTime, finishEndTime)
                .orderByDesc(FactoryOrder::getFinishedTime)
                .list().stream().map(FactoryOrder::getId).collect(Collectors.toList());
    }

    public Integer countByFactoryAndFinishedTime(Long factoryId, Long finishBeginTime, Long finishEndTime) {
        if (factoryId == null) {
            return 0;
        }
        return this.lambdaQuery()
                .eq(FactoryOrder::getFactoryId, factoryId)
                .eq(FactoryOrder::getStatus, FactoryOrderStatusConstant.COMPLETED)
                .between(FactoryOrder::getFinishedTime, finishBeginTime, finishEndTime)
                .count();
    }


    public void batchUpdateCarriageAmount(List<Long> averageFactoryOrderIds, BigDecimal averCarriageAmount) {
        this.lambdaUpdate()
                .in(FactoryOrder::getId, averageFactoryOrderIds)
                .set(FactoryOrder::getCarriageAmount, averCarriageAmount)
                .update();
    }

    public IPage<FactoryOrderSettleDetailResp> getMonthBillDetailPage(FactoryOrderSettleDetailParam pageParam, Page<FactoryOrderSettleDetailResp> pagination) {
        return this.factoryOrderMapper.factoryOrderMonthBillDetailPage(pagination, pageParam);
    }


    public IPage<FactoryOrderSettleDetailExportBo> getMonthBillExportDataList(Page<FactoryOrderSettleDetailExportBo> pagination, Long factoryId, long beginFinishTime, long endFinishTime) {
        return this.factoryOrderMapper.getMonthBillExportDataList(pagination, factoryId, beginFinishTime, endFinishTime);
    }

    @Resource
    private WarehouseService warehouseService;
    @Resource
    private ImportedPlatformOrderFbaItemService importedPlatformOrderFbaItemService;

    public FactoryOrderRespDto detailByNo(String no, Long factoryId) {
        List<Integer> status = Lists.newArrayList();
        status.addAll(FactoryOrderStatusService.factoryOrderProduceStatus);
        status.add(FactoryOrderStatusEnum.ACCOMPLISH.getStatus());
        status.add(FactoryOrderStatusEnum.NO_PASS_GC.getStatus());
        status.add(FactoryOrderStatusEnum.SHELVE.getStatus());

        FactoryOrder factoryOrder = this.lambdaQuery()
                .eq(FactoryOrder::getNo, no)
                .eq(FactoryOrder::getFactoryId, factoryId)
                .one();
        if (factoryOrder == null) {
            return null;
        }

        List<FactoryOrderRespDto> records = this.packageFactoryOrders(Lists.newArrayList(factoryOrder), factoryId);

        FactoryOrderRespDto result = records.get(0);
        this.formatShipmentBelongingTenantName(Lists.newArrayList(result.getOrder()));
        Long merchantOrderId = result.getOrder().getId();

        Map<Long, Warehouse> warehouseMap = this.warehouseService.findByOrderIds(Lists.newArrayList(merchantOrderId));
        if (CollUtil.isNotEmpty(warehouseMap)) {
            result.setWarehouse(BeanUtil.copyProperties(warehouseMap.get(merchantOrderId), WarehouseDTO.class));
        }

        List<ImportedPlatformOrderFbaItemRespDto> fbaItemRespDtoList = this.importedPlatformOrderFbaItemService.getByOrderItemIds(String.valueOf(result.getOrderItemId()), "");
        if (CollUtil.isNotEmpty(fbaItemRespDtoList)) {
            result.setFnsku(fbaItemRespDtoList.get(0).getLabelFnsku());
        }

        return result;
    }

    public Map<String, Integer> findStatusByOrderItemNos(Collection<String> orderItemNos) {
        if (CollUtil.isEmpty(orderItemNos)) {
            return Collections.emptyMap();
        }
        return this.lambdaQuery().select(FactoryOrder::getNo, FactoryOrder::getStatus)
                .in(FactoryOrder::getNo, orderItemNos).list()
                .stream().collect(Collectors.toMap(FactoryOrder::getNo, FactoryOrder::getStatus));
    }

    /**
     * 校验是否全部生产完成
     *
     * @param excludeRefuse 是否排除打回订单
     */
    public boolean checkAllProduction(String merchantOrderNo, boolean excludeRefuse) {
        // 未生成完成的数量
        Integer count = this.lambdaQuery().select(FactoryOrder::getId)
                .eq(excludeRefuse, FactoryOrder::getRefuseType, BasePoConstant.NO)
                .in(FactoryOrder::getStatus, FactoryOrderStatusEnum.UN_ACCOMPLISH_PRODUCTION_STATUS)
                .eq(FactoryOrder::getMerchantOrderNo, merchantOrderNo).count();
        return count == null || count == 0;
    }

    public boolean allMatchByMerchantOrderAndStatus(String merchantOrderNo, Collection<Integer> statusList) {
        if (CollUtil.isEmpty(statusList)) {
            return false;
        }
        Integer count = this.lambdaQuery().select(FactoryOrder::getId)
                .notIn(FactoryOrder::getStatus, statusList)
                .eq(FactoryOrder::getMerchantOrderNo, merchantOrderNo).count();
        return count == null || count == 0;
    }

    public List<Integer> findStatusByMerchantOrderNo(String merchantOrderNo) {
        return this.lambdaQuery().select(FactoryOrder::getStatus)
                .eq(FactoryOrder::getMerchantOrderNo, merchantOrderNo).list()
                .stream().map(FactoryOrder::getStatus).collect(Collectors.toList());
    }

    public List<FactoryOrder> findStatusByMerchantOrderNos(Collection<String> merchantOrderNos, List<Long> excludeIds) {
        if (CollUtil.isEmpty(merchantOrderNos)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().select(FactoryOrder::getId, FactoryOrder::getMerchantOrderNo, FactoryOrder::getStatus)
                .notIn(CollUtil.isNotEmpty(excludeIds), FactoryOrder::getId, excludeIds)
                .in(FactoryOrder::getMerchantOrderNo, merchantOrderNos).list();
    }

    public List<FactoryOrder> findAllByItemNos(List<String> itemNos) {
        if (CollUtil.isEmpty(itemNos)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(FactoryOrder::getNo, itemNos).list();
    }

    public List<FactoryOrder> findByNoOrMerchantOrderNoForQc(String no) {
        FactoryOrder one = this.lambdaQuery().select(FactoryOrder::getMerchantOrderNo)
                .eq(FactoryOrder::getNo, no).one();
        if (one != null) {
            no = one.getMerchantOrderNo();
        }
        return this.findByMerchantOrderNo(no);
    }

    public FactoryOrder minDateByMerchantOrderNo(String merchantOrderNo) {
        FactoryOrder one = this.baseMapper.minDateByMerchantOrderNo(merchantOrderNo);
        return one == null ? new FactoryOrder() : one;
    }

    public Map<Long, Integer> mapQcPassQty(Collection<Long> orderItemIds) {
        if (CollUtil.isEmpty(orderItemIds)) {
            return Collections.emptyMap();
        }
        List<OrderItemTransferHistory> transferHistoryList = this.orderItemTransferHistoryService.findAllByItemIds(orderItemIds
                , Collections.singletonList(EnumOrderItemTransferType.LOSE_RESEND.value));
        // 补件单-原单id
        Map<Long, Long> loseIdRelMap = ListUtil.toMap(OrderItemTransferHistory::getNewOrderItemId, OrderItemTransferHistory::getOrderItemId, transferHistoryList);
        // 把所有相关的原单、补件单都查出来
        Set<Long> itemIds = new HashSet<>(orderItemIds);
        itemIds.addAll(loseIdRelMap.keySet());
        itemIds.addAll(loseIdRelMap.values());
        List<FactoryOrder> factoryOrderList = this.findByOrderItemIds(BaseListQueryDTO.of(itemIds
                , "orderItemId,num,status,refuseType,refuseNum,beResendForLose"));
        Map<Long, Integer> passQtyMap = new HashMap<>(factoryOrderList.size());
        factoryOrderList.forEach(i -> {
            if (FactoryOrderStatusEnum.isCancel(i.getStatus())) {
                return;
            }
            Long orderItemId = i.getOrderItemId();
            int passQty = 0;
            if (i.getBeResendForLose() && loseIdRelMap.containsKey(orderItemId)) {
                // 补件单，通过不加，没通过要扣原单数
                if (FactoryOrderStatusEnum.isQc(i.getStatus())) {
                    return;
                }
                orderItemId = loseIdRelMap.get(orderItemId);
                passQty = -i.getNum();
            } else {
                // 正常单/原单
                if (FactoryOrderStatusEnum.isQc(i.getStatus())) {
                    // 通过的加
                    passQty = i.getNum();
                } else if (QcStatusEnum.isResendNoRefund(i.getRefuseType())) {
                    // 没过，但被打回的
                    passQty = i.getNum() - i.getRefuseNum();
                }
            }
            if (passQty == 0) {
                return;
            }
            Integer totalPassQty = passQtyMap.getOrDefault(orderItemId, 0);
            passQtyMap.put(orderItemId, totalPassQty + passQty);
        });
        // 删掉没有通过的子单
        passQtyMap.entrySet().removeIf(i -> i.getValue() <= 0);
        return passQtyMap;
    }

    public void compensationFinish(Long id, Long compensationTime, Double compensationAmount, Integer compensationStatus) {
        this.lambdaUpdate().eq(FactoryOrder::getId, id)
                .set(FactoryOrder::getCompensationTime, compensationTime)
                .setSql("compensation_amount = compensation_amount + " + compensationAmount)
                .setSql("new_total_price = total_price - " + compensationAmount)
                .set(FactoryOrder::getCompensationStatus, compensationStatus).update();
    }

    public List<FactoryOrderDownloadCustomInfoResp> factoryOrderDownloadCustomInfoByTaskId(Long taskId, String downStatus) {
        List<FactoryOrder> factoryOrders = this.lambdaQuery()
                .eq(FactoryOrder::getTaskId, taskId)
                .eq(StringUtils.isNotBlank(downStatus), FactoryOrder::getDownStatus, downStatus)
                .eq(FactoryOrder::getRequireManuscript, BasePoConstant.NO_SIMPLE_STRING)
                .list();
        if (CollectionUtil.isEmpty(factoryOrders)) {
            return Lists.newArrayList();
        }
        DesignProductCustomInfoReqDto customInfoReqDto = new DesignProductCustomInfoReqDto();
        List<Long> orderItemIds = factoryOrders.stream().map(FactoryOrder::getOrderItemId).collect(Collectors.toList());
        customInfoReqDto.setOrderItemIds(orderItemIds);
        Map<Long, OrderItem> orderItemMap = this.orderItemService.findByIds(orderItemIds).stream().collect(Collectors.toMap(OrderItem::getId, Function.identity()));

        List<DesignProductCustomInfoRespDto> designProductCustomInfos = this.designProductFeign.customImageDesignPodInfos(customInfoReqDto);
        Map<String, DesignProductCustomInfoRespDto> productCustomInfoMap = designProductCustomInfos.stream().collect(Collectors.toMap(DesignProductCustomInfoRespDto::getDesignProductId, Function.identity()));

        Set<Long> materialIds = factoryOrders.stream().filter(d -> JSONUtil.isJson(d.getMaterialIds())).flatMap(d -> JSONUtil.toList(d.getMaterialIds(), Long.class).stream())
                .filter(m -> m != 0L).collect(Collectors.toSet());
        Map<Long, MaterialRespDto> materialMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(materialIds)) {
            IdsSearchHelper idsSearchHelper = new IdsSearchHelper();
            idsSearchHelper.setIds(Lists.newArrayList(materialIds));
            idsSearchHelper.setFields("name,id,imgUrl,showUrl,fileCode,sourceType");
            materialMap = this.materialFeign.findByIds(idsSearchHelper).stream().collect(Collectors.toMap(MaterialRespDto::getId, Function.identity()));
        }

        List<Long> factoryOrderIds = factoryOrders.stream().map(FactoryOrder::getId).collect(Collectors.toList());
        Map<Long, String> factoryOrderIdSerialNumberMap = this.factoryOrderSerialService.getFactoryOrderIdSerialNumberMap(factoryOrderIds);

        ArrayList<FactoryOrderDownloadCustomInfoResp> list = Lists.newArrayList();
        int seriesNum = 0;
        for (FactoryOrder factoryOrder : factoryOrders) {
            OrderItem orderItem = orderItemMap.get(factoryOrder.getOrderItemId());
            FactoryOrderDownloadCustomInfoResp factoryOrderDownloadCustomInfo = new FactoryOrderDownloadCustomInfoResp();
            factoryOrderDownloadCustomInfo.setId(factoryOrder.getId());
            factoryOrderDownloadCustomInfo.setSeriesNum(++seriesNum);
            factoryOrderDownloadCustomInfo.setSerialNumber(factoryOrderIdSerialNumberMap.getOrDefault(factoryOrder.getId(), ""));
            factoryOrderDownloadCustomInfo.setProductName(factoryOrder.getProductName());
            factoryOrderDownloadCustomInfo.setProductSku(factoryOrder.getProductSku());
            factoryOrderDownloadCustomInfo.setProductId(factoryOrder.getProductId());
            factoryOrderDownloadCustomInfo.setNo(factoryOrder.getNo());
            factoryOrderDownloadCustomInfo.setProductColorName(factoryOrder.getProductColorName());
            factoryOrderDownloadCustomInfo.setProductSize(factoryOrder.getProductSize());
            factoryOrderDownloadCustomInfo.setNum(factoryOrder.getNum());
            factoryOrderDownloadCustomInfo.setKeyId(orderItem.getKeyId());
            DesignProductCustomInfoRespDto designProductCustomInfoRespDto = productCustomInfoMap.get(factoryOrder.getEndProductId() + "");
            factoryOrderDownloadCustomInfo.setCustomInfoResp(designProductCustomInfoRespDto);
            factoryOrderDownloadCustomInfo.setRemark(designProductCustomInfoRespDto != null ? designProductCustomInfoRespDto.getRemark() : "");
            this.formatMaterialMap(factoryOrder, materialMap, factoryOrderDownloadCustomInfo);
            this.formatImgUrl(designProductCustomInfoRespDto, factoryOrderDownloadCustomInfo);
            list.add(factoryOrderDownloadCustomInfo);
        }
        return list;
    }

    private void formatImgUrl(DesignProductCustomInfoRespDto designProductCustomInfo, FactoryOrderDownloadCustomInfoResp factoryOrderDownloadCustomInfo) {
        if (designProductCustomInfo == null || CollUtil.isEmpty(designProductCustomInfo.getImgUrl())) {
            return;
        }
        List<FactoryOrderDownloadCustomInfoResp.ImageVo> imageVos = Lists.newArrayList();
        for (String imgUrl : designProductCustomInfo.getImgUrl()) {
            if (StringUtils.isBlank(imgUrl)) {
                continue;
            }
            FactoryOrderDownloadCustomInfoResp.ImageVo imageVo = new FactoryOrderDownloadCustomInfoResp.ImageVo();
            imageVo.setImgUrl(imgUrl);
            imageVos.add(imageVo);
        }
        factoryOrderDownloadCustomInfo.setImgUrls(imageVos);
    }


    private void formatMaterialMap(FactoryOrder factoryOrder, Map<Long, MaterialRespDto> materialMap, FactoryOrderDownloadCustomInfoResp factoryOrderDownloadCustomInfo) {
        if (factoryOrder.getMaterialIds() == null || !JSONUtil.isJson(factoryOrder.getMaterialIds())) {
            factoryOrderDownloadCustomInfo.setMaterialUrls(Lists.newArrayList());
            return;
        }
        List<Long> factoryOrderMaterialIds = JSONUtil.toList(factoryOrder.getMaterialIds(), Long.class);
        List<FactoryOrderDownloadCustomInfoResp.ImageVo> imageVos = Lists.newArrayList();
        for (Long materialId : factoryOrderMaterialIds) {
            MaterialRespDto material = materialMap.get(materialId);
            if (material == null) {
                continue;
            }
            FactoryOrderDownloadCustomInfoResp.ImageVo imageVo = new FactoryOrderDownloadCustomInfoResp.ImageVo();
            String imgUrl;
            //素材的官方素材需要解密才行
            if (OFFICIAL_MATERIAL == material.getSourceType()) {
                String[] fileCodes = material.getFileCode().split("\\.");
                if (fileCodes.length != 2) {
                    throw new BusinessException("素材fileCode异常");
                }
                String decFileCode = AesUtil.dec1000ThumbnailFileCode(fileCodes[0]);
                imgUrl = HTTPS_S3_CN_NORTH_URL + S3_OFFICE_IMG_PREVIEW + decFileCode + "." + fileCodes[1];
            } else {
                imgUrl = HTTPS_S3_CN_NORTH_URL + "images/" + S3_PREVIEW + material.getFileCode();
            }
            imageVo.setImgUrl(imgUrl);
            imageVo.setThumbUrl(material.getShowUrl());
            imageVos.add(imageVo);
        }
        factoryOrderDownloadCustomInfo.setMaterialUrls(imageVos);
    }

    public List<PrintTemplateRespDto> printTemplates(Long factoryId) {
        FoProductionPrintTemplateQueryDTO templateQueryDTO = new FoProductionPrintTemplateQueryDTO();
        templateQueryDTO.setFactoryId(factoryId);
        templateQueryDTO.setStatus(BasePoConstant.YES);
        templateQueryDTO.setPage(1);
        templateQueryDTO.setSize(100);
        PageResultDto<FoProductionPrintTemplateRespDTO> templatePage = this.foProductionPrintTemplateManager.pageList(templateQueryDTO);
        List<FoProductionPrintTemplateRespDTO> templatePageList = templatePage.getList();
        if (CollUtil.isEmpty(templatePageList)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(templatePageList, PrintTemplateRespDto.class);
    }

    public static String getVariantIdIdAndSupplyChainTypeKey(String supplyChainTyp, Long variantId) {
        return supplyChainTyp + "#" + variantId;
    }

    public FactoryOrderPrintInfoRespDto printInfos(FactoryOrderPrintInfoReqDto dto) {
        FactoryOrderPrintInfoRespDto factoryOrderPrintInfoRespDto = new FactoryOrderPrintInfoRespDto();

        FoProductionPrintTemplateConfigDTO templateConfigDTO = this.foProductionPrintTemplateManager.getConfig(dto.getPrintTemplateId());
        FactoryOrderPrintInfoRespDto.PrintTemplateBean printTemplateBean = BeanUtil.copyProperties(templateConfigDTO, FactoryOrderPrintInfoRespDto.PrintTemplateBean.class);
        factoryOrderPrintInfoRespDto.setPrintTemplate(printTemplateBean);

        List<FactoryOrderPrintInfoRespDto.FactoryOrdersBean> factoryOrdersBeans = this.printFactoryOrderInfo(dto.getFactoryOrderIds(), dto.getTenantId());
        factoryOrderPrintInfoRespDto.setFactoryOrders(factoryOrdersBeans);

        return factoryOrderPrintInfoRespDto;
    }

    public String genShapeCode(String no) {
        if (StrUtil.isBlank(no)) {
            return "";
        }
        ByteArrayOutputStream out = BarcodeUtil.generateByMsgWidth(no);
        try {
            return this.s3ServiceV2.upload(new ByteArrayInputStream(out.toByteArray()), String.format("barcode_%s_%s.%s", no, BarcodeUtil.BARCODE_VERSION, BarcodeUtil.BARCODE_FORMAT));
        } catch (IOException e) {
            e.printStackTrace();
            throw new BusinessException("barcode generate error");
        }
    }

    public List<FactoryOrderPrintInfoRespDto.FactoryOrdersBean> printFactoryOrderInfo(List<Long> factoryOrderIds, Long tenantId) {
        List<FactoryOrder> factoryOrders = this.findByIds(factoryOrderIds);
        Long factoryId = factoryOrders.get(0).getFactoryId();
        List<String> orderNos = factoryOrders.stream().map(i -> i.getMerchantOrderNo()).distinct().collect(Collectors.toList());
        List<Order> orders = this.orderService.findByNos(orderNos);
        Map<String, ProductSupplyDTO> productSupplyMap = this.getProductSupplyDTOMap(factoryOrders, factoryId);
        // 生产计划
        List<FactoryTaskOrderRel> factoryTaskOrderRels = this.factoryTaskOrderRelService.findByOrderIds(factoryId, factoryOrderIds);
        Map<Long, FactoryTaskOrderRel> factoryTaskOrderRelMap = ListUtil.toMap(FactoryTaskOrderRel::getFactoryOrderId, factoryTaskOrderRels);
        Map<Long, FactoryTask> factoryTaskMap = this.getFactoryTaskMap(factoryId, factoryTaskOrderRels);
        // 预警标签
        List<FactoryOrderRespDto> factoryOrderDtos = RelationsBinder.convertAndBind(factoryOrders, FactoryOrderRespDto.class
                , "orderEarlyWarnings,outPaymentDate,outExpressDate,outHarvestDate,outConfirmDate,outDate,manuscriptFeedbackTime,requireManuscript");
        this.addWarning(factoryOrderDtos);
        // 产品
        Set<Long> variantIds = ListUtil.toValueSet(FactoryOrder::getProductId, factoryOrders);
        List<ProductRespDto> productVariantList = productFeign.getAllByIds(BaseListQueryDTO.of(variantIds, "id,name,englishName"));
        Map<Long, ProductRespDto> productVariantMap = ListUtil.toMap(ProductRespDto::getId, productVariantList);
        // 仓库
        List<Long> orderIds = orders.stream().map(i -> i.getId()).collect(Collectors.toList());
        Map<Long, Warehouse> orderIdWarehouseMap = this.warehouseService.findByOrderIds(orderIds);
        // 物流
        Map<Long, TenantLogisticsRespDto> idLogisticsMap = this.getTenantLogisticsRespDtoMap(orders);
        // 发货方
        List<OrderRespDto> orderRespDtos = BeanUtil.copyToList(orders, OrderRespDto.class);
        this.formatShipmentBelongingTenantName(orderRespDtos);
        Map<String, OrderRespDto> orderRespDtoMap = ListUtil.toMap(OrderRespDto::getNo, orderRespDtos);
        // 发货仓
        Set<Long> issuingBayIds = factoryOrders.stream().map(i -> i.getIssuingBayId()).collect(Collectors.toSet());
        List<IssuingBayRespDto> issuingBayRespDtos = this.issuingBayFeign.findByIds("", issuingBayIds);
        Map<Long, IssuingBayRespDto> issuingBayRespDtoMap = ListUtil.toMap(IssuingBayRespDto::getId, issuingBayRespDtos);
        // 效果图
        List<Long> orderItemIds = factoryOrders.stream().map(FactoryOrder::getOrderItemId).collect(Collectors.toList());
        Map<Long, List<String>> factoryOrderIdPrintImageUrlsMap = this.getFactoryOrderIdPrintImageUrlsMap(factoryOrders, orderItemIds);
        // 供应关系
        List<OrderItemSupplyChainDTO> orderItemSupplyChainDTOS = this.orderItemSupplyChainService.findByIds(orderItemIds);
        Map<Long, OrderItemSupplyChainDTO> orderItemIdKeySupplyChainMap = ListUtil.toMap(OrderItemSupplyChainDTO::getId, orderItemSupplyChainDTOS);
        // 产品导入数据
        Map<Long, ImportProductDto> importProductDtoMap = this.orderItemImportProductService.findMapByIds(orderItemIds);
        // 定制信息
        Map<Long, DesignProductCustomInfoRespDto> factoryOrderIdCustomInfoRespDtoMap = this.designProductFeign.findCustomImageDesignByFactoryOrderIds(BaseListDto.of(factoryOrderIds));
        // 备注
        List<OrderRemark> orderRemarks = this.orderRemarkService.getSortedListByOrderIdsAndType(factoryOrderIds, OrderRemarkTypeEnum.FACTORY_ORDER_TYPE);
        Map<Long, List<OrderRemark>> factoryOrderIdRemarksMap = ListUtil.toMapValueList(OrderRemark::getOrderId, orderRemarks);
        // 生产线
        String productIdsStr = factoryOrders.stream().map(i -> String.valueOf(i.getProductId())).distinct().collect(Collectors.joining(","));
        Map<Long, ProductionLineRespDto> productionLineItemMap = Maps.newLinkedHashMap();
        ProductionLineRespDto defaultProductionLineDto = new ProductionLineRespDto();
        if (null != factoryId) {
            List<ProductionLineRespDto> lineRespDtos = this.productionLineService.listByFactoryAndProductIds(factoryId, productIdsStr);
            productionLineItemMap = lineRespDtos.stream().collect(Collectors.toMap(ProductionLineRespDto::getProductId, ProductionLineItem -> ProductionLineItem));
            defaultProductionLineDto = this.productionLineService.defaultProductionLine(factoryId);
        }
        this.formatPrintNum(factoryOrderDtos);
        // 总件数
        Map<Long, Integer> orderIdProductNumMap = getOrderIdProductNumMap(orderIds);
        // 运单号
        Map<Long, List<String>> orderIdCarriageNosMap = orderService.orderIdCarriageNosMap(orders);
        Map<Long, String> orderIdCarriageNoMap = orderIdCarriageNosMap.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().stream()
                    .distinct()  // 去重
                    .collect(Collectors.joining(",")) // 逗号拼接
            ));

        //分销工厂单id和分销租户id map
        Map<Long, Long> distributorFactoryOrderIdAndTenantIdMap = factoryOrders
                .stream()
                .filter(a -> !TenantCommonConstant.isSdsdiy(tenantId) && !tenantId.equals(a.getTenantId()))
                .collect(Collectors.toMap(FactoryOrder::getId, FactoryOrder::getTenantId));

        Collection<Long> distributorIds = distributorFactoryOrderIdAndTenantIdMap.values();
        Map<Long, String> factoryOrderIdAndDistributorNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(distributorIds)) {
            TenantListReq req = new TenantListReq();
            req.setInIds(new ArrayList<>(distributorIds));
            List<TenantRespDto> tenants = tenantFeign.listDto(req);
            if (CollUtil.isNotEmpty(tenants)) {
                Map<Long, String> tenantNameMap = tenants.stream().collect(Collectors.toMap(TenantRespDto::getId, TenantRespDto::getName));
                for (Map.Entry<Long, Long> entry : distributorFactoryOrderIdAndTenantIdMap.entrySet()) {
                    factoryOrderIdAndDistributorNameMap.put(entry.getKey(), tenantNameMap.get(entry.getValue()));
                }
            }
        }

        List<FactoryOrderPrintInfoRespDto.FactoryOrdersBean> printInfos = Lists.newArrayList();
        for (FactoryOrderRespDto factoryOrderDto : factoryOrderDtos) {
            ProductRespDto productVariant = productVariantMap.get(factoryOrderDto.getProductId());
            FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean = new FactoryOrderPrintInfoRespDto.FactoryOrdersBean();
            factoryOrdersBean.setId(factoryOrderDto.getId());
            factoryOrdersBean.setNo(factoryOrderDto.getNo());
            factoryOrdersBean.setRequireManuscript(factoryOrderDto.getRequireManuscript());
            factoryOrdersBean.setMerchantOrderNo(factoryOrderDto.getMerchantOrderNo());
            factoryOrdersBean.setNum(factoryOrderDto.getNum());
            factoryOrdersBean.setMerchantNo(factoryOrderDto.getMerchantNo());
            factoryOrdersBean.setProductName(productVariant.getName());
            factoryOrdersBean.setCreateTime(cn.hutool.core.date.DateUtil.formatDateTime(factoryOrderDto.getCreateTime()));
            factoryOrdersBean.setPrintImageUrls(factoryOrderIdPrintImageUrlsMap.getOrDefault(factoryOrderDto.getId(), Collections.emptyList()));
            OrderRespDto order = orderRespDtoMap.get(factoryOrderDto.getMerchantOrderNo());
            Assert.validateNull(order, "客户订单不存在，工厂单号：" + factoryOrderDto.getNo());

            factoryOrdersBean.setOutOrderNo(order.getOutOrderNo());
            formatImportSku(factoryOrderDto, factoryOrdersBean, importProductDtoMap);
            formatProductSupply(productSupplyMap, orderItemIdKeySupplyChainMap, factoryOrderDto, factoryOrdersBean);
            formatFactoryTask(factoryTaskOrderRelMap, factoryTaskMap, factoryOrderDto, factoryOrdersBean);
            formatOrderType(idLogisticsMap, factoryOrderDto, factoryOrdersBean, order);
            formatInWarehouse(orderIdWarehouseMap, factoryOrdersBean, order);
            formatProduct(factoryOrderDto, factoryOrdersBean, productVariant);
            formatTexture(factoryOrderDto, factoryOrdersBean);
            formatIssuingBay(issuingBayRespDtoMap, factoryOrderDto, factoryOrdersBean);
            factoryOrdersBean.setShipper(order.getShipmentBelongingTenantName());
            formatLogistics(idLogisticsMap, factoryOrdersBean, order);
            formatProductionLine(productionLineItemMap, defaultProductionLineDto, factoryOrderDto, factoryOrdersBean);
            formatOrderEarlyWarnings(factoryOrderDto, factoryOrdersBean);
            formatCustomizes(factoryOrderIdCustomInfoRespDtoMap, factoryOrderDto, factoryOrdersBean);
            this.formatRemarks(factoryOrderIdRemarksMap, factoryOrderDto, factoryOrdersBean);

            factoryOrdersBean.setOrderBarCodeUrl(this.genShapeCode(factoryOrdersBean.getMerchantOrderNo()));
            factoryOrdersBean.setNoBarCodeUrl(this.genShapeCode(factoryOrdersBean.getNo()));
            factoryOrdersBean.setProductSupplyCodeBarCodeUrl(null != factoryOrdersBean.getProductSupply() ? this.genShapeCode(factoryOrdersBean.getProductSupply().getCode()) : "");
            factoryOrdersBean.setOrderProductNum(orderIdProductNumMap.get(order.getId()));
            factoryOrdersBean.setCarriageNo(orderIdCarriageNoMap.get(order.getId()));
            factoryOrdersBean.setDistributorName(factoryOrderIdAndDistributorNameMap.get(factoryOrderDto.getId()));
            printInfos.add(factoryOrdersBean);
        }
        // 针对旧数据没有颜色备注
        this.formatColorRemark(printInfos);
        return printInfos;
    }

    @NotNull
    private Map<Long, Integer> getOrderIdProductNumMap(List<Long> orderIds) {
        List<OrderItem> orderItems = orderItemService.findByOrderIds(orderIds);
        List<OrderItem> realItems = OrderItemService.filterRealItem(orderItems);
        // realItems根据orderId分组，num求和
        return realItems.stream()
            .collect(Collectors.groupingBy(
                OrderItem::getOrderId,
                Collectors.summingInt(OrderItem::getNum)
            ));
    }

    private void formatImportSku(FactoryOrderRespDto factoryOrderDto
            , FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean, Map<Long, ImportProductDto> importProductDtoMap) {
        ImportProductDto importProductDto = importProductDtoMap.get(factoryOrderDto.getOrderItemId());
        if (importProductDto != null) {
            factoryOrdersBean.setImportSku(importProductDto.getImportSku());
        }
    }

    private void formatPrintNum(List<FactoryOrderRespDto> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        for (FactoryOrderRespDto resp : records) {
            Integer refuseType = resp.getRefuseType();
            if (NumberUtils.greaterZero(refuseType)) {
                if (refuseType.equals(QcStatusEnum.REJECT_REFUND.code)) {
                    resp.setRejectCancel(resp.getRefuseNum());
                    // 如果是驳回取消或者漏件取消的话，总数 == num + refuseNum
                    // 驳回 漏件则总数 == num
                    resp.setNum(resp.getNum() + resp.getRefuseNum());
                } else if (refuseType.equals(QcStatusEnum.LESS_REFUND.code)) {
                    resp.setLoseCancel(resp.getRefuseNum());
                    resp.setNum(resp.getNum() + resp.getRefuseNum());
                }
            }
            if (QcStatusEnum.isResendNoRefund(resp.getRefuseType()) && resp.getRefuseNum() > 0) {
                resp.setNum(resp.getRefuseNum());
            }
        }
    }

    private void formatColorRemark(List<FactoryOrderPrintInfoRespDto.FactoryOrdersBean> printInfos) {
        if (CollUtil.isEmpty(printInfos)) {
            return;
        }
        List<FactoryOrderPrintInfoRespDto.FactoryOrdersBean> notColorRemarkFactoryOrders = printInfos.stream()
                .filter(i -> null != i.getProduct())
                .filter(i -> StrUtil.isBlank(i.getProduct().getColorRemark())).collect(Collectors.toList());
        if (CollUtil.isEmpty(notColorRemarkFactoryOrders)) {
            return;
        }
        String colorIdsStr = notColorRemarkFactoryOrders.stream()
                .map(i -> String.valueOf(i.getProduct().getColorId()))
                .distinct()
                .collect(Collectors.joining(","));
        List<com.sdsdiy.productapi.dto.color.ColorDto> colorDtos = this.colorFeign.getColorByids("", colorIdsStr);
        Map<Long, com.sdsdiy.productapi.dto.color.ColorDto> idColorMap = ListUtil.toMap(BaseDTO::getId, colorDtos);
        for (FactoryOrderPrintInfoRespDto.FactoryOrdersBean notColorRemarkFactoryOrder : notColorRemarkFactoryOrders) {
            Long colorId = notColorRemarkFactoryOrder.getProduct().getColorId();
            com.sdsdiy.productapi.dto.color.ColorDto colorDto = idColorMap.get(colorId);
            if (null != colorDto) {
                notColorRemarkFactoryOrder.getProduct().setColorRemark(colorDto.getChineseNotes());
            }
        }
    }

    private Map<Long, TenantLogisticsRespDto> getTenantLogisticsRespDtoMap(List<Order> orders) {
        List<Long> logisticsIds = orders.stream().map(i -> i.getLogisticsId()).distinct().collect(Collectors.toList());
        List<TenantLogisticsRespDto> logisticsRespDtos = this.tenantLogisticsFeign.findByIds(new BaseListReqDto(logisticsIds));
        Map<Long, TenantLogisticsRespDto> idLogisticsMap = ListUtil.toMap(TenantLogisticsRespDto::getId, logisticsRespDtos);
        return idLogisticsMap;
    }

    @NotNull
    private Map<Long, FactoryTask> getFactoryTaskMap(Long factoryId, List<FactoryTaskOrderRel> factoryTaskOrderRels) {
        Set<Long> taskIds = factoryTaskOrderRels.stream().map(FactoryTaskOrderRel::getTaskId).collect(Collectors.toSet());
        List<FactoryTask> factoryTasks = this.factoryTaskService.findByIds(factoryId, taskIds);
        Map<Long, FactoryTask> factoryTaskMap = factoryTasks.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        return factoryTaskMap;
    }

    @NotNull
    private Map<Long, List<String>> getFactoryOrderIdPrintImageUrlsMap(List<FactoryOrder> factoryOrders,
                                                                       List<Long> orderItemIds) {
        List<Long> factoryOrderIds = factoryOrders.stream().map(i -> i.getId()).collect(Collectors.toList());
        Map<Long, OrderItem> orderItemMap = this.orderItemService.findByIds(orderItemIds).stream().collect(Collectors.toMap(OrderItem::getId, Function.identity()));
        Map<Long, List<String>> factoryOrderIdPrintImageUrlsMap = Maps.newHashMap();
        Map<Long, FactoryOrderExtraResp> factoryOrderIdKeyExtraValueMap = this.factoryOrderExtraService.getFactoryOrderIdExtraValueMap(factoryOrderIds);
        for (FactoryOrder factoryOrder : factoryOrders) {
            List<String> printingUrls = Lists.newArrayList();
            if (StringUtils.isNotBlank(factoryOrder.getImgs())) {
                printingUrls = JSONUtil.toList(factoryOrder.getImgs(), String.class);
            }
            FactoryOrderExtraResp extra = factoryOrderIdKeyExtraValueMap.get(factoryOrder.getId());
            if (extra != null) {
                if (StrUtil.isNotEmpty(extra.getPrintImageUrls())) {
                    printingUrls = JSONUtil.toList(extra.getPrintImageUrls(), String.class);
                }
            }
            if (CollectionUtil.isNotEmpty(printingUrls)) {
                // 第一张包含有临时的图片的重新获取并保存
                OrderItem orderItem = orderItemMap.get(factoryOrder.getOrderItemId());
                if (printingUrls.get(0).contains("shengchengzhong.png") && orderItem != null && StrUtil.isNotBlank(orderItem.getCompoundId())) {
                    List<String> printImages = this.designProductImageFeign.findPrintImage(factoryOrder.getMerchantId(), orderItem.getCompoundId());
                    if (CollectionUtil.isNotEmpty(printImages)) {
                        printingUrls = printImages;
                    }
                }
            }
            factoryOrderIdPrintImageUrlsMap.put(factoryOrder.getId(), printingUrls);

        }
        return factoryOrderIdPrintImageUrlsMap;
    }

    private void formatRemarks(Map<Long, List<OrderRemark>> factoryOrderIdRemarksMap, FactoryOrderRespDto factoryOrderDto, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean) {
        List<RemarksBean> remarks = this.getRemarksBeans(factoryOrderIdRemarksMap, factoryOrderDto);
        factoryOrdersBean.setRemarks(remarks);
    }

    private static void formatCustomizes(Map<Long, DesignProductCustomInfoRespDto> factoryOrderIdCustomInfoRespDtoMap, FactoryOrderRespDto factoryOrderDto, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean) {
        List<CustomizesBean> customizes = getCustomizesBeans(factoryOrderIdCustomInfoRespDtoMap, factoryOrderDto);
        factoryOrdersBean.setCustomizes(customizes);
    }

    private static void formatOrderEarlyWarnings(FactoryOrderRespDto factoryOrderDto, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean) {
        List<OrderEarlyWarningsBean> orderEarlyWarningBeans = getOrderEarlyWarningsBeans(factoryOrderDto);
        factoryOrdersBean.setOrderEarlyWarnings(orderEarlyWarningBeans);
    }

    private static void formatProductionLine(Map<Long, ProductionLineRespDto> productionLineItemMap, ProductionLineRespDto defaultProductionLineDto, FactoryOrderRespDto factoryOrderDto, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean) {
        ProductionLineBean productionLineBean = getProductionLineBean(productionLineItemMap, defaultProductionLineDto, factoryOrderDto);
        factoryOrdersBean.setProductionLine(productionLineBean);
    }

    private static void formatLogistics(Map<Long, TenantLogisticsRespDto> idLogisticsMap, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean, OrderRespDto order) {
        LogisticsBean logisticsBean = getLogisticsBean(idLogisticsMap, order);
        factoryOrdersBean.setLogistics(logisticsBean);
    }

    private static void formatIssuingBay(Map<Long, IssuingBayRespDto> issuingBayRespDtoMap, FactoryOrderRespDto factoryOrderDto, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean) {
        IssuingBayBean issuingBayBean = getIssuingBayBean(issuingBayRespDtoMap, factoryOrderDto);
        factoryOrdersBean.setIssuingBay(issuingBayBean);
    }

    private static void formatTexture(FactoryOrderRespDto factoryOrderDto, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean) {
        TextureBean texture = getTextureBean(factoryOrderDto);
        factoryOrdersBean.setTexture(texture);
    }

    private static void formatProduct(FactoryOrderRespDto factoryOrderDto
            , FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean
            , ProductRespDto productVariant) {
        ProductBean product = getProductBean(factoryOrderDto, productVariant);
        factoryOrdersBean.setProduct(product);
    }

    private static void formatInWarehouse(Map<Long, Warehouse> orderIdWarehouseMap, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean, OrderRespDto order) {
        Warehouse warehouse = null;
        Boolean inWarehouse = Boolean.FALSE;
        if (null != order) {
            warehouse = orderIdWarehouseMap.get(order.getId());
            if (null != warehouse) {
                inWarehouse = Boolean.TRUE;
            }
        }
        factoryOrdersBean.setInWarehouse(inWarehouse);
        factoryOrdersBean.setWarehouseNo(null != warehouse ? warehouse.getLevel() + "-" + warehouse.getNo() + "-" + warehouse.getNum() : "");
    }

    private static void formatOrderType(Map<Long, TenantLogisticsRespDto> idLogisticsMap, FactoryOrderRespDto factoryOrderDto, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean, OrderRespDto order) {
        String orderType = getOrderType(idLogisticsMap, factoryOrderDto, order);
        factoryOrdersBean.setOrderType(orderType);
    }

    private static void formatFactoryTask(Map<Long, FactoryTaskOrderRel> factoryTaskOrderRelMap, Map<Long, FactoryTask> factoryTaskMap, FactoryOrderRespDto factoryOrderDto, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean) {
        FactoryTaskBean factoryTaskBean = getFactoryTaskBean(factoryTaskOrderRelMap, factoryTaskMap, factoryOrderDto);
        factoryOrdersBean.setFactoryTask(factoryTaskBean);
    }

    private static void formatProductSupply(Map<String, ProductSupplyDTO> productSupplyMap, Map<Long, OrderItemSupplyChainDTO> orderItemIdKeySupplyChainMap, FactoryOrderRespDto factoryOrderDto, FactoryOrderPrintInfoRespDto.FactoryOrdersBean factoryOrdersBean) {
        ProductSupplyBean productSupplyBean = getProductSupplyBean(productSupplyMap, orderItemIdKeySupplyChainMap, factoryOrderDto);
        factoryOrdersBean.setProductSupply(productSupplyBean);
    }

    @NotNull
    private List<RemarksBean> getRemarksBeans(Map<Long, List<OrderRemark>> factoryOrderIdRemarksMap, FactoryOrderRespDto factoryOrderDto) {
        List<RemarksBean> remarks = Lists.newArrayList();
        List<OrderRemark> orderRemarkList = factoryOrderIdRemarksMap.get(factoryOrderDto.getId());
        if (CollUtil.isNotEmpty(orderRemarkList)) {
            for (OrderRemark orderRemark : orderRemarkList) {
                RemarksBean remarksBean = new RemarksBean();
                remarksBean.setRemark(orderRemark.getRemark());
                remarksBean.setUserName(orderRemark.getUsername());
                remarksBean.setCreateTime(this.getGMTTime(orderRemark.getCreatedTime()));
                remarks.add(remarksBean);
            }
        }
        return remarks;
    }

    public String getGMTTime(Long time) {
        String str = null;
        if (time != null) {
            str = DateUtil.longToString(time);
        }
        return str;
    }

    @NotNull
    private static List<CustomizesBean> getCustomizesBeans(Map<Long, DesignProductCustomInfoRespDto> factoryOrderIdCustomInfoRespDtoMap, FactoryOrderRespDto factoryOrderDto) {
        DesignProductCustomInfoRespDto customInfoRespDto = factoryOrderIdCustomInfoRespDtoMap.get(factoryOrderDto.getId());
        if (null == customInfoRespDto || CollUtil.isEmpty(customInfoRespDto.getLayers())) {
            return Collections.emptyList();
        }
        if (!DesignProductTypeConstant.notDesign(customInfoRespDto.getDesignProductType())) {
            return Collections.emptyList();
        }
        List<CustomizesBean> customizes = Lists.newArrayList();
        List<DesignProductCustomLayerInfoRespDto> customLayers = customInfoRespDto.getLayers();
        for (DesignProductCustomLayerInfoRespDto customLayer : customLayers) {
            CustomizesBean customizesBean = new CustomizesBean();
            BeanUtil.copyProperties(customLayer, customizesBean);
            List<DesignProductCustomLayerInfoRespDto.FileUrlVo> fileUrls = customLayer.getFileUrls();
            if (CollUtil.isNotEmpty(fileUrls)) {
                List<CustomizesBean.FileUrlsBean> fileUrlsBeans = BeanUtil.copyToList(fileUrls, CustomizesBean.FileUrlsBean.class);
                customizesBean.setFileUrls(fileUrlsBeans);
            }
            customizes.add(customizesBean);
        }
        return customizes;
    }

    @NotNull
    private static List<OrderEarlyWarningsBean> getOrderEarlyWarningsBeans(FactoryOrderRespDto factoryOrderDto) {
        List<OrderEarlyWarningsBean> orderEarlyWarningBeans = Lists.newArrayList();
        List<OrderEarlyWarningDto> orderEarlyWarnings = factoryOrderDto.getOrderEarlyWarnings();
        if (CollUtil.isNotEmpty(orderEarlyWarnings)) {
            for (OrderEarlyWarningDto orderEarlyWarning : orderEarlyWarnings) {
                if (null == orderEarlyWarning.getEarlyWarningType()) {
                    continue;
                }
                OrderEarlyWarningsBean orderEarlyWarningsBean = new OrderEarlyWarningsBean();
                orderEarlyWarningsBean.setName(orderEarlyWarning.getEarlyWarningType().getName());
                orderEarlyWarningBeans.add(orderEarlyWarningsBean);
            }
        }
        return orderEarlyWarningBeans;
    }

    @NotNull
    private static ProductionLineBean getProductionLineBean(Map<Long, ProductionLineRespDto> productionLineItemMap, ProductionLineRespDto defaultProductionLineDto, FactoryOrderRespDto factoryOrderDto) {
        ProductionLineBean productionLineBean = new ProductionLineBean();
        ProductionLineRespDto productionLineRespDto = productionLineItemMap.get(factoryOrderDto.getProductId());
        if (null != productionLineRespDto) {
            productionLineBean.setName(productionLineRespDto.getName());
        } else {
            productionLineBean.setName(defaultProductionLineDto.getName());
        }
        return productionLineBean;
    }

    @Nullable
    private static LogisticsBean getLogisticsBean(Map<Long, TenantLogisticsRespDto> idLogisticsMap, OrderRespDto order) {
        LogisticsBean logisticsBean = null;
        TenantLogisticsRespDto tenantLogisticsRespDto = idLogisticsMap.get(order.getLogisticsId());
        if (null != tenantLogisticsRespDto) {
            logisticsBean = new LogisticsBean();
            logisticsBean.setName(tenantLogisticsRespDto.getName());
        }
        return logisticsBean;
    }

    @Nullable
    private static IssuingBayBean getIssuingBayBean(Map<Long, IssuingBayRespDto> issuingBayRespDtoMap, FactoryOrderRespDto factoryOrderDto) {
        IssuingBayBean issuingBayBean = null;
        IssuingBayRespDto issuingBayRespDto = issuingBayRespDtoMap.get(factoryOrderDto.getIssuingBayId());
        if (null != issuingBayRespDto) {
            issuingBayBean = new IssuingBayBean();
            issuingBayBean.setName(issuingBayRespDto.getName());
        }
        return issuingBayBean;
    }

    @NotNull
    private static TextureBean getTextureBean(FactoryOrderRespDto factoryOrderDto) {
        TextureBean texture = new TextureBean();
        texture.setName(factoryOrderDto.getTextureName());
        texture.setRemark(factoryOrderDto.getTextureChineseNotes());
        return texture;
    }

    @NotNull
    private static ProductBean getProductBean(FactoryOrderRespDto factoryOrderDto, ProductRespDto productVariant) {
        ProductBean product = new ProductBean();
        product.setId(factoryOrderDto.getProductId());
        product.setSize(factoryOrderDto.getProductSize());
        product.setSizeRemark(factoryOrderDto.getProductSizeRemark());
        ColorDto colorDto = JSONUtil.toBean(factoryOrderDto.getProdutColorBlock(), ColorDto.class);
        product.setColorName(colorDto.getColorName());
        product.setColorRemark(colorDto.getChineseName());
        product.setColorId(colorDto.getColorId());
        product.setProductNameEn(productVariant.getEnglishName());
        return product;
    }

    private static String getOrderType(Map<Long, TenantLogisticsRespDto> idLogisticsMap, FactoryOrderRespDto factoryOrderDto, OrderRespDto order) {
        if (null == order) {
            return "";
        }
        String orderType = "";
        if (factoryOrderDto.getOriginType().equals(OrderOriginType.FBA.getValue())) {
            orderType = EnumFactoryOrderPrintOrderType.FBA.getValue();
        } else if (order.getLogisticsCodeId().equals(LogisticsCodeIdEnum.CONSIGNMENT.getCodeId())) {
            orderType = EnumFactoryOrderPrintOrderType.JF.getValue();
        } else if (LogisticsCodeIdEnum.isNormalZt(order.getLogisticsCodeId())) {
            orderType = EnumFactoryOrderPrintOrderType.ZT.getValue();
        } else if (order.getExpressType().equals(EnumExpressType.URGENT.getValue())) {
            orderType = EnumFactoryOrderPrintOrderType.TK.getValue();
            TenantLogisticsRespDto tenantLogisticsRespDto = idLogisticsMap.get(order.getLogisticsId());
            if (null != tenantLogisticsRespDto && LogisticsServiceProviderEnum.includeDhl(Collections.singleton(tenantLogisticsRespDto.getServiceProviderId()))) {
                orderType = EnumFactoryOrderPrintOrderType.DHL.getValue();
            }
        }
        return orderType;
    }

    @NotNull
    private static Boolean getInWarehouse(OrderRespDto order, Map<Long, Warehouse> orderIdWarehouseMap) {
        Boolean inWarehouse = Boolean.FALSE;
        if (null != order) {
            Warehouse warehouse = orderIdWarehouseMap.get(order.getId());
            if (null != warehouse) {
                inWarehouse = Boolean.TRUE;
            }
        }
        return inWarehouse;
    }

    @Nullable
    private static FactoryTaskBean getFactoryTaskBean(Map<Long, FactoryTaskOrderRel> factoryTaskOrderRelMap, Map<Long, FactoryTask> factoryTaskMap, FactoryOrderRespDto factoryOrderDto) {
        FactoryTaskBean factoryTaskBean = null;
        FactoryTaskOrderRel factoryTaskOrderRel = factoryTaskOrderRelMap.get(factoryOrderDto.getId());
        if (null != factoryTaskOrderRel) {
            FactoryTask factoryTask = factoryTaskMap.get(factoryTaskOrderRel.getTaskId());
            if (null != factoryTask) {
                factoryTaskBean = new FactoryTaskBean();
                factoryTaskBean.setName(factoryTask.getName());
                factoryTaskBean.setNo(factoryTask.getNo());
            }
        }
        return factoryTaskBean;
    }

    @NotNull
    private Map<String, ProductSupplyDTO> getProductSupplyDTOMap(List<FactoryOrder> factoryOrders, Long factoryId) {
        Set<Long> productIds = factoryOrders.stream().map(FactoryOrder::getProductId).collect(Collectors.toSet());
        ProductSupplyFactoryIdsProductIdsDTO supplyDto = new ProductSupplyFactoryIdsProductIdsDTO();
        supplyDto.setFactoryIds(Collections.singleton(factoryId));
        supplyDto.setProductIds(productIds);
        List<ProductSupplyDTO> productSupplyDTOS = this.productSupplyFeign.findByFactoryIdsProductIds(supplyDto);
        Map<String, ProductSupplyDTO> productSupplyMap = Maps.newHashMap();
        for (ProductSupplyDTO ps : productSupplyDTOS) {
            String key = getVariantIdIdAndSupplyChainTypeKey(ps.getSupplyChainType(), ps.getProductId());
            productSupplyMap.put(key, ps);
        }
        return productSupplyMap;
    }

    @NotNull
    private static ProductSupplyBean getProductSupplyBean(Map<String, ProductSupplyDTO> productSupplyMap, Map<Long, OrderItemSupplyChainDTO> orderItemIdKeySupplyChainMap, FactoryOrderRespDto factoryOrderDto) {
        OrderItemSupplyChainDTO supplyChainDTO = orderItemIdKeySupplyChainMap.get(factoryOrderDto.getOrderItemId());
        String chainType = supplyChainDTO == null ? SupplyChainTypeEnum.ONE_PIECE.name() : supplyChainDTO.getSupplyType();
        String variantIdIdAndSupplyChainTypeKey = getVariantIdIdAndSupplyChainTypeKey(chainType, factoryOrderDto.getProductId());
        ProductSupplyDTO productSupplyDTO = productSupplyMap.get(variantIdIdAndSupplyChainTypeKey);
        ProductSupplyBean productSupplyBean = null;
        if (null != productSupplyDTO) {
            productSupplyBean = new ProductSupplyBean();
            productSupplyBean.setName(productSupplyDTO.getName());
            productSupplyBean.setCode(productSupplyDTO.getCode());
        }
        return productSupplyBean;
    }


    public Boolean haveProcessingOrder(List<Long> itemIds) {
        if (Objects.isNull(itemIds) || itemIds.isEmpty()) {
            return Boolean.FALSE;
        }
        Integer count = this.lambdaQuery().in(FactoryOrder::getOrderItemId, itemIds)
                .notIn(FactoryOrder::getStatus,
                        Arrays.asList(FactoryOrderStatusEnum.ACCOMPLISH.status,
                                FactoryOrderStatusEnum.CANCEL.status))
                .count();
        return count.compareTo(BasePoConstant.INTEGER_ZERO) > 0;
    }

    public int factoryOrdersDownManuscriptNum(List<Long> orderItemIds) {
        return this.baseMapper.factoryOrdersDownManuscriptNum(orderItemIds);
    }

    public List<FactoryOrderByProductionLineGroupingDto> productionLineGroupingByEs(FactoryOrderQueryParameters factoryOrderQueryParameters) {

        List<FactoryOrderByProductionLineGroupingDto> productGroupingDtoList;
        Long factoryId = factoryOrderQueryParameters.getFactoryId();

        EsChainQueryWrapper<FactoryOrderEsPO> wrapper = this.factoryOrderEsQueryService.genFactoryEndEsQueryWrapper(factoryOrderQueryParameters);
        TermsAggregationBuilder productLineBuilder = AggregationBuilders
                .terms("productLineAgg")
                .field("productionLineId")
                .size(Integer.MAX_VALUE);

        wrapper.addAggregationBuilder(productLineBuilder);
        Aggregations aggregations = wrapper.aggregation();

        Terms productLineAgg = aggregations.get("productLineAgg");
        List<Long> productionLineIds = productLineAgg.getBuckets().stream()
                .map(bucket -> Long.valueOf(bucket.getKeyAsString()))
                .collect(Collectors.toList());

        Map<Long, Long> productionLineCountMap = productLineAgg.getBuckets().stream()
                .collect(Collectors.toMap(
                        bucket -> Long.valueOf(bucket.getKeyAsString()),
                        Terms.Bucket::getDocCount
                ));
        if (CollectionUtil.isEmpty(productionLineIds)) {
            return Lists.newArrayList();
        }

        List<ProductionLineRespDto> lineRespDtos = this.productionLineService.listByIds(factoryId, productionLineIds);
        Map<Long, ProductionLineRespDto> productLineMap = lineRespDtos.stream().collect(Collectors.toMap(ProductionLineRespDto::getId, ProductionLineItem -> ProductionLineItem));
        //默认生产线
        ProductionLineRespDto productionLineDto = this.productionLineService.defaultProductionLine(factoryId);
        Map<Long, FactoryOrderByProductionLineGroupingDto> groupingDtoMap = Maps.newConcurrentMap();

        for (Long productionLineId : productionLineIds) {
            FactoryOrderByProductionLineGroupingDto factoryOrderByProductionLineGroupingDto = new FactoryOrderByProductionLineGroupingDto();
            if (productLineMap.containsKey(productionLineId)) {
                ProductionLineRespDto productionLineRespDto = productLineMap.get(productionLineId);
                this.assemblyProductionLineGroupingByEs(groupingDtoMap, productionLineRespDto, productionLineCountMap, factoryOrderByProductionLineGroupingDto, productionLineRespDto.getId());
            } else {
                this.assemblyProductionLineGroupingByEs(groupingDtoMap, productionLineDto, productionLineCountMap, factoryOrderByProductionLineGroupingDto, productionLineDto.getId());
            }
        }

        productGroupingDtoList = new ArrayList<>(groupingDtoMap.values());

        return productGroupingDtoList;
    }

    private void assemblyProductionLineGroupingByEs(Map<Long, FactoryOrderByProductionLineGroupingDto> map, ProductionLineRespDto productionLineDto, Map<Long, Long> productionLineCountMap, FactoryOrderByProductionLineGroupingDto factoryOrderByProductionLineGroupingDto, Long id) {
        if (map.containsKey(id)) {
            FactoryOrderByProductionLineGroupingDto dto = map.get(id);
            dto.setNum(dto.getNum() + productionLineCountMap.get(id).intValue());
            dto.setProductionLineName(productionLineDto.getName());

            dto.setProductionLineId(id);
            map.put(id, dto);
        } else {
            factoryOrderByProductionLineGroupingDto.setNum(productionLineCountMap.get(id).intValue());
            factoryOrderByProductionLineGroupingDto.setProductionLineName(productionLineDto.getName());

            factoryOrderByProductionLineGroupingDto.setProductionLineId(id);
            map.put(id, factoryOrderByProductionLineGroupingDto);
        }
    }

    public List<Long> listEsIdsByPodCondition(PodQueryFactoryOrderEsParam param) {

        EsChainQueryWrapper<FactoryOrderEsPO> wrapper = factoryOrderEsQueryService
                .genPodEndQueryWrapper(param.getTenantId(), param.getLastThreeMonths(), param.getUserId(),
                        param.getStartTime(), param.getEndTime(), param.getFactoryId(), param.getKeyword(),
                        param.getStatus(), param.getMerchantOrProductOrCrmUserName(), param.getIsManuscriptOver(),
                        param.getEarlyWarningId(), param.getIssuingBayAreaId(), param.getIssuingBayId(),
                        param.getOriginType(), param.getFinishedStartTime(), param.getFinishedEndTime(),
                        param.getOrderItemSupplyChainType(), param.getLogisticsNo(),
                        param.getBeginExpressTime(), param.getEndExpressTime(), param.getDistributorTenantId());
        if (Objects.isNull(param.getPage())) {
            param.setPage(1);
        }
        if (Objects.isNull(param.getSize())) {
            param.setSize(10000000);
        }
        wrapper.orderByDesc("createTime");
        EsResponse<FactoryOrderEsPO> esResponse = wrapper.page(param.getPage(), param.getSize());
        if (esResponse.getTotal() > 0) {
            return esResponse.getList().stream().map(FactoryOrderEsPO::getId).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    public FactoryOrderStatRespDTO factoryOrderEsStatByPodCondition(PodQueryFactoryOrderEsParam param) {

        EsChainQueryWrapper<FactoryOrderEsPO> wrapper = factoryOrderEsQueryService
                .genPodEndQueryWrapper(param.getTenantId(), param.getLastThreeMonths(), param.getUserId(),
                        param.getStartTime(), param.getEndTime(), param.getFactoryId(), param.getKeyword(),
                        param.getStatus(), param.getMerchantOrProductOrCrmUserName(), param.getIsManuscriptOver(),
                        param.getEarlyWarningId(), param.getIssuingBayAreaId(), param.getIssuingBayId(),
                        param.getOriginType(), param.getFinishedStartTime(), param.getFinishedEndTime(),
                        param.getOrderItemSupplyChainType(), param.getLogisticsNo(), param.getBeginExpressTime(),
                        param.getEndExpressTime(), param.getDistributorTenantId());
        wrapper.addAggregationBuilder(AggregationBuilders.count("idCount").field("id"));
        wrapper.addAggregationBuilder(AggregationBuilders.sum("numSum").field("num"));

        Aggregations aggregations = wrapper.aggregation();

        ParsedValueCount idCount = aggregations.get("idCount");
        long idTotal = idCount.getValue();

        ParsedSum numSum = aggregations.get("numSum");
        double numTotal = numSum.getValue();
        FactoryOrderStatRespDTO res = new FactoryOrderStatRespDTO();
        res.setQty((int) idTotal);
        res.setNum((int) numTotal);
        return res;
    }

    public List<FactoryOrderDto> findByIdsAndStatus(List<Long> ids,FactoryOrderStatusEnum statusEnum){
        if(CollUtil.isEmpty(ids)){
            return Collections.emptyList();
        }
        List<FactoryOrder> orders = lambdaQuery()
                .in(FactoryOrder::getId, ids)
                .eq(FactoryOrder::getStatus, statusEnum.getStatus())
                .list();
        return ListUtil.copyProperties(orders, FactoryOrderDto.class);
    }

    private List<FactoryOrder> factoryOrderListSortByEsList(List<Long> factoryIdList, List<FactoryOrder> factoryOrderList) {

        if (CollUtil.isEmpty(factoryIdList) || CollUtil.isEmpty(factoryOrderList)) {
            return new ArrayList<>();
        }

        Map<Long, FactoryOrder> factoryOrderMap = factoryOrderList
                .stream()
                .collect(Collectors.toMap(FactoryOrder::getId, Function.identity()));

        List<FactoryOrder> res = new ArrayList<>(factoryIdList.size());
        for (Long id : factoryIdList) {
            res.add(factoryOrderMap.get(id));
        }
        return res;
    }

    public void formatDistributorTenantName(Long tenantId, List<FactoryOrderRespDto> items) {
        if (CollUtil.isEmpty(items)) {
            return;
        }
        List<Long> distributorTenantIds = items
                .stream()
                .map(FactoryOrderRespDto::getTenantId)
                .filter(a -> !tenantId.equals(a))
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(distributorTenantIds)) {
            return;
        }
        TenantListReq req = new TenantListReq();
        req.setInIds(distributorTenantIds);
        List<TenantRespDto> tenantRespDtos = tenantFeign.listDto(req);
        Map<Long, String> distributorIdNameMap = tenantRespDtos.stream().collect(Collectors.toMap(TenantRespDto::getId, TenantRespDto::getName));
        for (FactoryOrderRespDto dto : items) {
            dto.setDistributorName(distributorIdNameMap.get(dto.getTenantId()));
        }
    }
}


