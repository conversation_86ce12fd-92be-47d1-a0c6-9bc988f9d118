package com.sdsdiy.orderimpl.controller;

import cn.hutool.core.bean.BeanUtil;
import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.orderapi.api.OrderAmountApi;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderdata.dto.OrderAmountAfterOrderDto;
import com.sdsdiy.orderdata.dto.OrderAmountDTO;
import com.sdsdiy.orderdata.dto.create.OrderAmountCreateDTO;
import com.sdsdiy.orderdata.dto.order.amount.OrderServiceAmountRespDTO;
import com.sdsdiy.orderdata.dto.order.update.OrderDecreaseTenantFreeGoldDTO;
import com.sdsdiy.orderdata.dto.order.update.OrderFreeGoldUpdateDTO;
import com.sdsdiy.orderdata.dto.order.update.OrderLogisticsUpdateBatchParam;
import com.sdsdiy.orderimpl.entity.po.OrderAmount;
import com.sdsdiy.orderimpl.manager.order.OrderAmountCalculateManager;
import com.sdsdiy.orderimpl.service.OrderAmountManage;
import com.sdsdiy.orderimpl.service.OrderAmountService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@RestController
@RequiredArgsConstructor
public class OrderAmountController implements OrderAmountApi {
    private final OrderAmountCalculateManager orderAmountCalculateManager;
    @Resource
    OrderAmountManage orderAmountManage;

    @Resource
    OrderAmountService orderAmountService;


    @Override
    public void updateBalanceFreeGold(OrderFreeGoldUpdateDTO orderFreeGoldUpdateDTO) {
        this.orderAmountManage.updateUseFreeGold(orderFreeGoldUpdateDTO.getOrderFreeGoldDTOList(), orderFreeGoldUpdateDTO.getOrderItemFreeGoldDTOS());
    }

    @Override
    public void decreaseTenantUsedFreeGold(OrderDecreaseTenantFreeGoldDTO orderDecreaseTenantFreeGoldDTO) {
        this.orderAmountService.decreaseTenantUsedFreeGold(orderDecreaseTenantFreeGoldDTO.getOrderId(), orderDecreaseTenantFreeGoldDTO.getDecreaseTenantFreeGold());
    }

    @Override
    public void batchUpdateLogisticsRefreshAmount(OrderLogisticsUpdateBatchParam orderLogisticsUpdateBatchParam) {
        this.orderAmountManage.batchUpdateLogisticsRefreshAmount(orderLogisticsUpdateBatchParam.getOrderLogisticsUpdateParams(), orderLogisticsUpdateBatchParam.getOrderLogisticsUpdateItemParams());
    }

    @Override
    public List<OrderAmountRespDTO> findByIds(IdsSearchHelper idsSearchHelper) {
        return this.orderAmountManage.findByIds(idsSearchHelper.getIds());
    }

    @Override
    public void updateResendTenantToSaasProductMoney(OrderAmountDTO orderAmountDTO) {
        this.orderAmountManage.updateResendTenantToSaasProductMoney(orderAmountDTO);
    }

    @Override
    public void createOrUpdateAfterServiceAmount(OrderAmountAfterOrderDto orderAmountAfterOrderDto) {
        this.orderAmountService.createOrUpdateAfterServiceAmount(orderAmountAfterOrderDto);
    }

    @Override
    public void updateProductAmount(Long id, OrderAmountCreateDTO orderAmountCreateDTO) {
        this.orderAmountService.updateProductAmount(id, orderAmountCreateDTO);
    }

    @Override
    public void updateTenantCarriageAmount(Long id, BigDecimal tenantCarriageAmount) {
        this.orderAmountService.updateTenantCarriageAmount(id, tenantCarriageAmount);
    }

    @Override
    public void batchUpdateAmount(List<OrderAmountDTO> updateOrderAmounts) {
        List<OrderAmount> orderAmounts = BeanUtil.copyToList(updateOrderAmounts, OrderAmount.class);
        this.orderAmountService.updateBatchById(orderAmounts);
    }

    @Override
    public OrderServiceAmountRespDTO orderServiceAmountCal(Long orderId) {
        return this.orderAmountCalculateManager.orderServiceAmountCal(orderId);
    }

    @Override
    public void oldDataUpdateOrderAmountPaymentType(String orderId,String lastIdStr,String stopIdStr) {
        orderAmountService.oldDataUpdateOrderAmountPaymentType(orderId,lastIdStr,stopIdStr);
    }
}

