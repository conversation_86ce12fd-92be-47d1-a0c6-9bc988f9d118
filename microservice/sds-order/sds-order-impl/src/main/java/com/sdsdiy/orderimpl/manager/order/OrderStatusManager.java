package com.sdsdiy.orderimpl.manager.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.BaseListQueryDTO;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.payment.PaymentTagConst;
import com.sdsdiy.core.transaction.TransactionUtil;
import com.sdsdiy.coreconfig.util.DingDingUtil;
import com.sdsdiy.orderapi.constant.EnumOrderPayType;
import com.sdsdiy.orderapi.constant.EnumOrderRefreshType;
import com.sdsdiy.orderapi.constant.OfflinePayRecordConstant;
import com.sdsdiy.orderapi.constant.PickUpLogisticsInfoStatusConstant;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.constant.event.message.admin.OrderCancelMessage;
import com.sdsdiy.orderapi.dto.OrderDTO;
import com.sdsdiy.orderapi.dto.offlinepay.AutoCreateOfflinePayRecodeParam;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayAmountDetailParam;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayRecordOrderParam;
import com.sdsdiy.orderapi.dto.order.OrderCarriageRespDto;
import com.sdsdiy.orderapi.dto.order.OrderEsSyncSqsMsg;
import com.sdsdiy.orderapi.enumeration.EnumOrderFbaStatus;
import com.sdsdiy.orderdata.constant.ParcelConstant;
import com.sdsdiy.orderdata.constant.aliexpress.AliexpressJitConstant;
import com.sdsdiy.orderdata.constant.order.OutPlatformPoConstant;
import com.sdsdiy.orderdata.dto.OrderItemRespDto;
import com.sdsdiy.orderdata.dto.aliexpress.AliexpressJitOrderDTO;
import com.sdsdiy.orderdata.dto.msg.OrderCancelMessageDTO;
import com.sdsdiy.orderdata.dto.msg.OrderShipmentMessageDTO;
import com.sdsdiy.orderdata.dto.order.amount.OrderAmountCalResultDTO;
import com.sdsdiy.orderdata.dto.parcel.OrderParcelItemDTO;
import com.sdsdiy.orderdata.enums.DeliveryTypeEnum;
import com.sdsdiy.orderdata.enums.OrderStatus;
import com.sdsdiy.orderdata.enums.OrderVersionEnum;
import com.sdsdiy.orderimpl.entity.po.*;
import com.sdsdiy.orderimpl.entity.po.outplatform.OutPlatformPurchaseOrder;
import com.sdsdiy.orderimpl.entity.po.parcel.OrderParcel;
import com.sdsdiy.orderimpl.feign.TenantLogisticsFeign;
import com.sdsdiy.orderimpl.feign.payment.TransactionFeign;
import com.sdsdiy.orderimpl.feign.user.MerchantUserAccountFeign;
import com.sdsdiy.orderimpl.manager.parcel.OrderParcelManager;
import com.sdsdiy.orderimpl.service.*;
import com.sdsdiy.orderimpl.service.aliexpress.AliexpressJitOrderService;
import com.sdsdiy.orderimpl.service.carriage.OrderCarriageService;
import com.sdsdiy.orderimpl.service.offlinepay.OfflinePayRecordService;
import com.sdsdiy.orderimpl.service.order.OrderVersionService;
import com.sdsdiy.orderimpl.service.order.PrepaidShippedService;
import com.sdsdiy.orderimpl.service.outplatform.OutPlatformPurchaseOrderService;
import com.sdsdiy.orderimpl.service.parcel.OrderParcelItemService;
import com.sdsdiy.orderimpl.service.parcel.OrderParcelService;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.RefundDto;
import com.sdsdiy.paymentapi.dto.msg.OrderFinanceUpdateMsg;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.dto.base.MerchantUserAccountRespDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderStatusManager {
    private final OrderService orderService;
    private final OrderEsService orderEsService;
    private final OrderItemService orderItemService;
    private final FactoryOrderService factoryOrderService;
    private final OrderParcelManager orderParcelManager;
    private final OrderParcelItemService orderParcelItemService;
    private final PrepaidShippedService prepaidShippedService;
    private final ImportedPlatformOrderFbaService importedPlatformOrderFbaService;
    private final OrderAmountService orderAmountService;

    private final RocketMQTemplate rocketMQTemplate;
    private final OrderAmountCalculateManager orderAmountCalculateManager;
    private final TransactionFeign transactionFeign;
    private final OfflinePayRecordService offlinePayRecordService;
    private final OrderCarriageService orderCarriageService;
    private final TenantLogisticsFeign tenantLogisticsFeign;
    private final OrderProgressManager orderProgressManager;
    private final OrderVersionService orderVersionService;
    private final AfterServiceAuditsService afterServiceAuditsService;
    private final PickUpLogisticsInfoService pickUpLogisticsInfoService;
    private final OrderFbaService orderFbaService;
    private final OrderParcelService orderParcelService;
    private final MerchantUserAccountFeign merchantUserAccountFeign;
    private final AliexpressJitOrderService aliexpressJitOrderService;
    private final OutPlatformPurchaseOrderService outPlatformPurchaseOrderService;

    public OrderStatus checkAndEndOrder(Long orderId) {
        OrderDTO orderDTO = this.orderService.findById(orderId, "originType,merchantId,isAdvance,customerId");
        return this.checkAndEndOrder(orderDTO, null);
    }

    /**
     * 检查订单是否完结，如果满足条件则进行完结
     *
     * @param currentShipmentParcelId 本次发货的包裹id,可以不传
     */
    public OrderStatus checkAndEndOrder(OrderDTO orderDTO, Long currentShipmentParcelId) {
        Long orderId = orderDTO.getId();
        log.info("checkAndEndOrder orderId:{}", orderId);
        if (OrderStatus.isEnd(orderDTO.getStatus())) {
            // 订单已经完结，状态有变动才返回
            return null;
        }
        if (!this.orderParcelService.existsParcel(orderId)) {
            log.info("checkAndEndOrder 包裹不存在");
            return null;
        }
        DeliveryTypeEnum deliveryType = DeliveryTypeEnum.matchOrder(orderDTO.getOriginType(), orderDTO.getLogisticsCodeId());

        Map<Long, Integer> unCancelItemQtyMap = this.orderItemService.mapRealItemQty(orderId);
        Order updateOrder = new Order();
        updateOrder.setId(orderId);
        OrderStatus orderStatus = null;
        if (unCancelItemQtyMap.size() == 0) {
            // 全部取消
            orderStatus = OrderStatus.CANCEL;
        } else {
            // 判断是否已全部发货
            if (deliveryType == DeliveryTypeEnum.FBA && !DeliveryTypeEnum.isFbaZt(orderDTO.getLogisticsCodeId())) {
                // FBA 需要手动点击发货，且必须全部一起发货
                return null;
            }
            if (deliveryType == DeliveryTypeEnum.JF) {
                Map<Long, Integer> unshipItemQty = this.prepaidShippedService.notFullShippedOrderItemIds(unCancelItemQtyMap);
                if (unshipItemQty.size() > 0) {
                    log.info("寄付未全部发货 unCancelItemQtyMap={}, unshipItemQty = {}，order状态={}", unCancelItemQtyMap, unshipItemQty, orderDTO.getStatus());
                    // 未全部发货
                    //部分发货，且状态不是部分发货
                    boolean isPartShippedStatus = orderDTO.getStatus() == OrderStatus.PART_SHIPPED.getStatus();
                    if (isPartShippedStatus) {
                        return null;
                    }
                    for (Map.Entry<Long, Integer> entry : unCancelItemQtyMap.entrySet()) {
                        Integer itemUnShippedQty = unshipItemQty.getOrDefault(entry.getKey(), 0);
                        Integer itemTotalQty = entry.getValue();
                        int itemShippedQty = itemTotalQty - itemUnShippedQty;
                        if (itemShippedQty > 0) {
                            orderStatus = OrderStatus.PART_SHIPPED;
                            break;
                        }
                    }
                } else {
                    orderStatus = OrderStatus.FINISH;
                }
            } else if (deliveryType == DeliveryTypeEnum.ZT) {
                Map<Long, Integer> unShipItemQty = this.orderParcelManager.mapUnshippedItemQty(orderId);
                if (unShipItemQty.size() > 0) {
                    // 未全部发货
                    log.info("未全部发货 unshippedOrderItemQty = {}", unShipItemQty.size());
                    return null;
                }
                boolean allPickUp = this.isSdAllPickUp(orderId);
                if (!allPickUp) {
                    // 未全部发货
                    log.info("未全部提货 orderId={}", orderId);
                    return null;
                }
                orderStatus = OrderStatus.FINISH;

            } else if (DeliveryTypeEnum.isFbaZt(orderDTO.getLogisticsCodeId())) {
                boolean allPickUp = this.fbZtPickUp(orderId);
                if (!allPickUp) {
                    // 未全部发货
                    log.info("未全部提货 orderId={}", orderId);
                    return null;
                }
                orderStatus = OrderStatus.FINISH;
            } else if (deliveryType == DeliveryTypeEnum.JIT) {
                // 速卖通半托管
                List<AliexpressJitOrderDTO> jitOrderList = this.aliexpressJitOrderService.findPoNotCanceledDtoByOrderId(orderId, "");
                long shippedNum = jitOrderList.stream().filter(i -> AliexpressJitConstant.StatusEnum.isShipped(i.getStatus())).count();
                if (shippedNum == 0) {
                    log.info("JIT未发货={}", orderId);
                    return null;
                }
                if (shippedNum == jitOrderList.size()) {
                    orderStatus = OrderStatus.FINISH;
                } else {
                    orderStatus = OrderStatus.PART_SHIPPED;
                }
            } else if (deliveryType == DeliveryTypeEnum.TEMU_FULLY) {
                // TEMU全托管
                List<OutPlatformPurchaseOrder> poList = this.outPlatformPurchaseOrderService.findByOrderId(orderId);
                poList = poList.stream().filter(i -> !OutPlatformPoConstant.Status.CANCEL.equalsCode(i.getStatus())).collect(Collectors.toList());
                long shippedNum = poList.stream().filter(i -> OutPlatformPoConstant.Status.isShipped(i.getStatus())).count();
                if (shippedNum == 0) {
                    log.info("TEMU_FULLY未发货={}", orderId);
                    return null;
                }
                if (shippedNum == poList.size()) {
                    orderStatus = OrderStatus.FINISH;
                } else {
                    orderStatus = OrderStatus.PART_SHIPPED;
                }
            } else {
                Map<Long, Integer> unShipItemQty = this.orderParcelManager.mapUnshippedItemQty(orderId);
                if (unShipItemQty.size() > 0) {
                    // 未全部发货
                    log.info("未全部发货 unshippedOrderItemQty = {}", unShipItemQty.size());
                    return null;
                }
                orderStatus = OrderStatus.FINISH;
            }
        }
        if (orderStatus == null) {
            return null;
        }
        Long currentUserId = McContentHelper.getCurrentUserId();
        if (orderStatus == OrderStatus.CANCEL) {
            updateOrder.setCancelTime(System.currentTimeMillis());
        } else if (orderStatus == OrderStatus.FINISH) {
            updateOrder.setFinishTime(System.currentTimeMillis());
            if (null != currentUserId) {
                updateOrder.setOptionUser(currentUserId.intValue());
            }
        }
        updateOrder.setStatus(orderStatus.status);
        updateOrder.setTransactionCode(IdGenerator.nextStringId());
        this.orderService.updatePo(updateOrder);
        if (orderStatus == OrderStatus.FINISH) {
            // 包裹未发货的标记为发货
            this.orderParcelService.updateByOrderShipment(orderId);
        } else if (orderStatus == OrderStatus.CANCEL) {
            this.importedPlatformOrderFbaService.deleteOriginFba(orderDTO);
            // 包裹金额清零
            this.orderParcelService.updateByOrderCancel(orderId);
        }
        // 消息
        if (orderStatus == OrderStatus.FINISH) {
            String orderProgress = this.orderProgressManager.sendShipOrderProgressMessage(orderDTO);
            // 打单发货操作统一消息
            OrderShipmentMessageDTO messageDTO = new OrderShipmentMessageDTO();
            messageDTO.setOrderId(orderId);
            messageDTO.setIsAdvance(orderDTO.getIsAdvance());
            messageDTO.setAllShipped(true);
            messageDTO.setParcelId(currentShipmentParcelId);
            messageDTO.setOrderProgress(orderProgress);
            messageDTO.setOperateUserId(currentUserId);
            this.rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.ORDER_SHIPMENT_TOPIC, messageDTO);
        } else if (orderStatus == OrderStatus.CANCEL) {
            // 进度
            OrderCancelMessage cancelMessage = new OrderCancelMessage();
            cancelMessage.setOrderId(orderId);
            this.rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.ORDER_PROGRESS_TOPIC, OrderProgressConstant.ORDER_CANCEL, cancelMessage);
            // 统一的订单取消消息
            OrderCancelMessageDTO cancelMessageDTO = OrderCancelMessageDTO.builder().orderId(orderId)
                    .fromCheckAndEndOrder(true).build();
            this.rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.EVENT_ORDER_CANCEL, cancelMessageDTO);
        }
        OrderEsSyncSqsMsg msg = new OrderEsSyncSqsMsg();
        msg.setOrderId(orderId);
        msg.setRefreshType(EnumOrderRefreshType.ORDER_STATUS.getValue());
        this.orderEsService.sendMqBatchToKafka(msg);
        return orderStatus;
    }

    public boolean allPickUp(Long orderId, String logisticsCodeId) {
        boolean allPickUp = false;
        if (DeliveryTypeEnum.isFbaZt(logisticsCodeId)) {
            allPickUp = this.fbZtPickUp(orderId);
        } else if (DeliveryTypeEnum.ZT_CODE_LIST.contains(logisticsCodeId)) {
            allPickUp = this.isSdAllPickUp(orderId);
        }
        return allPickUp;
    }

    private boolean isSdAllPickUp(Long orderId) {
        boolean allPickUp = false;
        List<PickUpLogisticsInfo> pickUpLogisticsInfos = this.pickUpLogisticsInfoService.findByOrderId(orderId);
        List<PickUpLogisticsInfo> notPickUpInfos = pickUpLogisticsInfos.stream().filter(i -> i.getStatus().equals(PickUpLogisticsInfoStatusConstant.notPickedUp)).collect(Collectors.toList());
        if (CollUtil.isEmpty(notPickUpInfos)) {
            allPickUp = true;
        }
        return allPickUp;
    }

    private boolean fbZtPickUp(Long orderId) {
        OrderFba orderFba = this.orderFbaService.findByOrderId(orderId);
        return orderFba.getStatus().equals(EnumOrderFbaStatus.FINISH.getValue());
    }

    public void syncOrderCarriageAmountToParcel(OrderAmountCalResultDTO amountResult) {
        Assert.validateFalse(amountResult.getParcelList().size() == 1, "同步包裹金额异常:" + amountResult.getOrderId());

        OrderAmountCalResultDTO.Parcel parcel = amountResult.getParcelList().get(0);
        // 当前物流金额，不能取差值difference，因为difference是0
        BigDecimal carriageAmount = amountResult.getCurrent().getCarriageAmount();
        BigDecimal tenantCarriageAmount = amountResult.getTenantCurrent().getCarriageAmount();
        if (tenantCarriageAmount == null) {
            // 为空表示非分销订单，则物流费一样，不存在差值
            tenantCarriageAmount = carriageAmount;
        }
        BigDecimal taxAmount = amountResult.getCurrent().getServiceAmount().getTaxAmount();
        BigDecimal logisticsServiceAmount = amountResult.getCurrent().getServiceAmount().getLogisticsServiceAmount();

        OrderParcel updateParcel = new OrderParcel();
        if (!NumberUtil.equals(carriageAmount, parcel.getCarriageAmount())) {
            updateParcel.setCarriageAmount(carriageAmount);
        }
        if (!NumberUtil.equals(tenantCarriageAmount, parcel.getTenantCarriageAmount())) {
            updateParcel.setTenantCarriageAmount(tenantCarriageAmount);
        }
        if (!NumberUtil.equals(taxAmount, parcel.getTaxAmount())) {
            updateParcel.setTaxAmount(taxAmount);
        }
        if (!NumberUtil.equals(logisticsServiceAmount, parcel.getLogisticsServiceAmount())) {
            updateParcel.setLogisticsServiceAmount(logisticsServiceAmount);
        }
        if (updateParcel.getCarriageAmount() == null && updateParcel.getTenantCarriageAmount() == null
                && updateParcel.getTaxAmount() == null && updateParcel.getLogisticsServiceAmount() == null) {
            log.info("syncOrderCarriageAmountToParcel-没有差价:{}", amountResult.getOrderId());
            return;
        }
        updateParcel.setId(parcel.getOrderParcelId());
        this.orderParcelService.updatePo(updateParcel);
    }

    @Transactional(rollbackFor = Exception.class)
    public void orderEndCalDifference(Long orderId) {
        if (!this.orderVersionService.match(orderId, OrderVersionEnum.SPRINT5_1)) {
            log.info("orderEndCalDifference-旧订单不处理:{}", orderId);
            return;
        }
        OrderAmountCalResultDTO amountResult = this.orderAmountCalculateManager.orderEndReCal(orderId);
        if (amountResult == null) {
            return;
        }
        if (amountResult.getSpecialInfo().getSyncOrderCarriageAmountToParcel()) {
            // 没拆包、没变更物流 的情况下，订单完结时，包裹上的金额需要以订单为准
            this.syncOrderCarriageAmountToParcel(amountResult);
            return;
        }
        BigDecimal totalAmount = amountResult.getDifference().getTotalAmount();
        if (CompareUtils.eqZero(totalAmount)) {
            log.info("orderEndCalDifference-没有差价:{}", orderId);
            return;
        }
        OrderAmountCalResultDTO.UserInfo userInfo = amountResult.getUserInfo();
        String tradeNo = "";
        if (CompareUtils.gtZero(totalAmount)) {
            // 退款
            List<RefundParam> paramList = this.buildRefundParam(amountResult);
            RefundDto refundDto = this.transactionFeign.createRefund(new MultiTransactionCreateParam().setRefundList(paramList));
            this.transactionFeign.operateRefund(refundDto.getId());
            tradeNo = refundDto.getTradeNo();
            this.orderProgressManager.orderEndRefund(orderId, totalAmount);
        } else {
            // 补款账单
            // 转成正数
            totalAmount = totalAmount.negate();
            OrderAmount orderAmount = this.orderAmountService.findById(orderId);
            List<OrderAmountCalResultDTO.Parcel> parcelList = amountResult.getParcelList();
            Map<Long, OrderAmountCalResultDTO.Parcel> parcelMap = ListUtil.toMap(OrderAmountCalResultDTO.Parcel::getOrderParcelId, parcelList);
            List<OrderCarriageRespDto> orderCarriageList = this.orderCarriageService.findByParcelIds(parcelMap.keySet());
            Set<Long> logisticsIds = ListUtil.toValueSet(OrderCarriageRespDto::getLogisticsId, orderCarriageList);
            Map<Long, String> logisticsNameMap = ListUtil.toMapByBaseIdAndName(this.tenantLogisticsFeign.findNameByIds(logisticsIds));
            DeliveryTypeEnum deliveryType = amountResult.getOrderInfo().getDeliveryType();
            AutoCreateOfflinePayRecodeParam param = new AutoCreateOfflinePayRecodeParam();
            param.setMerchantId(userInfo.getOrderMerchantId());
            param.setCreateUid(McContentHelper.getCurrentUserId());
            param.setTradeType(OfflinePayRecordConstant.TradeTypeEnum.FINISHED_DIFF.getName());
            param.setAmount(totalAmount);
            // 包裹信息
            String detail = orderCarriageList.stream().map(i -> {
                OrderAmountCalResultDTO.Parcel parcel = parcelMap.get(i.getOrderParcelId());
                return StrUtil.format(deliveryType == DeliveryTypeEnum.ZT
                                ? ParcelConstant.BILL_PARCEL_DESC_ZT : ParcelConstant.BILL_PARCEL_DESC
                        , ParcelConstant.formatName(parcel.getSortWeight(), deliveryType.code)
                        , parcel.getParcelItemList().size()
                        , parcel.getTotalQty()
                        , logisticsNameMap.getOrDefault(i.getLogisticsId(), i.getCarriageName())
                        , i.getCarriageNo()
                );
            }).collect(Collectors.joining("\n\n"));
            param.setDetail(detail);
            param.setBelongUserId(userInfo.getOrderMerchantUserId());
            param.setTradeFlow(orderAmount.getPaymentType());
            param.setTenantId(orderAmount.getProductTenantId());
            param.setIsOrderOffline(BasePoConstant.yesOrNo(PaymentMethodEnum.isOffline(orderAmount.getPaymentMethod())));
            param.setOrderNos(Collections.singletonList(new OfflinePayRecordOrderParam(amountResult.getOrderNo(), totalAmount)));

            List<OfflinePayAmountDetailParam> detailParams = this.buildOfflinePayAmountDetailParam(amountResult);
            param.setAmountDetails(detailParams);
            tradeNo = this.offlinePayRecordService.autoCreateOfflinePayRecode(param) + "";
            this.orderProgressManager.orderEndOfflinePay(orderId, totalAmount, tradeNo);
        }
        this.orderAmountCalculateManager.orderAmountUpdate(amountResult);
        this.orderAmountCalculateManager.recordChangeDetail(amountResult, tradeNo);
        TransactionUtil.afterCommit(() -> {
            DingDingUtil.sendDDMessage(DingDingUtil.ORDER_END_ROBOT_URL
                    , DingDingUtil.formatContent(amountResult.getOrderId() + "\n" + JSON.toJSONString(amountResult.getDifference())
                            , amountResult.getOrderNo() + " 金额差值( " + amountResult.getDifference().getTotalAmount() + " )")
                    , Collections.emptyList(), false);
        });
    }

    public List<RefundParam> buildRefundParam(OrderAmountCalResultDTO amountResult) {
        List<RefundParam> paramList = new ArrayList<>();
        OrderAmountCalResultDTO.UserInfo userInfo = amountResult.getUserInfo();
        String orderPaymentType = amountResult.getOrderInfo().getOrderPaymentType();
        String orderPaymentMethod = amountResult.getOrderInfo().getOrderPaymentMethod();
        {
            MerchantUserAccountRespDto merchantUserAccount = this.merchantUserAccountFeign.getOne(userInfo.getOrderMerchantId(), userInfo.getOrderMerchantUserId(), "");
            // 租户退给商户
            OrderAmountCalResultDTO.DifferencePayment merchantDiff = amountResult.getDifferencePayment();
            PaymentMethodEnum payMethod = PaymentMethodEnum.isOffline(orderPaymentMethod)
                    ? PaymentMethodEnum.OFFLINE : PaymentMethodEnum.BALANCE;
            BalanceUsedType balanceUsedType = merchantUserAccount != null && MerchantUserAccountConstant.ALLOCATION.equalsIgnoreCase(merchantUserAccount.getBalanceType())
                    ? BalanceUsedType.MERCHANT_ASSIGN_BALANCE : BalanceUsedType.MERCHANT_COMMON_BALANCE;
            RefundParam toMerchant = new RefundParam();
            paramList.add(toMerchant);
            toMerchant.setPurposeType(PurposeType.REFUND.getCode())
                    .setDetailPurpose(DetailPurpose.TENANT_REFUND_TO_MERCHANT.getCode())
                    .setPayType(TransactionPayTypeEnum.MAIN.getValue())
                    .setPayMethod(payMethod.getCode())
                    .setBillType(PaymentBillTypeEnum.DIRECT.getStatus())
                    .setSubject("订单完结退款")
                    .setBizNo(amountResult.getOrderNo())
                    .setBalanceType(balanceUsedType.getUsedType())
                    .setBalance(merchantDiff.getBalance())
                    .setBonus(merchantDiff.getFreeGold());
            toMerchant.setOperateRole(PaymentRoleEnum.SYSTEM.getCode())
                    .setOperateUserId(0L)
                    .setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode())
                    .setOperateTargetRoleId(userInfo.getOrderMerchantId());
            toMerchant.setSourceTenantId(userInfo.getOrderMerchantTenantId())
                    .setSourceMerchantId(0L)
                    .setSourceUserId(0L)
                    .setSourceRole(PaymentRoleEnum.TENANT.getCode());
            toMerchant.setTargetTenantId(userInfo.getOrderMerchantTenantId())
                    .setTargetMerchantId(userInfo.getOrderMerchantId())
                    // 分配余额才要传用户id
                    .setTargetUserId(BalanceUsedType.MERCHANT_ASSIGN_BALANCE == balanceUsedType ? userInfo.getOrderMerchantUserId() : 0L)
                    .setTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        }
        if (EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.equalsCode(orderPaymentType)) {
            BigDecimal tenantTotalAmount = amountResult.getTenantDifference().getTotalAmount();
            if (CompareUtils.gtZero(tenantTotalAmount)) {
                OrderAmountCalResultDTO.DifferencePayment tenantDiff = amountResult.getTenantDifferencePayment();
                RefundParam toTenant = new RefundParam();
                paramList.add(toTenant);
                toTenant.setPurposeType(PurposeType.REFUND.getCode())
                        .setDetailPurpose(DetailPurpose.TENANT_REFUND_TO_TENANT_DIS.getCode())
                        .setPayType(TransactionPayTypeEnum.SUB.getValue())
                        .setPayMethod(PaymentMethodEnum.BALANCE.getCode())
                        .setBillType(PaymentBillTypeEnum.DIRECT.getStatus())
                        .setSubject("订单完结退款")
                        .setBizNo(amountResult.getOrderNo())
                        .setBalanceType(BalanceUsedType.TENANT_DISTRIBUTION_BALANCE.getUsedType())
                        .setBalance(tenantDiff.getBalance())
                        .setBonus(tenantDiff.getFreeGold());
                toTenant.setOperateRole(PaymentRoleEnum.SYSTEM.getCode())
                        .setOperateUserId(0L)
                        .setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode())
                        .setOperateTargetRoleId(userInfo.getOrderMerchantId());
                toTenant.setSourceTenantId(userInfo.getProductTenantId())
                        .setSourceMerchantId(0L)
                        .setSourceUserId(0L)
                        .setSourceRole(PaymentRoleEnum.TENANT_SUP.getCode());
                toTenant.setTargetTenantId(userInfo.getOrderMerchantTenantId())
                        .setTargetMerchantId(0L)
                        .setTargetUserId(0L)
                        .setTargetRole(PaymentRoleEnum.TENANT_DIS.getCode());
            }
        }
        return paramList;
    }

    public List<OfflinePayAmountDetailParam> buildOfflinePayAmountDetailParam(OrderAmountCalResultDTO amountResult) {
        List<OfflinePayAmountDetailParam> amountDetails = new ArrayList<>();
        OrderAmountCalResultDTO.UserInfo userInfo = amountResult.getUserInfo();
        String orderPaymentType = amountResult.getOrderInfo().getOrderPaymentType();
        BigDecimal totalAmount = amountResult.getDifference().getTotalAmount().negate();
        {
            OfflinePayAmountDetailParam detailParam = new OfflinePayAmountDetailParam();
            detailParam.setSourceTenantId(userInfo.getOrderMerchantTenantId());
            detailParam.setSourceMerchantId(userInfo.getOrderMerchantId());
            detailParam.setSourceUserId(userInfo.getOrderMerchantUserId());
            detailParam.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
            detailParam.setTargetTenantId(userInfo.getOrderMerchantTenantId());
            detailParam.setTargetMerchantId(0L);
            detailParam.setTargetRole(PaymentRoleEnum.TENANT.getCode());
            detailParam.setAmount(totalAmount);
            amountDetails.add(detailParam);
        }
        if (EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.equalsCode(orderPaymentType)) {
            BigDecimal tenantTotalAmount = amountResult.getTenantDifference().getTotalAmount().negate();
            if (CompareUtils.gtZero(tenantTotalAmount)) {
                OfflinePayAmountDetailParam detailParam = new OfflinePayAmountDetailParam();
                detailParam.setSourceTenantId(userInfo.getOrderMerchantTenantId());
                detailParam.setSourceMerchantId(0L);
                detailParam.setSourceUserId(0L);
                detailParam.setSourceRole(PaymentRoleEnum.TENANT_DIS.getCode());
                detailParam.setTargetTenantId(userInfo.getProductTenantId());
                detailParam.setTargetMerchantId(0L);
                detailParam.setTargetRole(PaymentRoleEnum.TENANT_SUP.getCode());
                detailParam.setAmount(tenantTotalAmount);
                amountDetails.add(detailParam);
            }
        }
        return amountDetails;
    }

    /**
     * 未发货数=下单-已取消-已发货
     */
    public Map<Long, Integer> unShipOrderItemQty(Long orderId) {
        OrderDTO orderDTO = this.orderService.findById(orderId, "originType,merchantId");
        if (orderDTO == null || OrderStatus.isEnd(orderDTO.getStatus())) {
            // 订单已经完结
            return Collections.emptyMap();
        }
        DeliveryTypeEnum deliveryType = DeliveryTypeEnum.matchOrder(orderDTO.getOriginType(), orderDTO.getLogisticsCodeId());
        Map<Long, Integer> realItemQty = this.orderItemService.mapRealItemQty(orderDTO.getId());
        switch (deliveryType) {
            case FBA:
                return realItemQty;
            case JF:
                return this.prepaidShippedService.notFullShippedOrderItemIds(realItemQty);
            default:
                // 不能直接取包裹里的未发货数量
                // 因为线上订单可能已支付后又部分取消，拆包的时候会过滤掉已取消的线上子单，导致包裹和子单对不上
                // 但商户取消的时候又需要展示出这个子单
                Map<Long, Integer> shippedItemQty = this.orderParcelManager.mapShippedItemQty(orderId);
                Map<Long, Integer> unShipItemQty = new HashMap<>(realItemQty.size());
                for (Long id : realItemQty.keySet()) {
                    Integer shipped = shippedItemQty.getOrDefault(id, 0);
                    int unShip = realItemQty.get(id) - shipped;
                    if (unShip > 0) {
                        unShipItemQty.put(id, unShip);
                    }
                }
                return unShipItemQty;
        }
    }

    /**
     * 可发货数=已质检通过的-已发货
     * 【暂不支持FBA、寄付】
     */
    public Map<Long, Integer> shipableOrderItemQty(Long orderId) {
        List<OrderItemRespDto> orderItems = this.orderItemService.findByOrderId(BaseListQueryDTO.of(orderId, "id,status"));
        orderItems = orderItems.stream().filter(i -> !OrderStatus.isCancel(i.getStatus())).collect(Collectors.toList());
        List<Long> orderItemIds = ListUtil.toValueList(OrderItemRespDto::getId, orderItems);
        // 已发货
        Map<Long, Integer> shippedItemQtyMap = this.orderParcelManager.mapShippedItemQty(orderId);
        // 质检通过
        Map<Long, Integer> qcPassQtyMap = this.factoryOrderService.mapQcPassQty(orderItemIds);
        qcPassQtyMap.forEach((itemId, qcPassQty) -> {
            Integer shippedQty = shippedItemQtyMap.get(itemId);
            if (shippedQty == null) {
                return;
            }
            qcPassQtyMap.put(itemId, qcPassQty - shippedQty);
        });
        qcPassQtyMap.entrySet().removeIf(i -> i.getValue() <= 0);
        return qcPassQtyMap;
    }

    public Map<Long, Integer> shippedOrderItemQty(DeliveryTypeEnum deliveryType, Collection<Long> orderItemIds) {
        if (deliveryType == DeliveryTypeEnum.JF) {
            return this.prepaidShippedService.shippedItemNumMap(new ArrayList<>(orderItemIds));
        } else {
            return this.orderParcelManager.mapShippedItemQty(orderItemIds);
        }
    }

    public boolean checkParcelCanShip(Long parcelId) {
        // 指定包裹内的子单
        List<OrderParcelItemDTO> itemList = this.orderParcelItemService.findDtoByParcelIds(BaseListQueryDTO.of(parcelId));
        if (CollUtil.isEmpty(itemList)) {
            return false;
        }
        Set<Long> orderItemIds = ListUtil.toValueSet(OrderParcelItemDTO::getOrderItemId, itemList);
        // 质检通过数
        Map<Long, Integer> qcPassQtyMap = this.factoryOrderService.mapQcPassQty(orderItemIds);
        if (CollUtil.isEmpty(qcPassQtyMap)) {
            return false;
        }
        // 已发货数
        Map<Long, Integer> shippedItemQtyMap = this.orderParcelManager.mapShippedItemQty(orderItemIds);
        for (OrderParcelItemDTO itemDTO : itemList) {
            Integer qcPass = qcPassQtyMap.getOrDefault(itemDTO.getOrderItemId(), 0);
            Integer shipped = shippedItemQtyMap.getOrDefault(itemDTO.getOrderItemId(), 0);
            if (qcPass - shipped < itemDTO.getQty()) {
                // 可发货(质检-已发货) < 要发货
                return false;
            }
        }
        return true;
    }

    public void sendOrderFinanceUpdateMsg(Long orderId) {
        Order order = this.orderService.findById(orderId);
        log.info("orderId={},order={}", orderId, JSON.toJSONString(order));
        if (order.getPayTime() <= 0) {
            log.info("财务-发货完成或全部取消 订单未支付 无需发送消息 orderId={}", orderId);
            return;
        }
        List<AfterServiceAudit> afterServiceAudits = this.afterServiceAuditsService.getSuccessCancelByOrderId(orderId);
        List<Long> afterServiceAuditIds = afterServiceAudits.stream().map(AfterServiceAudit::getId).distinct().collect(Collectors.toList());

        Integer beAfterServiceOrder = order.getBeAfterServiceOrder();
        String billPeriodType = beAfterServiceOrder.equals(BasePoConstant.YES) ? OrderFinanceBillPeriodTypeEnum.AFTER_SALE_RESEND.getCode() : OrderFinanceBillPeriodTypeEnum.ORDER_FINISH.getCode();
        Long afterServiceAuditId = null;
        if (billPeriodType.equals(OrderFinanceBillPeriodTypeEnum.AFTER_SALE_RESEND.getCode())) {
            AfterServiceAudit afterServiceAudit = this.afterServiceAuditsService.getByResendOrderId(orderId);
            afterServiceAuditId = afterServiceAudit.getId();
        }

        OrderFinanceUpdateMsg orderFinanceUpdateMsg = OrderFinanceUpdateMsg.builder()
                .orderId(orderId)
                .orderNo(order.getNo())
                .originOrderId(order.getOriginalAsId())
                .originOrderNo(order.getOriginalAsNo())
                .afterServiceAuditId(afterServiceAuditId)
                .afterServiceAuditIds(afterServiceAuditIds)
                .billPeriodType(billPeriodType)
                .createdAt(System.currentTimeMillis())
                .build();
        this.rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PAYMENT, PaymentTagConst.ORDER_FINANCE_UPDATE_TOPIC, orderFinanceUpdateMsg);
    }
}
