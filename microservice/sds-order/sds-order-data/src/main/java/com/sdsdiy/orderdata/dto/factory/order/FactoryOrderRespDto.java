package com.sdsdiy.orderdata.dto.factory.order;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.sdsdiy.common.dtoconvert.annotation.DtoBind;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayRespDto;
import com.sdsdiy.orderdata.dto.OrderItemRespDto;
import com.sdsdiy.orderdata.dto.OrderRemarkRespDto;
import com.sdsdiy.orderdata.dto.OrderRespDto;
import com.sdsdiy.orderdata.dto.WarehouseDTO;
import com.sdsdiy.orderdata.dto.factory.task.FactoryTaskOrderRelRespDto;
import com.sdsdiy.orderdata.dto.warning.OrderEarlyWarningDto;
import com.sdsdiy.productapi.dto.CategoryDto;
import com.sdsdiy.productapi.dto.ProductDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @program: microservice-parent
 * @Package com.sdsdiy.orderapi.dto.factoryorder
 * @Description: 工厂订单列表聚合信息
 * @date 2021/12/30 17:19
 */
@ApiModel("工厂订单列表聚合信息")
@Data
public class FactoryOrderRespDto implements Serializable {

    @ApiModelProperty("主键")
    @DtoDefault
    private Long id;
    @ApiModelProperty("工厂单编号")
    @DtoDefault
    private String no;
    @ApiModelProperty("订单状态")
    @DtoDefault
    private Integer status;
    @ApiModelProperty("产品名称")
    @DtoDefault
    private String productName;
    @ApiModelProperty("订单类别")
    @DtoDefault
    private String originType;
    @ApiModelProperty("商户号")
    @DtoDefault
    private String merchantNo;
    @ApiModelProperty("商户订单号")
    @DtoDefault
    private String merchantOrderNo;
    @ApiModelProperty("是否下载稿件")
    @DtoDefault
    private Integer isDownMaterrial;
    @ApiModelProperty("是否重新下载稿件")
    @DtoDefault
    private Integer manuscriptFeedbackStatus;
    @ApiModelProperty("售后时间")
    @DtoDefault
    private Long afterServiceTime;
    @ApiModelProperty("是否售后订单 1 售后订单 0 非售后订单")
    @DtoDefault
    private Boolean beAfterServiceOrder;
    @ApiModelProperty("转移类型 transferType none 非转移 reject 驳回转移 lose_resend 补件转移 after_service 售后转移")
    @DtoDefault
    private String transferType;
    @ApiModelProperty("产品供应关系->工厂生产编码")
    @DtoDefault
    private String productSupplyCode;
    @ApiModelProperty("产品颜色")
    @DtoDefault
    private String produtColorBlock;

    @ApiModelProperty("打回产品数")
    @DtoDefault
    private Integer refuseNum;
    @ApiModelProperty("1 驳回 2 漏件")
    @DtoDefault
    private Integer refuseType;

    @ApiModelProperty("下单时间")
    @DtoDefault
    private Date beginTime;
    @ApiModelProperty("创建时间")
    @DtoDefault
    private Date createTime;
    @ApiModelProperty("修改时间")
    @DtoDefault
    private Date updateTime;
    @ApiModelProperty("确认时间")
    private Long confirmTime;

    @ApiModelProperty("发送时间")
    @DtoDefault
    private Long expressTime;

    @DtoDefault
    private Long productTime;
    @DtoDefault
    private Long cancelTime;
    @DtoDefault
    private Long shipTime;
    @DtoDefault
    private Date outCycleDate;

    @ApiModelProperty("超时时间")
    @DtoDefault
    private Long shipOverTime;
    @ApiModelProperty("完成时间")
    @DtoDefault
    private Long finishedTime;
    @ApiModelProperty("完成用户id")
    @DtoDefault
    private Long finishedUser;
    @ApiModelProperty("操作用户名")
    @DtoDefault
    private String optionUserName;

    @ApiModelProperty("下单时间-显示使用 create")
    private String gmtCreateTime;
    @ApiModelProperty("下单时间-显示使用 update")
    private String gmtUpdateTime;

    private String gmtExpressTime;
    private String gmtCancelTime;
    private String gmtFinish;

    @ApiModelProperty("下单时间-显示使用 update")
    private List<String> imageUrls = Lists.newArrayList();

    @ApiModelProperty("fnsku")
    private String fnsku;

    @ApiModelProperty("赔付状态")
    @DtoDefault
    private Integer compensationStatus;
    @ApiModelProperty("赔付金额")
    @DtoDefault
    private Double compensationAmount;
    @ApiModelProperty("1 平台责任 2 工厂责任")
    @DtoDefault
    private Integer dutyAffiliation;

    @ApiModelProperty("工厂id")
    @DtoDefault
    private Long factoryId;
    @ApiModelProperty("工厂名称")
    @DtoDefault
    private String factoryName;
    @ApiModelProperty("工厂类型，备用backup none 无")
    @DtoDefault
    private String factoryBackupType;


    @ApiModelProperty("订单子单id")
    @DtoDefault
    private Long orderItemId;
    @ApiModelProperty("订单类别")
    @DtoBind(selfField = "orderItemId", relateField = "id", showField = "status,imgs,materialIds,originId,produtColorBlock,productSize,textureName,refundStatus,imgUrl,sellerSku,endProductId,originPrice,price,amount,costAmount,orderId,originalAsId,originalRflId,no,factoryId,merchantId,productSku,productId,num,amount,productName,keyId", provider = "com.sdsdiy.orderimpl.relationprovider.OrderItemProvider")
    private OrderItemRespDto orderItem;

    @DtoBind(selfField = "merchantOrderNo", relateField = "no", showField = "status", provider = "com.sdsdiy.orderimpl.relationprovider.OrderProvider")
    private OrderRespDto order;

    @ApiModelProperty("产品id")
    @DtoDefault
    private Long productId;
    @ApiModelProperty("成品id")
    @DtoDefault
    private Long endProductId;
    @ApiModelProperty("产品信息")
    private ProductDto product;
    private List<CategoryDto> categories;
    @ApiModelProperty("发货仓id")
    @DtoDefault
    private Long issuingBayId;
    @DtoDefault
    private Long productionLineId;

    @ApiModelProperty("发货仓对象")
    private IssuingBayRespDto issuingBay;

    @ApiModelProperty("产品数")
    @DtoDefault
    private Integer num;

    @ApiModelProperty("订单备注信息")
    private List<OrderRemarkRespDto> remarks = Lists.newArrayList();

    @ApiModelProperty("订单类别")
    @DtoBind(selfField = "id", relateField = "factoryOrderId", provider = "com.sdsdiy.orderimpl.relationprovider.task.FactoryTaskOrderRelProvider")
    private FactoryTaskOrderRelRespDto taskOrderRelRespDto;

    @ApiModelProperty("打印状态")
    private String printStatus;
    @ApiModelProperty("下载状态")
    private String downStatus;
    @ApiModelProperty("任务编号")
    private String factoryTaskNo;
    @ApiModelProperty("任务名称")
    private String factoryTaskName;

    @ApiModelProperty("休假时间")
    private ProductHolidayDto holiday;

    @ApiModelProperty("单价")
    private Double price;
    @ApiModelProperty("总价")
    private Double totalPrice;

    private Date outPaymentDate;
    private Date outExpressDate;
    private Date outHarvestDate;
    private Date outConfirmDate;
    private Long outDate;
    private Date manuscriptFeedbackTime;

    @ApiModelProperty("驳回取消")
    private Integer rejectCancel;
    @ApiModelProperty("漏件取消")
    private Integer loseCancel;

    //    @ApiModelProperty("转移单原id")
//    private Long lastRefoundOrderId;
//    @ApiModelProperty("转移单原no")
//    private String lastRefoundOrderNo;
    @ApiModelProperty("转移单原外部单号")
    private String lastRefoundOrderOriginalAsNo;
    @ApiModelProperty("转移单数量")
    private Integer lastRefoundOrderProductNum;
    @ApiModelProperty("是否需要稿件 N:不需要 Y:需要")
    private String requireManuscript;

    private Integer sameOrderValidNum;

    @ApiModelProperty("订单告警关系")
    @DtoBind(selfField = "orderItemId", relateField = "orderItemId", showField = "earlyWarningType", provider = "com.sdsdiy.orderimpl.relationprovider.warning.OrderEarlyWarningProvider")
    private List<OrderEarlyWarningDto> orderEarlyWarnings = Lists.newArrayList();

    private WarehouseDTO warehouse;


    private List<OrderHistoryDto> orderHistorys = Lists.newArrayList();

    @ApiModelProperty("流水号")
    private String serialNumber;

    @ApiModelProperty("分销商")
    private String distributorName;

    @ApiModelProperty("买家商户的租户id")
    private Long tenantId;
    //超时时间
    public Long getShipOverTime() {
        Long shipOverTime = 0L;
        Long caculateTime = shipTime;
        if (shipTime == null || shipTime <= 0) {
            caculateTime = System.currentTimeMillis();
        }
        if (productTime != null && productTime > 0) {
            caculateTime = productTime;
        }
        if (cancelTime != null && cancelTime > 0) {
            caculateTime = cancelTime;
        }
        if (outCycleDate != null) {
            shipOverTime = caculateTime - outCycleDate.getTime();
        }
        return shipOverTime < 0 ? 0L : shipOverTime;
    }

    public String getGmtCreateTime() {
        String str = null;
        if (createTime != null) {
            str = DateUtil.formatDateTime(createTime);
        }
        return str;
    }


    public String getGmtUpdateTime() {
        String str = null;
        if (updateTime != null) {
            str = DateUtil.formatDateTime(updateTime);
        }
        return str;
    }

    public String getGmtExpressTime() {
        String str = null;
        if (expressTime != null && expressTime != 0L) {
            str = DateUtil.formatDateTime(new Date(expressTime));
        }
        return str;
    }

    public String getGmtCancelTime() {
        String str = null;
        if (cancelTime != null && cancelTime != 0L) {
            str = DateUtil.formatDateTime(new Date(cancelTime));
        }
        return str;
    }

    public String getGmtFinish() {
        String str = null;
        if (finishedTime != null && finishedTime != 0) {
            str = DateUtil.formatDateTime(new Date(finishedTime));
        }
        return str;
    }
    @DtoDefault
    @ApiModelProperty("产品规格")
    private String productSize;

    @DtoDefault
    @ApiModelProperty("产品规格")
    private String productSizeRemark;
    @DtoDefault
    private String textureName;
    @DtoDefault
    private String textureChineseNotes;
}
