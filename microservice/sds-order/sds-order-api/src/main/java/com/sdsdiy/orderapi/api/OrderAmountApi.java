package com.sdsdiy.orderapi.api;

import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderdata.dto.OrderAmountAfterOrderDto;
import com.sdsdiy.orderdata.dto.OrderAmountDTO;
import com.sdsdiy.orderdata.dto.create.OrderAmountCreateDTO;
import com.sdsdiy.orderdata.dto.order.amount.OrderServiceAmountRespDTO;
import com.sdsdiy.orderdata.dto.order.update.OrderDecreaseTenantFreeGoldDTO;
import com.sdsdiy.orderdata.dto.order.update.OrderFreeGoldUpdateDTO;
import com.sdsdiy.orderdata.dto.order.update.OrderLogisticsUpdateBatchParam;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/***
 *订单进度
 * <AUTHOR>
 * @date 2020/7/8 14:17
 */
@RequestMapping("/microservice/OrderAmounts")
public interface OrderAmountApi {


    @PutMapping("updateBalanceFreeGold")
    void updateBalanceFreeGold(@RequestBody OrderFreeGoldUpdateDTO orderFreeGoldUpdateDTO);

    @PutMapping("decreaseTenantUsedFreeGold")
    void decreaseTenantUsedFreeGold(@RequestBody OrderDecreaseTenantFreeGoldDTO orderDecreaseTenantFreeGoldDTO) ;
    @PutMapping("batchUpdateLogisticsRefreshAmount")
    void batchUpdateLogisticsRefreshAmount(@RequestBody OrderLogisticsUpdateBatchParam orderLogisticsUpdateBatchParam);

    @GetMapping("findByIds")
    List<OrderAmountRespDTO> findByIds(@SpringQueryMap IdsSearchHelper idsSearchHelper);

    @PutMapping("updateResendTenantToSaasProductMoney")
    void updateResendTenantToSaasProductMoney(@RequestBody OrderAmountDTO orderAmountDTO);

    @PutMapping("createOrUpdateAfterServiceAmount")
    void createOrUpdateAfterServiceAmount(@RequestBody OrderAmountAfterOrderDto orderAmountAfterOrderDto);

    @PutMapping("{id}")
    void updateProductAmount(@PathVariable Long id, @RequestBody OrderAmountCreateDTO orderAmountCreateDTO);

    @PutMapping("updateTenantCarriageAmount/{id}")
    void updateTenantCarriageAmount(@PathVariable Long id, @RequestParam BigDecimal tenantCarriageAmount);

    @PutMapping("/batch")
    void batchUpdateAmount(@RequestBody List<OrderAmountDTO> updateOrderAmounts);

    @GetMapping("/orderServiceAmountCal")
    OrderServiceAmountRespDTO orderServiceAmountCal(@RequestParam Long orderId);

    @PostMapping("oldDataUpdateOrderAmountPaymentType")
    void oldDataUpdateOrderAmountPaymentType(@RequestParam(value = "orderId", required = false) String orderId,
                                             @RequestParam(value = "lastIdStr", required = false) String lastIdStr,
                                             @RequestParam(value = "stopIdStr", required = false) String stopIdStr);
}

