package com.sdsdiy.orderapi.dto.offlinepay;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.sdsdiy.common.dtoconvert.annotation.DtoBind;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import com.sdsdiy.orderapi.constant.OfflinePayRecordConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 线下付款记录表(OfflinePayRecord)Resp类
 *
 * <AUTHOR>
 * @since 2023-02-23 22:03:06
 */
@Data
public class OfflinePayRecordDetailBo {
    @DtoDefault
    @ApiModelProperty("id")
    private Long id;

    @DtoDefault
    @ApiModelProperty("用途类型:admin_prepay_shipping(寄付收款),change_waybill(换单差价), cal_volume(计抛差价),customs_duty(海关税费),other(其他用途)")
    private String tradeType;

    @DtoDefault
    private String payBatchNo;

    @DtoDefault
    private String detail;

    @DtoDefault
    @DtoBind(selfField = "id", relateField = "offlinePayRecordId", provider = "com.sdsdiy.orderimpl.relationprovider.OfflinePayRecordOrderRelationProvider")
    private List<OfflinePayRecordOrderResp> orders;

    /**
     * 获取标题
     */
    public String getTitle() {
        StringBuilder title = new StringBuilder("补款账期id:" + this.id);
        if (OfflinePayRecordConstant.TradeTypeEnum.ALIEXPRESS_JIT.getName().equals(this.tradeType)) {
            if (!StrUtil.isBlank(this.detail)) {
                String replaced = this.detail.replaceAll("\n\n", ", ");
                title.append(",速卖通JIT订单自寄单号：").append(replaced);
                return title.toString();
            }
            title.append(",速卖通JIT订单自寄单号：").append(this.detail);
            return title.toString();
        } else if (OfflinePayRecordConstant.TradeTypeEnum.TEMU_FULLY.getName().equals(this.tradeType)) {
            if (!StrUtil.isBlank(this.detail)) {
                String replaced = this.detail.replaceAll("\n\n", ", ");
                title.append(",temu全托管发仓订单自寄发货单号：").append(replaced);
                return title.toString();
            }
            title.append(",temu全托管发仓订单自寄发货单号：").append(this.detail);
            return title.toString();
        }
        if (CollectionUtil.isEmpty(orders)) {
            return title.toString();
        }
        List<String> orderNosStr = orders.stream().map(OfflinePayRecordOrderResp::getOrderNo).collect(Collectors.toList());
        String ordersStr = CollectionUtil.join(orderNosStr, ",");
        title.append(",订单号:").append(ordersStr);
        return title.toString();
    }
}