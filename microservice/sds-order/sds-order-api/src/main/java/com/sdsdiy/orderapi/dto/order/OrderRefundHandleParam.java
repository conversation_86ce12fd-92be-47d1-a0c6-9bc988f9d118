package com.sdsdiy.orderapi.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@Accessors(chain = true)
@AllArgsConstructor
public class OrderRefundHandleParam {
    private Long orderId;
    /**
     * 租户退给商户的余额
     */
    private BigDecimal balance;
    /**
     * 租户退给租户的余额
     */
    private BigDecimal tenantBalance;
    /**
     * 租户退给商户的赠送金
     */
    private BigDecimal bonus;
    /**
     * 租户退给租户的赠送金
     */
    private BigDecimal tenantBonus;


    /**
     * 操作对象角色，
     * - 比如购买订单，那这边应该是商户
     * - 租户给商户购买服务，这边也应该是商户
     * - 租户找SAAS购买服务，这边是租户
     * - 即，当前交易对应的业务处理对象
     * {@link com.sdsdiy.paymentapi.constant.PaymentRoleEnum}
     */
    /**
     * 操作的角色
     */
    private String operateRole;

    private String purposeType;
    /**
     * 操作的角色id
     */
    private Long operateRoleId;

    private String subject;
    private String remark;
    private String detailPurpose;
    private String title;


}
