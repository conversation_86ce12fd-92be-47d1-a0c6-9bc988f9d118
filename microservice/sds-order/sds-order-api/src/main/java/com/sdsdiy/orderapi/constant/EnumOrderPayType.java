package com.sdsdiy.orderapi.constant;

import com.sdsdiy.common.base.enums.BaseEnum;
import com.sdsdiy.common.base.helper.NumberUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum EnumOrderPayType implements BaseEnum<String> {
    /**
     *
     */
    @Deprecated
    SAAS("saas", "直接付钱给saas，购买saas产品未开启线上付款"),
    @Deprecated
    TENANT("tenant", "购买租户的产品，付钱给租户，"),
    @Deprecated
    TENANT_SAAS("tenant_saas", "先付钱给租户，租户再付钱给saas，购买saas产品，产品开启线上付款"),

    // tenant_saas：分销sds产品，处理为MERCHANT_OWNTENANT_OTHERTENANT
    // saas：tenantid=1，sds租户买自己产品，处理为MERCHANT_OWNTENANT，tenantId>1,处理为MERCHANT_OWNTENANT_OTHERTENANT
    MERCHANT_OWNTENANT("tenant", "商户买自己租户产品，商户付给自己租户"),
    MERCHANT_OWNTENANT_OTHERTENANT("tenant_tenant", "商户买其他租户产品，商户付给自己租户，自己租户付给其他租户"),
    ;
    @Getter
    public final String value;
    @Getter
    public final String desc;


    public static EnumOrderPayType getByValue(String value) {
        for (EnumOrderPayType enumOrderPayType : EnumOrderPayType.values()) {
            if (enumOrderPayType.value.equalsIgnoreCase(value)) {
                return enumOrderPayType;
            }
        }
        throw new RuntimeException(value + "值异常");
    }

    public static boolean payTenantToTenant(String value) {
        return MERCHANT_OWNTENANT_OTHERTENANT.equalsCode(value);
    }

    public static boolean onlyPaySaas(String value) {
        return SAAS.value.equalsIgnoreCase(value);
    }

    public static boolean onlyPayTenant(String value) {
        return MERCHANT_OWNTENANT.value.equalsIgnoreCase(value);
    }

    public static String getPaymentType(Long tenantId,Long productTenantId){
        if(!NumberUtils.greaterZero(productTenantId)){
            return MERCHANT_OWNTENANT.getValue();
        }
        return tenantId.equals(productTenantId) ? MERCHANT_OWNTENANT.getValue() : MERCHANT_OWNTENANT_OTHERTENANT.getValue();
    }

    @Override
    public String getCode() {
        return this.value;
    }
}
