package com.sdsdiy.orderapi.dto.order;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/11 16:10
 **/
@Data
public class OrderPaymentAmountDto {
    private Long orderId;
    private String orderNo;
    private Long productTenantId;
    /**
     * 商户付给自己租户
     */
    private BigDecimal useBalance = BigDecimal.ZERO;
    private BigDecimal useFreeGold = BigDecimal.ZERO;
    private BigDecimal totalAmount = BigDecimal.ZERO;

    /**
     * 租户要付给其他租户
     */
    private BigDecimal tenantUseBalance = BigDecimal.ZERO;
    private BigDecimal tenantUseFreeGold = BigDecimal.ZERO;
    private BigDecimal tenantTotalAmount = BigDecimal.ZERO;
    /**
     * 子单支付金额
     */
    private List<OrderItemPaymentAmountDto> itemPayDtos;

}
