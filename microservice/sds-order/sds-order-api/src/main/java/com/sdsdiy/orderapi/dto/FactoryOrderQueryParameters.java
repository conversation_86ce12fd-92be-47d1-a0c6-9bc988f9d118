package com.sdsdiy.orderapi.dto;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/***
 *工厂订单查询参数
 * <AUTHOR>
 * @date 2020/9/23 18:48
 */
@Data
@ApiModel
public class FactoryOrderQueryParameters {

    @ApiModelProperty("模板类型")
    private String productPrototypeType;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("订单类别")
    private String originType;
    @ApiModelProperty(value = "预警id", hidden = true)
    private Long earlyWarningId;
    @ApiModelProperty(value = "或 预警id集合", hidden = true)
    private List<Long> earlyWarningIds;
    @ApiModelProperty("预警id集合")
    private String earlyWarningIdStr;
    @ApiModelProperty(value = "且 预警id集合", hidden = true)
    private List<Long> andEarlyWarningIds;


    @ApiModelProperty("模糊检索关键字")
    private String keyword;
    @ApiModelProperty("工厂单状态")
    private String status;
    @ApiModelProperty("产品尺寸")
    private String productSize;
    @ApiModelProperty("产品颜色")
    private String productColorName;
    @ApiModelProperty("发货仓区域Id")
    private Long issuingBayAreaId;
    @ApiModelProperty("发货仓Id")
    private Long issuingBayId;
    @ApiModelProperty("商户id")
    private String merchantNo;
    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("且 售后类型:1 驳回 2 漏件 3售后")
    private String afterServiceRefuseType;
    @ApiModelProperty("预警增加售后类型")
    private Boolean earlyWarnIncludeAfterSale = false;

    @ApiModelProperty("排序")
    private String sort;

    @ApiModelProperty("产品线id")
    private Long productionLineId;

    @ApiModelProperty("点单编号集合---精确搜索")
    private List<String> nos;

    @ApiModelProperty("点单编号集合---精确搜索")
    private List<Long> factoryOrderIds;

    @ApiModelProperty("稿件反馈状态")
    private Integer manuscriptFeedbackStatus;
    @ApiModelProperty("稿件反馈状态-- 排除项")
    private Integer noManuscriptFeedbackStatus;

    @ApiModelProperty("是否下载稿件")
    private Integer isDownMaterrial;

    @ApiModelProperty("稿件状态not 未下载 down下载成功")
    private String downStatus;

    @ApiModelProperty("是否需要稿件")
    private String requireManuscript;

    @ApiModelProperty("是否添加计划")
    private Integer isJoinTask;

    @ApiModelProperty("分类id")
    private Long categoryId;
    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("计划id")
    private Long taskId;

    private Integer settlesStatus;

    @ApiModelProperty(hidden = true)
    private String completeStartTime;
    @ApiModelProperty(hidden = true)
    private String completeEndTime;
    @ApiModelProperty("确认开始时间")
    private String confirmStartTime;
    @ApiModelProperty("确认结束时间")
    private String confirmEndTime;
    @ApiModelProperty("生产开始时间")
    private String produceStartTime;
    @ApiModelProperty("生产结束时间")
    private String produceEndTime;
    @ApiModelProperty("发货开始时间")
    private String deliverStartTime;
    @ApiModelProperty("发货结束时间")
    private String deliverEndTime;


    private Integer page;
    private Integer start;
    private Integer size;
    @ApiModelProperty(hidden = true)
    private String no;
    @ApiModelProperty(hidden = true)
    private String merchantId;
    @ApiModelProperty("精确搜索颜色名称")
    private String accurateProductColorName;
    @ApiModelProperty(hidden = true)
    private Integer factoryOrderSettleId;
    @ApiModelProperty(hidden = true)
    private String color;
    @ApiModelProperty(hidden = true)
    private Integer isShowRefund;
    @ApiModelProperty(hidden = true)
    private Integer isOverTime;
    @ApiModelProperty(hidden = true)
    private Integer productionType;
    @ApiModelProperty(hidden = true)
    private Integer manuscriptFeedbackStatusSearch;
    @ApiModelProperty(hidden = true)
    private Long factoryId;

    //拼接sql
    @ApiModelProperty(hidden = true)
    private String earlyWarningSql;
    @ApiModelProperty(hidden = true)
    private String leftJoinOrderSql;
    @ApiModelProperty(hidden = true)
    private String filterSql;

    @ApiModelProperty(hidden = true)
//    "类别集合用,分割开",
    private String categoryIds;

    //转换后时间
    @ApiModelProperty(hidden = true)
    private Long confirmStartTimeLong;
    @ApiModelProperty(hidden = true)
    private Long confirmEndTimeLong;
    @ApiModelProperty(hidden = true)
    private Long produceStartTimeLong;
    @ApiModelProperty(hidden = true)
    private Long produceEndTimeLong;
    @ApiModelProperty(hidden = true)
    private Long deliverStartTimeLong;
    @ApiModelProperty(hidden = true)
    private Long deliverEndTimeLong;

    @ApiModelProperty(hidden = true, value = "成品ids")
    private List<Long> orEndProductIds;

    @ApiModelProperty("流水号")
    private String serialNumber;
    @ApiModelProperty("件数类型：one-一件，multiple-多件")
    private String numType;
    @ApiModelProperty("SINGLE_ORDER_SINGLE_ITEM=单订单单件 SINGLE_ORDER_MULTIPLE_ITEMS=单订单多件 MULTIPLE_SUBORDERS=多子单")
    private String orderAllocationType;

    @ApiModelProperty("分销商租户ids 逗号分隔")
    private String distributorTenantIds;

    @ApiModelProperty(value = "工厂端租户id", hidden = true)
    private Long tenantId;
    public List<Long> getEarlyWarningIds() {
        if (this.earlyWarningIds == null && StrUtil.isNotBlank(this.earlyWarningIdStr)) {
            this.earlyWarningIds = Arrays.stream(this.earlyWarningIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
        }
        return this.earlyWarningIds;
    }

    public void addFilterSql(String sql) {
        if (StrUtil.isBlank(sql)) {
            return;
        }
        if (StrUtil.isBlank(filterSql)) {
            this.filterSql = " (" + sql + ") ";
        } else {
            this.filterSql = this.filterSql + " AND (" + sql + ") ";
        }
    }
}
