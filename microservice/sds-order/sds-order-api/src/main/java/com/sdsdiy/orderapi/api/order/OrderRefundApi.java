package com.sdsdiy.orderapi.api.order;

import com.sdsdiy.orderapi.dto.order.OrderRefundHandleParam;
import com.sdsdiy.paymentapi.dto.RefundDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @author: bin_lin
 * @date: 2023/9/25 12:09
 * @desc:
 */
@RequestMapping("/microservice/orderRefund")
public interface OrderRefundApi {
    @PostMapping("oneRefundHandle")
    RefundDto refundHandle(@RequestBody OrderRefundHandleParam param);


}
