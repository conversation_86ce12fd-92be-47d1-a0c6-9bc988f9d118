package com.sdsdiy.logisticsimpl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseFieldPO;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.enums.CountryEnum;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayAreaRespDto;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayRespDto;
import com.sdsdiy.logisticsapi.constant.CarriageNoRecodeConstant;
import com.sdsdiy.logisticsapi.constant.LogisticsConstant;
import com.sdsdiy.logisticsapi.constant.ServiceProviderConstant;
import com.sdsdiy.logisticsapi.dto.DeclarationTaxResp;
import com.sdsdiy.logisticsapi.dto.LogisticsFreightReqDto;
import com.sdsdiy.logisticsapi.dto.LogisticsProductGroupDto;
import com.sdsdiy.logisticsapi.dto.base.*;
import com.sdsdiy.logisticsapi.dto.countryexpressinfo.AreaInfoDto;
import com.sdsdiy.logisticsapi.dto.countryexpressinfo.CountryExpressInfoNewBaseDto;
import com.sdsdiy.logisticsapi.dto.countryexpressinfo.CountryExpressInfoNewDetailResp;
import com.sdsdiy.logisticsapi.dto.tariff.CountryExpressInfoTariffDto;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsFBASaveParam;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsFbmBatchSaveParam;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.LogisticsAndLogisticsSourceResp;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.TenantLogisticsAndChannelResp;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.TenantLogisticsAsConditionParam;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.TenantLogisticsSaasPageResp;
import com.sdsdiy.logisticsapi.enums.LogisticsCodeIdEnum;
import com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum;
import com.sdsdiy.logisticsapi.enums.TaxNumberStatusEnum;
import com.sdsdiy.logisticsapi.enums.TaxNumberTypeEnum;
import com.sdsdiy.logisticsapi.util.TaxOptionUtil;
import com.sdsdiy.logisticsdata.dto.base.LogisticsRespDto;
import com.sdsdiy.logisticsdata.dto.base.LogisticsSimpleRespDto;
import com.sdsdiy.logisticsdata.dto.base.NationalityRespDto;
import com.sdsdiy.logisticsdata.dto.base.ServiceProviderRespDto;
import com.sdsdiy.logisticsdata.vo.logistics.LogisticsGroupToSelectResp;
import com.sdsdiy.logisticsdata.vo.logistics.LogisticsIssuingAreaGroupDto;
import com.sdsdiy.logisticsimpl.entity.po.*;
import com.sdsdiy.logisticsimpl.environment.EnvironmentVariables;
import com.sdsdiy.logisticsimpl.feign.IssuingBayAreaFeign;
import com.sdsdiy.logisticsimpl.feign.IssuingBayFeign;
import com.sdsdiy.logisticsimpl.feign.TenantFeign;
import com.sdsdiy.logisticsimpl.manager.TenantAddressMapperManager;
import com.sdsdiy.logisticsimpl.manager.TenantLogisticsMapperManager;
import com.sdsdiy.logisticsimpl.service.accessiblearea.CountryExpressInfoAccessibleService;
import com.sdsdiy.orderapi.constant.EnumOrderAmountCode;
import com.sdsdiy.userapi.constant.enums.DistributionProductLogisticsSourceEnum;
import com.sdsdiy.userapi.dto.tenant.resp.TenantLogisticsSourceResp;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.NO;
import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.common.base.constant.tenant.TenantCommonConstant.SDSDIY_TENANT_ID;
import static com.sdsdiy.logisticsapi.constant.CountryExpressInfoNewConstant.*;
import static com.sdsdiy.logisticsapi.constant.LogisticsConstant.STATUS_OPEN;
import static com.sdsdiy.logisticsapi.constant.ServiceProviderConstant.*;
import static com.sdsdiy.logisticsapi.enums.CarriageConfigEnum.STORE_PLATFORM_UNRELATED_LOGISTICS_AREAS;
import static com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum.*;
import static com.sdsdiy.orderdata.constant.order.PlatformOrderExtendValueEnum.TEMU_Y2_ADVANCE_SALE;

/**
 * 物流渠道表(TenantLogistics)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-01 10:37:08
 */
@Log4j2
@Service
@DS("master")
public class TenantLogisticsService {
    @Resource
    private TenantLogisticsMapperManager tenantLogisticsMapperManager;
    @Resource
    private ServiceProviderService serviceProviderService;
    @Resource
    private TenantServiceProviderService tenantServiceProviderService;
    @Resource
    private LogisticsChannelService logisticsChannelService;
    @Resource
    private LogisticsProductService logisticsProductService;
    @Resource
    private EnvironmentVariables environmentVariables;
    @Resource
    private TenantFeign tenantFeign;
    @Resource
    private IssuingBayFeign issuingBayFeign;
    @Resource
    private IssuingBayAreaFeign issuingBayAreaFeign;
    @Resource
    private NationalityService nationalityService;
    @Resource
    private CountryExpressInfoNewService countryExpressInfoNewService;
    @Resource
    private CountryExpressInfoAreaService countryExpressInfoAreaService;
    @Resource
    private TenantServiceProviderAccountService tenantServiceProviderAccountService;
    @Resource
    private TenantIssuingBayLogisticsAccountService tenantIssuingBayLogisticsAccountService;
    @Resource
    private TenantAddressMapperManager tenantAddressMapperManager;
    @Resource
    private LogisticServiceAmountService logisticServiceAmountService;
    @Resource
    private CarriageConfigService carriageConfigService;
    @Resource
    private CountryExpressInfoTariffService countryExpressInfoTariffService;
    @Resource
    private CountryExpressInfoAccessibleService countryExpressInfoAccessibleService;


    public PageListResultRespDto<TenantLogisticsRespDto> page(Long tenantId, Long issuingBayAreaId, String keyword, String deliveryMethod, QueryParamHelper queryParamHelper) {
        List<Long> serviceProviderIds = Lists.newArrayList();
        if (keyword != null) {
            serviceProviderIds = serviceProviderService.getIdsByLikeName(keyword);
        }
        Page<TenantLogistics> page = new Page<>(queryParamHelper.getPage(), queryParamHelper.getSize());
        List<Long> finalServiceProviderIds = serviceProviderIds;
        Page<TenantLogistics> pageResult = tenantLogisticsMapperManager.getTenantLogisticsPage(tenantId, issuingBayAreaId, keyword, deliveryMethod, page, finalServiceProviderIds);
        PageListResultRespDto<TenantLogisticsRespDto> respDto = new PageListResultRespDto<>(pageResult);
        List<TenantLogisticsRespDto> items = RelationsBinder.convertAndBind(pageResult.getRecords(), TenantLogisticsRespDto.class, "logisticsChannel");
        List<Long> ids = items.stream().map(TenantLogisticsRespDto::getId).collect(Collectors.toList());
        Map<Long, LogisticsProductGroupDto> map = logisticsProductService.countByLogisticsGroup(ids);
        for (TenantLogisticsRespDto item : items) {
            LogisticsProductGroupDto dto = map.get(item.getId());
            item.setChannelType(ServiceProviderConstant.ONLINE_LIST.contains(item.getChannelType()) ? TYPE_ONLINE : item.getChannelType());
            if (dto != null) {
                item.setBindProductNum(dto.getNum());
            } else {
                item.setBindProductNum(0);
            }
        }
        respDto.setItems(items);
        return respDto;
    }

    public PageListResultRespDto<TenantLogisticsSaasPageResp> saasPage(Long tenantId, Long issuingBayAreaId, String keyword, String deliveryMethod, QueryParamHelper queryParamHelper) {
        List<Long> ids = Lists.newArrayList();
        if (!StringUtils.isEmpty(keyword)) {
            ids = tenantLogisticsMapperManager.lambdaQuery()
                    .eq(TenantLogistics::getTenantId, tenantId)
                    .eq(TenantLogistics::getIssuingBayAreaId, issuingBayAreaId)
                    .eq(TenantLogistics::getDeliveryMethod, deliveryMethod)
                    .like(TenantLogistics::getName, keyword)
                    .select(TenantLogistics::getId)
                    .list()
                    .stream().map(TenantLogistics::getId).collect(Collectors.toList());
            List<Long> accountIds = tenantServiceProviderAccountService.getIdByName(tenantId, keyword);
            List<TenantIssuingBayLogisticsAccount> tenantIssuingBayLogisticsAccounts = tenantIssuingBayLogisticsAccountService.listByTenantIdAndAccountIds(tenantId, accountIds);
            ids.addAll(tenantIssuingBayLogisticsAccounts.stream().map(TenantIssuingBayLogisticsAccount::getLogisticsId).collect(Collectors.toList()));
            if (CollectionUtil.isEmpty(ids)) {
                return new PageListResultRespDto<>();
            }
        }
        Page<TenantLogistics> page = new Page<>(queryParamHelper.getPage(), queryParamHelper.getSize());
        Page<TenantLogistics> pageResult = tenantLogisticsMapperManager.getTenantLogisticsSaasPage(tenantId, issuingBayAreaId, deliveryMethod, page, ids);
        if (CollectionUtil.isEmpty(pageResult.getRecords())) {
            return new PageListResultRespDto<>();
        }
        List<TenantLogisticsSaasPageResp> items = formatSaasPage(tenantId, pageResult);
        PageListResultRespDto<TenantLogisticsSaasPageResp> respDto = new PageListResultRespDto<>(pageResult);
        respDto.setItems(items);
        return respDto;
    }

    private List<TenantLogisticsSaasPageResp> formatSaasPage(Long tenantId, Page<TenantLogistics> pageResult) {
        List<TenantLogistics> records = pageResult.getRecords();
        List<Long> logisticsIds = records.stream().map(TenantLogistics::getId).collect(Collectors.toList());
        List<TenantIssuingBayLogisticsAccount> logisticsAccounts = tenantIssuingBayLogisticsAccountService.listByLogistics(tenantId, logisticsIds);
        Map<Long, TenantIssuingBayLogisticsAccount> bayLogisticsAccountMap = logisticsAccounts.stream().collect(Collectors.toMap(TenantIssuingBayLogisticsAccount::getLogisticsId, Function.identity(), (a, b) -> b));
        List<Long> tenantAccountIds = bayLogisticsAccountMap.values().stream().map(TenantIssuingBayLogisticsAccount::getTenantServiceProviderAccountId).collect(Collectors.toList());

        List<TenantServiceProviderAccount> tenantServiceProviderAccounts = tenantServiceProviderAccountService.getByIds(tenantAccountIds);
        Map<Long, TenantServiceProviderAccount> accountMap = tenantServiceProviderAccounts.stream().collect(Collectors.toMap(TenantServiceProviderAccount::getId, Function.identity()));
        List<Long> addressIds = accountMap.values().stream().map(TenantServiceProviderAccount::getTenantAddressId).collect(Collectors.toList());
        List<TenantAddress> tenantAddresses = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(addressIds)) {
            tenantAddresses = tenantAddressMapperManager.listByIds(addressIds);
        }
        Map<Long, TenantAddress> addressMap = tenantAddresses.stream().collect(Collectors.toMap(TenantAddress::getId, Function.identity()));
        List<TenantLogisticsSaasPageResp> items = RelationsBinder.convertAndBind(pageResult.getRecords(), TenantLogisticsSaasPageResp.class);
        for (TenantLogisticsSaasPageResp resp : items) {
            TenantIssuingBayLogisticsAccount bayLogisticsAccountBind = bayLogisticsAccountMap.get(resp.getId());
            if (bayLogisticsAccountBind == null) {
                continue;
            }
            TenantServiceProviderAccount account = accountMap.get(bayLogisticsAccountBind.getTenantServiceProviderAccountId());
            if (account == null) {
                continue;
            }
            TenantServiceProviderAccountRespDto accountRespDto = new TenantServiceProviderAccountRespDto();
            accountRespDto.setName(account.getName());
            resp.setAccount(accountRespDto);
            TenantAddress tenantAddress = addressMap.get(account.getTenantAddressId());
            if (tenantAddress == null) {
                continue;
            }
            TenantAddressRespDto tenantAddressRespDto = BeanUtil.toBean(tenantAddress, TenantAddressRespDto.class);
            resp.setTenantAddressRespDto(tenantAddressRespDto);
        }
        return items;
    }


    @Transactional(rollbackFor = Exception.class)
    public void fbmBatch(Long tenantId, Long tenantServiceProviderId, TenantLogisticsFbmBatchSaveParam param) {
        TenantServiceProvider one = tenantServiceProviderService.getOne(tenantId, tenantServiceProviderId);
        List<Long> paramIds = param.getList().stream().map(TenantLogisticsReqDto::getLogisticsChannelId).collect(Collectors.toList());
        List<LogisticsChannel> channelList = logisticsChannelService.getChannelListByIds(paramIds, YES, null, null);
        ServiceProvider serviceProvider = serviceProviderService.getById(one.getServiceProviderId());
        IssuingBayAreaRespDto issuingArea = issuingBayAreaFeign.findById(param.getIssuingBayAreaId(), "tenantId");
        Assert.validateNull(issuingArea, "区域异常！！");
        if (!tenantId.equals(issuingArea.getTenantId()) && NO.equals(issuingArea.getIsOpenToOtherTenant())) {
            throw new BusinessException("未开启跨区域功能");
        }
        if (!tenantId.equals(issuingArea.getTenantId()) && SELL_FAST_JIT.getNumber().equals(serviceProvider.getId())) {
            throw new BusinessException("SDS区域不能创建速卖通半托管物流");
        }
        List<TenantLogistics> logisticsList = Lists.newArrayList();
        for (LogisticsChannel channel : channelList) {
            TenantLogistics tenantLogistics = new TenantLogistics();
            tenantLogistics.setTenantId(tenantId);
            tenantLogistics.setLogisticsChannelId(channel.getId());
            tenantLogistics.setCodeId(channel.getCode());
            tenantLogistics.setServiceProviderId(one.getServiceProviderId());
            tenantLogistics.setIssuingBayAreaId(param.getIssuingBayAreaId());
            tenantLogistics.setName(channel.getName());
            tenantLogistics.setServiceProviderName(serviceProvider.getName());
            tenantLogistics.setChannelType(serviceProvider.getType());
            tenantLogistics.setIsSpanTenantArea(tenantId.equals(issuingArea.getTenantId()) ? 0 : 1);
            if(SERVICE_PROVIDER_ERP_OPEN_TEMU.getNumber().equals(channel.getServiceProviderId())){
                tenantLogistics.setTrackingMode(CarriageNoRecodeConstant.TRACK_METHOD_TRACKING17);
            }
            if (ONLINE_LIST.contains(serviceProvider.getType())) {
                tenantLogistics.setAllProductStatus(YES);

                if (!LogisticsServiceProviderEnum.thirdPartySpecialServiceProviderIds().contains(one.getServiceProviderId())) {
                    tenantLogistics.setApplyNode(LogisticsConstant.APPLY_NODE_BY_MERCHANT_STORE);
                    tenantLogistics.setCustomUploadLable(YES);
                }
            }
            //尾程物流商根据店铺配置
            if (TYPE_TAIL_ONLINE.equals(serviceProvider.getType())) {
                tenantLogistics.setApplyNode(LogisticsConstant.APPLY_NODE_BY_MERCHANT_STORE);
            }
            //手动物流新增可上传面单功能
            if (TYPE_MANUAL.equals(serviceProvider.getType())) {
                tenantLogistics.setCustomUploadLable(YES);
            }
            if (needCancelServiceProviderIds().contains(serviceProvider.getId())) {
                tenantLogistics.setNeedCancelOrderInThirdParty(YES);
            }
            tenantLogistics.setDeliveryMethod(serviceProvider.getDeliveryMethod());
            logisticsList.add(tenantLogistics);
            this.checkName(tenantId, channel.getName(), null, param.getIssuingBayAreaId());
        }
        tenantLogisticsMapperManager.saveBatch(logisticsList);
    }

    public void checkName(Long tenantId, String name, Long id, Long issuingBayAreaId) {
        Assert.validateNull(tenantId, "租户id异常");
        Assert.validateBlank(name, "代号不能为空");
        Assert.validateNull(issuingBayAreaId, "区域不能为空");
        String deliveryMethod = "";
        if (id != null) {
            TenantLogistics logistics = tenantLogisticsMapperManager.getById(id);
            Assert.validateNull(logistics, "渠道异常");
            deliveryMethod = logistics.getDeliveryMethod();
        }
        Integer count = tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getTenantId, tenantId)
                .eq(TenantLogistics::getName, name)
                .eq(TenantLogistics::getIssuingBayAreaId, issuingBayAreaId)
                .ne(id != null, TenantLogistics::getId, id)
                .eq(!StringUtils.isEmpty(deliveryMethod), TenantLogistics::getDeliveryMethod, deliveryMethod)
                .count();
        Assert.validateTrue(count != 0, "代号名称已存在，请换一个");
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(Long tenantId, Long id, TenantLogistics tenantLogistics) {
        this.getOne(tenantId, id);
        tenantLogistics.setId(id);
        tenantLogisticsMapperManager.updateById(tenantLogistics);
        saveOrUpdateBoxAmount(tenantLogistics.getServiceAmount(), id);
    }

    public void update(Long id, TenantLogistics tenantLogistics) {
        tenantLogistics.setId(id);
        tenantLogisticsMapperManager.updateById(tenantLogistics);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addFbaOne(Long tenantId, TenantLogisticsFBASaveParam param) {
        this.checkName(tenantId, param.getName(), null, param.getIssuingBayAreaId());
        TenantLogistics tenantLogistics = new TenantLogistics();
        tenantLogistics.setTenantId(tenantId);
        tenantLogistics.setServiceProviderId(SERVICE_PROVIDER_FBA_OFFLINE.getNumber());
        LogisticsChannel channel = logisticsChannelService.getChannelByServiceProviderIdAndCodeId(SERVICE_PROVIDER_FBA_OFFLINE.getNumber(), LogisticsCodeIdEnum.FBA.getCodeId());
        Assert.validateNull(channel, "物流渠道异常");
        tenantLogistics.setLogisticsChannelId(channel.getId());
        tenantLogistics.setIssuingBayAreaId(param.getIssuingBayAreaId());
        tenantLogistics.setCodeId(LogisticsCodeIdEnum.FBA.getCodeId());
        tenantLogistics.setName(param.getName());
        tenantLogistics.setExpressStatus(param.getExpressStatus());
        tenantLogistics.setRemark(param.getRemark());
        tenantLogistics.setAllProductStatus(YES);
        tenantLogistics.setChannelType(ServiceProviderConstant.TYPE_OFFLINE);
        tenantLogistics.setDeliveryMethod(DELIVERY_METHOD_FBA);
        // 其他租户物流可以自定义服务费、贴标费
        tenantLogistics.setServiceAmount(param.getServiceAmount());
        tenantLogistics.setFbaLabelAmount(param.getFbaLabelAmount());
        tenantLogisticsMapperManager.save(tenantLogistics);

        //箱子费
        saveOrUpdateBoxAmount(tenantLogistics.getServiceAmount(), tenantLogistics.getId());
    }

    private void saveOrUpdateBoxAmount(BigDecimal serviceAmount, Long logisticsId) {
        LogisticsServiceAmount logisticsServiceAmount = new LogisticsServiceAmount();
        logisticsServiceAmount.setLogisticsId(logisticsId);
        logisticsServiceAmount.setAmountCode(EnumOrderAmountCode.FBA_BOX.getValue());
        logisticsServiceAmount.setAmount(serviceAmount);
        logisticServiceAmountService.saveOrUpdateAmount(logisticsServiceAmount);
    }


    @Transactional(rollbackFor = Exception.class)
    public void addFbaZtOne(Long tenantId, TenantLogisticsFBASaveParam param) {
        TenantLogistics addOne = new TenantLogistics();
        addOne.setTenantId(tenantId);
        addOne.setServiceProviderId(ZT.getNumber());
        LogisticsChannel channel = logisticsChannelService.getChannelByServiceProviderIdAndCodeId(ZT.getNumber(), LogisticsCodeIdEnum.FBA_ZT.getCodeId());
        Assert.validateNull(channel, "物流渠道异常");
        addOne.setIssuingBayAreaId(param.getIssuingBayAreaId());
        addOne.setName(param.getName());
        addOne.setServiceProviderName("平台");
        addOne.setLogisticsChannelId(channel.getId());
        addOne.setCodeId(channel.getCode());
        addOne.setChannelType(TYPE_OTHER);
        addOne.setAllProductStatus(YES);
        addOne.setDeliveryMethod(DELIVERY_METHOD_FBA);
        addOne.setServiceAmount(param.getServiceAmount());
        addOne.setFbaLabelAmount(param.getFbaLabelAmount());
        addOne.setRemark(param.getRemark());
        addOne.setExpressStatus(param.getExpressStatus());
        tenantLogisticsMapperManager.save(addOne);
        if (TenantCommonConstant.isSdsdiy(tenantId)) {
            countryExpressInfoNewService.addNameAll(addOne.getId());
        }
        saveOrUpdateBoxAmount(addOne.getServiceAmount(), addOne.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void addFbmZtOne(Long tenantId, TenantLogisticsFBASaveParam param) {
        IssuingBayAreaRespDto bayArea = issuingBayAreaFeign.findById(param.getIssuingBayAreaId(), "tenantId");
        Assert.validateNull(bayArea, "发货区域异常");
        TenantLogistics tenantLogistics = new TenantLogistics();
        tenantLogistics.setTenantId(tenantId);
        tenantLogistics.setServiceProviderId(SERVICE_PROVIDER_FBM_ZT.getNumber());
        tenantLogistics.setIssuingBayAreaId(param.getIssuingBayAreaId());
        tenantLogistics.setName(param.getName());
        tenantLogistics.setServiceProviderName("平台");
        tenantLogistics.setIsSpanTenantArea(bayArea.getTenantId().equals(tenantId) ? 0 : 1);
        LogisticsChannel channel = logisticsChannelService.getChannelByServiceProviderIdAndCodeId(SERVICE_PROVIDER_FBM_ZT.getNumber(), LogisticsCodeIdEnum.ZT.getCodeId());
        Assert.validateNull(channel, "物流渠道异常");
        tenantLogistics.setLogisticsChannelId(channel.getId());
        tenantLogistics.setCodeId(LogisticsCodeIdEnum.ZT.getCodeId());
        tenantLogistics.setChannelType(TYPE_SELF_PICKUP);
        tenantLogistics.setAllProductStatus(YES);
        tenantLogistics.setDeliveryMethod(DELIVERY_METHOD_FBM);
        tenantLogisticsMapperManager.save(tenantLogistics);
        if (SDSDIY_TENANT_ID.equals(tenantId)) {
            countryExpressInfoNewService.addNameAll(tenantLogistics.getId());
        }
    }

    public TenantLogisticsRespDto getOneDetail(Long tenantId, Long id) {
        TenantLogistics po = this.getOne(tenantId, id);
        ServiceProvider serviceProvider = serviceProviderService.getById(po.getServiceProviderId());
        TenantLogisticsRespDto tenantLogistics = RelationsBinder.convertAndBind(po, TenantLogisticsRespDto.class, "countryExpressInfos");
        List<CountryExpressInfoNewDetailResp> countryExpressInfos = tenantLogistics.getCountryExpressInfos();
        if (CollectionUtil.isEmpty(countryExpressInfos)) {
            return tenantLogistics;
        }
        List<Long> countryExpressIds = countryExpressInfos.stream().map(CountryExpressInfoNewDetailResp::getId).collect(Collectors.toList());
        Map<String, NationalityRespDto> abbreviationKey = nationalityService.getALLNationality();
        Map<Long, AreaInfoDto> areaInfoMapByLogisticsIdMap = countryExpressInfoAreaService.getAreaInfoMapByLogisticsId(id, countryExpressInfos);
        Map<Long, List<CountryExpressInfoTariffDto>> countryExpressIdTariffMap = countryExpressInfoTariffService.getCountryExpressIdTariffMap(countryExpressIds);

        for (CountryExpressInfoNewDetailResp expressInfoNew : countryExpressInfos) {
            expressInfoNew.setNationality(abbreviationKey.get(expressInfoNew.getName()));
            expressInfoNew.setContinuousPrioritys(countryExpressInfoNewService.priceToContinuousPriority(expressInfoNew.getPrice()));
            expressInfoNew.setAreaInfo(areaInfoMapByLogisticsIdMap.getOrDefault(expressInfoNew.getId(), new AreaInfoDto()));
            expressInfoNew.setTariffList(countryExpressIdTariffMap.getOrDefault(expressInfoNew.getId(), new ArrayList<>()));
        }
        tenantLogistics.setIsOfficialFarawayArea(serviceProvider.getIsOfficialFarawayArea());
        countryExpressInfoAccessibleService.formatAccessibleInfo(tenantLogistics);
        return tenantLogistics;
    }

    public TenantLogistics getOne(Long tenantId, Long id) {
        TenantLogistics po = tenantLogisticsMapperManager.getById(id);
        if (po == null || !po.getTenantId().equals(tenantId)) {
            throw new BusinessException("数据异常！！");
        }
        return po;
    }

    public List<TenantLogistics> getAll(Long tenantId, Long serviceProviderId, Long nonServiceProviderId) {

        return tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getTenantId, tenantId)
                .eq(Objects.nonNull(serviceProviderId), TenantLogistics::getServiceProviderId, serviceProviderId)
                .ne(Objects.nonNull(nonServiceProviderId), TenantLogistics::getServiceProviderId, nonServiceProviderId)
                .list();
    }

    public List<TenantLogistics> getAll(Long tenantId) {
        return tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getTenantId, tenantId)
                .list();
    }

    public List<Long> getLogisticsIdsByIssuingBayAreaIds(Long tenantId, List<Long> issuingBayAreaIds) {
        List<TenantLogistics> logisticsList = tenantLogisticsMapperManager.getByIssuingAreaId(tenantId, issuingBayAreaIds);
        return logisticsList.stream().map(TenantLogistics::getId).collect(Collectors.toList());
    }


    public TenantLogistics getOneById(Long logisticsId) {
        return tenantLogisticsMapperManager.getById(logisticsId);
    }

    public TenantLogisticsRespDto getDtoById(Long logisticsId) {
        TenantLogistics tenantLogistics = getOneById(logisticsId);
        return RelationsBinder.convertAndBind(tenantLogistics, TenantLogisticsRespDto.class, "logisticsChannel");
    }

    public TenantLogisticsRespDto findDtoById(Long id, String fields) {
        TenantLogistics tenantLogistics = tenantLogisticsMapperManager.lambdaQuery(fields, TenantLogisticsRespDto.class)
                .eq(BaseFieldPO::getId, id).one();
        return RelationsBinder.convertAndBind(tenantLogistics, TenantLogisticsRespDto.class, fields);
    }


    public TenantLogisticsRespDto getLogisticsAndChannel(Long logisticsId) {
        TenantLogistics one = tenantLogisticsMapperManager.getById(logisticsId);
        Assert.validateNull(one, "物流异常");
        TenantLogisticsRespDto tenantLogisticsRespDto = BeanUtil.toBean(one, TenantLogisticsRespDto.class);
        LogisticsChannel channel = logisticsChannelService.getById(one.getLogisticsChannelId());
        Assert.validateNull(channel, "物流渠道异常");
        tenantLogisticsRespDto.setLogisticsChannel(BeanUtil.toBean(channel, LogisticsChannelRespDto.class));
        tenantLogisticsRespDto.setTenantId(one.getTenantId());
        return tenantLogisticsRespDto;
    }

    public TenantLogisticsRespDto getLogisticsAndProvider(Long logisticsId) {
        TenantLogistics one = tenantLogisticsMapperManager.getById(logisticsId);
        Assert.validateNull(one, "物流异常");
        TenantLogisticsRespDto tenantLogisticsRespDto = BeanUtil.toBean(one, TenantLogisticsRespDto.class);
        ServiceProvider serviceProvider = serviceProviderService.getById(one.getServiceProviderId());
        Assert.validateNull(serviceProvider, "物流渠道异常");
        tenantLogisticsRespDto.setServiceProviderRespDto(BeanUtil.toBean(serviceProvider, com.sdsdiy.logisticsapi.dto.base.ServiceProviderRespDto.class));
        tenantLogisticsRespDto.setTenantId(one.getTenantId());
        return tenantLogisticsRespDto;
    }

    public List<TenantLogisticsRespDto> getAsCondition(Long tenantId, TenantLogisticsAsConditionParam param) {

        boolean filterSdsArea = !BasePoConstant.LONG_ONE.equals(tenantId)
                && Objects.nonNull(param.getFilterSdsAreaIfIsTenant()) &&
                YES.equals(param.getFilterSdsAreaIfIsTenant());

        List<TenantLogistics> list = tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getTenantId, tenantId)
                .eq(param.getDeliveryMethod() != null, TenantLogistics::getDeliveryMethod, param.getDeliveryMethod())
                .eq(param.getChannelType() != null, TenantLogistics::getChannelType, param.getChannelType())
                .eq(param.getIssuingBayAreaId() != null, TenantLogistics::getIssuingBayAreaId, param.getIssuingBayAreaId())
                .eq(param.getExpressStatus() != null, TenantLogistics::getExpressStatus, param.getExpressStatus())
                .eq(Objects.nonNull(param.getStatus()), TenantLogistics::getStatus, param.getStatus())
                .ne(filterSdsArea, TenantLogistics::getIssuingBayAreaId, BasePoConstant.LONG_ONE)
                .list();
        List<TenantLogisticsRespDto> logisticsChannel = RelationsBinder.convertAndBind(list, TenantLogisticsRespDto.class, "logisticsChannel");
        if (param.getIsButtJoint() != null) {
            logisticsChannel = logisticsChannel.stream().filter(l -> param.getIsButtJoint().equals(l.getLogisticsChannel().getIsButtJoint())).collect(Collectors.toList());
        }
        if (param.getIsTrackInfo() != null) {
            logisticsChannel = logisticsChannel.stream().filter(l -> param.getIsTrackInfo().equals(l.getLogisticsChannel().getIsTrackInfo())).collect(Collectors.toList());
        }
        return logisticsChannel;
    }

    /**
     * 获取运费的物流接口
     *
     * @param dto
     * @return
     */
    public List<LogisticsRespDto> getListByExpensesParam(LogisticsFreightReqDto dto) {
        List<TenantLogistics> list = Lists.newArrayList();
        //获取区域信息
        IssuingBayAreaRespDto bayArea = issuingBayAreaFeign.findById(dto.getIssuingBayAreaId(), "tenantId");
        Assert.validateNull(bayArea, "发货区域异常");
        Assert.validateNull(dto.getTenantId(), "租户id必填");
        dto.setLogisticsTenantId(dto.getTenantId());
        dto.setAreaTenantId(bayArea.getTenantId());
        //物流源
        String finialLogisticsSource = this.getFinialLogisticsSource(dto, bayArea);
        //FBA物流
        if (dto.getLogisticsType() == null || dto.getLogisticsType() == 1) {
            List<TenantLogistics> fbaLogistics = tenantLogisticsMapperManager.getFbaLogistics(bayArea.getTenantId(), dto);
            if (!dto.getTenantId().equals(bayArea.getTenantId()) && TenantCommonConstant.isSdsdiy(bayArea.getTenantId())) {
                fbaLogistics = fbaLogistics.stream().filter(l -> !LogisticsServiceProviderEnum.getZt().contains(l.getServiceProviderId())).collect(Collectors.toList());
            }
            list.addAll(fbaLogistics);
        }
        //FBM物流
        if (dto.getLogisticsType() == null || dto.getLogisticsType() == 2) {
            //开启的物流账号
            List<Long> logisticsIds = getHaveLogisticsAccountLogisticIds(dto.getLogisticsTenantId(), dto.getIssuingBayAreaId(), dto.getIssuingBayId(), dto.getServiceProviderId(), dto.getNonServiceProviderIds());
            //获取本租户区域下的物流
            list.addAll(tenantLogisticsMapperManager.getTenantLogistics(dto, logisticsIds));
            //租户物流获取区域下的线上物流
            if (DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(finialLogisticsSource)) {
                List<Long> areaTenantIdLogistics = getHaveLogisticsAccountLogisticIds(bayArea.getTenantId(), dto.getIssuingBayAreaId(), dto.getIssuingBayId(), dto.getServiceProviderId(), dto.getNonServiceProviderIds());
                list.addAll(tenantLogisticsMapperManager.getAreaOnlineTenantOrManualLogistics(dto, areaTenantIdLogistics));
            }
            //获取区域下其他FBM物流 排查Y2单子
            if (!TEMU_Y2_ADVANCE_SALE.getCode().equals(dto.getOrderMode())) {
                list.addAll(tenantLogisticsMapperManager.getFbmOther(dto));
            }
        }

        //多包裹要排除国内寄付、自提、送货上门物流
        if (YES.equals(dto.getIsMultiParcel())) {
            list = list.stream().filter(l -> !getNoCostServiceProviders().contains(l.getServiceProviderId()) &&
                    !SERVICE_PROVIDER_FBM_ZT_SD.getNumber().equals(l.getServiceProviderId())).collect(Collectors.toList());
        }
        list = otherOnlineStorePlatformLogisticsFilter(list, this.unrelatedLogisticsAreas(), dto.getMerchantStorePlatformCode(), dto.getIssuingBayAreaId(), dto.getCountryCode());
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        list = filterList(dto.getMerchantId(), list);
        return getLogisticsRespList(list);
    }

    public List<TenantLogistics> filterList(Long merchantId, List<TenantLogistics> tenantLogisticses) {
        List<Long> noYunTuA01MerchantIds = environmentVariables.forbidMerchantIdList();
        List<Long> forbidProviderIds = Lists.newArrayList();
        if (noYunTuA01MerchantIds.contains(merchantId)) {
            forbidProviderIds.addAll(environmentVariables.imgCheckLogisticsProviderServiceIdList());
        }
        List<TenantLogistics> list = Lists.newArrayList();
        for (TenantLogistics tenantLogistics : tenantLogisticses) {
            if (!forbidProviderIds.contains(tenantLogistics.getServiceProviderId())) {
                list.add(tenantLogistics);
            }
        }
        log.info("filterList logistics merchantId {} noYunTuA01MerchantIds {}", merchantId, noYunTuA01MerchantIds);
        log.info("filterList logistics forbidProviderIds {}", forbidProviderIds);
        log.info("filterList logistics list {}", list);
        return list;
    }

    //店铺为线上物流的店铺，且区域非店铺专属的进行排除
    public List<TenantLogistics> otherOnlineStorePlatformLogisticsFilter(List<TenantLogistics> list, List<Long> noAreaList, String merchantStorePlatformCode, Long issuingBayAreaId, String countryCode) {
        List<String> unRealLogisticsPlatformCodes = MerchantStorePlatformEnum.getUnRealLogisticsPlatformCodes();
        //获取未对接物流的平台
        if (StringUtils.isEmpty(merchantStorePlatformCode) || !unRealLogisticsPlatformCodes.contains(merchantStorePlatformCode)) {
            return list;
        }
        //国外地址且是配置区域不用过滤
        if (!CountryEnum.CN.getCode().equals(countryCode) && noAreaList.contains(issuingBayAreaId)) {
            return list;
        }
        //国内物流+自提+寄付+平台专属的线上物流
        if (CountryEnum.CN.getCode().equals(countryCode)) {
//            return list.stream().filter(l -> TYPE_OTHER.equals(l.getChannelType()) || LogisticsServiceProviderEnum.getChainServiceProvider().contains(l.getServiceProviderId())).collect(Collectors.toList());
            return list;
        }
        //国外地址只留other类型的和排除自提累的 物流id非sds的不进行过滤
        return list.stream().filter(l -> (l.getTenantId().equals(SDSDIY_TENANT_ID) &&
                        (LogisticsServiceProviderEnum.getSpecialOnlineServiceProviderId().contains(l.getServiceProviderId()) ||
                                SERVICE_PROVIDER_ERP_OPEN_TEMU.equalsNumber(l.getServiceProviderId()))
                ) || !SDSDIY_TENANT_ID.equals(l.getTenantId()))
                .collect(Collectors.toList());
    }

    public List<Long> unrelatedLogisticsAreas() {
        List<Long> noAreaList = Lists.newArrayList();
        CarriageConfig noAreaConfig = carriageConfigService.getConfigByCode(STORE_PLATFORM_UNRELATED_LOGISTICS_AREAS.getCode());
        if (noAreaConfig == null || StringUtils.isEmpty(noAreaConfig.getValue())) {
            return noAreaList;
        }
        String[] split = noAreaConfig.getValue().split(",");
        for (String areaStr : split) {
            if (NumberUtil.isNumber(areaStr)) {
                noAreaList.add(Long.valueOf(areaStr));
            }
        }
        return noAreaList;
    }

    //不相等是分销产品
    public String getFinialLogisticsSource(LogisticsFreightReqDto dto, IssuingBayAreaRespDto bayArea) {
        if (bayArea.getTenantId().equals(dto.getTenantId())) {
            dto.setLogisticsTenantId(bayArea.getTenantId());
            return DistributionProductLogisticsSourceEnum.PRODUCT_TENANT.name();
        }
        //区域不是sds的现在没开放租户物流
        if (!SDSDIY_TENANT_ID.equals(bayArea.getTenantId())) {
            return DistributionProductLogisticsSourceEnum.PRODUCT_TENANT.name();
        }
        String finialLogisticsSource;
        if (StrUtil.isNotBlank(dto.getLogisticsSource())) {
            finialLogisticsSource = dto.getLogisticsSource();
        } else {
            if (YES.equals(bayArea.getIsOpenToOtherTenant())) {
                TenantLogisticsSourceResp tenant = tenantFeign.getDistributionProductLogisticsSource(dto.getTenantId());
                finialLogisticsSource = tenant.getDistributionProductLogisticsSource();
            } else {
                finialLogisticsSource = DistributionProductLogisticsSourceEnum.PRODUCT_TENANT.name();
            }
        }
        if (DistributionProductLogisticsSourceEnum.PRODUCT_TENANT.name().equals(finialLogisticsSource)) {
            dto.setLogisticsTenantId(bayArea.getTenantId());
        } else {
            finialLogisticsSource = DistributionProductLogisticsSourceEnum.ORDER_TENANT.name();
        }
        return finialLogisticsSource;
    }


    private List<Long> getHaveLogisticsAccountLogisticIds(Long tenantId, Long issuingBayAreaId,
                                                          Long issuingBayId, Long serviceProviderId,
                                                          List<Long> nonServiceProviderIds) {
        List<Long> accountIds = tenantServiceProviderAccountService.getOpenIdList(tenantId, serviceProviderId, nonServiceProviderIds);
        List<Long> bayIds;
        if (issuingBayId == null) {
            //区域下的主仓
            List<IssuingBayRespDto> mainWarehouses = issuingBayFeign.findMainWarehouse(issuingBayAreaId);
            bayIds = mainWarehouses.stream().map(IssuingBayRespDto::getId).collect(Collectors.toList());
        } else {
            bayIds = Lists.newArrayList(issuingBayId);
        }
        //获取是开启的物流账号且主仓有发货的物流id
        List<TenantIssuingBayLogisticsAccount> accounts = tenantIssuingBayLogisticsAccountService.listByAccountIdsAndBayIds(accountIds, bayIds);
        return accounts.stream().map(TenantIssuingBayLogisticsAccount::getLogisticsId).collect(Collectors.toList());
    }

    public List<Long> getHaveLogisticsAccountLogisticIdsMainIssuingBay(Long tenantId, Long issuingBayAreaId) {
        List<Long> accountIds = tenantServiceProviderAccountService.getOpenIdList(tenantId);
        List<IssuingBayRespDto> mainWarehouses = issuingBayFeign.findMainWarehouse(issuingBayAreaId);
        List<Long> bayIds = mainWarehouses.stream().map(IssuingBayRespDto::getId).collect(Collectors.toList());
        //获取是开启的物流账号且主仓有发货的物流id
        List<TenantIssuingBayLogisticsAccount> accounts = tenantIssuingBayLogisticsAccountService.listByAccountIdsAndBayIds(accountIds, bayIds);
        return accounts.stream().map(TenantIssuingBayLogisticsAccount::getLogisticsId).collect(Collectors.toList());
    }

    public LogisticsRespDto getLogisticsRespById(Long logisticId) {
        TenantLogistics logistics = tenantLogisticsMapperManager.getById(logisticId);
        if (logistics == null) {
            return null;
        }
        TenantLogisticsAndChannelResp logisticsChannels = RelationsBinder.convertAndBind(logistics, TenantLogisticsAndChannelResp.class, "logisticsChannel");
        return logisticsChannelToLogisticsResp(logisticsChannels);
    }

    public List<Long> getIds(Long serviceProviderId, String codeId) {
        List<TenantLogistics> list = tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getServiceProviderId, serviceProviderId)
                .eq(TenantLogistics::getCodeId, codeId)
                .select(TenantLogistics::getId)
                .list();
        return list.stream().map(TenantLogistics::getId).collect(Collectors.toList());
    }

    public List<LogisticsRespDto> getLogisticsRespList(BaseListReqDto reqDto) {
        if (CollectionUtil.isEmpty(reqDto.getIdList())) {
            return Lists.newArrayList();
        }
        List<TenantLogistics> tenantLogistics = tenantLogisticsMapperManager.listByIds(reqDto.getIdList());
        return getLogisticsRespList(tenantLogistics);
    }

    public List<LogisticsRespDto> all() {
        List<TenantLogistics> tenantLogistics = tenantLogisticsMapperManager
                .lambdaQuery().list();
        return getLogisticsRespList(tenantLogistics);
    }

    public List<LogisticsRespDto> listByIssuingBayIdAndIsTrackInfo(Long issuingBayAreaId, Integer isTrackInfo) {
        List<TenantLogistics> logisticsList = tenantLogisticsMapperManager.listByIssuingBayIdAndIsTrackInfo(issuingBayAreaId, isTrackInfo);
        return getLogisticsRespList(logisticsList);
    }

    public List<LogisticsRespDto> listByTenantIdAndIsTrackInfo(Long tenantId, Integer isTrackInfo) {
        List<TenantLogistics> logisticsList = tenantLogisticsMapperManager.listByTenantIdAndIsTrackInfo(tenantId, isTrackInfo);
        return getLogisticsRespList(logisticsList);
    }

    public List<LogisticsRespDto> getLogisticsListByIssuingBayAreaIdAndType(Long issuingBayAreaId, String type, Long merchantId) {
        List<TenantLogistics> list = tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getIssuingBayAreaId, issuingBayAreaId)
                .eq(TenantLogistics::getDeliveryMethod, type)
                .eq(TenantLogistics::getStatus, CommonStatus.ONLINE.getStatus())
                .list();
        list = filterList(merchantId, list);
        return getLogisticsRespList(list);
    }

    public List<TenantLogisticsRespDto> getByIssuingBayAreaIdAndType(Long issuingBayAreaId, String type) {
        List<TenantLogistics> list = tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getIssuingBayAreaId, issuingBayAreaId)
                .eq(TenantLogistics::getDeliveryMethod, type)
                .ne(TenantLogistics::getServiceProviderId, TEMU_FULLY_MANAGED.getNumber())
                .list();
        List<TenantLogisticsRespDto> respDtos = RelationsBinder.convertAndBind(list, TenantLogisticsRespDto.class, "");
        return respDtos;
    }

    public List<LogisticsRespDto> getLogisticsRespList(List<TenantLogistics> tenantLogistics) {
        List<LogisticsRespDto> results = Lists.newArrayList();
        if (CollectionUtil.isEmpty(tenantLogistics)) {
            return results;
        }
        List<TenantLogisticsAndChannelResp> logisticsChannels = RelationsBinder.convertAndBind(tenantLogistics, TenantLogisticsAndChannelResp.class, "logisticsChannel");
        for (TenantLogisticsAndChannelResp logistics : logisticsChannels) {
            LogisticsRespDto logisticsRespDto = this.logisticsChannelToLogisticsResp(logistics);
            results.add(logisticsRespDto);
        }
        return results;
    }


    private LogisticsRespDto logisticsChannelToLogisticsResp(TenantLogisticsAndChannelResp logistics) {
        LogisticsRespDto logisticsResp = new LogisticsRespDto();
        logisticsResp.setId(logistics.getId());
        logisticsResp.setBulkBase(logistics.getBulkBase());
        logisticsResp.setTenantId(logistics.getTenantId());
        logisticsResp.setName(logistics.getName());
        logisticsResp.setServiceProviderId(logistics.getServiceProviderId());
        logisticsResp.setDiscount(logistics.getDiscount().doubleValue());
        logisticsResp.setRemark(logistics.getRemark());
        logisticsResp.setStatus(logistics.getStatus());
        logisticsResp.setPackingCharges(logistics.getPackingCharges().doubleValue());
        logisticsResp.setCodeId(logistics.getCodeId());
        logisticsResp.setExpressStatus(logistics.getExpressStatus());
        logisticsResp.setAllProductStatus(logistics.getAllProductStatus());
        logisticsResp.setRefundPercent(logistics.getRefundPercent());
        logisticsResp.setBulkOnOff(logistics.getBulkOnOff());
        logisticsResp.setBulkRate(logistics.getBulkRate().doubleValue());
        logisticsResp.setIssuingBayAreaId(logistics.getIssuingBayAreaId());
        logisticsResp.setServiceAmount(logistics.getServiceAmount().doubleValue());
        logisticsResp.setFixedServiceAmount(logistics.getFixedServiceAmount().doubleValue());
        logisticsResp.setApplyNode(logistics.getApplyNode());
        logisticsResp.setFbaLabelAmount(logistics.getFbaLabelAmount().doubleValue());
        logisticsResp.setCustomUploadLable(logistics.getCustomUploadLable());
        logisticsResp.setChannelType(logistics.getChannelType());
        logisticsResp.setDeliveryMethod(logistics.getDeliveryMethod());
        logisticsResp.setTrackingMode(logistics.getTrackingMode());
        logisticsResp.setCourierCode(logistics.getCourierCode());
        logisticsResp.setIsSpanTenantArea(logistics.getIsSpanTenantArea());
        logisticsResp.setShipmentPlaceType(logistics.getShipmentPlaceType());
        logisticsResp.setLogisticsChannelId(logistics.getLogisticsChannelId());
        LogisticsChannelRespDto channel = logistics.getLogisticsChannel();
        if (channel != null) {
            logisticsResp.setIsButtJoint(channel.getIsButtJoint());
            logisticsResp.setIsTrackInfo(channel.getIsTrackInfo());
            logisticsResp.setAliexpressCode(channel.getAliexpressCode());
            logisticsResp.setWarehouseCarrierService(channel.getWarehouseCarrierService());
            logisticsResp.setStandardName(channel.getStandardName());
            logisticsResp.setMethodName(channel.getMethodName());
            logisticsResp.setIsOther(channel.getIsOther());
            logisticsResp.setIossType(channel.getIossType());
            logisticsResp.setDduType(channel.getDduType());
            logisticsResp.setIsCollectVatAble(channel.getIsCollectVatAble());
            logisticsResp.setDhGateShippingType(channel.getDhGateShippingType());
        }
        ServiceProviderRespDto serviceProviderRespDto = new ServiceProviderRespDto();
        serviceProviderRespDto.setId(logistics.getServiceProviderId());
        serviceProviderRespDto.setName(logistics.getServiceProviderName());
        logisticsResp.setServiceProviderRespDto(serviceProviderRespDto);
        if (DELIVERY_METHOD_FBM.equals(logistics.getDeliveryMethod())) {
            logisticsResp.setType(2);
        } else {
            logisticsResp.setType(1);
        }
        if (DELIVERY_METHOD_FBM.equals(logistics.getDeliveryMethod()) && !TYPE_OFFLINE.equals(logistics.getChannelType())) {
            logisticsResp.setBeCommon(NO);
        } else {
            logisticsResp.setBeCommon(YES);
        }
        logisticsResp.setNeedCancelOrderInThirdParty(logistics.getNeedCancelOrderInThirdParty());
        return logisticsResp;
    }


    public void checkChargeTypeRule(CountryExpressInfoNewBaseDto dto, Long logisticsId) {
        TenantLogistics tenantLogistics = tenantLogisticsMapperManager.getById(logisticsId);
        String chargeType = dto.getChargeType();
        if (DELIVERY_METHOD_FBM.equals(tenantLogistics.getDeliveryMethod()) && TYPE_OFFLINE.equals(tenantLogistics.getChannelType())) {
            Assert.validateTrue(!CHARGE_TYPE_WEIGHT.equals(chargeType), "FBM线下物流，只可用按重量收费");
        }
        if (DELIVERY_METHOD_FBM.equals(tenantLogistics.getDeliveryMethod()) && (ONLINE_LIST.contains(tenantLogistics.getChannelType()) || ZT.getNumber().equals(tenantLogistics.getServiceProviderId()))) {
            Assert.validateTrue(!CHARGE_TYPE_ORDER_NUM.equals(chargeType) && !CHARGE_TYPE_PRODUCT_NUM.equals(chargeType), "FBM线上物流和自提，只可用按订单收费和按件数收费");
        }
        if (DELIVERY_METHOD_FBA.equals(tenantLogistics.getDeliveryMethod()) && ZT.getNumber().equals(tenantLogistics.getServiceProviderId())) {
            Assert.validateTrue(!CHARGE_TYPE_ORDER_NUM.equals(chargeType) && !CHARGE_TYPE_PRODUCT_NUM.equals(chargeType), "FBA自提，只可用按订单收费和按件数收费");
        }
        if (DELIVERY_METHOD_FBA.equals(tenantLogistics.getDeliveryMethod()) && SERVICE_PROVIDER_FBA_OFFLINE.getNumber().equals(tenantLogistics.getServiceProviderId())) {
            Assert.validateTrue(!CHARGE_TYPE_OFFLINE.equals(chargeType), "FBA线下物流，只可用线下核算收费");
        }
        Assert.validateTrue(YES.equals(dto.getIsServiceChargeLowerLimit()) && dto.getServiceChargeLowerCost() == null, "最低上限启用，最低费用不能为空");
        Assert.validateTrue(YES.equals(dto.getIsServiceChargeUpperLimit()) && dto.getServiceChargeUpperCost() == null, "最高上限启用，最高费用不能为空");
        Assert.validateTrue(YES.equals(dto.getIsServiceChargeDerated()) && dto.getServiceChargeDeratedConditionalCost() == null, "减免已启用，条件费用不能为空");
        if (CHARGE_TYPE_PRODUCT_NUM.equals(chargeType) && YES.equals(dto.getIsServiceChargeLowerLimit()) && YES.equals(dto.getIsServiceChargeUpperLimit())) {
            Assert.validateTrue(dto.getServiceChargeUpperCost().compareTo(dto.getServiceChargeLowerCost()) < 0, "服务费上限费用不能大于服务费下限");
        }
    }

    public List<TenantLogistics> findByIds(Collection<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return tenantLogisticsMapperManager.listByIds(idList);
    }

    public List<TenantLogistics> getListByServiceProviders(List<Long> serviceProviderIds) {
        if (CollectionUtil.isEmpty(serviceProviderIds)) {
            return Lists.newArrayList();
        }
        return tenantLogisticsMapperManager.lambdaQuery()
                .in(TenantLogistics::getServiceProviderId, serviceProviderIds)
                .select(TenantLogistics::getId, TenantLogistics::getCodeId)
                .list();
    }

    public List<TenantLogistics> getListByServiceProviders(List<Long> serviceProviderIds,Long tenantId) {
        if (CollectionUtil.isEmpty(serviceProviderIds)) {
            return Lists.newArrayList();
        }
        return tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getTenantId, tenantId)
                .in(TenantLogistics::getServiceProviderId, serviceProviderIds)
                .select(TenantLogistics::getId, TenantLogistics::getCodeId)
                .list();
    }

    public void saveOne(TenantLogistics oldLogistics) {
        tenantLogisticsMapperManager.save(oldLogistics);
    }

    public List<LogisticsChannel> getLogisticsChannelContainSDS(Long tenantId, Long serviceProviderId, Long nonServiceProviderId) {
        List<TenantLogistics> list = this.getAll(tenantId, serviceProviderId, nonServiceProviderId);
        if (!SDSDIY_TENANT_ID.equals(tenantId)) {
            List<TenantLogistics> sdsLogistics = this.getAll(SDSDIY_TENANT_ID, serviceProviderId, nonServiceProviderId);
            TenantLogisticsSourceResp logisticsSource = tenantFeign.getDistributionProductLogisticsSource(tenantId);
            //开启租户物流的  保留sds区域的未开启跨租户区域物流
            if (DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(logisticsSource.getDistributionProductLogisticsSource())) {
                List<IssuingBayAreaRespDto> issuingBayAreaResp = issuingBayAreaFeign.listAll(SDSDIY_TENANT_ID, null, "");
                List<Long> noOpenAreaIds = issuingBayAreaResp.stream().filter(area -> NO.equals(area.getIsOpenToOtherTenant())).map(IssuingBayAreaRespDto::getId).collect(Collectors.toList());
                sdsLogistics = sdsLogistics.stream().filter(logistics -> noOpenAreaIds.contains(logistics.getIssuingBayAreaId())).collect(Collectors.toList());
            }
            list.addAll(sdsLogistics);
        }
        List<Long> channelIds = list.stream()
                .filter(l -> CommonStatus.ONLINE.getStatus().equals(l.getStatus()) && DELIVERY_METHOD_FBM.equals(l.getDeliveryMethod()))
                .map(TenantLogistics::getLogisticsChannelId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(channelIds)) {
            return Lists.newArrayList();
        }
        List<Long> nonServiceProviderIds = Objects.isNull(nonServiceProviderId) ? null : Collections.singletonList(nonServiceProviderId);
        return logisticsChannelService.getChannelListByIds(channelIds, null, serviceProviderId, nonServiceProviderIds);
    }

    public Map<Long, LogisticsAndLogisticsSourceResp> getLogisticsAndLogisticsSource(Long tenantId, List<Long> logisticsIds) {
        if (CollectionUtil.isEmpty(logisticsIds)) {
            return Maps.newHashMap();
        }
        List<TenantLogistics> logistics = tenantLogisticsMapperManager.listByIds(logisticsIds);
        List<LogisticsAndLogisticsSourceResp> logisticsSources = BeanUtil.copyToList(logistics, LogisticsAndLogisticsSourceResp.class);
        TenantLogisticsSourceResp tenantSource = tenantFeign.getDistributionProductLogisticsSource(tenantId);
        for (LogisticsAndLogisticsSourceResp logisticsSource : logisticsSources) {
            if (logisticsSource.getIsSpanTenantArea().equals(NO)) {
                if (!SDSDIY_TENANT_ID.equals(tenantId) && SDSDIY_TENANT_ID.equals(logisticsSource.getTenantId())
                        && (LogisticsServiceProviderEnum.getOnlineServiceProviderId().contains(logisticsSource.getServiceProviderId()) || LogisticsCodeIdEnum.isFbmZtOrConsignment(logisticsSource.getCodeId()))) {
                    logisticsSource.setLogisticsSource(tenantSource.getDistributionProductLogisticsSource());
                } else {
                    logisticsSource.setLogisticsSource(DistributionProductLogisticsSourceEnum.PRODUCT_TENANT.name());
                }
            } else {
                logisticsSource.setLogisticsSource(DistributionProductLogisticsSourceEnum.ORDER_TENANT.name());
            }
            logisticsSource.setDistributionProductLogisticsSource(tenantSource.getDistributionProductLogisticsSource());
        }
        return logisticsSources.stream().collect(Collectors.toMap(LogisticsAndLogisticsSourceResp::getId, Function.identity()));
    }

    public List<TenantLogistics> getSpanTenantAreaLogisticsAndNoAllProduct(Long tenantId) {
        return tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getTenantId, tenantId)
                .eq(TenantLogistics::getIsSpanTenantArea, YES)
                .eq(TenantLogistics::getAllProductStatus, NO)
                .list();

    }

    public List<TenantLogistics> getSpanTenantAreaLogisticsByAreaId(Long areaId) {
        return tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getIssuingBayAreaId, areaId)
                .eq(TenantLogistics::getIsSpanTenantArea, YES)
                .list();

    }

    public void updateShipmentPlaceType(Long id, String shipmentPlaceType) {
        TenantLogistics logistics = new TenantLogistics();
        logistics.setId(id);
        logistics.setShipmentPlaceType(shipmentPlaceType);
        tenantLogisticsMapperManager.updateById(logistics);
    }

    public Map<Long, Long> getSpanAreaLogisticsNumMap(List<Long> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Maps.newHashMap();
        }
        List<TenantLogistics> tenantLogistics = tenantLogisticsMapperManager.lambdaQuery()
                .in(TenantLogistics::getTenantId, idList)
                .eq(TenantLogistics::getIsSpanTenantArea, YES)
                .select(TenantLogistics::getTenantId)
                .list();
        return tenantLogistics.stream().collect(Collectors.groupingBy(TenantLogistics::getTenantId, Collectors.counting()));
    }

    public List<TenantLogistics> getLogisticsByIssuingBayAreaIds(List<Long> issuingBayAreaIds) {
        return tenantLogisticsMapperManager.lambdaQuery()
                .in(TenantLogistics::getIssuingBayAreaId, issuingBayAreaIds)
                .list();
    }

    public List<LogisticsRespDto> getSpanTenantAreaLogistics(Long tenantId, Long issuedBayAreaId) {
        List<TenantLogistics> tenantLogistics = tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getTenantId, tenantId)
                .eq(TenantLogistics::getIsSpanTenantArea, YES)
                .eq(TenantLogistics::getIssuingBayAreaId, issuedBayAreaId)
                .eq(TenantLogistics::getStatus, CommonStatus.ONLINE.getStatus())
                .list();
        LogisticsFreightReqDto logisticsFreightReqDto = new LogisticsFreightReqDto();
        logisticsFreightReqDto.setIssuingBayAreaId(issuedBayAreaId);
        logisticsFreightReqDto.setTenantId(tenantId);
        tenantLogistics.addAll(tenantLogisticsMapperManager.getFbmOther(logisticsFreightReqDto));
        return getLogisticsRespList(tenantLogistics);
    }

    public List<TenantLogistics> getSpanTenantAreaLogistics() {
        return tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getIsSpanTenantArea, YES)
                .list();
    }

    public List<Long> getIdsByChannelIds(Long issuingBayAreaId, List<Long> channelIds) {

        if (channelIds.isEmpty()) {
            return Collections.emptyList();
        }
        return tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getIssuingBayAreaId, issuingBayAreaId)
                .eq(TenantLogistics::getStatus, CommonStatus.ONLINE.getStatus())
                .in(TenantLogistics::getLogisticsChannelId, channelIds)
                .list().stream().map(TenantLogistics::getId).collect(Collectors.toList());
    }

    public List<TenantLogisticsRespDto> getByChannelIds(Long issuingBayAreaId, List<Long> channelIds) {
        if (channelIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<TenantLogistics> list = tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getIssuingBayAreaId, issuingBayAreaId)
                .in(TenantLogistics::getLogisticsChannelId, channelIds)
                .list();
        return BeanUtil.copyToList(list, TenantLogisticsRespDto.class);
    }

    public List<TenantLogisticsRespDto> getCNSelfSend(Long tenantId, Long issuingAreaId, Long issuingBayId, Integer isTemuFullySelfSend) {
        List<Long> logisticIds = tenantIssuingBayLogisticsAccountService.getBindAndOpenAccountLogisticIds(issuingBayId);
        if (CollectionUtil.isEmpty(logisticIds)) {
            return Lists.newArrayList();
        }
        Set<Long> filterCountryLogistics = countryExpressInfoNewService.lambdaQuery()
                .select(CountryExpressInfoNew::getLogisticsId)
                .in(CountryExpressInfoNew::getName, Lists.newArrayList(NAME_ALL, NAME_CN))
                .in(CountryExpressInfoNew::getLogisticsId, logisticIds)
                .eq(CountryExpressInfoNew::getStatus, CommonStatus.ONLINE.getStatus())
                .list().stream().map(CountryExpressInfoNew::getLogisticsId).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(filterCountryLogistics)) {
            return Lists.newArrayList();
        }
        List<TenantLogistics> tenantLogistics = tenantLogisticsMapperManager.lambdaQuery()
                .in(TenantLogistics::getId, filterCountryLogistics)
                .eq(TenantLogistics::getTenantId, tenantId)
                .eq(TenantLogistics::getStatus, STATUS_OPEN)
                .eq(TenantLogistics::getChannelType, TYPE_OFFLINE)
                .eq(TenantLogistics::getIssuingBayAreaId, issuingAreaId)
                .eq(TenantLogistics::getDeliveryMethod, DELIVERY_METHOD_FBM)
                //如果是temu全托管自寄发货 只能返回中通、顺丰、京东
                .in(BasePoConstant.yes(isTemuFullySelfSend), TenantLogistics::getServiceProviderId, LogisticsServiceProviderEnum.temuFullySelfSendServiceProviderIds())
                .list();
        if (CollectionUtil.isEmpty(tenantLogistics)) {
            return Lists.newArrayList();
        }
        return BeanUtil.copyToList(tenantLogistics, TenantLogisticsRespDto.class);
    }

    public List<TenantLogistics> findNeedCancelOrderInThirdPartyLogisticsByIds(List<Long> logisticsIds) {
        if (CollUtil.isEmpty(logisticsIds)) {
            return Collections.emptyList();
        }
        return tenantLogisticsMapperManager.lambdaQuery()
                .in(TenantLogistics::getId, logisticsIds)
                .eq(TenantLogistics::getNeedCancelOrderInThirdParty, YES)
                .list();
    }

    public List<TenantLogistics> findNeedCancelLogistics() {
        return tenantLogisticsMapperManager.lambdaQuery()
                .eq(TenantLogistics::getNeedCancelOrderInThirdParty, YES)
                .list();
    }

    public DeclarationTaxResp getTaxInfos(Long logisticsId, String countryCode) {
        DeclarationTaxResp carriageDeclarationTax = new DeclarationTaxResp();
        carriageDeclarationTax.setTaxType(TaxNumberTypeEnum.NONE.getCode());
        carriageDeclarationTax.setTaxStatus(TaxNumberStatusEnum.NONE.getValue());
        if (logisticsId == null || logisticsId == 0L || StringUtils.isEmpty(countryCode)) {
            return carriageDeclarationTax;
        }
        TenantLogistics logistics = tenantLogisticsMapperManager.getById(logisticsId);
        if (logistics == null) {
            return carriageDeclarationTax;
        }
        LogisticsChannel logisticsChannel = logisticsChannelService.getById(logistics.getLogisticsChannelId());
        Pair<TaxNumberTypeEnum, TaxNumberStatusEnum> taxStatusEnum = TaxOptionUtil.getTaxTypeEnumAndTaxStatusEnum(countryCode, logistics.getServiceProviderId(), logisticsChannel.getIsCollectVatAble(), logisticsChannel.getIossType(), logisticsChannel.getDduType());
        TaxNumberStatusEnum taxNumberStatusEnum = taxStatusEnum.getRight();
        TaxNumberTypeEnum taxNumberTypeEnum = taxStatusEnum.getLeft();
        carriageDeclarationTax.setTaxStatus(taxNumberStatusEnum.getValue());
        carriageDeclarationTax.setTaxType(taxNumberTypeEnum.getValue());
        return carriageDeclarationTax;
    }


    public List<LogisticsGroupToSelectResp> logisticsGroupToSelect(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<TenantLogistics> logistics = tenantLogisticsMapperManager.listByIds(ids);
        if (CollectionUtil.isEmpty(logistics)) {
            return Lists.newArrayList();
        }
        //获取租户信息
        List<Long> tenantIds = logistics.stream().map(TenantLogistics::getTenantId).collect(Collectors.toList());
        Map<Long, String> tenantNameMap = ListUtil.toMapByBaseIdAndName(this.tenantFeign.findNameByIds(BaseListDto.of(tenantIds)));
        //获取区域信息
        List<Long> areaIds = logistics.stream().map(TenantLogistics::getIssuingBayAreaId).collect(Collectors.toList());
        Map<Long, String> areaIdKeyNameValueMap = this.issuingBayAreaFeign.findByIds("id,name", areaIds)
                .stream().collect(Collectors.toMap(IssuingBayAreaRespDto::getId, IssuingBayAreaRespDto::getName));
        //拼装结果
        List<LogisticsGroupToSelectResp> groupToSelectList = Lists.newArrayList();
        for (TenantLogistics logistic : logistics) {
            //groupToSelectList是否包含logistic中的tenantId
            Optional<LogisticsGroupToSelectResp> groupToSelectOptional = groupToSelectList
                    .stream().filter(groupToSelect -> logistic.getTenantId().equals(groupToSelect.getTenantId()))
                    .findFirst();
            LogisticsGroupToSelectResp logisticsGroupToSelectResp;
            if (groupToSelectOptional.isPresent()) {
                logisticsGroupToSelectResp = groupToSelectOptional.get();
            } else {
                logisticsGroupToSelectResp = new LogisticsGroupToSelectResp();
                logisticsGroupToSelectResp.setTenantId(logistic.getTenantId());
                logisticsGroupToSelectResp.setTenantName(tenantNameMap.getOrDefault(logistic.getTenantId(), ""));
                logisticsGroupToSelectResp.setLogisticsAreas(Lists.newArrayList());
                groupToSelectList.add(logisticsGroupToSelectResp);
            }
            //getLogisticsAreas是否包含logistic中的区域id
            List<LogisticsIssuingAreaGroupDto> logisticsAreas = logisticsGroupToSelectResp.getLogisticsAreas();
            Optional<LogisticsIssuingAreaGroupDto> issuingAreaGroupOptional = logisticsAreas
                    .stream().filter(areaResp -> logistic.getIssuingBayAreaId().equals(areaResp.getIssuingAreaId()))
                    .findFirst();
            LogisticsIssuingAreaGroupDto issuingAreaGroup;
            if (issuingAreaGroupOptional.isPresent()) {
                issuingAreaGroup = issuingAreaGroupOptional.get();
            } else {
                issuingAreaGroup = new LogisticsIssuingAreaGroupDto();
                issuingAreaGroup.setIssuingAreaId(logistic.getIssuingBayAreaId());
                issuingAreaGroup.setIssuingAreaName(areaIdKeyNameValueMap.getOrDefault(logistic.getIssuingBayAreaId(), ""));
                issuingAreaGroup.setLogisitcsList(Lists.newArrayList());
                logisticsAreas.add(issuingAreaGroup);
            }
            issuingAreaGroup.getLogisitcsList().add(BeanUtil.toBean(logistic, LogisticsSimpleRespDto.class));
        }
        return groupToSelectList;
    }

    public TenantAddressRespDto getTenantAddress(Long logisticsId, Long issuingBayId) {
        TenantLogistics logistics = getOneById(logisticsId);
        Assert.validateNull(logistics, "物流异常");
        TenantIssuingBayLogisticsAccount account = tenantIssuingBayLogisticsAccountService.getOne(logisticsId, issuingBayId);
        Assert.validateNull(account, "物流账号异常");
        Long tenantServiceProviderAccountId = account.getTenantServiceProviderAccountId();
        TenantAddress addresses = tenantServiceProviderAccountService.getAddresses(logistics.getTenantId(), tenantServiceProviderAccountId);
        return BeanUtil.copyProperties(addresses, TenantAddressRespDto.class);
    }
}