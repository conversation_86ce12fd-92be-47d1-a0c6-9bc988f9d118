package com.sdsdiy.logisticsimpl.service.expense;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sdsdiy.common.base.entity.dto.BaseListQueryDTO;
import com.sdsdiy.logisticsapi.dto.*;
import com.sdsdiy.logisticsdata.dto.base.LogisticsRespDto;
import com.sdsdiy.logisticsdata.dto.base.ShipAmountDto;
import com.sdsdiy.logisticsdata.util.CarriageAmountUtil;
import com.sdsdiy.logisticsimpl.entity.bo.ShipCostBo;
import com.sdsdiy.logisticsimpl.feign.ApplicationFeign;
import com.sdsdiy.logisticsimpl.feign.TenantWalletFeign;
import com.sdsdiy.userapi.dto.application.TenantApplicationScenarioRelRespDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.tenant.TenantCommonConstant.SDSDIY_TENANT_ID;
import static com.sdsdiy.common.base.enums.ApplicationCodeEnum.TENANT_CARRIAGE_COMMISSION_RATE;
import static com.sdsdiy.logisticsapi.constant.ServiceProviderConstant.DELIVERY_METHOD_FBM;
import static com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum.SERVICE_PROVIDER_PREPAID;
import static com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum.getZt;

/**
 * @author: bin_lin
 * @date: 2024/3/18 20:01
 * @desc:
 */
@Service
@RequiredArgsConstructor
public class TenantCarriageCommissionService {

    private final TenantWalletFeign tenantWalletFeign;

    private final ApplicationFeign applicationFeign;

    public void shipAmountFormatCommission(ShipCostReqDto dto, LogisticsRespDto logisticsRespDto, ShipCostBo shipCostBo, ShipAmountDto shipAmountDto) {
        //佣金费率
        BigDecimal tenantCarriageCommissionRate = this.getTenantCarriageCommissionRate(dto.getOrderTenantId(), dto.getTenantCarriageCommissionRate());
        shipAmountDto.setTenantCarriageCommissionRate(BigDecimal.ZERO);
        if (SDSDIY_TENANT_ID.equals(dto.getOrderTenantId()) || logisticsRespDto.getTenantId().equals(dto.getOrderTenantId()) || tenantCarriageCommissionRate.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        shipAmountDto.setTenantCarriageCommissionRate(tenantCarriageCommissionRate);
        //非FBM的不算
        if (!DELIVERY_METHOD_FBM.equals(logisticsRespDto.getDeliveryMethod())) {
            return;
        }
        //自提或者寄付的不用算
        if (getZt().contains(logisticsRespDto.getServiceProviderId()) || SERVICE_PROVIDER_PREPAID.getNumber().equals(logisticsRespDto.getServiceProviderId())) {
            return;
        }
        shipAmountDto.setShipAmount(CarriageAmountUtil.getAddTenantCarriageCommissionFreight(shipCostBo.getShipCost(), tenantCarriageCommissionRate));
    }


    public void batchTenantCarriageCommission(List<BatchLogisticsFreightReqDto> dtoList, List<BatchLogisticsFreightRespDto> resultList, LogisticsRespDto logisticsResp) {
        Set<Long> tenantIds = dtoList.stream().map(BatchLogisticsFreightReqDto::getTenantId).filter(tenantId -> !SDSDIY_TENANT_ID.equals(tenantId)).collect(Collectors.toSet());
        Map<Long, Boolean> onlineOpenMap = Maps.newHashMap();
        List<TenantApplicationScenarioRelRespDto> tenantAppScenarios = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(tenantIds)) {
            //线上收款未开启，没有佣金费
            onlineOpenMap = tenantWalletFeign.isOnlineOpenMap(new BaseListQueryDTO<>(tenantIds));
            //佣金配置
            tenantAppScenarios = applicationFeign.getTenantAppScenarioRelByTenantIdsAndCode(new BaseListQueryDTO<>(tenantIds), TENANT_CARRIAGE_COMMISSION_RATE.getCode());
        }
        Map<Long, BigDecimal> scenarioRelRespMap = tenantAppScenarios.stream().filter(t -> t.getValue() != null).collect(Collectors.toMap(TenantApplicationScenarioRelRespDto::getTenantId, TenantApplicationScenarioRelRespDto::getValue));
        //计算费用
        for (BatchLogisticsFreightRespDto freightResp : resultList) {
            freightResp.setOriginalFreight(freightResp.getFreight());
            Boolean isOnline = onlineOpenMap.getOrDefault(freightResp.getTenantId(), false);
            BigDecimal tenantCarriageCommissionRate = scenarioRelRespMap.getOrDefault(freightResp.getTenantId(), BigDecimal.ZERO);
            if (freightResp.getParamTenantCarriageCommissionRate() != null && freightResp.getParamTenantCarriageCommissionRate().compareTo(BigDecimal.ZERO) >= 0) {
                tenantCarriageCommissionRate = freightResp.getParamTenantCarriageCommissionRate();
            }
            freightResp.setTenantCarriageCommissionRate(BigDecimal.ZERO);
            if (SDSDIY_TENANT_ID.equals(freightResp.getTenantId()) || logisticsResp.getTenantId().equals(freightResp.getTenantId()) || !isOnline || tenantCarriageCommissionRate.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            freightResp.setTenantCarriageCommissionRate(tenantCarriageCommissionRate);
            //非FBM的不算
            if (!DELIVERY_METHOD_FBM.equals(logisticsResp.getDeliveryMethod())) {
                continue;
            }
            //自提或者寄付的不用算
            if (getZt().contains(logisticsResp.getServiceProviderId()) || SERVICE_PROVIDER_PREPAID.getNumber().equals(logisticsResp.getServiceProviderId())) {
                continue;
            }
            Double newFreight = CarriageAmountUtil.getAddTenantCarriageCommissionFreight(freightResp.getFreight(), tenantCarriageCommissionRate);
            freightResp.setFreight(newFreight);
        }
    }

    public void formatTenantCarriageCommissionRate(List<LogisticsFreightRespDto> resultList, LogisticsFreightReqDto dto) {
        if (CollectionUtil.isEmpty(resultList) || SDSDIY_TENANT_ID.equals(dto.getTenantId()) || SDSDIY_TENANT_ID.equals(dto.getAreaTenantId())) {
            return;
        }
        //佣金费用
        BigDecimal rate = this.getTenantCarriageCommissionRate(dto.getTenantId(), dto.getTenantCarriageCommissionRate());
        if (rate == null || BigDecimal.ZERO.equals(rate)) {
            return;
        }
        for (LogisticsFreightRespDto respDto : resultList) {
            //租户使用自己物流不算佣金费
            if (dto.getTenantId().equals(respDto.getLogistics().getTenantId())) {
                continue;
            }
            respDto.setTenantCarriageCommissionRate(rate);
            //非FBM的不算
            if (!DELIVERY_METHOD_FBM.equals(respDto.getLogistics().getDeliveryMethod())) {
                continue;
            }
            //自提或者寄付的不用算
            if (getZt().contains(respDto.getLogistics().getServiceProviderId()) || SERVICE_PROVIDER_PREPAID.getNumber().equals(respDto.getLogistics().getServiceProviderId())) {
                continue;
            }
            Double newFreight = CarriageAmountUtil.getAddTenantCarriageCommissionFreight(respDto.getFreight(), rate);
            respDto.setFreight(newFreight);
        }
    }

    private BigDecimal getTenantCarriageCommissionRate(Long tenantId, BigDecimal tenantCarriageCommissionRate) {
        //线上收款未开启，没有佣金费
        Boolean onlineOpen = tenantWalletFeign.isOnlineOpen(tenantId);
        if (!onlineOpen) {
            return BigDecimal.ZERO;
        }
        //大于等于0直接返回入参的佣金率
        if (tenantCarriageCommissionRate != null && BigDecimal.ZERO.compareTo(tenantCarriageCommissionRate) <= 0) {
            return tenantCarriageCommissionRate;
        }
        return this.getTenantApplicationCommissionRate(tenantId);
    }

    private BigDecimal getTenantApplicationCommissionRate(Long tenantId) {
        //查看配置佣金率
        TenantApplicationScenarioRelRespDto tenantApp = applicationFeign.getTenantAppScenarioRelByTenantIdAndCode(tenantId, TENANT_CARRIAGE_COMMISSION_RATE.getCode());
        if (tenantApp == null || tenantApp.getValue() == null || BigDecimal.ZERO.equals(tenantApp.getValue())) {
            return BigDecimal.ZERO;
        }
        return tenantApp.getValue();
    }

}
