package com.sdsdiy.logisticsapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: bin_lin
 * @date: 2020/7/6 15:31
 * @desc:
 */
@Data
public class LogisticsFreightReqDto {
    @ApiModelProperty("仅仅用于标示")
    private Long id;
    @ApiModelProperty("查找类型 1.推荐物流 不传更多")
    private Integer findType;
    @ApiModelProperty(value = "物流id")
    private Long logisticsId;
    @ApiModelProperty(value = "产品codeId")
    private String codeId;
    @ApiModelProperty(value = "物流类型是否特快 1特快2不是A")
    private Integer expressType;
    @ApiModelProperty(value = "物流类型 1.FBA 2.非FBA")
    private Integer logisticsType;
    private Integer originType;
    @ApiModelProperty(value = "平台code")
    private String merchantStorePlatformCode;
    @ApiModelProperty(value = "第三方单号")
    private String outOrderNo;
    @ApiModelProperty(value = "货款总价", required = true)
    private BigDecimal goodsTotalPrice;
    @ApiModelProperty(value = "商户id", required = true)
    private Long merchantId;
    @NotNull(message = "国家简码不能为空！！")
    @ApiModelProperty(value = "国家简码", required = true)
    private String countryCode;
    private String provinceCode;
    private String province;
    private String cityCode;
    private String city;
    private String postcode;
    private Long logisticsChannelId;
    private Long merchantStoreId;
    private String merchantStoreSellerId;
    @NotEmpty(message = "产品IdList")
    @Valid
    @ApiModelProperty(value = "产品IdList", required = true)
    private List<LogisticsFreightProductReqDto> productList;
    @NotNull(message = "发货区域id!")
    @ApiModelProperty(value = "发货区域id", required = true)
    private Long issuingBayAreaId;
    @ApiModelProperty(value = "发货仓id")
    private Long issuingBayId;
    private String clientSelectedLogistics;
    private Integer importOrderSourceType;
    @ApiModelProperty(value = "订单源")
    private String orderOriginType;
    @ApiModelProperty(value = "地址类型")
    private String addressType;
    @ApiModelProperty(value = "优先级物流")
    private Long priorLogisticsId;
    @ApiModelProperty(value = "排除物流Id")
    private List<Long> excludeLogisticsIds;
    /**
     * 产品总数
     */
    private Integer totalProductNum;
    /**
     * 是否需要匹配运费地区
     */
    private Boolean isCountryExpressArea;
    /**
     * 订单租户id 必填
     */
    private Long tenantId;
    /**
     * 物流来源 选填 PRODUCT_TENANT or ORDER_TENANT
     */
    private String logisticsSource;
    @ApiModelProperty(value = "是否检测重量 1需要 0不需要  不传也标识需要检验重量")
    private Integer isCheckWeight;
    @ApiModelProperty(value = "是否多包裹 多包裹要排除特殊物流")
    private Integer isMultiParcel;
    @ApiModelProperty(value = "租户物流费用")
    private BigDecimal tenantCarriageCommissionRate;
    /**
     * 物流租户id 内部参数，外部传入无效
     **/
    private Long logisticsTenantId;
    /**
     * 区域租户id 内部参数，外部传入无效
     */
    private Long areaTenantId;
    private Long serviceProviderId;
    /**
     * 指定的物流商
     */
    private List<Long> selectServiceProviderIds;
    private List<Long> nonServiceProviderIds;
    private Date orderCreateTime;
    /***
     * 订单模式 Y2_advance_sale
     */
    private String orderMode;


}
