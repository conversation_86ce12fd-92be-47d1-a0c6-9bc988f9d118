package com.sdsdiy.productimpl.service.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.enums.ApplicationCodeEnum;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.CompareUtils;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.productapi.dto.product.PlatformPriceDto;
import com.sdsdiy.productapi.dto.product.ProductInquiryPriceReqDto;
import com.sdsdiy.productapi.dto.product.ProductPriceResp;
import com.sdsdiy.productapi.dto.product.PromotionLadderPriceDto;
import com.sdsdiy.productdata.constants.ProductEnumConstant;
import com.sdsdiy.productdata.dto.preferential.ProductPreferentialActivityDTO;
import com.sdsdiy.productdata.dto.preferential.req.ProductProcessingActivityReqDTO;
import com.sdsdiy.productdata.dto.price.*;
import com.sdsdiy.productdata.dto.product.SmallOrderPriceDto;
import com.sdsdiy.productdata.enums.supply.SupplyChainTypeEnum;
import com.sdsdiy.productimpl.entity.bo.ProductCurrentPriceCalBO;
import com.sdsdiy.productimpl.entity.po.Product;
import com.sdsdiy.productimpl.entity.po.ProductAccumulatePrice;
import com.sdsdiy.productimpl.entity.po.ProductMerchantPrice;
import com.sdsdiy.productimpl.entity.po.distribution.DistributionProductTenantPrice;
import com.sdsdiy.productimpl.feign.ApplicationFeign;
import com.sdsdiy.productimpl.feign.MerchantFeign;
import com.sdsdiy.productimpl.feign.TenantWalletFeign;
import com.sdsdiy.productimpl.service.ProductAccumulatePriceService;
import com.sdsdiy.productimpl.service.ProductMerchantPriceService;
import com.sdsdiy.productimpl.service.auth.MerchantAuthProductParentService;
import com.sdsdiy.productimpl.service.distribution.DistributionProductTenantPriceService;
import com.sdsdiy.productimpl.service.distribution.ProductDistributionService;
import com.sdsdiy.productimpl.service.preferential.ProductPreferentialActivityService;
import com.sdsdiy.productimpl.util.ProductPriceUtil;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userdata.enums.MemberLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.*;
import static com.sdsdiy.productdata.constants.ProductConstant.*;

/**
 * 旧的代码不动，新写一个，只计算价格，不做其他事
 *
 * <AUTHOR>
 * @date 2021/11/20
 */
@Slf4j
@Service
public class ProductPriceV2Service {
    @Resource
    private ProductReadService productReadService;
    @Resource
    private MerchantFeign merchantFeign;
    @Resource
    private ProductAccumulatePriceService productAccumulatePriceService;
    @Resource
    private ProductDistributionService productDistributionService;
    @Resource
    private ProductMerchantPriceService productMerchantPriceService;
    @Resource
    private DistributionProductTenantPriceService distributionProductTenantPriceService;
    @Resource
    private ProductPreferentialActivityService productPreferentialActivityService;
    @Resource
    private ApplicationFeign applicationFeign;
    @Resource
    private TenantWalletFeign tenantWalletFeign;
    @Resource
    private ProductInquiryPriceService productInquiryPriceService;
    @Resource
    private MerchantAuthProductParentService merchantAuthProductParentService;

    public List<TenantPurchasePriceRespDTO> currentParentTenantPurchasePrice(Long tenantId, List<Long> parentIds) {
        if (CollUtil.isEmpty(parentIds)) {
            return Collections.emptyList();
        }
        List<Product> variants = this.productReadService.findSupplyChainOnlineVariantByParentIds(parentIds, null, SupplyChainTypeEnum.ONE_PIECE.name());
        Map<Long, List<TenantPurchasePriceRespDTO>> respMap = this.currentTenantPurchasePrice(tenantId, variants).stream()
                .collect(Collectors.groupingBy(TenantPurchasePriceRespDTO::getProductParentId));
        List<TenantPurchasePriceRespDTO> parentRespList = new ArrayList<>();
        respMap.forEach((parentId, variantList) -> {
            TenantPurchasePriceRespDTO parentResp = new TenantPurchasePriceRespDTO();
            parentResp.setProductParentId(parentId);
            List<BigDecimal> onSalePriceList = new ArrayList<>();
            variantList.forEach(variant -> {
                ProductPriceUtil.compareToMinMaxValue(parentResp, variant);
                if (ON_SALE_STATUS_OPEN.equals(variant.getOnSaleStatus())) {
                    parentResp.setOnSaleStatus(ON_SALE_STATUS_OPEN);
                    onSalePriceList.add(variant.getOnSalePrice());
                }
            });
            parentResp.setOnSalePriceStr(ProductPriceUtil.getMinMaxDTO(onSalePriceList).getValueStr());
            parentRespList.add(parentResp);
        });
        return parentRespList;
    }

    public List<TenantPurchasePriceRespDTO> currentVariantTenantPurchasePrice(Long tenantId, List<Long> variantIds) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyList();
        }
        List<Product> variants = this.productReadService.findByIdsUnDelete(variantIds, null);
        return this.currentTenantPurchasePrice(tenantId, variants);
    }

    /**
     * 租户进货价（供应商供货价）
     * !!目前按当次买一件的价格算!!
     */
    private List<TenantPurchasePriceRespDTO> currentTenantPurchasePrice(Long tenantId, List<Product> variants) {
        if (CollUtil.isEmpty(variants)) {
            return Collections.emptyList();
        }
        List<Long> variantIds = new ArrayList<>(), distributionVariantIds = new ArrayList<>();
        variants.forEach(variant -> {
            variantIds.add(variant.getId());
            if (YES.equals(variant.getIsDistribution())) {
                distributionVariantIds.add(variant.getId());
            }
        });
        // 是否享受分销价
        boolean enjoyDistributionPrice = CollUtil.isNotEmpty(distributionVariantIds) &&
                this.applicationFeign.checkApplicationIsOpenByCode(tenantId, ApplicationCodeEnum.DISTRIBUTE_EXTERNAL_PRODUCT.getCode());
        // 分销平台价
        Map<Long, List<LadderPriceDTO>> distPlatformPriceMap = enjoyDistributionPrice ?
                this.productDistributionService.mapDistributionPlatformPrice(distributionVariantIds) : Collections.emptyMap();
        // 分销授权价
        Map<Long, List<LadderPriceDTO>> distAuthPriceMap = enjoyDistributionPrice ?
                this.productDistributionService.mapDistributionAuthPrice(tenantId, distributionVariantIds) : Collections.emptyMap();
        // 活动价格
        Map<Long, ProductPreferentialActivityDTO> activityMap = this.productPreferentialActivityService.getProcessingActivityByProduct(variantIds, null);

        long currentTimeMillis = System.currentTimeMillis();
        List<TenantPurchasePriceRespDTO> priceRespList = new ArrayList<>();
        variants.forEach(variant -> {
            TenantPurchasePriceRespDTO resp = new TenantPurchasePriceRespDTO();
            resp.setProductId(variant.getId()).setProductParentId(variant.getParentId());
            // sds促销价，无论什么情况都会享受
            if (ON_SALE_STATUS_OPEN.equals(variant.getOnSaleStatus()) &&
                    currentTimeMillis >= variant.getOnSaleBeginTime() && currentTimeMillis <= variant.getOnSaleEndTime()) {
                ProductPriceUtil.compareToMinMaxValue(resp, variant.getOnSalePrice());
                resp.setOnSaleStatus(ON_SALE_STATUS_OPEN).setOnSalePrice(variant.getOnSalePrice());
            }
            // 供应商平台价
            List<LadderPriceDTO> platformPrice = ProductPriceUtil.platformPriceStr2List(variant.getPrice());

            if (YES.equals(variant.getIsDistribution()) && enjoyDistributionPrice) {
                // 分销产品 且 开启了[分销应用]
                // 分销价 授权>统一
                List<LadderPriceDTO> disPrice = distAuthPriceMap.get(variant.getId());
                if (CollUtil.isEmpty(disPrice)) {
                    disPrice = distPlatformPriceMap.get(variant.getId());
                }
                if (CollUtil.isNotEmpty(disPrice)) {
                    ProductPriceUtil.compareToMinMaxValue(resp, disPrice.get(0).getPrice());
                }
            } else {
                // 平台价
                ProductPriceUtil.compareToMinMaxValue(resp, platformPrice.get(0).getPrice());
            }
            // 活动价
            if (activityMap.containsKey(variant.getId())) {
                ProductPriceUtil.compareToMinMaxValue(resp, platformPrice.get(0).getPrice().subtract(activityMap.get(variant.getId()).getPrice()));
                // 避免出现负数
                resp.setMin(BigDecimal.ZERO.max(resp.getMin()));
            }
            priceRespList.add(resp);
        });
        return priceRespList;
    }

    private void tenantPurchasePriceHandler(List<ProductPriceResp> priceRespList, ProductCurrentPriceCalBO calBO) {
        List<ProductPriceResp> distributionRespList = new ArrayList<>();
        Set<Long> variantIds = new HashSet<>();
        // 分销变体id
        Set<Long> distributionVariantIds = new HashSet<>();
        for (ProductPriceResp priceResp : priceRespList) {
            if (priceResp.getMerchantTenantId().equals(priceResp.getProductTenantId())) {
                // 没有开启分销，只是单独授权
                priceResp.setTenantCurrentPrice(priceResp.getCurrentPrice());
                continue;
            }
            // 要重新算，不然租户设置更低的价格，会导致sds结算价格也变低
            priceResp.setTenantCurrentPrice(PRODUCT_MAX_PRICE);
            variantIds.add(priceResp.getId());
            if (YES.equals(priceResp.getIsDistribution())) {
                distributionVariantIds.add(priceResp.getId());
            }
            distributionRespList.add(priceResp);
        }
        if (CollUtil.isEmpty(distributionRespList)) {
            return;
        }
        Map<Long, Product> variantMap = calBO.getVariants().stream()
                .filter(v -> variantIds.contains(v.getId())).collect(Collectors.toMap(Product::getId, i -> i));
        Long merchantTenantId = calBO.getMerchantInfo().getTenantId();
        // 分销应用
        if (!calBO.getMerchantInfo().getOpenDistributionApp()) {
            // 关
            distributionRespList.forEach(distributionResp -> {
                Product variant = variantMap.get(distributionResp.getId());
                List<LadderPriceDTO> platformPrice = ProductPriceUtil.platformPriceStr2List(variant.getPrice());
                // sds平台价
                distributionLadderPriceHandle(platformPrice, distributionResp.getNum()
                        , distributionResp, calBO.getPreferentialActivityMap().get(distributionResp.getId()));
                // sds促销价
                distributionOnSalePriceHandle(calBO.getCurrentTimeMillis(), distributionResp, variant.getOnSaleStatus(),
                        variant.getOnSaleBeginTime(), variant.getOnSaleEndTime(), variant.getOnSalePrice());
                if (distributionResp.getCurrentPrice() == null) {
                    // 商户看到的价格=进货价
                    distributionResp.setCurrentPrice(distributionResp.getTenantCurrentPrice());
                    distributionResp.setPlatformPrice(ListUtil.copyProperties(platformPrice, PlatformPriceDto.class));
                }
            });
        } else {
            // 分销平台价 只查询开启分销的变体，因为可能有些产品曾经开启过分销，又关了但价格数据没清
            Map<Long, List<LadderPriceDTO>> platformPriceMap = this.productDistributionService.mapDistributionPlatformPrice(distributionVariantIds);
            // 分销授权价
            Map<Long, List<LadderPriceDTO>> authPriceMap = this.productDistributionService.mapDistributionAuthPrice(merchantTenantId, distributionVariantIds);
            distributionRespList.forEach(distributionResp -> {
                Long variantId = distributionResp.getId();
                Product variant = variantMap.get(variantId);
                // 分销价 分销授权>分销统一
                List<LadderPriceDTO> disPrice = null;
                if (YES.equals(variant.getIsDistribution())) {
                    // 分销授权
                    disPrice = authPriceMap.get(variantId);
                    if (CollUtil.isEmpty(disPrice)) {
                        // 分销统一
                        disPrice = platformPriceMap.get(variantId);
                    }
                }
                // 平台价
                List<LadderPriceDTO> platformPrice = ProductPriceUtil.platformPriceStr2List(variant.getPrice());
                if (CollUtil.isEmpty(disPrice)) {
                    // 没有配置分销价时，取平台价
                    disPrice = platformPrice;
                }
                distributionLadderPriceHandle(disPrice, distributionResp.getNum()
                        , distributionResp, calBO.getPreferentialActivityMap().get(variantId));
                // 活动价，活动只作用于平台价上
                if (calBO.getPreferentialActivityMap().containsKey(variantId)) {
                    distributionLadderPriceHandle(platformPrice, distributionResp.getNum()
                            , distributionResp, calBO.getPreferentialActivityMap().get(variantId));
                }
                // sds促销价
                distributionOnSalePriceHandle(calBO.getCurrentTimeMillis(), distributionResp, variant.getOnSaleStatus(),
                        variant.getOnSaleBeginTime(), variant.getOnSaleEndTime(), variant.getOnSalePrice());

                if (distributionResp.getCurrentPrice() == null) {
                    // 商户看到的价格=进货价
                    distributionResp.setCurrentPrice(distributionResp.getTenantCurrentPrice());
                    distributionResp.setPlatformPrice(ListUtil.copyProperties(disPrice, PlatformPriceDto.class));
                }
            });
        }
    }

    public List<ProductPriceResp> getTenantCurrentParentPriceList(Long merchantTenantId, Long tenantId, Collection<Long> parentIds) {
        if (CollUtil.isEmpty(parentIds)) {
            return Collections.emptyList();
        }
        Assert.validateBool(QueryParamHelper.checkQueryId(merchantTenantId) || QueryParamHelper.checkQueryId(tenantId), "租户信息异常");
        //根据母体id获得变体
        List<Product> variants = this.productReadService.findOnlineVariantByParentIds(parentIds, null);
        if (CollectionUtil.isEmpty(variants)) {
            return Collections.emptyList();
        }
        // 当前用户为非sds的租户时，估算价格时候按1件算
        int num = TenantCommonConstant.isSdsdiy(tenantId) ? PRODUCT_MAX_NUM : 1;
        String supplyChainType = TenantCommonConstant.isSdsdiy(merchantTenantId) ? null : SupplyChainTypeEnum.ONE_PIECE.name();
        //获取当前价
        List<CurrentProductPriceReq> reqList = Lists.newArrayList();
        for (Product variant : variants) {
            if (merchantTenantId != null && !TenantCommonConstant.isSdsdiy(merchantTenantId) && ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name().equals(variant.getOnePieceSupplyChainStatus())) {
                continue;
            }
            CurrentProductPriceReq req = new CurrentProductPriceReq();
            req.setProductId(variant.getId());
            req.setSupplyChainType(supplyChainType);
            req.setNum(num);
            reqList.add(req);
        }
        return this.getCurrentProductPriceList(new CurrentProductPriceCalDTO(0L, tenantId, reqList));
    }

    public List<ProductPriceResp> getCurrentParentPriceList(Long merchantId, Long tenantId, Collection<Long> parentIds) {
        if (CollUtil.isEmpty(parentIds)) {
            return Collections.emptyList();
        }
        Assert.validateBool(QueryParamHelper.checkQueryId(merchantId) || QueryParamHelper.checkQueryId(tenantId), "商户信息异常");
        //根据母体id获得变体
        List<Product> variants = this.productReadService.findOnlineVariantByParentIds(parentIds, null);
        if (CollectionUtil.isEmpty(variants)) {
            return Collections.emptyList();
        }
        // 非sds的商户，估算价格时候按1件算
        int num = TenantCommonConstant.isSdsdiy(tenantId) ? PRODUCT_MAX_NUM : 1;
        String supplyChainType = tenantId == null || TenantCommonConstant.isSdsdiy(tenantId) ? null : SupplyChainTypeEnum.ONE_PIECE.name();
        //获取当前价
        List<CurrentProductPriceReq> reqList = Lists.newArrayList();
        for (Product variant : variants) {
            if (tenantId != null && !TenantCommonConstant.isSdsdiy(tenantId) && ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name().equals(variant.getOnePieceSupplyChainStatus())) {
                continue;
            }
            CurrentProductPriceReq req = new CurrentProductPriceReq();
            req.setProductId(variant.getId());
            req.setSupplyChainType(supplyChainType);
            req.setNum(num);
            reqList.add(req);
        }
        return this.getCurrentProductPriceList(new CurrentProductPriceCalDTO(merchantId, tenantId, reqList));
    }

    /***
     * 按照母体计算数量
     */
    public List<ProductPriceResp> getCurrentOrderProductPriceByParent(OrderProductPriceParam param) {
        Set<Long> variantIds = param.getItems().stream().map(OrderItemProductPriceDto::getProductId).collect(Collectors.toSet());
        List<Product> variants = this.productReadService.findByIds(variantIds);
        Assert.validateTrue(CollectionUtil.isEmpty(variants), "产品信息异常！！");
        Map<Long, Product> productMap = variants.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
        for (OrderItemProductPriceDto dto : param.getItems()) {
            Product product = productMap.get(dto.getProductId());
            Assert.validateNull(product, "产品信息异常！！！");
            dto.setProductParentId(product.getParentId());
        }
        Map<String, Integer> parentIdKeyNumValueMap = param.getItems().stream().collect(Collectors.groupingBy(p -> fetchGroupKey(p.getKeyFlag(), p.getProductParentId()), Collectors.summingInt(OrderItemProductPriceDto::getNum)));
        ArrayList<CurrentProductPriceReq> reqs = Lists.newArrayList();
        for (OrderItemProductPriceDto dto : param.getItems()) {
            Integer num = dto.getNum();
            //产能线为空或者一件的 购买数量按照母体计算
            if (dto.getSupplyChainType() == null || SupplyChainTypeEnum.isOnePiece(dto.getSupplyChainType())) {
                num = parentIdKeyNumValueMap.get(fetchGroupKey(dto.getKeyFlag(), dto.getProductParentId()));
            }
            CurrentProductPriceReq currentProductPriceReq = new CurrentProductPriceReq();
            currentProductPriceReq.setProductId(dto.getProductId());
            currentProductPriceReq.setNum(num);
            currentProductPriceReq.setKeyFlag(dto.getKeyFlag());
            currentProductPriceReq.setItemFlag(dto.getItemFlag());
            currentProductPriceReq.setCartId(dto.getCartId());
            currentProductPriceReq.setIsLimitSpecialPriceMaxNum(false);
            currentProductPriceReq.setSupplyChainType(dto.getSupplyChainType());
            reqs.add(currentProductPriceReq);
        }
        CurrentProductPriceCalDTO calDTO = new CurrentProductPriceCalDTO();
        calDTO.setMerchantId(param.getMerchantId());
        calDTO.setLogisticsId(param.getLogisticsId() == null ? 0L : param.getLogisticsId());
        calDTO.setOrderType(param.getOrderType());
        calDTO.setCalculateTenantPrice(param.getCalculateTenantPrice() == null || param.getCalculateTenantPrice());
        calDTO.setReqList(reqs);
        return this.getCurrentProductPriceList(calDTO);
    }

    public List<ProductPriceResp> getCurrentProductPriceList(CurrentProductPriceCalDTO calDTO) {
        Set<Long> variantIds = ListUtil.toValueSet(CurrentProductPriceReq::getProductId, calDTO.getReqList());
        // 产品
        List<Product> variants = this.productReadService.findByIds(variantIds);
        if (CollectionUtil.isEmpty(variants)) {
            return Lists.newArrayList();
        }
//        Assert.validateTrue(CollectionUtil.isEmpty(variants), "产品信息异常！！");
        return this.getCurrentProductPriceList(calDTO, variants);
    }


    public BigDecimal getCurrentProductTotalPrice(CurrentProductPriceCalDTO calDTO) {
        List<ProductPriceResp> currentProductPriceList = this.getCurrentProductPriceList(calDTO);
        BigDecimal totalPrice = BigDecimal.ZERO;
        for (ProductPriceResp priceResp : currentProductPriceList) {
            int realNum = priceResp.getRealNum() == null ? priceResp.getNum() : priceResp.getRealNum();
            totalPrice = NumberUtil.add(totalPrice, NumberUtil.mul(priceResp.getCurrentPrice(), realNum));
        }
        return totalPrice;
    }

    private static String fetchGroupKey(String keyFlag, Long productParentId) {
        return keyFlag + "##" + productParentId;
    }

    private List<ProductPriceResp> getCurrentProductPriceList(CurrentProductPriceCalDTO calDTO, List<Product> variants) {
        Map<Long, List<CurrentProductPriceReq>> reqMap = calDTO.getReqList().stream().collect(Collectors.groupingBy(CurrentProductPriceReq::getProductId));
        // 商户
        Set<Long> variantIds = reqMap.keySet();
        Long merchantTenantId;
        MemberLevelEnum merchantLevel = null;
        if (QueryParamHelper.checkQueryId(calDTO.getMerchantId())) {
            MerchantRespDto merchant = this.merchantFeign.findDtoById(calDTO.getMerchantId(), "");
            if (QueryParamHelper.checkQueryId(calDTO.getOrderTenantId())) {
                merchantTenantId = calDTO.getOrderTenantId();
            } else {
                merchantTenantId = merchant != null ? merchant.getTenantId() : calDTO.getCurrentTenantId();
            }
            if (merchant != null) {
                merchantLevel = MemberLevelEnum.getMemberLevel(merchant.getMemberLevel());
            }
        } else {
            merchantTenantId = calDTO.getCurrentTenantId();
            calDTO.setMerchantId(0L);
        }
        ProductCurrentPriceCalBO calBO = new ProductCurrentPriceCalBO();
        calBO.setCurrentTenantId(calDTO.getCurrentTenantId());
        //获取小单的指定供应价
        if (QueryParamHelper.checkQueryId(calDTO.getMerchantId())) {
            calBO.setVariantIdKeyLadderNumsMap(this.merchantAuthProductParentService.smallOrderAuthLadderMinNumMap(calDTO.getMerchantId(), variants));
        }
        //其他租户的变体ids
        List<Long> otherTenantVariantIds = variants.stream().filter(variant -> !variant.getTenantId().equals(merchantTenantId))
                .map(Product::getId).collect(Collectors.toList());
        // 租户分销应用开关
        boolean openDistributionApp = CollUtil.isNotEmpty(otherTenantVariantIds)
                && this.applicationFeign.checkApplicationIsOpenByCode(merchantTenantId, ApplicationCodeEnum.DISTRIBUTE_EXTERNAL_PRODUCT.getCode());
        boolean openOnlinePay = CollUtil.isNotEmpty(otherTenantVariantIds) && this.tenantWalletFeign.isOnlineOpen(merchantTenantId);
        if (openOnlinePay) {
            // 租户价格
            Map<Long, DistributionProductTenantPrice> tenantPriceMap = this.distributionProductTenantPriceService.mapTenantPrice(otherTenantVariantIds, merchantTenantId);
            calBO.setDistributionTenantPriceMap(tenantPriceMap);
        }

        // 累计数、一口价
        Map<Long, ProductMerchantPrice> merchantPriceMap = this.productMerchantPriceService.listByMerchantIdAndVariantIds(calDTO.getMerchantId(), variantIds)
                .stream().collect(Collectors.toMap(ProductMerchantPrice::getProductId, merchantPrice -> merchantPrice));
        calBO.setMerchantPriceMap(merchantPriceMap);
        // 累计价
        List<ProductAccumulatePrice> accumulatePriceList = this.productAccumulatePriceService.list(variantIds);
        if (CollUtil.isNotEmpty(accumulatePriceList)) {
            Map<Long, List<ProductAccumulatePrice>> accumulatePriceMap = accumulatePriceList.stream().collect(Collectors.groupingBy(ProductAccumulatePrice::getProductId));
            Set<Long> parentIds = ListUtil.toValueSet(ProductAccumulatePrice::getParentProductId, accumulatePriceList);
            // 母体的总累计数
            Map<Long, Integer> currentOrderAccumulateNum = MemberLevelEnum.isEnjoyAccumulatePrice(merchantLevel == null ? null : merchantLevel.value) ?
                    // v1及以上 包括授权的
                    this.productMerchantPriceService.getCurrentAccumulateNum(calDTO.getMerchantId(), parentIds)
                    // 其他 只计算订单的
                    : this.productMerchantPriceService.getCurrentOrderAccumulateNum(calDTO.getMerchantId(), parentIds);
            calBO.setAccumulatePriceMap(accumulatePriceMap)
                    .setCurrentOrderAccumulateNum(currentOrderAccumulateNum);
        }
        //限时特惠价
        Map<Long, Product> variantIdKeyProductValue = variants.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
        List<ProductInquiryPriceReqDto> inquiryPriceReqList = Lists.newArrayList();
        calDTO.getReqList().forEach(req -> {
            ProductInquiryPriceReqDto productInquiryPriceReqDto = new ProductInquiryPriceReqDto();
            Product product = variantIdKeyProductValue.getOrDefault(req.getProductId(), new Product().setParentId(0L));
            productInquiryPriceReqDto.setParentProductId(product.getParentId());
            productInquiryPriceReqDto.setProductId(req.getProductId());
            productInquiryPriceReqDto.setNum(req.getIsLimitSpecialPriceMaxNum() != null && req.getIsLimitSpecialPriceMaxNum() ? PRODUCT_MAX_NUM : req.getNum());
            inquiryPriceReqList.add(productInquiryPriceReqDto);
        });
        Map<Long, VariantLimitSpecialPricesDTO> variantLimitSpecialPricesMap = this.productInquiryPriceService.getVariantLimitSpecialPricesMap(calDTO.getMerchantId(), inquiryPriceReqList);
        calBO.setLimitSpecialPriceMap(variantLimitSpecialPricesMap);
        // 营销活动价应用
        ProductProcessingActivityReqDTO activityReqDTO = new ProductProcessingActivityReqDTO();
        activityReqDTO.setProductIds(Lists.newArrayList(variantIds));
        if (merchantLevel != null) {
            activityReqDTO.setMemberLevel(merchantLevel.value);
        }
        activityReqDTO.setLogisticsId(calDTO.getLogisticsId());
        activityReqDTO.setOrderType(calDTO.getOrderType());
        calBO.setPreferentialActivityMap(this.productPreferentialActivityService.getProcessingActivityByProductStrict(activityReqDTO));
        // 商户信息
        ProductCurrentPriceCalBO.MerchantInfo merchantInfo = new ProductCurrentPriceCalBO.MerchantInfo();
        merchantInfo.setMerchantId(calDTO.getMerchantId()).setMemberLevel(merchantLevel).setTenantId(merchantTenantId)
                .setOpenDistributionApp(openDistributionApp).setOpenOnlinePay(openOnlinePay);

        calBO.setMerchantInfo(merchantInfo).setReqMap(reqMap).setVariants(variants)
                .setCurrentTimeMillis(System.currentTimeMillis());
        List<ProductPriceResp> productPriceResps = this.calCurrentProductPriceList(calBO);
        if (calDTO.getCalculateTenantPrice() || calBO.getCalculateTenantPrice()) {
            this.tenantPurchasePriceHandler(productPriceResps, calBO);
        }
        return productPriceResps;
    }


    /**
     * 功能描述: 计算当前价格
     */
    private List<ProductPriceResp> calCurrentProductPriceList(ProductCurrentPriceCalBO calBO) {
        List<ProductPriceResp> currentProductPriceList = Lists.newArrayList();
        calBO.getVariants().forEach(variant -> {
            List<CurrentProductPriceReq> reqListValue = calBO.getReqMap().get(variant.getId());
            for (CurrentProductPriceReq req : reqListValue) {
                ProductPriceResp resp = new ProductPriceResp();
                resp.setParentId(variant.getParentId()).setId(variant.getId()).setProductTenantId(variant.getTenantId())
                        .setNum(req.getNum()).setPublicStatus(variant.getPublicStatus()).setRealNum(req.getRealNum())
                        .setMerchantTenantId(calBO.getMerchantInfo().getTenantId())
                        .setPreferentialLabel(PREFERENTIAL_LABEL_DEFAULT)
                        .setOriginalPrice(variant.getMinPrice()).setCurrentPrice(PRODUCT_MAX_PRICE)
                        .setOnSaleStatus(variant.getOnSaleStatus()).setOnSaleValidStatus(CommonStatus.OFFLINE.getStatus())
                        .setIsDistribution(variant.getIsDistribution())
                        .setKeyFlag(req.getKeyFlag())
                        .setItemFlag(req.getItemFlag())
                        .setIsMeetNum(YES)
                        .setCartId(req.getCartId());
                if (calBO.getMerchantInfo().getTenantId() != null && !TenantCommonConstant.isSdsdiy(calBO.getMerchantInfo().getTenantId()) && JSONUtil.isJson(variant.getPrice())) {
                    List<PlatformPriceDto> platformPriceList = JSONObject.parseArray(variant.getPrice(), PlatformPriceDto.class);
                    if (CollectionUtil.isNotEmpty(platformPriceList)) {
                        resp.setOriginalPrice(platformPriceList.get(0).getPrice());
                    }
                }
                if (!variant.getTenantId().equals(calBO.getMerchantInfo().getTenantId())) {
                    // 其他租户产品、分销产品
                    DistributionProductTenantPrice tenantPrice = calBO.getDistributionTenantPriceMap().get(variant.getId());
                    if (calBO.getMerchantInfo().getOpenOnlinePay() && (tenantPrice != null && StrUtil.isNotBlank(tenantPrice.getPlatformPrice()))) {
                        // 开启线上收款 且 设置了租户平台价
                        platformPriceHandle(tenantPrice.getPlatformPrice(), resp, req.getNum(), null);
                        onSalePriceHandle(calBO.getCurrentTimeMillis(), resp, req.getNum(), tenantPrice.getOnSaleStatus(), tenantPrice.getOnSaleBeginTime()
                                , tenantPrice.getOnSaleEndTime(), tenantPrice.getPromotionLadderPrice());
                        // 授权一口价
                        ProductMerchantPrice merchantPrice = calBO.getMerchantPriceMap().get(variant.getId());
                        if (merchantPrice != null) {
                            // 专属授权价
                            authOnePriceHandle(resp, req.getNum(), merchantPrice.getAuthOnePrice()
                                    , merchantPrice.getAuthBatchPrice(), merchantPrice.getAuthBatchMinNum());
                        }
                    } else {
                        // 没设置自己的价格，=供应价
                        resp.setCurrentPrice(null);
                        resp.setPreferentialLabel(PREFERENTIAL_LABEL_PLATFORM);
                        calBO.setCalculateTenantPrice(true);
                    }
                } else {
                    if (StringUtils.isBlank(req.getSupplyChainType()) || SupplyChainTypeEnum.isOnePiece(req.getSupplyChainType())) {
                        this.onePiecePrice(calBO, variant, req, resp);
                    }
                    if ((calBO.getCurrentTenantId() == null || TenantCommonConstant.isSdsdiy(calBO.getMerchantInfo().getTenantId())) && (StringUtils.isBlank(req.getSupplyChainType()) || SupplyChainTypeEnum.isSmallOrder(req.getSupplyChainType()))) {
                        this.smallOrderPlatformPriceHandle(calBO, variant, req, resp);
                    }
                }
                currentProductPriceList.add(resp);
            }
        });
        return currentProductPriceList;
    }

    private void onePiecePrice(ProductCurrentPriceCalBO calBO, Product variant, CurrentProductPriceReq req, ProductPriceResp resp) {
        if (ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name().equals(variant.getOnePieceSupplyChainStatus()) && StringUtils.isBlank(req.getSupplyChainType())) {
            return;
        }
        resp.setTenantCurrentPrice(PRODUCT_MAX_PRICE);
        resp.setSupplyChainType(SupplyChainTypeEnum.ONE_PIECE.name());
        // 自己租户的产品、sds独品
        // 平台价
        platformPriceHandle(variant.getPrice(), resp, req.getNum(),
                calBO.getPreferentialActivityMap().get(variant.getId()));
        // 促销价
        onSalePriceHandle(calBO.getCurrentTimeMillis(), resp, req.getNum(), variant.getOnSaleStatus(), variant.getOnSaleBeginTime()
                , variant.getOnSaleEndTime(), variant.getPromotionLadderPrice());
        ProductMerchantPrice merchantPrice = calBO.getMerchantPriceMap().get(variant.getId());
        if (merchantPrice != null
                && (!TenantCommonConstant.isSdsdiy(variant.getTenantId())
                || MemberLevelEnum.isEnjoyAuthPrice(calBO.getMerchantInfo().getMemberLevel().value))) {
            // 专属授权价 专业版才有资格
            authOnePriceHandle(resp, req.getNum(), merchantPrice.getAuthOnePrice()
                    , merchantPrice.getAuthBatchPrice(), merchantPrice.getAuthBatchMinNum());
        }
        // 专属授权后、无视累计价
        if (resp.getExclusiveAuthorizationPrice() == null) {
            accumulatePriceHandle(calBO.getAccumulatePriceMap().get(variant.getId()),
                    calBO.getCurrentOrderAccumulateNum().get(variant.getParentId()),
                    resp, calBO.getMerchantInfo().getMemberLevel());
        }
        //限时特惠价
        this.limitSpecialPricesHandle(calBO, variant, resp);
        resp.setOnePieceCurrentPrice(resp.getCurrentPrice());
    }

    private void smallOrderPlatformPriceHandle(ProductCurrentPriceCalBO calBO, Product variant, CurrentProductPriceReq req, ProductPriceResp resp) {
        if (!JSONUtil.isJson(variant.getSmallOrderPrice()) || (StringUtils.isBlank(req.getSupplyChainType()) && ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name().equals(variant.getSmallOrderSupplyChainStatus()))) {
            return;
        }
        List<SmallOrderPriceDto> smallOrderPlatformPrices = JSONObject.parseArray(variant.getSmallOrderPrice(), SmallOrderPriceDto.class);
        if (CollectionUtil.isEmpty(smallOrderPlatformPrices)) {
            return;
        }
        Map<Long, Integer> variantIdKeyLadderNumsMap = calBO.getVariantIdKeyLadderNumsMap();
        List<SmallOrderPriceDto> newSmallOrderPriceDtos = this.merchantAuthProductParentService.getNewSmallOrderPriceDtos(variant.getId(), smallOrderPlatformPrices, variantIdKeyLadderNumsMap);
        SmallOrderPriceDto firstOrderPrice = newSmallOrderPriceDtos.get(0);
        resp.setSupplyChainType(SupplyChainTypeEnum.SMALL_ORDER.name());
        resp.setIsMeetNum(NO);
        resp.setSmallOrderPrices(newSmallOrderPriceDtos);
        for (int i = newSmallOrderPriceDtos.size() - 1; i >= 0; i--) {
            SmallOrderPriceDto currentLevel = newSmallOrderPriceDtos.get(i);
            currentLevel.setIsCurrentLevel(NO);
            if (req.getNum() >= currentLevel.getNum()) {
                currentLevel.setIsCurrentLevel(YES);
                resp.setIsMeetNum(YES);
                if (resp.getCurrentPrice() == null || resp.getCurrentPrice().compareTo(currentLevel.getPrice()) > 0) {
                    resp.setCurrentPrice(currentLevel.getPrice());
                    resp.setOriginalPrice(resp.getPreferentialLabel() == null || PREFERENTIAL_LABEL_DEFAULT == resp.getPreferentialLabel() ? firstOrderPrice.getPrice() : resp.getOriginalPrice());
                    resp.setPreferentialLabel(PREFERENTIAL_LABEL_SMALL_ORDER_PLATFORM);
                }
                break;
            }
        }
        if (NO.equals(resp.getIsMeetNum()) && (resp.getCurrentPrice().compareTo(PRODUCT_MAX_PRICE) == 0 || BigDecimal.ZERO.compareTo(resp.getCurrentPrice()) >= 0)) {
            resp.setCurrentPrice(firstOrderPrice.getPrice());
            resp.setOriginalPrice(resp.getPreferentialLabel() == null || PREFERENTIAL_LABEL_DEFAULT == resp.getPreferentialLabel() ? firstOrderPrice.getPrice() : resp.getOriginalPrice());
            resp.setPreferentialLabel(PREFERENTIAL_LABEL_SMALL_ORDER_PLATFORM);
        }
    }

    /***
     * 特惠价
     */
    private void limitSpecialPricesHandle(ProductCurrentPriceCalBO calBO, Product variant, ProductPriceResp resp) {
        VariantLimitSpecialPricesDTO variantLimitSpecialPricesDTO = calBO.getLimitSpecialPriceMap().get(variant.getId());
        if (variantLimitSpecialPricesDTO != null) {
            resp.setLimitSpecialPurchaseNum(variantLimitSpecialPricesDTO.getPurchaseNum());
            resp.setLimitSpecialPrice(variantLimitSpecialPricesDTO.getPrice());
            resp.setLimitSpecialEndTime(new Date(variantLimitSpecialPricesDTO.getEndTime()));
            if (resp.getCurrentPrice().compareTo(resp.getLimitSpecialPrice()) >= 0) {
                resp.setCurrentPrice(resp.getLimitSpecialPrice());
                resp.setPreferentialLabel(PREFERENTIAL_LABEL_LIMIT_TIME_SPECIAL);
                resp.setProductInquiryItemId(variantLimitSpecialPricesDTO.getProductInquiryItemId());
            }
        }
    }

    /**
     * 计算平台价
     */
    private static void platformPriceHandle(String platformPriceStr, ProductPriceResp resp, Integer purchaseNum
            , ProductPreferentialActivityDTO activityDTO) {
        if (!JSONUtil.isJsonArray(platformPriceStr)) {
            return;
        }
        List<PlatformPriceDto> platformPriceList = JSON.parseArray(platformPriceStr, PlatformPriceDto.class);
        resp.setPlatformPrice(platformPriceList);
        if (CollectionUtil.isEmpty(platformPriceList)) {
            return;
        }
        PlatformPriceDto oneLevel = platformPriceList.get(0);
        PlatformPriceDto currentLevel = platformPriceList.stream()
                .sorted(Comparator.comparing(PlatformPriceDto::getNum).reversed())
                .filter(platformPrice -> purchaseNum >= platformPrice.getNum())
                .findFirst().orElse(oneLevel);
        resp.setCurrentPrice(currentLevel.getPrice());
        resp.setPreferentialLabel(PREFERENTIAL_LABEL_PLATFORM);
        resp.setOriginalPrice(oneLevel.getPrice());
        resp.setSupplyChainType(SupplyChainTypeEnum.ONE_PIECE.name());
        resp.setIsMeetNum(YES);
        if (activityDTO != null) {
            // 一档价格-活动优惠
            BigDecimal price = oneLevel.getPrice().subtract(activityDTO.getPrice());
            if (price.compareTo(resp.getCurrentPrice()) <= 0) {
                // 避免出现负数
                price = BigDecimal.ZERO.max(price);
                resp.setCurrentPrice(price);
                resp.setPreferentialLabel(PREFERENTIAL_LABEL_ACTIVITY);
                resp.setActivity(activityDTO);
            }
        }
    }

    private static void onSalePriceHandle(long currentTimeMillis, ProductPriceResp resp, Integer purchaseNum,
                                          Integer onSaleStatus, Long onSaleBegin, Long onSaleEnd, String promotionLadderPriceStr) {
        if (!OPEN.equals(onSaleStatus)) {
            return;
        }
        if (!JSONUtil.isJson(promotionLadderPriceStr)) {
            return;
        }
        List<PromotionLadderPriceDto> promotionLadderPrices = JSONObject.parseArray(promotionLadderPriceStr, PromotionLadderPriceDto.class);
        resp.setPromotionLadderPrices(promotionLadderPrices);
        if (CollectionUtil.isEmpty(promotionLadderPrices)) {
            return;
        }
        PromotionLadderPriceDto oneLevel = promotionLadderPrices.get(0);
        PromotionLadderPriceDto currentLevel = promotionLadderPrices.stream()
                .sorted(Comparator.comparing(PromotionLadderPriceDto::getNum).reversed())
                .filter(platformPrice -> purchaseNum >= platformPrice.getNum())
                .findFirst().orElse(oneLevel);
        resp.setOnSaleBegin(new Date(onSaleBegin));
        resp.setOnSaleEnd(new Date(onSaleEnd));
        if (currentTimeMillis >= onSaleBegin && currentTimeMillis <= onSaleEnd) {
            resp.setOnSaleValidStatus(OPEN);
            if (resp.getCurrentPrice().compareTo(currentLevel.getPrice()) >= 0) {
                resp.setCurrentPrice(currentLevel.getPrice());
                resp.setPreferentialLabel(PREFERENTIAL_LABEL_ON_SALE);
            }
        }
    }

    private static void authOnePriceHandle(ProductPriceResp resp, Integer num
            , BigDecimal onePrice, BigDecimal batchPrice, Integer batchMinNum) {
        if (BigDecimal.ZERO.compareTo(onePrice) < 0 || BigDecimal.ZERO.compareTo(batchPrice) < 0) {
            ProductPriceResp.ExclusiveAuthorizationPriceDto authorizationPrice = new ProductPriceResp.ExclusiveAuthorizationPriceDto();
            if (BigDecimal.ZERO.compareTo(onePrice) < 0) {
                authorizationPrice.setOnePrice(onePrice);
                if (resp.getCurrentPrice().compareTo(authorizationPrice.getOnePrice()) >= 0) {
                    resp.setPreferentialLabel(PREFERENTIAL_LABEL_AUTH);
                    resp.setCurrentPrice(authorizationPrice.getOnePrice());
                }
            }
            if (BigDecimal.ZERO.compareTo(batchPrice) < 0) {
                authorizationPrice.setBatchMinNum(batchMinNum);
                authorizationPrice.setBatchPrice(batchPrice);
                if (resp.getCurrentPrice().compareTo(authorizationPrice.getBatchPrice()) >= 0
                        && num >= authorizationPrice.getBatchMinNum()) {
                    resp.setPreferentialLabel(PREFERENTIAL_LABEL_AUTH);
                    resp.setCurrentPrice(authorizationPrice.getBatchPrice());
                }
            }
            resp.setExclusiveAuthorizationPrice(authorizationPrice);
        }
    }

    /**
     * 计算累计价
     *
     * @param accumulatePriceList 累计价
     * @param accumulateNum       总累计数（订单+授权）
     * @param merchantLevel       会员等级
     */
    private static void accumulatePriceHandle(List<ProductAccumulatePrice> accumulatePriceList,
                                              Integer accumulateNum,
                                              ProductPriceResp resp, MemberLevelEnum merchantLevel) {
        if (accumulatePriceList == null) {
            return;
        }
        if (accumulateNum == null) {
            accumulateNum = 0;
        }
        List<LadderPriceDTO> ladderPriceList = ProductPriceUtil.accumulatePrice2LadderPriceDTO(accumulatePriceList);
        int size = ladderPriceList.size();
        // 累计档位，没买过按0算
        Integer accumulateLevel = ProductPriceUtil.calculateCurrentAccumulatorLevel(ladderPriceList, accumulateNum);
        // 与等级授权档位比，取较高的
        accumulateLevel = Math.max(accumulateLevel, merchantLevel.authAccumulatePriceLevel);
        // 与产品累计价档位比较，取较低的，避免越界
        accumulateLevel = Math.min(accumulateLevel, size);
        ProductPriceResp.AccumulatePriceInfoDto accumulatePriceInfo = new ProductPriceResp.AccumulatePriceInfoDto();
        for (int i = 0; i < size; i++) {
            ProductAccumulatePrice accumulate = accumulatePriceList.get(i);
            if (i == 0 && accumulateLevel == 0) {
                int differenceNum = accumulate.getBeginNum() - accumulateNum;
                differenceNum = Math.max(differenceNum, 0);
                accumulatePriceInfo.setNextDifferenceNum(differenceNum);
                accumulatePriceInfo.setNextLevel(accumulate.getLevel());
                accumulatePriceInfo.setNextPrice(accumulate.getPrice());
            }
            if (i == size - 1 && accumulateLevel.equals(accumulate.getLevel())) {
                accumulatePriceInfo.setCurrentLevel(accumulate.getLevel());
                accumulatePriceInfo.setCurrentPrice(accumulate.getPrice());
                accumulatePriceInfo.setNextDifferenceNum(0);
                accumulatePriceInfo.setNextLevel(accumulate.getLevel());
                break;
            }
            //当前累计数在范围之内
            if (accumulateLevel.equals(accumulate.getLevel())) {
                accumulatePriceInfo.setCurrentLevel(accumulate.getLevel());
                accumulatePriceInfo.setCurrentPrice(accumulate.getPrice());
                ProductAccumulatePrice nextAccumulate = accumulatePriceList.get(i + 1);
                int differenceNum = nextAccumulate.getBeginNum() - accumulateNum;
                differenceNum = Math.max(differenceNum, 0);
                accumulatePriceInfo.setNextDifferenceNum(differenceNum);
                accumulatePriceInfo.setNextLevel(nextAccumulate.getLevel());
                accumulatePriceInfo.setNextPrice(nextAccumulate.getPrice());
                break;
            }
        }
        if (accumulatePriceInfo.getCurrentPrice() != null
                // V1及以上才享受累计价
                && MemberLevelEnum.isEnjoyAccumulatePrice(merchantLevel.value)
                && resp.getCurrentPrice().compareTo(accumulatePriceInfo.getCurrentPrice()) >= 0) {
            resp.setPreferentialLabel(PREFERENTIAL_LABEL_ACCUMULATE);
            resp.setCurrentPrice(accumulatePriceInfo.getCurrentPrice());
        }
        if (MemberLevelEnum.V0 == merchantLevel) {
            // 会员授权档位与实际档位对比，取较高的。但又不能超过价格最高档避免数组越界
            accumulatePriceInfo.setV1Price(accumulatePriceList.get(Math.min(size, Math.max(accumulateLevel, MemberLevelEnum.V1.authAccumulatePriceLevel)) - 1).getPrice());
            accumulatePriceInfo.setV2Price(accumulatePriceList.get(Math.min(size, Math.max(accumulateLevel, MemberLevelEnum.V2.authAccumulatePriceLevel)) - 1).getPrice());
        }
        resp.setAccumulatePriceInfo(accumulatePriceInfo);
    }

    /**
     * 分销阶梯价
     */
    private static void distributionLadderPriceHandle(List<LadderPriceDTO> ladderPriceList,
                                                      Integer num,
                                                      ProductPriceResp distributionResp,
                                                      ProductPreferentialActivityDTO activityDTO) {
        if (CollUtil.isEmpty(ladderPriceList)) {
            return;
        }
        LadderPriceDTO oneLevel = ladderPriceList.get(0);
        Integer finalNum = num == null ? 0 : num;
        LadderPriceDTO accumulatePrice = ladderPriceList.stream()
                .sorted(Comparator.comparing(LadderPriceDTO::getNum).reversed())
                .filter(p -> finalNum.compareTo(p.getNum()) >= 0)
                .findFirst().orElse(oneLevel);
        if (distributionResp.getTenantCurrentPrice().compareTo(accumulatePrice.getPrice()) >= 0) {
            distributionResp.setTenantCurrentPrice(accumulatePrice.getPrice());
        }
        if (activityDTO != null) {
            BigDecimal price = oneLevel.getPrice().subtract(activityDTO.getPrice());
            if (0 <= distributionResp.getTenantCurrentPrice().compareTo(price)) {
                // 避免出现负数
                price = BigDecimal.ZERO.max(price);
                distributionResp.setTenantCurrentPrice(price);
            }
        }
    }

    /**
     * 分销促销价
     */
    private static void distributionOnSalePriceHandle(long currentTimeMillis, ProductPriceResp distributionResp,
                                                      Integer onSaleStatus, Long onSaleBegin, Long onSaleEnd, BigDecimal onSalePrice) {
        if (!OPEN.equals(onSaleStatus)) {
            return;
        }
        if (currentTimeMillis >= onSaleBegin && currentTimeMillis <= onSaleEnd) {
            if (distributionResp.getTenantCurrentPrice().compareTo(onSalePrice) >= 0) {
                distributionResp.setTenantCurrentPrice(onSalePrice);
            }
        }
    }

    private static void distributionAuthOnePriceHandle(ProductPriceResp distributionResp, Integer num
            , BigDecimal onePrice, BigDecimal batchPrice, Integer batchMinNum) {
        if (BigDecimal.ZERO.compareTo(onePrice) < 0 || BigDecimal.ZERO.compareTo(batchPrice) < 0) {
            if (BigDecimal.ZERO.compareTo(onePrice) < 0) {
                if (distributionResp.getTenantCurrentPrice().compareTo(onePrice) >= 0) {
                    distributionResp.setTenantCurrentPrice(onePrice);
                }
            }
            if (BigDecimal.ZERO.compareTo(batchPrice) < 0) {
                if (num >= batchMinNum
                        && distributionResp.getTenantCurrentPrice().compareTo(batchPrice) >= 0) {
                    distributionResp.setTenantCurrentPrice(batchPrice);
                }
            }
        }
    }

    public void formatMerchantUpMemberUnitPrice(MerchantUpMemberProductPriceReqDTO reqDTO, MerchantUpMemberProductPriceRespDTO respDTO) {
        Set<Long> variantIds = ListUtil.toValueSet(MerchantUpMemberProductPriceReqDTO.VariantItemDTO::getVariantId, reqDTO.getItemList());
        // 累计价
        List<ProductAccumulatePrice> accumulatePriceList = this.productAccumulatePriceService.list(variantIds);
        if (CollUtil.isEmpty(accumulatePriceList)) {
            // 没有累计价
            return;
        }
        Map<Long, List<ProductAccumulatePrice>> accumulatePriceMap = ListUtil.toMapValueList(ProductAccumulatePrice::getProductId, accumulatePriceList);
        Set<Long> pIds = ListUtil.toValueSet(ProductAccumulatePrice::getParentProductId, accumulatePriceList);
        // 母体的订单累计数（不包括授权的）
        Map<Long, Integer> currentOrderAccumulateNum = this.productMerchantPriceService.getCurrentOrderAccumulateNum(reqDTO.getMerchantId(), pIds);
        // 结果子项
        List<MerchantUpMemberProductPriceRespDTO.VariantItemDTO> v1List = new ArrayList<>();
        List<MerchantUpMemberProductPriceRespDTO.VariantItemDTO> v2List = new ArrayList<>();
        for (MerchantUpMemberProductPriceReqDTO.VariantItemDTO i : reqDTO.getItemList()) {
            List<ProductAccumulatePrice> priceList = accumulatePriceMap.get(i.getVariantId());
            if (CollUtil.isEmpty(priceList)) {
                continue;
            }
            List<LadderPriceDTO> ladderPriceList = ProductPriceUtil.accumulatePrice2LadderPriceDTO(priceList);
            // 累计数
            Integer accumulateNum = currentOrderAccumulateNum.getOrDefault(priceList.get(0).getParentProductId(), 0);
            // 档位
            Integer accumulateLevel = ProductPriceUtil.calculateCurrentAccumulatorLevel(ladderPriceList, accumulateNum);
            // 会员授权档位与实际档位对比，取较高的
            BigDecimal v1UnitPrice = ProductPriceUtil.leLevelLadderPrice(ladderPriceList, Math.max(accumulateLevel, MemberLevelEnum.V1.authAccumulatePriceLevel));
            BigDecimal v2UnitPrice = ProductPriceUtil.leLevelLadderPrice(ladderPriceList, Math.max(accumulateLevel, MemberLevelEnum.V2.authAccumulatePriceLevel));
            MerchantUpMemberProductPriceRespDTO.VariantItemDTO v1 = new MerchantUpMemberProductPriceRespDTO.VariantItemDTO()
                    .setVariantId(i.getVariantId()).setProductPrice(v1UnitPrice);
            MerchantUpMemberProductPriceRespDTO.VariantItemDTO v2 = new MerchantUpMemberProductPriceRespDTO.VariantItemDTO()
                    .setVariantId(i.getVariantId()).setProductPrice(v2UnitPrice);
            v1List.add(v1);
            v2List.add(v2);
        }
        if (v1List.size() == 0 && v2List.size() == 0) {
            return;
        }
        // 封装结果
        respDTO.setIsDiscount(BasePoConstant.YES)
                .setV1(new MerchantUpMemberProductPriceRespDTO.LevelDTO().setItemList(v1List))
                .setV2(new MerchantUpMemberProductPriceRespDTO.LevelDTO().setItemList(v2List));
    }

    public void formatMerchantUpMemberTotalPrice(MerchantUpMemberProductPriceReqDTO reqDTO, MerchantUpMemberProductPriceRespDTO respDTO) {
        // 按变体汇总数量
        List<CurrentProductPriceReq> reqList = new ArrayList<>();
        Map<Long, Integer> variantNumMap = reqDTO.getItemList().stream().collect(Collectors.groupingBy(MerchantUpMemberProductPriceReqDTO.VariantItemDTO::getVariantId
                , Collectors.summingInt(MerchantUpMemberProductPriceReqDTO.VariantItemDTO::getNum)));
        variantNumMap.forEach((k, v) -> reqList.add(new CurrentProductPriceReq(k, v)));
        // 计算价格
        CurrentProductPriceCalDTO calDTO = new CurrentProductPriceCalDTO();
        calDTO.setMerchantId(reqDTO.getMerchantId()).setCurrentTenantId(reqDTO.getTenantId())
                .setReqList(reqList);
        List<ProductPriceResp> respList = this.getCurrentProductPriceList(calDTO);
        // 结果总价
        BigDecimal v1TotalAmount = BigDecimal.ZERO;
        BigDecimal v2TotalAmount = BigDecimal.ZERO;
        for (ProductPriceResp i : respList) {
            ProductPriceResp.AccumulatePriceInfoDto accumulatePriceInfo = i.getAccumulatePriceInfo();
            if (accumulatePriceInfo == null) {
                // 同订单中有可能部分产品没有累计价，则按实际价格计算
                BigDecimal addAmount = i.getCurrentPrice().multiply(BigDecimal.valueOf(i.getNum()));
                v1TotalAmount = v1TotalAmount.add(addAmount);
                v2TotalAmount = v2TotalAmount.add(addAmount);
            } else {
                if (accumulatePriceInfo.getV1Price() != null) {
                    v1TotalAmount = v1TotalAmount.add(accumulatePriceInfo.getV1Price().multiply(BigDecimal.valueOf(i.getNum())));
                }
                if (accumulatePriceInfo.getV2Price() != null) {
                    v2TotalAmount = v2TotalAmount.add(accumulatePriceInfo.getV2Price().multiply(BigDecimal.valueOf(i.getNum())));
                }
            }
        }
        if (CompareUtils.eqZero(v1TotalAmount) && CompareUtils.eqZero(v2TotalAmount)) {
            // 没有累计价
            return;
        }
        // 封装结果
        respDTO.setIsDiscount(BasePoConstant.YES)
                .setV1(new MerchantUpMemberProductPriceRespDTO.LevelDTO().setOrderDiscountAmount(v1TotalAmount))
                .setV2(new MerchantUpMemberProductPriceRespDTO.LevelDTO().setOrderDiscountAmount(v2TotalAmount));
    }
}
