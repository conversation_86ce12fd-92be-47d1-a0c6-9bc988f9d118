package com.sdsdiy.productimpl.manager.auth;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.McContentHelper;
import com.sdsdiy.core.redis.lock.LockUtil;
import com.sdsdiy.productdata.constants.ProductConstant;
import com.sdsdiy.productdata.dto.MinMaxValueDTO;
import com.sdsdiy.productdata.dto.auth.AuthOnePriceParam;
import com.sdsdiy.productdata.dto.auth.ProductAuthPageResultDTO;
import com.sdsdiy.productdata.dto.auth.distribution.*;
import com.sdsdiy.productdata.dto.distribution.DistributionProductAuthPageReqDTO;
import com.sdsdiy.productdata.dto.price.TenantPurchasePriceRespDTO;
import com.sdsdiy.productimpl.entity.po.AuthOnePrice;
import com.sdsdiy.productimpl.entity.po.Product;
import com.sdsdiy.productimpl.entity.po.auth.MerchantAuthProductParent;
import com.sdsdiy.productimpl.entity.po.auth.TenantAuthProductParent;
import com.sdsdiy.productimpl.feign.MerchantFeign;
import com.sdsdiy.productimpl.manager.mapper.AuthOnePriceMapperManager;
import com.sdsdiy.productimpl.service.auth.MerchantAuthProductParentService;
import com.sdsdiy.productimpl.service.auth.TenantAuthProductParentService;
import com.sdsdiy.productimpl.service.authPrice.AuthOnePriceService;
import com.sdsdiy.productimpl.service.product.ProductPriceV2Service;
import com.sdsdiy.productimpl.service.product.ProductReadService;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userdata.dto.merchant.MerchantPageReqDTO;
import com.sdsdiy.userdata.dto.merchant.MerchantPageRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/8
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributionAuthMerchantManager {
    @Autowired
    private DistributionAuthMerchantManager selfClass;
    private final ProductReadService productReadService;
    private final MerchantAuthProductParentService merchantAuthProductParentService;
    private final MerchantFeign merchantFeign;
    private final TenantAuthProductParentService tenantAuthProductParentService;
    private final LockUtil lockUtil;
    private final AuthOnePriceMapperManager authOnePriceMapperManager;
    private final ProductPriceV2Service productPriceV2Service;
    private final AuthOnePriceService authOnePriceService;

    /**
     * 商户注册，自动授权分销产品
     */
    public void authMerchantByRegister(Long merchantId) {
        MerchantRespDto merchant = this.merchantFeign.findDtoById(merchantId, "");
        if (merchant == null) {
            return;
        }
        Long merchantTenantId = merchant.getTenantId();
        // 其他租户产品开启全部商户授权的产品ids
        List<Long> parentIds = this.tenantAuthProductParentService
                .findProductIdsByTargetTenantAllMerchant(merchantTenantId);

        DistributionProductAuthPageReqDTO distributionReqDTO = new DistributionProductAuthPageReqDTO();
        distributionReqDTO.setMerchantTenantId(merchantTenantId)
                .setCrossTenant(true).setInParentIds(parentIds)
                .setTenantId(merchantTenantId);
        List<Product> parentList = this.productReadService.listDistributionProduct(distributionReqDTO);
        if (CollUtil.isEmpty(parentList)) {
            return;
        }
        this.selfClass.merchantAuthProductParentService.authCrossTenantProduct(merchantTenantId
                , Collections.singletonList(merchantId), parentList);
    }

    public void authMerchantBatch(DistributionProductAuthMerchantBatchDTO reqDTO) {
        Long currentTenantId = McContentHelper.getCurrentTenantId();
        lockUtil.tryLockWithUnlock(ProductConstant.DISTRIBUTION_PRODUCT_AUTH_MERCHANT_LOCK + currentTenantId
                , TimeUnit.SECONDS.toMillis(3), TimeUnit.MINUTES.toMillis(5), "分销产品授权商户处理中，请稍后"
                , () -> {
                    DistributionProductAuthPageReqDTO distributionReqDTO = new DistributionProductAuthPageReqDTO();
                    distributionReqDTO.setMerchantTenantId(currentTenantId)
                            .setCrossTenant(true)
                            .setInParentIds(BasePoConstant.yes(reqDTO.getAllProduct()) ? null : reqDTO.getParentIds())
                            .setTenantId(currentTenantId);
                    List<Product> parentList = this.productReadService.listDistributionProduct(distributionReqDTO);
                    Assert.validateEmpty(parentList, "没有可授权的产品");
                    boolean allMerchant = BasePoConstant.yes(reqDTO.getAllMerchant());
                    List<Long> merchantIds = allMerchant ? this.merchantFeign.allMerchantIdsByTenantId(currentTenantId)
                            : reqDTO.getMerchantIds();
                    Assert.validateEmpty(merchantIds, "没有可授权的商户");
                    if (allMerchant) {
                        // 标记全部授权
                        List<Long> parentIds = ListUtil.toValueList(parentList, Product::getId);
                        this.tenantAuthProductParentService.updateAllMerchant(currentTenantId, parentIds, true);
                    }
                    ListUtil.splitDo(merchantIds, 800, mIds -> {
                        ListUtil.splitDo(parentList, 500, pList -> {
                            this.merchantAuthProductParentService.authCrossTenantProduct(currentTenantId, mIds, pList);
                        });
                    });
                });
    }

    public void authMerchant(DistributionProductAuthMerchantDTO dto) {
        Assert.validateEmpty(dto.getMerchantIds(), "没有勾选商户");
        Long currentTenantId = McContentHelper.getCurrentTenantId();
        Product parent = this.productReadService.findById(dto.getParentId());
        Assert.validateNull(parent, "产品已删除");
        Assert.validateFalse(BasePoConstant.ONLINE.equals(parent.getStatus())
                        && BasePoConstant.yes(parent.getIsDistribution())
                        && !parent.getTenantId().equals(currentTenantId)
                , "产品已下架");

        this.merchantAuthProductParentService.authCrossTenantProduct(currentTenantId
                , dto.getMerchantIds(), Collections.singletonList(parent));
    }

    public void allAuthMerchant(DistributionProductAuthMerchantTypeDTO dto) {
        boolean allAuth = BasePoConstant.yes(dto.getAllAuth());
        List<Long> parentIds = Collections.singletonList(dto.getParentId());
        if (allAuth) {
            DistributionProductAuthMerchantBatchDTO batchDTO = new DistributionProductAuthMerchantBatchDTO();
            batchDTO.setParentIds(parentIds)
                    .setAllMerchant(BasePoConstant.YES);
            this.authMerchantBatch(batchDTO);
        } else {
            this.tenantAuthProductParentService.updateAllMerchant(McContentHelper.getCurrentTenantId()
                    , parentIds, false);
        }
    }

    public void removeAuthedMerchant(DistributionProductAuthMerchantDelDTO delDTO) {
        Long currentTenantId = McContentHelper.getCurrentTenantId();
        lockUtil.tryLockWithUnlock(ProductConstant.DISTRIBUTION_PRODUCT_AUTH_MERCHANT_LOCK + currentTenantId
                , TimeUnit.SECONDS.toMillis(1), TimeUnit.MINUTES.toMillis(5), "分销产品授权商户处理中，请稍后"
                , () -> {
                    boolean allDel = BasePoConstant.yes(delDTO.getAllDel());
                    List<MerchantAuthProductParent> authList = this.merchantAuthProductParentService
                            .findCrossTenantProductAuth(delDTO.getParentId(), currentTenantId
                                    , allDel ? null : delDTO.getMerchantIds());
                    ListUtil.splitDo(authList, 900, split -> {
                        this.merchantAuthProductParentService.realDel(split, currentTenantId);
                    });
                });
    }

    public ProductAuthPageResultDTO<DistributionProductUnAuthMerchantRespDTO> pageUnAuthMerchant(DistributionProductAuthMerchantPageReqDTO pageDTO) {
        Long currentTenantId = McContentHelper.getCurrentTenantId();
        Long parentId = pageDTO.getParentId();
        TenantAuthProductParent auth = this.tenantAuthProductParentService.findByTargetTenantAndParentId(currentTenantId, parentId);
        Assert.validateNull(auth, "供应商已取消产品授权");
        ProductAuthPageResultDTO<DistributionProductUnAuthMerchantRespDTO> resultDTO = new ProductAuthPageResultDTO<>();
        resultDTO.setAllAuth(auth.getAllMerchant());

        List<Long> authMerchantIds = this.merchantAuthProductParentService.findMerchantIdByProductAndMerchantTenant(
                Collections.singleton(parentId), currentTenantId);
//        //有关联业务员，搜索有权限的商户id，没有关联业务员，搜索全部
//        UserFilterMerchantDto userFilterMerchantDto = new UserFilterMerchantDto();
//        userFilterMerchantDto.setUserId(McContentHelper.getCurrentUserId());
//        List<Long> finalFilterMerchantIds = this.tenantSysUserFeign.getFinalFilterMerchantIds(userFilterMerchantDto);

        MerchantPageReqDTO merchantReqDTO = BeanUtil.copyProperties(pageDTO, MerchantPageReqDTO.class);
        merchantReqDTO.setNotInIds(authMerchantIds).setWithSetMeal(false)
//                .setInIds(finalFilterMerchantIds)
                .setTenantId(currentTenantId);
        PageResultDto<MerchantPageRespDTO> merchantPage = this.merchantFeign.pageMerchantWithSetMeal(merchantReqDTO);
        resultDTO.setTotalCount(merchantPage.getTotalCount());
        List<DistributionProductUnAuthMerchantRespDTO> respList = ListUtil.copyProperties(merchantPage.getList()
                , DistributionProductUnAuthMerchantRespDTO.class, (s, t) -> {
                    t.setContactTel(StrUtil.desensitized(s.getContactTel(), DesensitizedUtil.DesensitizedType.MOBILE_PHONE));
                });
        resultDTO.setList(respList);
        return resultDTO;
    }

    public PageResultDto<DistributionProductAuthedMerchantRespDTO> pageAuthedMerchant(DistributionProductAuthMerchantPageReqDTO pageDTO) {
        Long currentTenantId = McContentHelper.getCurrentTenantId();
        List<Long> authMerchantIds = this.merchantAuthProductParentService.findMerchantIdByProductAndMerchantTenant(
                Collections.singleton(pageDTO.getParentId()), currentTenantId);
        if (CollUtil.isEmpty(authMerchantIds)) {
            return PageResultDto.empty();
        }
        //有关联业务员，搜索有权限的商户id，没有关联业务员，搜索全部
//        UserFilterMerchantDto userFilterMerchantDto = new UserFilterMerchantDto();
//        userFilterMerchantDto.setUserId(McContentHelper.getCurrentUserId());
//        userFilterMerchantDto.setMerchantIds(authMerchantIds);
//        List<Long> finalFilterMerchantIds = this.tenantSysUserFeign.getFinalFilterMerchantIds(userFilterMerchantDto);

        MerchantPageReqDTO merchantReqDTO = BeanUtil.copyProperties(pageDTO, MerchantPageReqDTO.class);
        merchantReqDTO.setInIds(authMerchantIds).setWithSetMeal(false)
                .setTenantId(currentTenantId);
        PageResultDto<MerchantPageRespDTO> merchantPage = this.merchantFeign.pageMerchantWithSetMeal(merchantReqDTO);
        if (CollUtil.isEmpty(merchantPage.getList())) {
            return PageResultDto.empty();
        }
        List<Long> merchantIds = ListUtil.toValueList(merchantPage.getList(), MerchantPageRespDTO::getId);
        Map<Long, MinMaxValueDTO<BigDecimal>> onePriceMap = this.authOnePriceMapperManager.mapOnePriceByMerchantIds(pageDTO.getParentId(), merchantIds);

        List<DistributionProductAuthedMerchantRespDTO> respList = ListUtil.copyProperties(merchantPage.getList()
                , DistributionProductAuthedMerchantRespDTO.class, (s, t) -> {
                    t.setContactTel(StrUtil.desensitized(s.getContactTel(), DesensitizedUtil.DesensitizedType.MOBILE_PHONE));
                    MinMaxValueDTO<BigDecimal> onePrice = onePriceMap.get(s.getId());
                    t.setSalePrice(onePrice != null ? onePrice.getValueStr() : ProductConstant.EQUAL_PLATFORM_PRICE);
                });
        return PageResultDto.success(merchantPage.getTotalCount(), respList);
    }


    public DistributionProductAuthMerchantPriceRespDTO getAuthMerchantPrice(Long parentId, Long merchantId) {
        Long currentTenantId = McContentHelper.getCurrentTenantId();
        DistributionProductAuthMerchantPriceRespDTO respDTO = new DistributionProductAuthMerchantPriceRespDTO();
        respDTO.setParentId(parentId).setVariantList(new ArrayList<>());
        // 一件产能线上架的变体
        List<Product> variants = this.productReadService.findOnePieceOnlineVariantByParentId(parentId, null);
        if (CollUtil.isEmpty(variants) || variants.get(0).getTenantId().equals(currentTenantId)) {
            return respDTO;
        }
        List<Long> variantIds = ListUtil.toValueList(variants, Product::getId);
        // 租户供货价
        Map<Long, TenantPurchasePriceRespDTO> purchasePriceMap = this.productPriceV2Service.currentVariantTenantPurchasePrice(currentTenantId, variantIds)
                .stream().collect(Collectors.toMap(TenantPurchasePriceRespDTO::getProductId, i -> i));
        // 一口价
        Map<Long, AuthOnePrice> authOnePriceMap = this.authOnePriceMapperManager.mapByMerchantIdAndVariantIds(merchantId, variantIds);
        variants.forEach(variant -> {
            DistributionProductAuthMerchantPriceRespDTO.VariantPrice dto = BeanUtil.copyProperties(variant, DistributionProductAuthMerchantPriceRespDTO.VariantPrice.class);
            respDTO.getVariantList().add(dto);
            TenantPurchasePriceRespDTO purchasePrice = purchasePriceMap.get(variant.getId());
            if (purchasePrice != null) {
                dto.setMinSupplyPrice(purchasePrice.getMin())
                        .setMaxSupplyPrice(purchasePrice.getMax())
                        .setMaxOneLevelSupplyPrice(purchasePrice.getMax());
            }
            AuthOnePrice authOnePrice = authOnePriceMap.get(variant.getId());
            if (authOnePrice != null) {
                dto.setOnePrice(authOnePrice.getOnePrice());
            }
        });
        return respDTO;
    }

    public void setAuthMerchantPrice(DistributionProductAuthMerchantPriceReqDTO reqDTO) {
        List<AuthOnePriceParam> priceParamList = new ArrayList<>();
        reqDTO.getVariantList().forEach(i -> {
            AuthOnePriceParam param = new AuthOnePriceParam();
            priceParamList.add(param);
            param.setProductId(reqDTO.getParentId())
                    .setVariantId(i.getId())
                    .setOnePrice(i.getOnePrice())
                    .setBatchPrice(BigDecimal.ZERO)
                    .setBatchMinNum(0);
        });
        this.authOnePriceService.set(reqDTO.getMerchantId(), priceParamList);
    }
}
