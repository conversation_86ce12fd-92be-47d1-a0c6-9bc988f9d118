package com.sdsdiy.productimpl.service.distribution;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.BaseFieldPO;
import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.McContentHelper;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import com.sdsdiy.paymentapi.param.TenantDistributionQueryParam;
import com.sdsdiy.productdata.constants.ProductConstant;
import com.sdsdiy.productdata.dto.MinMaxValueDTO;
import com.sdsdiy.productdata.dto.auth.MerchantUnAuthProductRespDTO;
import com.sdsdiy.productdata.dto.distribution.*;
import com.sdsdiy.productdata.dto.price.LadderPriceCalDTO;
import com.sdsdiy.productdata.dto.price.ProductFactorySupplyPriceDTO;
import com.sdsdiy.productdata.dto.price.TenantPurchasePriceRespDTO;
import com.sdsdiy.productdata.enums.supply.SupplyChainTypeEnum;
import com.sdsdiy.productdata.param.ListDistributionProductCountParam;
import com.sdsdiy.productimpl.entity.po.Product;
import com.sdsdiy.productimpl.entity.po.auth.TenantAuthProductParent;
import com.sdsdiy.productimpl.entity.po.distribution.ProductDistributionAuthFactory;
import com.sdsdiy.productimpl.entity.po.distribution.ProductDistributionAuthPrice;
import com.sdsdiy.productimpl.feign.TenantFeign;
import com.sdsdiy.productimpl.feign.payment.TenantDistributionWalletFeign;
import com.sdsdiy.productimpl.manager.auth.TenantAuthProductManager;
import com.sdsdiy.productimpl.service.FactoryService;
import com.sdsdiy.productimpl.service.ProductSupplyService;
import com.sdsdiy.productimpl.service.auth.TenantAuthProductParentService;
import com.sdsdiy.productimpl.service.product.ProductPriceV2Service;
import com.sdsdiy.productimpl.service.product.ProductReadService;
import com.sdsdiy.productimpl.util.ProductPriceUtil;
import com.sdsdiy.userapi.dto.tenant.TenantListReq;
import com.sdsdiy.userapi.dto.tenant.TenantRespDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/6
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProductDistributionAuthService {
    private final ProductReadService productReadService;
    private final ProductPriceV2Service productPriceV2Service;
    private final ProductDistributionAuthFactoryService productDistributionAuthFactoryService;
    private final ProductDistributionAuthPriceService productDistributionAuthPriceService;
    private final ProductDistributionService productDistributionService;
    private final FactoryService factoryService;
    private final ProductSupplyService productSupplyService;
    private final TenantAuthProductManager tenantAuthProductManager;
    private final TenantAuthProductParentService tenantAuthProductParentService;
    private final TenantDistributionWalletFeign tenantDistributionWalletFeign;
    private final TenantFeign tenantFeign;

    public PageResultDto<MerchantUnAuthProductRespDTO> pageDistributionUnAuth(DistributionProductAuthTenantReqDTO reqDTO) {
        DistributionProductAuthPageReqDTO authPageReqDTO = BeanUtil.copyProperties(reqDTO, DistributionProductAuthPageReqDTO.class);
        authPageReqDTO.setAuthTenant(false)
                .setMerchantTenantId(reqDTO.getDistributionTenantId())
                .setTenantId(McContentHelper.getCurrentTenantId());
        Page<Product> productPage = this.productReadService.pageDistributionProduct(authPageReqDTO);
        if (CollUtil.isEmpty(productPage.getRecords())) {
            return PageResultDto.empty();
        }
        List<Long> parentIds = productPage.getRecords().stream().map(Product::getId).collect(Collectors.toList());
        // 母体供货价
        List<TenantPurchasePriceRespDTO> priceRespList = this.productPriceV2Service.currentParentTenantPurchasePrice(reqDTO.getDistributionTenantId(), parentIds);
        Map<Long, TenantPurchasePriceRespDTO> purchasePriceMap = ListUtil.toMap(TenantPurchasePriceRespDTO::getProductParentId, priceRespList);
        List<MerchantUnAuthProductRespDTO> dtoList = new ArrayList<>();
        productPage.getRecords().forEach(product -> {
            MerchantUnAuthProductRespDTO dto = BeanUtil.copyProperties(product, MerchantUnAuthProductRespDTO.class);
            TenantPurchasePriceRespDTO purchasePrice = purchasePriceMap.get(product.getId());
            dto.setMinProductPrice(purchasePrice.getMin());
            dto.setOnSaleStatus(purchasePrice.getOnSaleStatus());
            dtoList.add(dto);
        });
        return PageResultDto.success(productPage.getTotal(), dtoList);
    }

    public PageResultDto<DistributionProductAuthTenantRespDTO> pageDistributionAuthed(DistributionProductAuthTenantReqDTO reqDTO) {
        Long distributionTenantId = reqDTO.getDistributionTenantId();
        DistributionProductAuthPageReqDTO authPageReqDTO = BeanUtil.copyProperties(reqDTO, DistributionProductAuthPageReqDTO.class);
        authPageReqDTO.setAuthTenant(true)
                .setMerchantTenantId(distributionTenantId)
                .setTenantId(McContentHelper.getCurrentTenantId());
        Page<Product> productPage = this.productReadService.pageDistributionProduct(authPageReqDTO);
        if (CollUtil.isEmpty(productPage.getRecords())) {
            return PageResultDto.empty();
        }
        List<Long> parentIds = ListUtil.toValueList(productPage.getRecords(), Product::getId);
        // 统一分销价
        Map<Long, LadderPriceCalDTO> platformPriceMap = this.productDistributionService.mapDistributionPlatformPriceCalculate(parentIds);
        // 授权分销价
        Map<Long, MinMaxValueDTO<BigDecimal>> authPriceMap = this.productDistributionAuthPriceService.mapParentPriceByDistributionTenant(distributionTenantId, parentIds);
        // 授权工厂
        List<ProductDistributionAuthFactory> authFactoryList = this.productDistributionAuthFactoryService.findByDistributionTenantAndParentIds(distributionTenantId, parentIds);
        Map<Long, List<ProductDistributionAuthFactory>> authFactoryMap = ListUtil.toMapValueList(ProductDistributionAuthFactory::getProductParentId, authFactoryList);
        // 工厂
        Set<Long> factoryIds = ListUtil.toValueSet(authFactoryList, ProductDistributionAuthFactory::getFactoryId);
        Map<Long, BaseIdAndNameDTO> factoryMap = ListUtil.toMapDtoByBaseIdAndName(this.factoryService.findNameByIds(factoryIds));

        List<DistributionProductAuthTenantRespDTO> dtoList = new ArrayList<>();
        productPage.getRecords().forEach(product -> {
            DistributionProductAuthTenantRespDTO dto = BeanUtil.copyProperties(product, DistributionProductAuthTenantRespDTO.class);
            dtoList.add(dto);
            // 工厂
            dto.setAuthFactoryList(new ArrayList<>());
            authFactoryMap.getOrDefault(product.getId(), Collections.emptyList()).forEach(f -> {
                DistributionProductAuthTenantRespDTO.AuthFactory factoryDto = new DistributionProductAuthTenantRespDTO.AuthFactory();
                dto.getAuthFactoryList().add(factoryDto);
                factoryDto.setFactory(NumberUtils.greaterZero(f.getFactoryId())
                                ? factoryMap.get(f.getFactoryId()) : new BaseIdAndNameDTO(f.getFactoryId(), "不限工厂"))
                        .setDayLimit(f.getDayLimit())
                        .setIsUnlimitedProduction(f.getIsUnlimitedProduction());
            });
            // 价格
            boolean authPrice = false;
            MinMaxValueDTO<BigDecimal> disPrice = authPriceMap.get(product.getId());
            if (disPrice == null) {
                disPrice = platformPriceMap.get(product.getId());
            } else {
                authPrice = true;
            }
            dto.setIsAuthPrice(BasePoConstant.yesOrNo(authPrice));
            dto.setDistributionPrice(disPrice == null ? ProductConstant.PRICE_UN_SET
                    : (authPrice ? disPrice.getValueStr() : StrUtil.format("供应商统一价（{}）", disPrice.getValueStr()))
            );
        });
        return PageResultDto.success(productPage.getTotal(), dtoList);
    }

    public void addDistributionAuth(DistributionAuthDTO authDTO) {
        this.tenantAuthProductManager.authDistributionTenant(Collections.singletonList(authDTO.getMerchantTenantId())
                , authDTO.getParentIdsOrParentId(), McContentHelper.getCurrentUserId(), null);
    }

    public void removeDistributionAuth(DistributionAuthDTO authDTO) {
        List<TenantAuthProductParent> authList = this.tenantAuthProductParentService
                .findByTargetTenantAndParentIds(authDTO.getMerchantTenantId(), authDTO.getParentIdsOrParentId());
        this.tenantAuthProductManager.delAuthDistributionTenant(authList
                , McContentHelper.getCurrentTenantId(), McContentHelper.getCurrentUserId());
    }

    public void removeTargetTenantAllDistributionAuth(Long productTenantId, Long targetTenantId) {
        List<TenantAuthProductParent> authList = this.tenantAuthProductParentService
                .findByProductTenantAndTargetTenant(productTenantId, targetTenantId);
        this.tenantAuthProductManager.delAuthDistributionTenant(authList
                , productTenantId, McContentHelper.getCurrentUserId());
    }

    public DistributionAuthTenantPriceRespDTO getDistributionAuthTenantPrice(Long parentId, Long distributionTenantId) {
        DistributionAuthTenantPriceRespDTO respDTO = new DistributionAuthTenantPriceRespDTO();
        respDTO.setParentId(parentId).setVariantList(new ArrayList<>());
        // 上架的变体
        List<Product> variants = this.productReadService.findOnePieceOnlineVariantByParentId(parentId, null);
        if (CollUtil.isEmpty(variants)) {
            return respDTO;
        }
        // 变体供货价
        Map<Long, ProductFactorySupplyPriceDTO> supplyPriceMap = this.productSupplyService
                .mapProductVariantSupplyPrice(parentId, SupplyChainTypeEnum.ONE_PIECE.name());
        // 授权价
        List<ProductDistributionAuthPrice> authPriceList = this.productDistributionAuthPriceService
                .findByDistributionTenantAndParentIds(distributionTenantId, Collections.singleton(parentId));
        Map<Long, ProductDistributionAuthPrice> authPriceMap = ListUtil.toMap(ProductDistributionAuthPrice::getProductVariantId, authPriceList);

        variants.forEach(variant -> {
            DistributionAuthTenantPriceRespDTO.VariantPrice variantPrice = BeanUtil.copyProperties(variant
                    , DistributionAuthTenantPriceRespDTO.VariantPrice.class);
            respDTO.getVariantList().add(variantPrice);
            ProductFactorySupplyPriceDTO priceDTO = supplyPriceMap.get(variant.getId());
            if (priceDTO != null) {
                variantPrice.setMinSupplyPrice(priceDTO.getMinPrice())
                        .setMaxSupplyPrice(priceDTO.getMaxPrice())
                        .setMaxOneLevelSupplyPrice(priceDTO.getMaxOneLevelSupplyPrice());
            } else {
                variantPrice.setMaxOneLevelSupplyPrice(BigDecimal.ZERO);
            }
            ProductDistributionAuthPrice authPrice = authPriceMap.get(variant.getId());
            if (authPrice == null) {
                variantPrice.setDistributionPrice(Collections.emptyList());
            } else {
                variantPrice.setDistributionPrice(ProductPriceUtil.platformPriceStr2List(authPrice.getPrice()));
            }
        });
        return respDTO;
    }

    public void setDistributionAuthTenantPrice(DistributionAuthTenantPriceReqDTO reqDTO) {
        Long currentTenantId = McContentHelper.getCurrentTenantId();
        List<ProductDistributionAuthPrice> oldList = this.productDistributionAuthPriceService
                .findByDistributionTenantAndParentIds(reqDTO.getDistributionTenantId(), Collections.singleton(reqDTO.getParentId()));
        Map<Long, Long> oldMap = ListUtil.toMap(ProductDistributionAuthPrice::getProductVariantId, BaseFieldPO::getId, oldList);
        if (CollUtil.isNotEmpty(reqDTO.getVariantList())) {
            List<ProductDistributionAuthPrice> poList = reqDTO.getVariantList().stream().map(i -> {
                BigDecimal firstLevelPrice = ProductPriceUtil.checkLadderPrice(i.getDistributionPrice(), ProductPriceUtil.NUM_MIN_1);
                ProductDistributionAuthPrice po = new ProductDistributionAuthPrice();
                po.setDistributionTenantId(reqDTO.getDistributionTenantId())
                        .setProductTenantId(currentTenantId)
                        .setProductParentId(reqDTO.getParentId())
                        .setProductVariantId(i.getId())
                        .setPrice(ProductPriceUtil.ladderPriceList2JsonStr(i.getDistributionPrice()))
                        .setPriceLevelNum(i.getDistributionPrice().size())
                        .setFirstLevelPrice(firstLevelPrice)
                        .setId(oldMap.get(i.getId()));
                oldMap.remove(i.getId());
                return po;
            }).collect(Collectors.toList());
            this.productDistributionAuthPriceService.saveOrUpdateBatch(poList, BaseFieldPO::getId);
        }
        // 删除
        this.productDistributionAuthPriceService.delByIds(oldMap.values(), McContentHelper.getCurrentUserId());
    }

    public List<TenantAuthProductCountDTO> listAuthProductCount(ListDistributionProductCountParam param) {
        return tenantAuthProductParentService.listAuthProductCount(param);
    }

    public List<Long> listSupplierTenantIdsByProduct(Long distributorTenantId, List<Long> productParentIds) {
        if (CollUtil.isEmpty(productParentIds)) {
            return new ArrayList<>();
        }
        return tenantAuthProductParentService.lambdaQuery()
                .select(TenantAuthProductParent::getProductTenantId)
                .eq(TenantAuthProductParent::getTargetTenantId, distributorTenantId)
                .in(TenantAuthProductParent::getProductParentId, productParentIds)
                .list()
                .stream()
                .map(TenantAuthProductParent::getProductTenantId)
                .distinct().collect(Collectors.toList());
    }

    public List<Long> listDistributorTenantIdsByProduct(Long supplierTenantId, List<Long> productParentIds) {
        if (CollUtil.isEmpty(productParentIds)) {
            return new ArrayList<>();
        }
        return tenantAuthProductParentService.lambdaQuery()
                .select(TenantAuthProductParent::getTargetTenantId)
                .eq(TenantAuthProductParent::getProductTenantId, supplierTenantId)
                .in(TenantAuthProductParent::getProductParentId, productParentIds)
                .list()
                .stream()
                .map(TenantAuthProductParent::getTargetTenantId)
                .distinct().collect(Collectors.toList());
    }

    public List<BaseIdAndNameDTO> factoryProductHaveAuthDistributorList(Long tenantId, Long factoryId, List<Long> mustShowDistributorTenantIds) {

        TenantDistributionQueryParam param = new TenantDistributionQueryParam();
        param.setSupTenantId(Collections.singletonList(tenantId));
        List<TenantDistributorWalletDto> tenantDistributorWalletDtos = tenantDistributionWalletFeign.queryWallets(param);
        List<BaseIdAndNameDTO> res = new ArrayList<>();
        boolean haveMustShowTenantIds = CollUtil.isNotEmpty(mustShowDistributorTenantIds);
        if (CollUtil.isEmpty(tenantDistributorWalletDtos) && !haveMustShowTenantIds) {
            return res;
        }
        List<Long> distributorTenantIds = tenantDistributorWalletDtos.stream().map(TenantDistributorWalletDto::getDisTenantId).collect(Collectors.toList());

        List<TenantAuthProductParent> tenantAuthProductParents = CollUtil.isEmpty(distributorTenantIds) ? Collections.emptyList() :
                tenantAuthProductParentService.lambdaQuery()
                        .select(TenantAuthProductParent::getProductParentId, TenantAuthProductParent::getTargetTenantId)
                        .eq(TenantAuthProductParent::getProductTenantId, tenantId)
                        .in(TenantAuthProductParent::getTargetTenantId, distributorTenantIds)
                        .list();

        List<Long> productParentIds = tenantAuthProductParents
                .stream()
                .map(TenantAuthProductParent::getProductParentId)
                .distinct()
                .collect(Collectors.toList());

        List<Long> existProductParentIds = this.productSupplyService.filterExistProductsByFactory(factoryId, productParentIds, SupplyChainTypeEnum.ONE_PIECE.name());

        List<Long> finalExistProductParentIds = existProductParentIds;
        List<TenantAuthProductParent> filterTenantAuthProductParents = tenantAuthProductParents.stream()
                .filter(item -> finalExistProductParentIds.contains(item.getProductParentId()))
                .collect(Collectors.toList());

        distributorTenantIds = filterTenantAuthProductParents.stream().map(TenantAuthProductParent::getTargetTenantId).distinct().collect(Collectors.toList());
        existProductParentIds = filterTenantAuthProductParents.stream().map(TenantAuthProductParent::getProductParentId).distinct().collect(Collectors.toList());

        //查这张表，因为有些产品是分销给指定工厂
        //如果这张表有记录，且没有factory_id = 0 且factory_id=当前工厂id的记录，则说明这这个工厂的这件产品没有分销给某个租户
        List<ProductDistributionAuthFactory> distributionAuthFactoryList = productDistributionAuthFactoryService.findByDistributionTenantIdsAndParentIds(distributorTenantIds, existProductParentIds);
        Map<String, List<ProductDistributionAuthFactory>> keyAndProductAuthFactoryListMap = distributionAuthFactoryList.stream().collect(Collectors.groupingBy(a -> this.genProductIdAndDisTenantIdKey(a.getProductParentId(), a.getDistributionTenantId())));
        Set<String> needRemoveTenantAuthProductTenantKeys = new HashSet<>();
        for (Map.Entry<String, List<ProductDistributionAuthFactory>> entry : keyAndProductAuthFactoryListMap.entrySet()) {
            List<ProductDistributionAuthFactory> value = entry.getValue();
            if (CollUtil.isEmpty(value)) {
                continue;
            }
            List<Long> existFactoryIds = value.stream().map(ProductDistributionAuthFactory::getFactoryId).distinct().collect(Collectors.toList());
            if (existFactoryIds.contains(BasePoConstant.LONG_ZERO) || existFactoryIds.contains(factoryId)) {
                continue;
            }
            needRemoveTenantAuthProductTenantKeys.add(entry.getKey());
        }
        filterTenantAuthProductParents = filterTenantAuthProductParents
                .stream()
                .filter(a -> !needRemoveTenantAuthProductTenantKeys.contains(this
                        .genProductIdAndDisTenantIdKey(a.getProductParentId(), a.getTargetTenantId())))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(filterTenantAuthProductParents) && !haveMustShowTenantIds) {
            return res;
        }
        List<Long> targetTenantIds = haveMustShowTenantIds ? mustShowDistributorTenantIds : new ArrayList<>();
        targetTenantIds.addAll(filterTenantAuthProductParents
                .stream()
                .map(TenantAuthProductParent::getTargetTenantId)
                .distinct()
                .collect(Collectors.toList()));
        if (CollUtil.isEmpty(targetTenantIds)) {
            return res;
        }
        TenantListReq req = new TenantListReq();
        req.setInIds(targetTenantIds);
        List<TenantRespDto> tenantRespDtoList = tenantFeign.listDto(req);
        for (TenantRespDto tenantRespDto : tenantRespDtoList) {
            BaseIdAndNameDTO dto = new BaseIdAndNameDTO();
            dto.setId(tenantRespDto.getId());
            dto.setName(tenantRespDto.getName());
            res.add(dto);
        }
        return res;
    }

    private String genProductIdAndDisTenantIdKey(Long productId, Long distributorTenantId) {
        return productId + "_" + distributorTenantId;
    }
}
