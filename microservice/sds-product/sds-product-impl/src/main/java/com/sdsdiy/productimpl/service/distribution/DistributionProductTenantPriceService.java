package com.sdsdiy.productimpl.service.distribution;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.status.ProductPublicStatus;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.productdata.dto.price.LadderPriceDTO;
import com.sdsdiy.productdata.dto.price.PriceCalculateParameterDTO;
import com.sdsdiy.productdata.dto.price.ProductMinMaxValueDTO;
import com.sdsdiy.productdata.dto.price.ProductPlatformPriceEditDTO;
import com.sdsdiy.productdata.dto.product.edit.ProductUpdateOnSalePriceReqDTO;
import com.sdsdiy.productdata.enums.supply.SupplyChainTypeEnum;
import com.sdsdiy.productimpl.entity.po.Product;
import com.sdsdiy.productimpl.entity.po.distribution.DistributionProductTenantPrice;
import com.sdsdiy.productimpl.service.distribution.manager.DistributionProductTenantPriceManager;
import com.sdsdiy.productimpl.service.product.ProductReadService;
import com.sdsdiy.productimpl.util.ProductPriceUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.sdsdiy.productimpl.util.ProductPriceUtil.NUM_MIN_1;

/**
 * <AUTHOR>
 * @date 2021/11/18
 */
@Service
@Log4j2
public class DistributionProductTenantPriceService {
    @Autowired
    private DistributionProductTenantPriceManager distributionProductTenantPriceManager;
    @Autowired
    @Lazy
    private ProductReadService productReadService;
    @Autowired
    @Lazy
    private ProductDistributionService productDistributionService;

    public Map<Long, ProductMinMaxValueDTO> mapTenantPriceMinMaxPrice(Collection<Long> parentIds, Long tenantId) {
        Map<Long, List<DistributionProductTenantPrice>> map = distributionProductTenantPriceManager.mapByParentIds(parentIds, tenantId);
        return map.entrySet().stream().map(entry -> {
            ProductMinMaxValueDTO dto = new ProductMinMaxValueDTO(entry.getKey());
            List<BigDecimal> platformPriceList = new ArrayList<>();
            List<BigDecimal> onSalePriceList = new ArrayList<>();
            entry.getValue().forEach(one -> {
                List<LadderPriceDTO> platformLadderPriceList = ProductPriceUtil.platformPriceStr2List(one.getPlatformPrice());
                if (CollUtil.isNotEmpty(platformLadderPriceList)) {
                    platformPriceList.addAll(platformLadderPriceList.stream().map(LadderPriceDTO::getPrice).collect(Collectors.toList()));
                }
                if (BasePoConstant.OPEN.equals(one.getOnSaleStatus())) {
                    List<LadderPriceDTO> onSaleLadderPriceList = ProductPriceUtil.platformPriceStr2List(one.getPromotionLadderPrice());
                    onSalePriceList.addAll(onSaleLadderPriceList.stream().map(LadderPriceDTO::getPrice).collect(Collectors.toList()));
                }
            });
            dto.setOnSalePrice(ProductPriceUtil.getMinMaxDTO(onSalePriceList));
            dto.setPlatformPrice(ProductPriceUtil.getMinMaxDTO(platformPriceList));
            return dto;
        }).collect(Collectors.toMap(ProductMinMaxValueDTO::getParentId, i -> i));
    }

    public Map<Long, DistributionProductTenantPrice> mapTenantPriceByParentId(Long parentId, Long tenantId) {
        return distributionProductTenantPriceManager.listByParentId(parentId, tenantId)
                .stream().collect(Collectors.toMap(DistributionProductTenantPrice::getProductId, i -> i));
    }

    public Map<Long, DistributionProductTenantPrice> mapTenantPrice(Collection<Long> variantIds, Long tenantId) {
        return distributionProductTenantPriceManager.listByVariantIds(variantIds, tenantId)
                .stream().collect(Collectors.toMap(DistributionProductTenantPrice::getProductId, i -> i));
    }

    /**
     * 平台价
     */
    public Map<Long, List<LadderPriceDTO>> mapPlatformPriceByParentId(Long parentId, Long tenantId) {
        return distributionProductTenantPriceManager.listByParentId(parentId, tenantId)
                .stream().collect(Collectors.toMap(DistributionProductTenantPrice::getProductId,
                        i -> ProductPriceUtil.platformPriceStr2List(i.getPlatformPrice())));
    }

    /**
     * 保存平台价
     */
    public void updateTenantPlatformPrice(ProductPlatformPriceEditDTO editDTO) {
        Map<Long, DistributionProductTenantPrice> tenantPriceMap = this.mapTenantPriceByParentId(editDTO.getParentId(), editDTO.getTenantId());
        List<DistributionProductTenantPrice> saveList = new ArrayList<>();
        editDTO.getVariantList().forEach(variant -> {
            String err = variant.check();
            Assert.validateBool(err == null, err);
            ProductPriceUtil.checkLadderPrice(variant.getPlatformPrice(), ProductPriceUtil.NUM_MIN_1);
            DistributionProductTenantPrice one = new DistributionProductTenantPrice();
            if (tenantPriceMap.containsKey(variant.getId())) {
                one.setId(tenantPriceMap.get(variant.getId()).getId());
                one.setUpdateUid(editDTO.getUid());
            } else {
                one.setProductId(variant.getId()).setProductParentId(editDTO.getParentId())
                        .setTenantId(editDTO.getTenantId()).setProductTenantId(editDTO.getProductTenantId())
                        .setChainedU(editDTO.getUid(), editDTO.getUid());
            }
            one.setPlatformPrice(ProductPriceUtil.ladderPriceList2JsonStr(variant.getPlatformPrice()));
            one.setIsBatchPrice(variant.getPlatformPrice().size() > 1 ? BasePoConstant.YES : BasePoConstant.NO);
            saveList.add(one);
        });
        distributionProductTenantPriceManager.saveOrUpdateList(saveList);
    }

    /**
     * 保存促销价
     */
    public void updateTenantOnSalePrice(ProductUpdateOnSalePriceReqDTO reqDto) {
        String err = reqDto.check(false);
        Assert.validateBool(err == null, err);
        List<Product> variantList = productReadService.findOnePieceOnlineVariantByParentId(reqDto.getParentId(), null);
        Map<Long, DistributionProductTenantPrice> tenantPriceMap = this.mapTenantPriceByParentId(reqDto.getParentId(), reqDto.getTenantId());
        variantList.forEach(v -> {
            DistributionProductTenantPrice tenantPrice = tenantPriceMap.get(v.getId());
            Assert.validateTrue(tenantPrice == null || StrUtil.isBlank(tenantPrice.getPlatformPrice()), "请先设置平台价");
        });
        List<DistributionProductTenantPrice> saveList = new ArrayList<>();
        reqDto.getList().forEach(variant -> {
            DistributionProductTenantPrice one = new DistributionProductTenantPrice();
            //是否开启促销
            boolean onSaleOpen = BasePoConstant.OPEN.equals(variant.getOnSaleStatus());
            if (onSaleOpen) {
                ProductPriceUtil.checkLadderPrice(variant.getLadderPrices(), NUM_MIN_1);
            }
            if (tenantPriceMap.containsKey(variant.getId())) {
                one.setId(tenantPriceMap.get(variant.getId()).getId());
                one.setUpdateUid(reqDto.getUid());
            } else {
                one.setProductId(variant.getId()).setProductParentId(reqDto.getParentId())
                        .setTenantId(reqDto.getTenantId()).setProductTenantId(reqDto.getProductTenantId())
                        .setChainedU(reqDto.getUid(), reqDto.getUid());
            }
            // 设置变体
            one.setOnSaleBeginTime(onSaleOpen ? QueryParamHelper.getTimestamp(QueryParamHelper.getQueryDateStart(variant.getOnSaleBeginDate())) : 0L);
            one.setOnSaleEndTime(onSaleOpen ? QueryParamHelper.getTimestamp(QueryParamHelper.getQueryDateStart(variant.getOnSaleEndDate())) : 0L);
            one.setPromotionLadderPrice(onSaleOpen ? ProductPriceUtil.ladderPriceList2JsonStr(variant.getLadderPrices()) : "");
            one.setOnSalePrice(onSaleOpen ? variant.getLadderPrices().get(0).getPrice() : BigDecimal.ZERO);
            one.setOnSaleStatus(variant.getOnSaleStatus());
            saveList.add(one);
        });
        distributionProductTenantPriceManager.saveOrUpdateList(saveList);
    }

    public void setPlatformPriceBatch(PriceCalculateParameterDTO parameterDTO) {
        List<Long> parentIds = parameterDTO.getIds();
        if (CollUtil.isEmpty(parentIds)) {
            return;
        }
        Long tenantId = parameterDTO.getTenantId();
        List<Product> variantList = productReadService.findSupplyChainOnlineVariantByParentIds(parentIds, ProductPublicStatus.OPEN, SupplyChainTypeEnum.ONE_PIECE.name());
        Map<Long, List<LadderPriceDTO>> distributionPlatformPriceMap = productDistributionService.mapDistributionPlatformPrice(variantList.stream().map(Product::getId).collect(Collectors.toList()));
        Map<Long, List<DistributionProductTenantPrice>> parentPriceMap = distributionProductTenantPriceManager.mapByParentIds(parentIds, tenantId);
        Map<Long, DistributionProductTenantPrice> variantPriceMap = new HashMap<>(parentPriceMap.size());
        parentPriceMap.values().forEach(priceList -> priceList.forEach(price -> variantPriceMap.put(price.getProductId(), price)));

        Long uid = parameterDTO.getUid();
        List<DistributionProductTenantPrice> saveList = new ArrayList<>();
        variantList.forEach(variant -> {
            List<LadderPriceDTO> ladderPriceList = distributionPlatformPriceMap.containsKey(variant.getId()) ?
                    // 如果分销平台价没设置，则按平台价走
                    distributionPlatformPriceMap.get(variant.getId()) : ProductPriceUtil.platformPriceStr2List(variant.getPrice());
            if (CollUtil.isEmpty(ladderPriceList)) {
                return;
            }
            ladderPriceList.forEach(price -> price.setPrice(ProductPriceUtil.calculatePrice(parameterDTO, price.getPrice())));
            String priceStr = ProductPriceUtil.ladderPriceList2JsonStr(ladderPriceList);
            DistributionProductTenantPrice one = new DistributionProductTenantPrice();
            one.setPlatformPrice(priceStr);
            one.setIsBatchPrice(ladderPriceList.size() > 1 ? BasePoConstant.YES : BasePoConstant.NO);

            if (variantPriceMap.containsKey(variant.getId())) {
                one.setId(variantPriceMap.get(variant.getId()).getId());
                one.setUpdateUid(uid);
            } else {
                one.setProductId(variant.getId()).setProductParentId(variant.getParentId())
                        .setTenantId(tenantId).setProductTenantId(variant.getTenantId())
                        .setChainedU(uid, uid);
            }

            saveList.add(one);
        });
        distributionProductTenantPriceManager.saveOrUpdateList(saveList);
    }
}
