package com.sdsdiy.productimpl.controller.product;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.status.ProductPublicStatus;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.*;
import com.sdsdiy.common.base.enums.ApplicationCodeEnum;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.core.aws.s3.S3Util;
import com.sdsdiy.core.redis.util.RedisUtil;
import com.sdsdiy.productapi.api.ProductSummaryInfoApi;
import com.sdsdiy.productdata.constants.ProductConstant;
import com.sdsdiy.productdata.constants.ProductEnumConstant;
import com.sdsdiy.productdata.dto.price.ProductFactorySupplyPriceDTO;
import com.sdsdiy.productdata.dto.price.ProductMinMaxValueDTO;
import com.sdsdiy.productdata.dto.price.TenantPurchasePriceRespDTO;
import com.sdsdiy.productdata.dto.product.*;
import com.sdsdiy.productdata.enums.ProductSupplyHolidayStatus;
import com.sdsdiy.productdata.enums.supply.SupplyChainTypeEnum;
import com.sdsdiy.productimpl.entity.po.*;
import com.sdsdiy.productimpl.entity.po.prototype.PrototypeGroup;
import com.sdsdiy.productimpl.feign.ApplicationFeign;
import com.sdsdiy.productimpl.feign.TenantFeign;
import com.sdsdiy.productimpl.feign.user.DataSyncValueRelFeign;
import com.sdsdiy.productimpl.service.CategoryService;
import com.sdsdiy.productimpl.service.LanguageProductDetailsService;
import com.sdsdiy.productimpl.service.ProductSupplyService;
import com.sdsdiy.productimpl.service.auth.MerchantAuthProductParentService;
import com.sdsdiy.productimpl.service.dict.TextureService;
import com.sdsdiy.productimpl.service.distribution.DistributionProductTenantPriceService;
import com.sdsdiy.productimpl.service.product.*;
import com.sdsdiy.productimpl.service.product.copy.ProductCopyRecordService;
import com.sdsdiy.userdata.dto.datasync.DataSyncValueBatchDTO;
import com.sdsdiy.userdata.dto.datasync.DataSyncValueRelDTO;
import com.sdsdiy.userdata.enums.DataSyncStatus;
import com.sdsdiy.userdata.enums.DataSyncType;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.NO;
import static com.sdsdiy.common.base.constant.BasePoConstant.YES;

/**
 * <p>
 * 产品汇总信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021/10/22
 */
@RestController
@RequiredArgsConstructor
public class ProductSummaryInfoController implements ProductSummaryInfoApi {
    private final TenantFeign tenantFeign;
    private final ApplicationFeign applicationFeign;
    @Autowired
    private ProductSummaryInfoService productSummaryInfoService;
    @Autowired
    private ProductSupplyService productSupplyService;
    @Autowired
    private ProductReadService productReadService;
    @Autowired
    private CategoryService categoryService;
    @Autowired
    private TextureService textureService;
    @Autowired
    private MerchantAuthProductParentService merchantAuthProductParentService;
    @Autowired
    private LanguageProductDetailsService languageProductDetailsService;
    @Autowired
    private DistributionProductTenantPriceService distributionProductTenantPriceService;
    @Autowired
    private ProductPriceV2Service productPriceV2Service;
    @Resource
    private ProductTenantVisibleRelService productTenantVisibleRelService;
    @Resource
    private DataSyncValueRelFeign dataSyncValueRelFeign;
    @Resource
    private ProductCopyRecordService productCopyRecordService;

    @Override
    public void initSummaryInfo(ProductIdReqDTO dto) {
        if (CollUtil.isNotEmpty(dto.getIds())) {
            if (dto.getIds().contains(0L)) {
                dto.setIds(this.productReadService.findAllParentProductIdList());
            }
            dto.getIds().forEach(id -> this.productSummaryInfoService.initSummaryInfo(id));
        } else {
            this.productSummaryInfoService.initSummaryInfo(dto.getId());
        }
    }

    @Override
    public void updateProductSummary(ProductSummaryInfoUpdateDTO dto) {
        if (dto.getId() == null) {
            throw new BusinessException("更新id不能为null");
        }
        this.productSummaryInfoService.updateSummaryInfo(dto);
    }

    @Override
    public ProductSummaryInfoDTO getProductSummary(Long id) {
        return this.productSummaryInfoService.getOneDto(id);
    }

    @Override
    public PageListResultDTO<PlatformProductPageRespDTO> pageOfPlatformProduct(PlatformProductPageReqDTO pageSelect) {
        Page<ProductSummaryInfo> summaryPage = this.productSummaryInfoService.pageOfPlatformProduct(pageSelect);
        if (CollUtil.isEmpty(summaryPage.getRecords())) {
            return new PageListResultDTO<>(summaryPage.getTotal(), Collections.emptyList());
        }
        boolean sdsdiy = TenantCommonConstant.isSdsdiy(pageSelect.getTenantId());
        // 母体ids
        List<Long> parentIds = new ArrayList<>();
        Set<Long> categoryIds = new HashSet<>();
        Set<Long> textureIds = new HashSet<>();
        Set<String> syncFailIds = new HashSet<>();
        Set<String> syncSuccessIds = new HashSet<>();
        summaryPage.getRecords().forEach(parent -> {

            parentIds.add(parent.getId());
            categoryIds.add(parent.getCategoryId());
            textureIds.add(parent.getTextureId());
            if (sdsdiy) {
                if (DataSyncStatus.FAIL.equals(parent.getSyncBigolloStatus())) {

                    syncFailIds.add(parent.getId().toString());
                } else if (DataSyncStatus.FINISH.equals(parent.getSyncBigolloStatus())) {
                    syncSuccessIds.add(parent.getId().toString());
                }
            }
        });
        Map<Long, List<Category>> categoryMap = this.categoryService.mapAllParentLink(categoryIds);
        Map<Long, BaseIdAndNameDTO> textureMap = this.textureService.mapTextureName(textureIds);
        // 一件产能线预下架变体列表
        Map<Long, List<Product>> variantMap = this.productReadService.mapVariantOfflineForeshow(parentIds, SupplyChainTypeEnum.ONE_PIECE.name());
        //小单产能线预下架变体列表
        Map<Long, List<Product>> smallOrderVariantMap = this.productReadService.mapVariantOfflineForeshow(parentIds, SupplyChainTypeEnum.SMALL_ORDER.name());
        // 有设置休假的供应关系
        Map<Long, Map<Long, List<ProductSupply>>> holidayParentSupplyMap = this.productSupplyService.mapHolidaySupplyByParentId(parentIds,
                SupplyChainTypeEnum.ONE_PIECE.name());
        // 有设置休假的小单供应关系
        Map<Long, Map<Long, List<ProductSupply>>> smallOrderHolidayParentSupplyMap = this.productSupplyService.mapHolidaySupplyByParentId(parentIds,
                SupplyChainTypeEnum.SMALL_ORDER.name());
        // 供货价
        Map<Long, List<ProductFactorySupplyPriceDTO>> supplyPriceMap = this.productSupplyService.mapProductParentFactorySupplyPrice(
                parentIds, SupplyChainTypeEnum.ONE_PIECE.name());
        // 小单供货价
        Map<Long, List<ProductFactorySupplyPriceDTO>> smallOrderSupplyPriceMap = this.productSupplyService.mapProductParentFactorySupplyPrice(
                parentIds, SupplyChainTypeEnum.SMALL_ORDER.name());
        Map<Long, ProductCopyRecord> parentIdKeyProductCopyMap = this.productCopyRecordService.getListByNewProductIds(parentIds).stream()
                .collect(Collectors.toMap(ProductCopyRecord::getNewProductParentId, Function.identity(), (a, b) -> b));

        // 同步bigollo信息
        Map<Long, String> syncFailMap = null;
        Map<Long, List<String>> syncSkuMap = null;
        if (sdsdiy) {
            // 同步失败原因
            DataSyncValueBatchDTO failBatch = new DataSyncValueBatchDTO(
                    MerchantStorePlatformEnum.BIGOLLO, DataSyncType.PRODUCT, DataSyncStatus.FAIL);
            failBatch.setQueryDTO(new BaseListQueryDTO<>(syncFailIds));
            List<DataSyncValueRelDTO> failRelList = this.dataSyncValueRelFeign.findLastRelByInners(failBatch);
            syncFailMap = ListUtil.toMap(r -> Long.parseLong(r.getInnerValue()), DataSyncValueRelDTO::getSyncRemark, failRelList);
            // 同步成功sku
            DataSyncValueBatchDTO successBatch = new DataSyncValueBatchDTO(
                    MerchantStorePlatformEnum.BIGOLLO, DataSyncType.PRODUCT_OUT_SKU, DataSyncStatus.FINISH);
            successBatch.setQueryDTO(new BaseListQueryDTO<>(syncSuccessIds));
            List<DataSyncValueRelDTO> successSkuList = this.dataSyncValueRelFeign.findByInners(successBatch);
            syncSkuMap = successSkuList.stream().collect(Collectors.groupingBy(r -> Long.parseLong(r.getInnerValue()),
                    Collectors.mapping(DataSyncValueRelDTO::getOutValue, Collectors.toList())));
        }

        List<PlatformProductPageRespDTO> dtoList = new ArrayList<>();
        for (ProductSummaryInfo parent : summaryPage.getRecords()) {
            PlatformProductPageRespDTO dto = BeanUtil.copyProperties(parent, PlatformProductPageRespDTO.class);
            dto.setThumbImgUrl(S3Util.urlThumb(parent.getImgUrl()));
            dto.setTexture(textureMap.get(parent.getTextureId()));
            dto.setCategoryList(ListUtil.copyProperties(categoryMap.get(parent.getCategoryId()), BaseIdAndNameDTO.class));
            // 模板组
            List<PlatformProductPageRespDTO.PrototypeGroupDTO> prototypeGroupStr = new ArrayList<>();
            List<PrototypeGroup> prototypeGroup = JSON.parseArray(parent.getPrototypeGroup(), PrototypeGroup.class);
            if (CollUtil.isNotEmpty(prototypeGroup)) {
                prototypeGroup.forEach(g -> prototypeGroupStr.add(new PlatformProductPageRespDTO.PrototypeGroupDTO(
                        g.getPrototypeGroupName(), g.getBindNum(), dto.getChildrenNum())));
            }
            dto.setPrototypeGroupList(prototypeGroupStr);
            // 一件产能线预下架变体
            dto.setOfflineForeshow(ListUtil.copyProperties(variantMap.get(parent.getId()), ProductVariantDTO.class));
            // 小单产能线预下架变体
            dto.setSmallOrderOfflineForeshow(ListUtil.copyProperties(smallOrderVariantMap.get(parent.getId()), ProductVariantDTO.class));
            // 一件供应关系、价格、休假状态
            List<ProductFactorySupplyPriceDTO> factoryList = supplyPriceMap.get(parent.getId());
            if (factoryList == null) {
                dto.setProductSupply(Collections.emptyList());
            } else {
                List<PlatformProductPageRespDTO.SupplyInfoDTO> infoDtoList = new ArrayList<>();
                // 工厂休假
                Map<Long, List<ProductSupply>> holidayFactoryMap = holidayParentSupplyMap.get(parent.getId());
                factoryList.forEach(factory -> {
                    PlatformProductPageRespDTO.SupplyInfoDTO supplyInfoDTO = BeanUtil.copyProperties(factory, PlatformProductPageRespDTO.SupplyInfoDTO.class);
                    // 设置工厂的休假状态
                    setFactoryHolidayStatus(supplyInfoDTO, holidayFactoryMap);
                    infoDtoList.add(supplyInfoDTO);
                });
                dto.setProductSupply(infoDtoList);
            }
            // 小单供应关系、价格、休假状态
            List<ProductFactorySupplyPriceDTO> smallOrderFactoryList = smallOrderSupplyPriceMap.get(parent.getId());
            if (smallOrderFactoryList == null) {
                dto.setProductSmallOrderSupply(Collections.emptyList());
            } else {
                List<PlatformProductPageRespDTO.SupplyInfoDTO> smallOrderInfoDtoList = new ArrayList<>();
                // 小单工厂休假
                Map<Long, List<ProductSupply>> smallOrderHolidayFactoryMap = smallOrderHolidayParentSupplyMap.get(parent.getId());
                smallOrderFactoryList.forEach(factory -> {
                    PlatformProductPageRespDTO.SupplyInfoDTO smallOrderSupplyInfoDTO = BeanUtil.copyProperties(factory, PlatformProductPageRespDTO.SupplyInfoDTO.class);
                    // 设置工厂的休假状态
                    setFactoryHolidayStatus(smallOrderSupplyInfoDTO, smallOrderHolidayFactoryMap);
                    smallOrderInfoDtoList.add(smallOrderSupplyInfoDTO);
                });
                dto.setProductSmallOrderSupply(smallOrderInfoDtoList);
            }
            //复制记录的关系
            ProductCopyRecord productCopyRecord = parentIdKeyProductCopyMap.get(parent.getId());
            if (productCopyRecord != null) {
                PlatformProductPageRespDTO.ProductCopyRecordDto productCopyRecordDto = new PlatformProductPageRespDTO.ProductCopyRecordDto(productCopyRecord.getStatus(), productCopyRecord.getErrorMsg());
                dto.setProductCopyRecord(productCopyRecordDto);
            }
            if (sdsdiy) {
                if (DataSyncStatus.FAIL.equals(parent.getSyncBigolloStatus())) {
                    dto.setSyncBigollo(new PlatformProductPageRespDTO
                            .SyncInfoDTO(parent.getSyncBigolloStatus(), syncFailMap.get(parent.getId())));
                } else if (DataSyncStatus.FINISH.equals(parent.getSyncBigolloStatus())) {
                    dto.setSyncBigollo(new PlatformProductPageRespDTO
                            .SyncInfoDTO(parent.getSyncBigolloStatus(), syncSkuMap.get(parent.getId())));
                } else {
                    dto.setSyncBigollo(new PlatformProductPageRespDTO
                            .SyncInfoDTO(parent.getSyncBigolloStatus()));
                }
            }
            dtoList.add(dto);
        }
        return new PageListResultDTO<>(summaryPage.getTotal(), dtoList);
    }

    /**
     * 设置工厂的休假状态
     */
    private static void setFactoryHolidayStatus(PlatformProductPageRespDTO.SupplyInfoDTO supplyInfoDTO, Map<Long, List<ProductSupply>> holidayFactoryMap) {
        if (holidayFactoryMap != null) {
            List<ProductSupply> holidaySupplyList = holidayFactoryMap.get(supplyInfoDTO.getFactoryId());
            if (holidaySupplyList != null) {
                for (ProductSupply s : holidaySupplyList) {
                    supplyInfoDTO.setHolidayStatus(s.getHolidayStatus());
                    if (ProductSupplyHolidayStatus.isHoliday(s.getHolidayStatus())) {
                        break;
                    }
                }
            } else {
                supplyInfoDTO.setHolidayStatus(ProductSupplyHolidayStatus.NONE.status);
            }
        }
    }

    @Override
    public PageResultDto<TenantProductPageRespDTO> pageOfOtherTenantProduct(TenantProductPageReqDTO pageSelect) {
        Boolean openDistributionApp = applicationFeign.checkApplicationIsOpenByCode(pageSelect.getTenantId(), ApplicationCodeEnum.DISTRIBUTE_EXTERNAL_PRODUCT.getCode());
        if (!Boolean.TRUE.equals(openDistributionApp)) {
            return PageResultDto.empty();
        }
        Page<ProductSummaryInfo> summaryPage = this.productSummaryInfoService.pageOfTenantProduct(pageSelect);
        if (CollUtil.isEmpty(summaryPage.getRecords())) {
            return new PageResultDto<>(summaryPage.getTotal());
        }
        // 母体ids
        List<Long> parentIds = new ArrayList<>();
        Set<Long> categoryIds = new HashSet<>();
        Set<Long> textureIds = new HashSet<>();
        Set<Long> productTenantIds = new HashSet<>();
        summaryPage.getRecords().forEach(parent -> {
            parentIds.add(parent.getId());
            categoryIds.add(parent.getCategoryId());
            textureIds.add(parent.getTextureId());
            productTenantIds.add(parent.getTenantId());
        });
        Map<Long, List<Category>> categoryMap = this.categoryService.mapAllParentLink(categoryIds);
        Map<Long, BaseIdAndNameDTO> textureMap = this.textureService.mapTextureName(textureIds);
        // 预下架变体列表
        Map<Long, List<Product>> variantMap = this.productReadService.mapVariantOfflineForeshow(parentIds, SupplyChainTypeEnum.ONE_PIECE.name());
        // 有设置休假的供应关系
        Map<Long, Map<Long, List<ProductSupply>>> holidayParentSupplyMap = this.productSupplyService.mapHolidaySupplyByParentId(parentIds, SupplyChainTypeEnum.ONE_PIECE.name());
        // 供货价
        Map<Long, TenantPurchasePriceRespDTO> purchasePriceMap = this.productPriceV2Service.currentParentTenantPurchasePrice(pageSelect.getTenantId(), parentIds)
                .stream().collect(Collectors.toMap(TenantPurchasePriceRespDTO::getProductParentId, i -> i));
        // 分销授权商户数
        Map<Long, Integer> authMerchantNumMap = this.merchantAuthProductParentService.mapCountParentAuthMerchantNum(parentIds, pageSelect.getTenantId());
        // 多语言
        Map<Long, String> languageMap = this.languageProductDetailsService.batchGetByProductIds(parentIds, pageSelect.getTenantId());
        // 租户 平台价、促销价
        Map<Long, ProductMinMaxValueDTO> minMaxValueMap = this.distributionProductTenantPriceService.mapTenantPriceMinMaxPrice(parentIds, pageSelect.getTenantId());
        // 租户官网显示产品
        List<Long> visibleParentIds = this.productTenantVisibleRelService.findByTenantId(pageSelect.getTenantId());
        // 租户
        List<BaseIdAndNameDTO> tenantList = this.tenantFeign.findNameByIds(BaseListDto.of(productTenantIds));
        Map<Long, BaseIdAndNameDTO> tenantMap = ListUtil.toMapDtoByBaseIdAndName(tenantList);
        List<TenantProductPageRespDTO> dtoList = new ArrayList<>();
        summaryPage.getRecords().forEach(parent -> {
            TenantProductPageRespDTO dto = BeanUtil.copyProperties(parent, TenantProductPageRespDTO.class);
            dto.setDistributionType(ProductEnumConstant.DistributionType.getByTenantId(parent.getTenantId()).code)
                    .setProductTenant(tenantMap.get(parent.getTenantId()))
                    .setThumbImgUrl(S3Util.urlThumb(parent.getImgUrl())).setTexture(textureMap.get(parent.getTextureId()))
                    .setCategoryList(ListUtil.copyProperties(categoryMap.get(parent.getCategoryId()), BaseIdAndNameDTO.class));
            // 预下架变体
            dto.setOfflineForeshow(ListUtil.copyProperties(variantMap.get(parent.getId()), ProductVariantDTO.class));
            // 供应关系、价格、休假状态
            TenantProductPageRespDTO.TenantSupplyInfoDTO supplyInfoDTO = new TenantProductPageRespDTO.TenantSupplyInfoDTO();
            // 工厂休假
            boolean isHoliday = false, isHolidayForeshow = false;
            Map<Long, List<ProductSupply>> holidayFactoryMap = holidayParentSupplyMap.get(parent.getId());
            if (holidayFactoryMap != null) {
                for (List<ProductSupply> list : holidayFactoryMap.values()) {
                    for (ProductSupply ps : list) {
                        if (ProductSupplyHolidayStatus.READY.equals(ps.getHolidayStatus())) {
                            isHolidayForeshow = true;
                        } else if (ProductSupplyHolidayStatus.HOLIDAY.equals(ps.getHolidayStatus())) {
                            isHoliday = true;
                        }
                        if (isHolidayForeshow && isHoliday) {
                            break;
                        }
                    }
                }
            }
            supplyInfoDTO.setIsHoliday(isHoliday);
            supplyInfoDTO.setIsHolidayForeshow(isHolidayForeshow);
            // 供货价
            TenantPurchasePriceRespDTO purchasePrice = purchasePriceMap.getOrDefault(parent.getId(), TenantPurchasePriceRespDTO.getEmptyObject());
            supplyInfoDTO.setMinPrice(purchasePrice.getMin());
            supplyInfoDTO.setMaxPrice(purchasePrice.getMax());
            dto.setProductSupply(supplyInfoDTO);
            dto.setAuthMerchantNum(authMerchantNumMap.getOrDefault(parent.getId(), 0));
            ProductMinMaxValueDTO maxValueDTO = minMaxValueMap.get(parent.getId());
            if (maxValueDTO == null) {
                // 未设置时候=供应价
                dto.setPlatformPrice(ProductConstant.EQUAL_SUPPLY_PRICE).setIsPlatformPrice(NO)
                        .setOnSalePrice(ProductConstant.PRICE_UN_SET).setOnSaleStatus(ProductConstant.ON_SALE_STATUS_CLOSE);
            } else {
                dto.setPlatformPrice(maxValueDTO.getPlatformPrice().getValueStr()).setIsPlatformPrice(YES)
                        .setOnSalePrice(maxValueDTO.getOnSalePrice().getValueStr())
                        .setOnSaleStatus(maxValueDTO.getOnSalePrice().getMin() != null ? ProductConstant.ON_SALE_STATUS_OPEN : ProductConstant.ON_SALE_STATUS_CLOSE);
            }
            dto.setLanguageDetail(languageMap.getOrDefault(parent.getId(), ProductConstant.PRICE_UN_SET));
            if (!ProductConstant.STATUS_ONLINE.equals(dto.getStatus()) || !ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name().equals(dto.getOnePieceSupplyChainStatus())) {
                dto.setStatus(ProductConstant.STATUS_OFFLINE);
            }
            dto.setWebVisible(BasePoConstant.yesOrNo(visibleParentIds.contains(dto.getId())));
            dtoList.add(dto);
        });
        return new PageResultDto<>(summaryPage.getTotal(), dtoList);
    }

    @Autowired
    private ProductOldDataService productOldDataService;

    @Override
    public void sprint7_0_0(JSONObject jsonObject) {
        this.productOldDataService.sprint7_0_0(jsonObject);
    }

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public void unlockTest(String key) {
        this.redisUtil.unlock(key);
    }

}

