package com.sdsdiy.productimpl.mapper.distribution;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdsdiy.common.base.entity.dto.SqlKeyValueBo;
import com.sdsdiy.core.base.util.SimpleSelectInLangDriver;
import com.sdsdiy.productdata.dto.price.LadderPriceCalDTO;
import com.sdsdiy.productimpl.entity.po.distribution.ProductDistributionPlatformPrice;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 产品分销平台（批发）价 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021/11/14
 */
public interface ProductDistributionPlatformPriceMapper extends BaseMapper<ProductDistributionPlatformPrice> {

    @Select(" SELECT product_parent_id, IFNULL( max( price ), 0 ) max, IFNULL( min( price ), 0 ) min, count(DISTINCT num ) level_num " +
            " FROM product_distribution_platform_price where product_parent_id in (#{parentIds}) " +
            " AND product_id in (select id from sds_product.product where status = 1 and one_piece_supply_chain_status = 'ONLINE' and parent_id in(#{parentIds})) GROUP BY product_parent_id ")
    @Lang(SimpleSelectInLangDriver.class)
    List<LadderPriceCalDTO> calculateDistributionPlatformPrice(@Param("parentIds") Collection<Long> parentIds);


    @Select(" SELECT product_id AS `key`, IFNULL( min( price ), 0 )  as value" +
            " FROM product_distribution_platform_price where product_id in (#{variantIds}) " +
            " GROUP BY product_id ")
    @Lang(SimpleSelectInLangDriver.class)
    @Results(value = {@Result(column = "key", property = "key", javaType = Long.class)})
    List<SqlKeyValueBo<Long, BigDecimal>> minDistributionPlatformPriceByVariantIds(@Param("variantIds") Collection<Long> variantIds);

}
