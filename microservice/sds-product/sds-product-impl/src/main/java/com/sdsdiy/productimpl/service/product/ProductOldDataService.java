package com.sdsdiy.productimpl.service.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.enums.ApplicationCodeEnum;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.OptionalBean;
import com.sdsdiy.orderapi.dto.CommissionProductStatRespDto;
import com.sdsdiy.productdata.constants.ProductEnumConstant;
import com.sdsdiy.productdata.dto.distribution.DistributionAuthAccumulateNumReqDTO;
import com.sdsdiy.productdata.dto.distribution.ProductDistributionStatusDTO;
import com.sdsdiy.productdata.dto.price.LadderPriceDTO;
import com.sdsdiy.productdata.dto.product.CopyProductReqDTO;
import com.sdsdiy.productimpl.entity.po.AuthOnePrice;
import com.sdsdiy.productimpl.entity.po.Product;
import com.sdsdiy.productimpl.entity.po.ProductDistributionAccumulatePrice;
import com.sdsdiy.productimpl.entity.po.auth.MerchantAuthProductParent;
import com.sdsdiy.productimpl.entity.po.distribution.ProductDistributionAuthOnePrice;
import com.sdsdiy.productimpl.entity.po.distribution.ProductDistributionAuthPrice;
import com.sdsdiy.productimpl.feign.ApplicationFeign;
import com.sdsdiy.productimpl.feign.CommissionProductStatFeign;
import com.sdsdiy.productimpl.feign.TenantFeign;
import com.sdsdiy.productimpl.manager.auth.TenantAuthProductManager;
import com.sdsdiy.productimpl.manager.mapper.AuthOnePriceMapperManager;
import com.sdsdiy.productimpl.service.ProductMerchantPriceService;
import com.sdsdiy.productimpl.service.auth.MerchantAuthProductParentService;
import com.sdsdiy.productimpl.service.auth.TenantAuthProductParentService;
import com.sdsdiy.productimpl.service.distribution.ProductDistributionAuthPriceService;
import com.sdsdiy.productimpl.service.distribution.ProductDistributionService;
import com.sdsdiy.productimpl.service.distribution.manager.ProductDistributionAccumulatePriceService;
import com.sdsdiy.productimpl.service.distribution.manager.ProductDistributionAuthOnePriceManager;
import com.sdsdiy.productimpl.util.ProductPriceUtil;
import com.sdsdiy.userapi.dto.tenant.resp.TenantDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.tenant.TenantCommonConstant.SDSDIY_TENANT_ID;

/**
 * <AUTHOR>
 * @date 2021/11/25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProductOldDataService {
    private final ProductDistributionAccumulatePriceService productDistributionAccumulatePriceService;
    private final ProductDistributionAuthOnePriceManager productDistributionAuthOnePriceManager;
    private final MerchantAuthProductParentService merchantAuthProductParentService;
    private final TenantAuthProductManager tenantAuthProductManager;
    private final ProductDistributionService productDistributionService;
    private final ProductWriteService productWriteService;
    private final ApplicationFeign applicationFeign;
    private final TenantAuthProductParentService tenantAuthProductParentService;
    private final ProductDistributionAuthPriceService productDistributionAuthPriceService;
    private final CommissionProductStatFeign commissionProductStatFeign;
    private final AuthOnePriceMapperManager authOnePriceMapperManager;
    private final ProductMerchantPriceService productMerchantPriceService;
    private final TenantFeign tenantFeign;
    @Autowired
    private ProductOldDataService selfClass;


    public void sprint7_0_0(JSONObject jsonObject) {
        if (BasePoConstant.yes(jsonObject.getInteger("sprint7_0_0_initDistributionProduct"))) {
            this.selfClass.sprint7_0_0_initDistributionProduct();
        }
        if (BasePoConstant.yes(jsonObject.getInteger("sprint7_0_0_authRelPublic"))) {
            this.selfClass.sprint7_0_0_authRelPublic();
        }
        if (BasePoConstant.yes(jsonObject.getInteger("sprint7_0_0_authRelPrivate"))) {
            this.selfClass.sprint7_0_0_authRelPrivate();
        }
        if (BasePoConstant.yes(jsonObject.getInteger("sprint7_0_0_distributionOnePrice"))) {
            this.selfClass.sprint7_0_0_distributionOnePrice();
        }
        if (BasePoConstant.yes(jsonObject.getInteger("sprint7_0_0_distributionAccumulatePrice"))) {
            this.sprint7_0_0_distributionAccumulatePrice(jsonObject.getLong("sprint7_0_0_distributionAccumulatePriceLastTenantId"));
        }
        if (BasePoConstant.yes(jsonObject.getInteger("sprint7_0_0_onePrice"))) {
            this.selfClass.sprint7_0_0_onePrice();
            // 私有产品的授权累计档位不处理
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void sprint7_0_0_initDistributionProduct() {
        // 产品id
        List<Long> parentIds = this.merchantAuthProductParentService.lambdaQuery()
                .select(MerchantAuthProductParent::getProductParentId)
                .eq(MerchantAuthProductParent::getProductTenantId, SDSDIY_TENANT_ID)
                .ne(MerchantAuthProductParent::getMerchantTenantId, SDSDIY_TENANT_ID)
                .groupBy(MerchantAuthProductParent::getProductParentId).list()
                .stream().map(MerchantAuthProductParent::getProductParentId).collect(Collectors.toList());
        log.info("sprint7_0_0_initDistributionProduct:{}", parentIds.size());
        if (CollUtil.isEmpty(parentIds)) {
            return;
        }
        // 初始化分销价
        this.productDistributionService.initDistributionPrice(parentIds, 0L);
        // 设置分销状态
        ProductDistributionStatusDTO distributionStatusDTO = new ProductDistributionStatusDTO();
        distributionStatusDTO.setIds(parentIds).setIsDistribution(BasePoConstant.YES);
        this.productWriteService.updateIsDistribution(distributionStatusDTO);
        log.info("sprint7_0_0_initDistributionProduct end");
    }

    @Transactional(rollbackFor = Exception.class)
    public void sprint7_0_0_authRelPublic() {
        // 公开 分销产品
        List<Product> parentList = this.productWriteService.lambdaQuery(
                        "status,isDistribution,publicStatus,tenantId", null)
                .eq(Product::getTenantId, SDSDIY_TENANT_ID)
                .eq(Product::getPublicStatus, BasePoConstant.OPEN)
                .eq(Product::getIsDistribution, BasePoConstant.YES)
                .eq(Product::getParentId, 0L)
                .eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                .eq(Product::getStatus, BasePoConstant.ONLINE).list();
        log.info("sprint7_0_0_authRelPublic:{}", parentList.size());
        if (CollUtil.isEmpty(parentList)) {
            return;
        }
        // 开启分销应用的租户
        List<Long> targetTenantIds = this.applicationFeign.findOpenTenantIdsByAppCode(ApplicationCodeEnum.DISTRIBUTE_EXTERNAL_PRODUCT.getCode());
        targetTenantIds.remove(SDSDIY_TENANT_ID);
        // 增加授权关系
        ListUtil.splitDo(parentList, 200, s -> {
            this.tenantAuthProductManager.authDistributionTenant(targetTenantIds, s, 0L);
        });
        log.info("sprint7_0_0_authRelPublic end");
    }

    @Transactional(rollbackFor = Exception.class)
    public void sprint7_0_0_authRelPrivate() {
        // 商户授权关系
        List<MerchantAuthProductParent> authList = this.findSdsPrivateAuth();
        log.info("sprint7_0_0_authRelPrivate authList:{}", authList.size());
        if (CollUtil.isEmpty(authList)) {
            return;
        }
        Set<Long> parentIds = ListUtil.toValueSet(authList, MerchantAuthProductParent::getProductParentId);
        // 私有 分销产品
        List<Product> parentList = this.productWriteService.lambdaQuery(
                        "status,isDistribution,tenantId", null)
                .in(Product::getId, parentIds)
                .eq(Product::getTenantId, SDSDIY_TENANT_ID)
                .eq(Product::getPublicStatus, BasePoConstant.CLOSE)
                .eq(Product::getIsDistribution, BasePoConstant.YES)
                .eq(Product::getParentId, 0L)
                .eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                .eq(Product::getStatus, BasePoConstant.ONLINE).list();
        log.info("sprint7_0_0_authRelPrivate parentList:{}", parentList.size());
        if (CollUtil.isEmpty(parentList)) {
            return;
        }
        ListUtil.toMapValueList(MerchantAuthProductParent::getMerchantTenantId
                        , MerchantAuthProductParent::getProductParentId, authList)
                .forEach((tId, pIds) -> {
                    List<Product> pList = ListUtil.filter(parentList, i -> pIds.contains(i.getId()));
                    // 租户授权关系
                    this.tenantAuthProductManager.authDistributionTenant(Collections.singletonList(tId), pList, 0L);
                    log.info("sprint7_0_0_authRelPrivate tId:{},pList:{}", tId, pList.size());
                });
        log.info("sprint7_0_0_authRelPrivate end");
    }

    @Transactional(rollbackFor = Exception.class)
    public void sprint7_0_0_distributionOnePrice() {
        List<ProductDistributionAuthOnePrice> authOnePriceList = this.productDistributionAuthOnePriceManager.lambdaQuery()
                .eq(ProductDistributionAuthOnePrice::getProductTenantId, SDSDIY_TENANT_ID)
                .ne(ProductDistributionAuthOnePrice::getMerchantTenantId, SDSDIY_TENANT_ID)
                .gt(ProductDistributionAuthOnePrice::getOnePrice, BigDecimal.ZERO)
                .list();
        log.info("sprint7_0_0_distributionOnePrice :{}", authOnePriceList.size());
        if (CollUtil.isEmpty(authOnePriceList)) {
            return;
        }
        Set<Long> parentIds = ListUtil.toValueSet(authOnePriceList, ProductDistributionAuthOnePrice::getProductParentId);
        Set<Long> merchantTenantIds = ListUtil.toValueSet(authOnePriceList, ProductDistributionAuthOnePrice::getMerchantTenantId);
        // 租户授权关系
        Set<String> authedKeySet = this.tenantAuthProductParentService.authedKeySet(merchantTenantIds, parentIds);
        ListUtil.toMapValueList(ProductDistributionAuthOnePrice::getMerchantTenantId, authOnePriceList)
                // 按租户分组
                .forEach((tId, authList) -> {
                    Set<Long> pIds = ListUtil.toValueSet(authList, ProductDistributionAuthOnePrice::getProductParentId);
                    // 已设置授权分销价
                    List<ProductDistributionAuthPrice> oldList = this.productDistributionAuthPriceService
                            .findByDistributionTenantAndParentIds(tId, pIds);
                    Set<Long> oldVariantIds = ListUtil.toValueSet(ProductDistributionAuthPrice::getProductVariantId, oldList);
                    // 新增数据
                    List<ProductDistributionAuthPrice> addList = new ArrayList<>();
                    authList.stream().filter(a ->
                                    // 没设置过
                                    !oldVariantIds.contains(a.getProductId())
                                            // 有授权关系
                                            && authedKeySet.contains(a.getMerchantTenantId() + "&&" + a.getProductParentId()))
                            .forEach(a -> {
                                ProductDistributionAuthPrice po = new ProductDistributionAuthPrice();
                                addList.add(po);
                                po.setDistributionTenantId(a.getMerchantTenantId())
                                        .setProductTenantId(a.getProductTenantId())
                                        .setProductParentId(a.getProductParentId())
                                        .setProductVariantId(a.getProductId())
                                        .setPrice(ProductPriceUtil.ladderPriceList2JsonStr(Collections
                                                .singletonList(new LadderPriceDTO(1, a.getOnePrice()))))
                                        .setPriceLevelNum(1)
                                        .setFirstLevelPrice(a.getOnePrice());
                            });
                    this.productDistributionAuthPriceService.saveBatch(addList);
                    log.info("sprint7_0_0_distributionOnePrice tId:{},addList:{}", tId, addList.size());
                });
        log.info("sprint7_0_0_distributionOnePrice end");
    }

    public void sprint7_0_0_distributionAccumulatePrice(Long lastTenantId) {
        // 公开 分销产品
        List<Long> parentIds = this.productWriteService.lambdaQuery()
                .select(Product::getId)
                .eq(Product::getTenantId, SDSDIY_TENANT_ID)
                .eq(Product::getPublicStatus, BasePoConstant.OPEN)
                .eq(Product::getIsDistribution, BasePoConstant.YES)
                .eq(Product::getParentId, 0L)
                .eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                .eq(Product::getStatus, BasePoConstant.ONLINE).list()
                .stream().map(Product::getId).collect(Collectors.toList());
        log.info("sprint7_0_0_distributionAccumulatePrice:{}", parentIds.size());
        if (CollUtil.isEmpty(parentIds)) {
            return;
        }
        // 开启分销应用的租户
        List<Long> targetTenantIds = this.applicationFeign.findOpenTenantIdsByAppCode(ApplicationCodeEnum.DISTRIBUTE_EXTERNAL_PRODUCT.getCode())
                .stream().filter(i -> lastTenantId == null || i > lastTenantId).collect(Collectors.toList());
        targetTenantIds.remove(SDSDIY_TENANT_ID);

        ListUtil.splitDo(parentIds, 20, pIds -> {
            // 变体
            List<Product> variantList = this.productWriteService.lambdaQuery(
                            "parentId,tenantId,distributionAccumulatePriceLevel", null)
                    .in(Product::getParentId, pIds)
                    .eq(Product::getTenantId, SDSDIY_TENANT_ID)
                    .eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                    .eq(Product::getStatus, BasePoConstant.ONLINE).list();
            List<Long> variantIds = ListUtil.toValueList(variantList, Product::getId);
            // 分销累计价
            Map<Long, List<LadderPriceDTO>> distAccumulatePriceMap = this.mapDistributionAccumulatePriceByVariantIds(variantIds);
            for (Long targetTenantId : targetTenantIds) {
                // 租户分销累计数
                Map<Long, CommissionProductStatRespDto> tenantParentAccumulateNumMap = this.mapTenantParentAccumulateNum(targetTenantId, pIds);
                // 已设置授权分销价
                List<ProductDistributionAuthPrice> oldList = this.productDistributionAuthPriceService
                        .findByDistributionTenantAndParentIds(targetTenantId, pIds);
                Set<Long> oldVariantIds = ListUtil.toValueSet(ProductDistributionAuthPrice::getProductVariantId, oldList);
                // 新增数据
                List<ProductDistributionAuthPrice> addList = new ArrayList<>();
                variantList.stream().filter(v -> !oldVariantIds.contains(v.getId()))
                        .forEach(v -> {
                            // 变体累计价
                            List<LadderPriceDTO> distAccumulatePrice = distAccumulatePriceMap.get(v.getId());
                            if (CollUtil.isEmpty(distAccumulatePrice)) {
                                return;
                            }
                            CommissionProductStatRespDto distAccumulateNum = tenantParentAccumulateNumMap.get(v.getParentId());
                            boolean hasAuth = distAccumulateNum != null && distAccumulateNum.getAuthDistributionAccumulateNum() >= 0;
                            Integer level = hasAuth ?
                                    ProductPriceUtil.calculateCurrentAccumulatorLevel(distAccumulatePrice
                                            , distAccumulateNum.getNum() + distAccumulateNum.getAuthDistributionAccumulateNum())
                                    : ProductPriceUtil.calculateCurrentAccumulatorLevel(distAccumulatePrice
                                    , OptionalBean.ofNullable(distAccumulateNum).getBean(CommissionProductStatRespDto::getNum).orElse(0)
                                    , v.getDistributionAccumulatePriceLevel());
                            if (level <= 0) {
                                return;
                            }
                            level = Math.min(level, distAccumulatePrice.size());
                            BigDecimal price = distAccumulatePrice.get(level - 1).getPrice();
                            ProductDistributionAuthPrice po = new ProductDistributionAuthPrice();
                            addList.add(po);
                            po.setDistributionTenantId(targetTenantId)
                                    .setProductTenantId(v.getTenantId())
                                    .setProductParentId(v.getParentId())
                                    .setProductVariantId(v.getId())
                                    .setPrice(ProductPriceUtil.ladderPriceList2JsonStr(Collections
                                            .singletonList(new LadderPriceDTO(1, price))))
                                    .setPriceLevelNum(1)
                                    .setFirstLevelPrice(price);
                        });
                this.productDistributionAuthPriceService.saveBatch(addList);
                log.info("sprint7_0_0_distributionAccumulatePrice tId:{},addList:{}", targetTenantId, addList.size());
            }
        });
        log.info("sprint7_0_0_distributionAccumulatePrice end");
    }

    @Transactional(rollbackFor = Exception.class)
    public void sprint7_0_0_onePrice() {
        // 商户授权关系
        List<MerchantAuthProductParent> authList = this.findSdsPrivateAuth();
        log.info("sprint7_0_0_onePrice authList:{}", authList.size());
        if (CollUtil.isEmpty(authList)) {
            return;
        }
        Set<Long> authParentIds = ListUtil.toValueSet(authList, MerchantAuthProductParent::getProductParentId);
        // 私有 分销产品
        List<Long> parentIds = this.productWriteService.lambdaQuery()
                .select(Product::getId)
                .in(Product::getId, authParentIds)
                .eq(Product::getTenantId, SDSDIY_TENANT_ID)
                .eq(Product::getPublicStatus, BasePoConstant.CLOSE)
                .eq(Product::getIsDistribution, BasePoConstant.YES)
                .eq(Product::getParentId, 0L)
                .eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                .eq(Product::getStatus, BasePoConstant.ONLINE).list()
                .stream().map(Product::getId).collect(Collectors.toList());
        log.info("sprint7_0_0_onePrice parentIds:{}", parentIds.size());
        if (CollUtil.isEmpty(parentIds)) {
            return;
        }
        Map<Long, Long> merchantTenantIdMap = ListUtil.toMap(MerchantAuthProductParent::getMerchantId
                , MerchantAuthProductParent::getMerchantTenantId, authList);
        authList.stream().filter(i -> parentIds.contains(i.getProductParentId()))
                .collect(Collectors.groupingBy(MerchantAuthProductParent::getMerchantId))
                .forEach((mId, aList) -> {
                    Set<Long> pIds = ListUtil.toValueSet(aList, MerchantAuthProductParent::getProductParentId);
                    List<AuthOnePrice> authOnePriceList = this.authOnePriceMapperManager.lambdaQuery()
                            .eq(AuthOnePrice::getMerchantId, mId)
                            .in(AuthOnePrice::getProductId, pIds)
                            .gt(AuthOnePrice::getOnePrice, BigDecimal.ZERO).list();
                    if (CollUtil.isEmpty(authOnePriceList)) {
                        return;
                    }
                    Long targetTenantId = merchantTenantIdMap.get(mId);
                    // 已设置授权分销价
                    List<ProductDistributionAuthPrice> oldList = this.productDistributionAuthPriceService
                            .findByDistributionTenantAndParentIds(targetTenantId, pIds);
                    Set<Long> oldVariantIds = ListUtil.toValueSet(ProductDistributionAuthPrice::getProductVariantId, oldList);
                    // 新增数据
                    List<ProductDistributionAuthPrice> addList = new ArrayList<>();
                    authOnePriceList.stream().filter(v -> !oldVariantIds.contains(v.getVariantId()))
                            .forEach(v -> {
                                ProductDistributionAuthPrice po = new ProductDistributionAuthPrice();
                                addList.add(po);
                                po.setDistributionTenantId(targetTenantId)
                                        .setProductTenantId(SDSDIY_TENANT_ID)
                                        .setProductParentId(v.getProductId())
                                        .setProductVariantId(v.getVariantId())
                                        .setPrice(ProductPriceUtil.ladderPriceList2JsonStr(Collections
                                                .singletonList(new LadderPriceDTO(1, v.getOnePrice()))))
                                        .setPriceLevelNum(1)
                                        .setFirstLevelPrice(v.getOnePrice());
                            });
                    this.productDistributionAuthPriceService.saveBatch(addList);
                    log.info("sprint7_0_0_onePrice tId:{},addList:{}", targetTenantId, addList.size());
                });
        log.info("sprint7_0_0_onePrice end");
    }

    private Map<Long, CommissionProductStatRespDto> mapTenantParentAccumulateNum(Long merchantTenantId, Collection<Long> parentIds) {
        DistributionAuthAccumulateNumReqDTO reqDTO = new DistributionAuthAccumulateNumReqDTO(Collections.singleton(merchantTenantId), parentIds);
        return this.commissionProductStatFeign.listByMerchantTenantIdsAndParentIds(reqDTO).stream().collect(
                Collectors.toMap(CommissionProductStatRespDto::getProductParentId, i -> i));
    }

    private Map<Long, List<LadderPriceDTO>> mapDistributionAccumulatePriceByVariantIds(Collection<Long> variantIds) {
        List<ProductDistributionAccumulatePrice> list = this.productDistributionAccumulatePriceService.listByVariantIds(variantIds);
        return list.stream().collect(Collectors.groupingBy(ProductDistributionAccumulatePrice::getProductId,
                Collectors.mapping(i -> new LadderPriceDTO(i.getBeginNum(), i.getPrice()), Collectors.toList())));
    }

    private List<MerchantAuthProductParent> findSdsPrivateAuth() {
        // sds私有产品
        List<Long> parentIds = this.productWriteService.lambdaQuery().select(Product::getId)
                .eq(Product::getParentId, 0L)
                .eq(Product::getTenantId, SDSDIY_TENANT_ID)
                .eq(Product::getPublicStatus, BasePoConstant.CLOSE)
                .eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                .eq(Product::getStatus, BasePoConstant.ONLINE).list()
                .stream().map(Product::getId).collect(Collectors.toList());
        // 主商户ids
        List<TenantDTO> allTenant = this.tenantFeign.allTenant(null);
        List<Long> mainMerchantIds = ListUtil.toValueList(allTenant, TenantDTO::getMerchantId);
        // 授权关系
        return this.merchantAuthProductParentService.lambdaQuery()
                .in(MerchantAuthProductParent::getMerchantId, mainMerchantIds)
                .in(MerchantAuthProductParent::getProductParentId, parentIds)
                .eq(MerchantAuthProductParent::getProductTenantId, SDSDIY_TENANT_ID)
                .ne(MerchantAuthProductParent::getMerchantTenantId, SDSDIY_TENANT_ID).list();
    }
}
