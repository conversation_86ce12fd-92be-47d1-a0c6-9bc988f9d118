package com.sdsdiy.paymentimpl.service.transaction;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.payment.PaymentTagConst;
import com.sdsdiy.core.redis.lock.LockUtil;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.msg.SubTransactionMsg;
import com.sdsdiy.paymentapi.msg.TenantDisBalanceChangeMsg;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentapi.param.lakala.PaidNotifyBaseParam;
import com.sdsdiy.paymentapi.param.lakala.PaidNotifyBaseRecord;
import com.sdsdiy.paymentimpl.bo.TransactionCreateEntityBo;
import com.sdsdiy.paymentimpl.bo.TransactionOperateRelatedEntityBo;
import com.sdsdiy.paymentimpl.bo.TransactionPayRelatedEntityBo;
import com.sdsdiy.paymentimpl.entity.*;
import com.sdsdiy.paymentimpl.manager.*;
import com.sdsdiy.paymentimpl.service.PaymentPayFactory;
import com.sdsdiy.paymentimpl.service.PaymentService;
import com.sdsdiy.paymentimpl.service.pay.IPayCreate;
import com.sdsdiy.paymentimpl.service.pay.IPayment;
import com.sdsdiy.paymentimpl.service.refund.IRefund;
import com.sdsdiy.paymentimpl.service.refund.RefundFactory;
import com.sdsdiy.paymentimpl.service.transaction.notify.IOnlinePayNotify;
import com.sdsdiy.paymentimpl.service.transaction.notify.OnlinePayNotifyFactory;
import com.sdsdiy.paymentimpl.util.TradeNoUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionPayService {

    private final TransactionCheckService transactionCheckService;
    private final TransactionDbService transactionDbService;
    private final PaymentMapperManager paymentMapperManager;
    private final LockUtil lockUtil;
    private final TenantWalletMapperManager tenantWalletMapperManager;
    private final MerchantMapperManager merchantMapperManager;
    private final MerchantUserAccountMapperManager merchantUserAccountMapperManager;
    private final TenantDistributorWalletManager tenantDistributorWalletManager;
    private final TransactionEntryManage transactionEntryManage;
    private final RefundMapperManager refundMapperManager;
    private final RocketMQTemplate rocketMQTemplate;
    private final OnlinePayNotifyFactory onlinePayNotifyFactory;
    private final SubTransactionTaskManage subTransactionTaskManage;

    public boolean checkMainTransactionFinish(String tradeNo) {
        Payment mainPayment = paymentMapperManager.findOneTradeNo(tradeNo);
        if (mainPayment != null) {
            return BasePoConstant.yes(mainPayment.getBalanceOperateFinish())
                && PaymentStatusEnum.PAID.getStatus().equals(mainPayment.getStatus());
        }

        Refund mainRefund = refundMapperManager.findOneTradeNo(tradeNo);
        if (mainRefund != null) {
            return BasePoConstant.yes(mainRefund.getBalanceOperateFinish())
                && PaymentStatusEnum.PAID.getStatus().equals(mainRefund.getStatus());
        }
        return false;
    }


    public void doSubTransaction(Long subTransactionId) {
        SubTransactionTask subTransactionTask = subTransactionTaskManage.getById(subTransactionId);
        if (subTransactionTask == null) {
            return;
        }
        String parentTradeNo = subTransactionTask.getParentTradeNo();
        if (checkMainTransactionFinish(parentTradeNo)) {
            log.info("sub transaction task error parent transaction not finish, subTransactionId={}", subTransactionId);
            return;
        }

        if (subTransactionTask.getPaymentId() > 0) {
            this.processPayment(subTransactionTask.getPaymentId());
            log.info("sub payment transaction success id={}", subTransactionTask.getPaymentId());
        } else if (subTransactionTask.getRefundId() > 0) {
            this.processRefund(subTransactionTask.getRefundId());
            log.info("sub refund transaction success id={}", subTransactionTask.getRefundId());
        }
        subTransactionTaskManage.removeById(subTransactionId);
    }


    public Payment refreshPaymentPaidStatus(Long paymentId) {
        Payment payment = paymentMapperManager.getById(paymentId);
        if (ObjectUtil.isNull(payment)) {
            throw new BusinessException("Payment不存在，请校验数据");
        }
        if (!PaymentStatusEnum.WAIT_PAY.getStatus().equals(payment.getStatus())) {
            return payment;
        }

        IOnlinePayNotify<PaidNotifyBaseParam, PaidNotifyBaseRecord> strategy = onlinePayNotifyFactory.getStrategy(payment.getMethod());
        strategy.fetchPaidResultAndUpdatePayment(payment);
        return payment;
    }


    private void addRelatedTenantWallet(long tenantId, Map<Long, TenantWallet> tenantWalletMap) {
        if (tenantWalletMap.containsKey(tenantId)) {
            return;
        }
        TenantWallet tenantWallet = tenantWalletMapperManager.getById(tenantId);
        Assert.validateNull(tenantWallet, "租户钱包信息获取失败");
        tenantWalletMap.put(tenantId, tenantWallet);
    }

    private void addRelatedMerchant(long merchantId, Map<Long, Merchant> merchantMap) {
        if (merchantMap.containsKey(merchantId)) {
            return;
        }
        Merchant merchant = merchantMapperManager.getById(merchantId);
        Assert.validateNull(merchant, "商户信息获取失败");
        merchantMap.put(merchantId, merchant);
    }


    public TransactionOperateRelatedEntityBo genRelatedEntityByRefunds(List<Long> refundIds) {
        TransactionOperateRelatedEntityBo relatedEntityBo = new TransactionOperateRelatedEntityBo();

        List<Refund> refundse = refundMapperManager.listByIds(refundIds);
        relatedEntityBo.getWantOperateRefunds().addAll(refundse);

        List<String> paymentTradeNos = new ArrayList<>();
        for (Refund payment : relatedEntityBo.getWantOperateRefunds()) {
            paymentTradeNos.add(payment.getTradeNo());
            if (PaymentRoleEnum.TENANT.getCode().equals(payment.getSourceRole())) {
                addRelatedTenantWallet(payment.getSourceTenantId(), relatedEntityBo.getTenantWalletMap());
            } else if (PaymentRoleEnum.MERCHANT.getCode().equals(payment.getSourceRole())) {
                addRelatedMerchant(payment.getSourceMerchantId(), relatedEntityBo.getMerchantMap());
            }
            if (PaymentRoleEnum.TENANT.getCode().equals(payment.getTargetRole())) {
                addRelatedTenantWallet(payment.getTargetTenantId(), relatedEntityBo.getTenantWalletMap());
            } else if (PaymentRoleEnum.MERCHANT.getCode().equals(payment.getTargetRole())) {
                addRelatedMerchant(payment.getTargetMerchantId(), relatedEntityBo.getMerchantMap());
            }

            if (MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType().equals(payment.getBalanceType())) {
                MerchantUserAccount merchantUserAccount = merchantUserAccountMapperManager.findOneByMerchantUserId(payment.getSourceUserId());
                Assert.validateNull(merchantUserAccount, "用户账户不能为空merchantUserAccount" + payment.getSourceUserId());
                relatedEntityBo.getMerchantUserAccountMap().put(merchantUserAccount.getMerchantUserId(), merchantUserAccount);
            }

            if (PaymentRoleEnum.TENANT_SUP.getCode().equals(payment.getSourceRole())
                && PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getTargetRole())
            ) {
                TenantDisWallet tenantDisWallet = tenantDistributorWalletManager.getByDisAndSupTenantId(payment.getTargetTenantId(), payment.getSourceTenantId());
                String key = tenantDisWallet.getDisTenantId() + "-" + tenantDisWallet.getSupTenantId();
                relatedEntityBo.getTenantDisWalletMap().put(key, tenantDisWallet);
            }

            if (PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getSourceRole())
                && PaymentRoleEnum.TENANT_SUP.getCode().equals(payment.getTargetRole())
            ) {
                TenantDisWallet tenantDisWallet = tenantDistributorWalletManager.getByDisAndSupTenantId(payment.getSourceTenantId(), payment.getTargetTenantId());
                String key = tenantDisWallet.getDisTenantId() + "-" + tenantDisWallet.getSupTenantId();
                relatedEntityBo.getTenantDisWalletMap().put(key, tenantDisWallet);
            }
        }

        List<TransactionEntry> transactionEntryList = transactionEntryManage.findAllByTradeNos(paymentTradeNos);
        relatedEntityBo.setTransactionEntryList(transactionEntryList);

        return relatedEntityBo;
    }

    public TransactionOperateRelatedEntityBo genRelatedEntityByPayments(List<Long> paymentIds) {
        TransactionOperateRelatedEntityBo relatedEntityBo = new TransactionOperateRelatedEntityBo();

        List<Payment> payments = paymentMapperManager.listByIds(paymentIds);
        relatedEntityBo.setWantOperatePayments(payments);

        List<String> paymentTradeNos = new ArrayList<>();
        for (Payment payment : relatedEntityBo.getWantOperatePayments()) {
            paymentTradeNos.add(payment.getTradeNo());
            if (PaymentRoleEnum.TENANT.getCode().equals(payment.getSourceRole())) {
                addRelatedTenantWallet(payment.getSourceTenantId(), relatedEntityBo.getTenantWalletMap());
            } else if (PaymentRoleEnum.MERCHANT.getCode().equals(payment.getSourceRole())) {
                addRelatedMerchant(payment.getSourceMerchantId(), relatedEntityBo.getMerchantMap());
            }
            if (PaymentRoleEnum.TENANT.getCode().equals(payment.getTargetRole())) {
                addRelatedTenantWallet(payment.getTargetTenantId(), relatedEntityBo.getTenantWalletMap());
            } else if (PaymentRoleEnum.MERCHANT.getCode().equals(payment.getTargetRole())) {
                addRelatedMerchant(payment.getTargetMerchantId(), relatedEntityBo.getMerchantMap());
            }
            if (BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType().equals(payment.getBalanceType())) {
                MerchantUserAccount merchantUserAccount = merchantUserAccountMapperManager.findOneByMerchantUserId(payment.getSourceUserId());
                Assert.validateNull(merchantUserAccount, "用户账户不能为空merchantUserAccount" + payment.getSourceUserId());
                relatedEntityBo.getMerchantUserAccountMap().put(merchantUserAccount.getMerchantUserId(), merchantUserAccount);
            }

            if (PaymentRoleEnum.TENANT_SUP.getCode().equals(payment.getSourceRole())
                && PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getTargetRole())
            ) {
                TenantDisWallet tenantDisWallet = tenantDistributorWalletManager.getByDisAndSupTenantId(payment.getTargetTenantId(), payment.getSourceTenantId());
                String key = tenantDisWallet.getDisTenantId() + "-" + tenantDisWallet.getSupTenantId();
                relatedEntityBo.getTenantDisWalletMap().put(key, tenantDisWallet);
            }

            if (PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getSourceRole())
                && PaymentRoleEnum.TENANT_SUP.getCode().equals(payment.getTargetRole())
            ) {
                TenantDisWallet tenantDisWallet = tenantDistributorWalletManager.getByDisAndSupTenantId(payment.getSourceTenantId(), payment.getTargetTenantId());
                String key = tenantDisWallet.getDisTenantId() + "-" + tenantDisWallet.getSupTenantId();
                relatedEntityBo.getTenantDisWalletMap().put(key, tenantDisWallet);
            }
        }

        List<TransactionEntry> transactionEntryList = transactionEntryManage.findAllByTradeNos(paymentTradeNos);
        relatedEntityBo.setTransactionEntryList(transactionEntryList);

        return relatedEntityBo;
    }


    public Payment processPayment(Long paymentId) {
        Payment payment = paymentMapperManager.getById(paymentId);
        String tradeNo = payment.getTradeNo();
        String tradeLockKey = PaymentService.tradeLockKey(tradeNo);
        try {
            if (!lockUtil.tryLock(tradeLockKey, PaymentService.TRADE_WAIT_LOCK_MILLISECOND, PaymentService.TRADE_LOCK_MILLISECOND)) {
                throw new BusinessException("支付订单加锁失败，请稍后再尝试支付");
            }
            TransactionOperateRelatedEntityBo relatedEntity = genRelatedEntityByPayments(Collections.singletonList(paymentId));
            log.info("payment transaction related entity info={}", JSON.toJSONString(relatedEntity));
            for (Payment pay : relatedEntity.getWantOperatePayments()) {
                if (BasePoConstant.YES.equals(payment.getBalanceOperateFinish())) {
                    continue;
                }

                IPayment payImpl = PaymentPayFactory.getPayImpl(pay.getMethod());
                if (payImpl == null) {
                    throw new BusinessException("数据异常，不存在对应的Payment支付逻辑");
                }
                payImpl.operate(pay, relatedEntity);
            }

            transactionDbService.saveAfterOperateFinish(relatedEntity, tradeNo, payment.isMainTransaction());
            sendTenantDisBalanceChangeMsg(relatedEntity.getTenantDisBalanceLogs());

            return relatedEntity.getWantOperatePayments().stream().filter(payment1 -> paymentId.equals(payment1.getId())).findFirst().get();
        } catch (BusinessException e) {
            log.error("pay and bill business exception", e);
            throw e;
        } catch (Exception e) {
            throw e;
        } finally {
            lockUtil.unlock(tradeLockKey);
        }
    }


    public void sendTenantDisBalanceChangeMsg(List<TenantDisBalanceLog> balanceLogs) {
        if (CollUtil.isEmpty(balanceLogs)) {
            return;
        }
        List<TenantDisBalanceChangeMsg> msgs = new ArrayList<>();
        for (TenantDisBalanceLog balanceLog : balanceLogs) {
            TenantDisBalanceChangeMsg msg = balanceLog.geneerateTenantDisBalanceChangeMsg();
            if (msg != null) {
                msgs.add(msg);
            }
        }
        rocketMQTemplate.sendNormalBatchAfterCommit(RocketMqTopicConst.EVENT_PAYMENT, PaymentTagConst.TENANT_DIS_BALANCE_CHANGE_TAG, msgs);
    }


    public Refund processRefund(Long refundId) {
        Refund funde = refundMapperManager.getById(refundId);
        String tradeNo = funde.getTradeNo();
        String tradeLockKey = PaymentService.tradeLockKey(tradeNo);
        try {
            if (!lockUtil.tryLock(tradeLockKey, PaymentService.TRADE_WAIT_LOCK_MILLISECOND, PaymentService.TRADE_LOCK_MILLISECOND)) {
                throw new BusinessException("退款订单加锁失败，请稍后再尝试支付");
            }
            TransactionOperateRelatedEntityBo relatedEntity = genRelatedEntityByRefunds(Collections.singletonList(refundId));
            log.info("refund transaction related entity info={}", JSON.toJSONString(relatedEntity));
            for (Refund pay : relatedEntity.getWantOperateRefunds()) {
                if (BasePoConstant.YES.equals(funde.getBalanceOperateFinish())) {
                    continue;
                }
                IRefund impl = RefundFactory.getImpl(pay.getPayMethod());
                if (impl == null) {
                    throw new BusinessException("数据异常，不存在对应的Refund退款逻辑");
                }
                impl.doRefund(pay, relatedEntity);
            }
            transactionDbService.saveAfterOperateFinish(relatedEntity, tradeNo, funde.isMainTransaction());
            sendTenantDisBalanceChangeMsg(relatedEntity.getTenantDisBalanceLogs());
            return relatedEntity.getWantOperateRefunds().stream().filter(payment1 -> refundId.equals(payment1.getId())).findFirst().get();
        } catch (BusinessException e) {
            log.error("pay and bill business exception", e);
            throw e;
        } catch (Exception e) {
            throw e;
        } finally {
            lockUtil.unlock(tradeLockKey);
        }
    }

    private TransactionCreateEntityBo create(TradeNoEnum noEnum, MultiTransactionCreateParam param) {
        log.info("transaction createForPay param={}", JSON.toJSONString(param));
        transactionCheckService.validateMultiTransactionCreateParam(param);
        String tradeNo = TradeNoUtil.generateTradeNo(noEnum);

        TransactionPayRelatedEntityBo relatedEntityBo = new TransactionPayRelatedEntityBo();

        List<Payment> paymentList = new ArrayList<>();
        List<Refund> refundList = new ArrayList<>();
        List<PaymentOnlineConfig> paymentOnlineConfigs = new ArrayList<>();

        for (PaymentParam paymentParam : param.getPaymentList()) {
            IPayCreate iPayCreate = PaymentPayFactory.getPayImpl(paymentParam.getMethod());
            if (iPayCreate == null) {
                throw new BusinessException("数据异常，不存在对应的Payment处理逻辑");
            }
            Payment payment = iPayCreate.generate(tradeNo, paymentParam, relatedEntityBo);
            if (payment.getPaymentOnlineConfig() != null) {
                paymentOnlineConfigs.add(payment.getPaymentOnlineConfig());
            }
            paymentList.add(payment);
        }
        for (RefundParam refundParam : param.getRefundList()) {
            IRefund impl = RefundFactory.getImpl(refundParam.getPayMethod());
            if (impl == null) {
                throw new BusinessException("数据异常，不存在对应的Payment处理逻辑");
            }
            Refund funde = impl.generate(tradeNo, refundParam, relatedEntityBo);
            refundList.add(funde);
        }

        List<TransactionEntry> transactionEntries = BeanUtil.copyToList(param.getTransactionEntryList(), TransactionEntry.class);

        TransactionCreateEntityBo createEntityBo = new TransactionCreateEntityBo()
            .setPaymentList(paymentList)
            .setRefundList(refundList)
            .setPaymentOnlineConfigs(paymentOnlineConfigs)
            .setTransactionEntries(transactionEntries);

        return transactionDbService.saveForCreateTransaction(createEntityBo);
    }

    public Payment createForPay(MultiTransactionCreateParam param) {
        TransactionCreateEntityBo result = create(TradeNoEnum.PAY, param);
        return result.getPaymentList().stream()
            .filter(p -> p.getTradeNo().equals(p.getParentTradeNo()))
            .findFirst()
            .orElse(null);
    }

    public Refund createForRefund(MultiTransactionCreateParam param) {
        TransactionCreateEntityBo result = create(TradeNoEnum.REFUND, param);
        return result.getRefundList().stream()
            .filter(p -> p.getTradeNo().equals(p.getParentTradeNo()))
            .findFirst()
            .orElse(null);
    }

    private void sendSubTransactionTaskMessages(List<SubTransactionTask> subTransactionTasks) {
        if (CollUtil.isEmpty(subTransactionTasks)) {
            return;
        }

        List<SubTransactionMsg> messages = new ArrayList<>();
        for (SubTransactionTask subTransactionTask : subTransactionTasks) {
            SubTransactionMsg msg = new SubTransactionMsg()
                .setSubTransactionId(subTransactionTask.getId())
                .setCreateTime(String.valueOf(subTransactionTask.getCreatedAt()));
            messages.add(msg);
        }

        // 批量发送消息
        rocketMQTemplate.sendNormalBatch(
            RocketMqTopicConst.EVENT_PAYMENT,
            PaymentTagConst.SUB_TRANSACTION_OPERATE,
            messages
        );
    }

}
