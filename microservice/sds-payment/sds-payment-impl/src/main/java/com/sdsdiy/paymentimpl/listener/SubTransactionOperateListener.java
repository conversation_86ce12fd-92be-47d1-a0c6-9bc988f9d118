package com.sdsdiy.paymentimpl.listener;

import com.sdsdiy.common.dtoconvert.annotation.LogTraceId;
import com.sdsdiy.core.mq.MqListenerRegisterCondition;
import com.sdsdiy.core.mq.annotation.RocketMQMessageListener;
import com.sdsdiy.core.mq.core.RocketMQListener;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.payment.PaymentConsumerConst;
import com.sdsdiy.core.mq.queue.payment.PaymentTagConst;
import com.sdsdiy.core.mq.support.RocketMQUtil;
import com.sdsdiy.paymentapi.dto.msg.OrderFinanceUpdateMsg;
import com.sdsdiy.paymentapi.dto.msg.SubTransactionMsg;
import com.sdsdiy.paymentimpl.service.transaction.TransactionPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Conditional(MqListenerRegisterCondition.class)
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = RocketMqTopicConst.EVENT_PAYMENT,
    consumerGroup = PaymentConsumerConst.GID_SUB_TRANSACTION_OPERATE,
    tag = PaymentTagConst.SUB_TRANSACTION_OPERATE)
public class SubTransactionOperateListener implements RocketMQListener {

    private final TransactionPayService transactionPayService;

    @Override
    public void consumeMsg(MessageView messageView) {
        SubTransactionMsg dto = RocketMQUtil.getBodyBean(messageView, SubTransactionMsg.class);
    }

    @LogTraceId
    public void onMessage(OrderFinanceUpdateMsg msg) {
    }
}
