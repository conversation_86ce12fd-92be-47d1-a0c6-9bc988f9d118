package com.sdsdiy.paymentimpl.listener;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.common.dtoconvert.annotation.LogTraceId;
import com.sdsdiy.core.mq.MqListenerRegisterCondition;
import com.sdsdiy.core.mq.annotation.RocketMQMessageListener;
import com.sdsdiy.core.mq.core.RocketMQListener;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.payment.PaymentConsumerConst;
import com.sdsdiy.core.mq.queue.payment.PaymentTagConst;
import com.sdsdiy.core.mq.support.RocketMQUtil;
import com.sdsdiy.paymentapi.dto.msg.OrderFinanceUpdateMsg;
import com.sdsdiy.paymentimpl.service.finance.OrderFinanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Conditional(MqListenerRegisterCondition.class)
@RequiredArgsConstructor
@RocketMQMessageListener(topic = RocketMqTopicConst.EVENT_PAYMENT,
    consumerGroup = PaymentConsumerConst.GID_COSUMER_ORDER_FINANCE_UPDATE,
    tag = PaymentTagConst.ORDER_FINANCE_UPDATE_TOPIC)
public class OrderFinanceUpdateListener implements RocketMQListener {


    private final OrderFinanceService orderFinanceService;
    @Override
    public void consumeMsg(MessageView messageView) {
        OrderFinanceUpdateMsg dto = RocketMQUtil.getBodyBean(messageView, OrderFinanceUpdateMsg.class);
        onMessage(dto);
    }

    @LogTraceId
    public void onMessage(OrderFinanceUpdateMsg msg) {
        log.info("orderFinance update msg receive: {}", JSON.toJSONString(msg));
        orderFinanceService.processFinanceMsg(msg);
        log.info("orderFinance update success orderNo={}", msg.getOrderNo());
    }
}
