package com.sdsdiy.paymentimpl.manager;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.mapper.PaymentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 支付记录表(Payment)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-29 17:30:06
 */
@Service
@Slf4j
@DS("master")
public class PaymentMapperManager extends ServiceImpl<PaymentMapper, Payment> {

    public List<Payment> findAllByIds(List<Long> ids) {
        return lambdaQuery()
            .in(Payment::getId, ids)
            .list();
    }

    public List<Payment> findAllByTradeNo(String tradeNo) {
        return lambdaQuery()
            .in(Payment::getTradeNo, tradeNo)
            .list();
    }


    public Payment findOneByTradeNoAndMethod(String tradeNo, String payMethod) {
        return lambdaQuery()
            .eq(Payment::getTradeNo, tradeNo)
            .eq(Payment::getMethod, payMethod)
            .one();
    }

    public List<Payment> findAllByParentTradeNo(String parentTradeNo) {
        return lambdaQuery()
            .eq(Payment::getParentTradeNo, parentTradeNo)
            .ne(Payment::getTradeNo, parentTradeNo) // 排除主交易本身
            .list();
    }

}
