package com.sdsdiy.paymentimpl.service.transaction.balance;

import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.paymentapi.constant.BalanceOperator;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import com.sdsdiy.paymentimpl.bo.BalanceOperatorBo;
import com.sdsdiy.paymentimpl.bo.TransactionOperateRelatedEntityBo;
import com.sdsdiy.paymentimpl.bo.WalletType;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.entity.Refund;
import com.sdsdiy.paymentimpl.entity.TenantDisBalanceLog;
import com.sdsdiy.paymentimpl.entity.TenantDisWallet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
@RequiredArgsConstructor
public class TenantDisBalanceOperate extends AbstractBalanceOperate {

    public static TenantDisBalanceLog calculateBalanceAndGenLog(String purpose, TenantDisWallet tenantWallet, BalanceOperatorBo operatorBo) {
        BigDecimal originBalance = tenantWallet.getBalance();
        BigDecimal originGiftMoney = tenantWallet.getGiftMoney();

        BigDecimal disposeBalance = originBalance;
        BigDecimal disposeGiftMoney = originGiftMoney;
        if (BalanceOperator.ADD.equals(operatorBo.getOperatorEnum())) {
            disposeBalance = tenantWallet.getBalance().add(operatorBo.getChangeBalance());
            disposeGiftMoney = tenantWallet.getGiftMoney().add(operatorBo.getChangeGiftMoney());
        } else if (BalanceOperator.SUBTRACT.equals(operatorBo.getOperatorEnum())) {
            disposeBalance = tenantWallet.getBalance().subtract(operatorBo.getChangeBalance());
            disposeGiftMoney = tenantWallet.getGiftMoney().subtract(operatorBo.getChangeGiftMoney());
        }

        tenantWallet.setBalance(disposeBalance);
        tenantWallet.setGiftMoney(disposeGiftMoney);

        return new TenantDisBalanceLog()
            .setPurpose(purpose)
            .setBizNo(operatorBo.getBizNo())
            .setRemark(operatorBo.getRemark())
            .setSupTenantId(tenantWallet.getSupTenantId())
            .setDisTenantId(tenantWallet.getDisTenantId())
            .setDisposeBalance(disposeBalance)
            .setDisposeBonus(disposeGiftMoney)
            .setOriginBalance(originBalance)
            .setOriginBonus(originGiftMoney)
            .setChangedBalance(operatorBo.getChangeBalance())
            .setChangedBonus(operatorBo.getChangeGiftMoney())
            .setTradeNo(operatorBo.getTradeNo())
            .setCreatedAt(System.currentTimeMillis());
    }

    @Override
    public String getRole() {
        return PaymentRoleEnum.TENANT_DIS.getCode();
    }

    @Override
    public void doForRefund(WalletType walletType, BalanceOperatorBo balanceOperatorBo, TransactionOperateRelatedEntityBo relatedEntity) {
        String tradeNo = balanceOperatorBo.getTradeNo();
        Refund current = relatedEntity.getWantOperateRefunds().stream()
            .filter(refund -> tradeNo.equals(refund.getTradeNo()))
            .findFirst()
            .orElse(null);
        if (current == null) {
            throw new BusinessException("Refund数据异常");
        }

        String key = walletType.getDisTenantId() + "-" + walletType.getSupTenantId();
        TenantDisWallet tenantDisWallet = relatedEntity.getTenantDisWalletMap().get(key);
        if (tenantDisWallet == null) {
            throw new BusinessException("未找到租户分销钱包信息，tenantId: " + key);
        }

        TenantDisBalanceLog balanceLog = calculateBalanceAndGenLog(current.getPurposeType(), tenantDisWallet, balanceOperatorBo);
        relatedEntity.getTenantDisBalanceLogs().add(balanceLog);

        this.generateBillForRefund(current, balanceLog, relatedEntity);
    }

    @Override
    public void doForPayment(WalletType walletType, BalanceOperatorBo balanceOperatorBo, TransactionOperateRelatedEntityBo relatedEntity) {
        String tradeNo = balanceOperatorBo.getTradeNo();
        Payment currentPayment = relatedEntity.getWantOperatePayments().stream()
            .filter(payment -> tradeNo.equals(payment.getTradeNo()))
            .findFirst()
            .orElse(null);
        if (currentPayment == null) {
            throw new BusinessException("Payment数据异常");
        }

        String key = walletType.getDisTenantId() + "-" + walletType.getSupTenantId();
        TenantDisWallet tenantDisWallet = relatedEntity.getTenantDisWalletMap().get(key);
        if (tenantDisWallet == null) {
            throw new BusinessException("未找到租户分销钱包信息：" + key);
        }
        TenantDisBalanceLog balanceLog = handleTenantDisBalance(currentPayment.getPurposeType(), balanceOperatorBo, relatedEntity, tenantDisWallet);

        this.generateBillForPayment(currentPayment, balanceLog, relatedEntity);
    }

    private TenantDisBalanceLog handleTenantDisBalance(
        String purpose,
        BalanceOperatorBo balanceOperatorBo,
        TransactionOperateRelatedEntityBo relatedEntity,
        TenantDisWallet tenantDisWallet
    ) {
        TenantDisBalanceLog balanceLog = calculateBalanceAndGenLog(purpose, tenantDisWallet, balanceOperatorBo);
        relatedEntity.getTenantDisBalanceLogs().add(balanceLog);
        return balanceLog;
    }

}
