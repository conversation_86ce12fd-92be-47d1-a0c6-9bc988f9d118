package com.sdsdiy.paymentimpl.entity;


import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.sdsdiy.paymentapi.constant.PaymentMethodEnum;
import com.sdsdiy.paymentapi.constant.PaymentStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import javafx.util.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "payment")
@Accessors(chain = true)
public class Payment {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String bizNo;
    private String tradeNo;
    private String parentTradeNo;

    private String method;

    private String title;

    private String imgUrl;

    private Integer status;
    
    private Long createdTime;
    private Long payTime;
    private Long expiredTime;

    private BigDecimal totalAmount;
    private BigDecimal balance;
    private BigDecimal bonus;

    private String sourceRole;
    private Long sourceTenantId;
    private Long sourceMerchantId;
    private Long sourceUserId;
    private Boolean sourceOpenOnlinePay;

    private Boolean targetOpenOnlinePay;
    private Long targetTenantId;
    private Long targetMerchantId;
    private String targetRole;

    @TableField("merchant_balance_used_type")
    private Integer balanceType;

    private Integer balanceOperateFinish;
    private Integer billType;

    private String purposeType;
    private String detailPurpose;

    private Long operateUserId;
    private String operateRole;

    /**
     * 操作对象角色
     * {@link com.sdsdiy.paymentapi.constant.PaymentRoleEnum}
     */
    private String operateTargetRole;

    /*** 操作对象角色ID */
    private Long operateTargetRoleId;

    @Version
    private Integer lockVersion;

    private String appId;

    private String remark;

    @ApiModelProperty("支付渠道，比如微信，支付宝，银联")
    private String payChannel;

    @ApiModelProperty("第三方订单号，比如支付宝订单号，拉卡拉订单号")
    private String alipayTradeNo;

    @ApiModelProperty("保存第三方应用的信息")
    @TableField(exist = false)
    private PaymentOnlineConfig paymentOnlineConfig;


    public boolean needCheckPaidStatus() {
        if (!PaymentStatusEnum.WAIT_PAY.getStatus().equals(this.status)) {
            return false;
        }
        if (PaymentMethodEnum.ALI_PAY.getCode().equals(this.method)
            && PaymentMethodEnum.LAKALA.getCode().equals(this.method)) {
            return false;
        }

        return true;
    }

    public boolean isMainTransaction() {
        return CharSequenceUtil.isBlank(this.getParentTradeNo());
    }


    public void setTradeNos(Pair<String, String> tradeNos) {
        this.parentTradeNo = tradeNos.getKey();
        this.tradeNo = tradeNos.getValue();
    }
}
