package com.sdsdiy.paymentimpl.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SubTransaction {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String parentTradeNo;
    private Long parentPaymentId;
    private Long parentRefundId;
    private String tradeNo;
    private Long refundId;
    private Long paymentId;

    private Long createdAt;
    private Integer processCount;
    private Long successAt;
    private String remark;
}
