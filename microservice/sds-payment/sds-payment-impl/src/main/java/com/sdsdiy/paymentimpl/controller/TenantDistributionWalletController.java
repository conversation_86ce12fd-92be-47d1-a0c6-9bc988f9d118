package com.sdsdiy.paymentimpl.controller;

import cn.hutool.core.bean.BeanUtil;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.paymentapi.api.TenantDistributionWalletApi;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import com.sdsdiy.paymentapi.dto.TenantOnlinePayDto;
import com.sdsdiy.paymentapi.dto.wallet.TenantDisWalletCountDTO;
import com.sdsdiy.paymentapi.param.TenantDistributionQueryParam;
import com.sdsdiy.paymentapi.param.distribution.TenantDisWalletRechargeParam;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.manager.TenantDistributorWalletManager;
import com.sdsdiy.paymentimpl.service.TenantDistributionWalletService;
import com.sdsdiy.paymentimpl.service.TenantPaymentConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Slf4j
public class TenantDistributionWalletController implements TenantDistributionWalletApi {
    private final TenantDistributorWalletManager tenantDistributorWalletManager;
    private final TenantDistributionWalletService tenantDistributionWalletService;
    private final TenantPaymentConfigService tenantPaymentConfigService;


    @Override
    public List<TenantDistributorWalletDto> queryWallets(TenantDistributionQueryParam param) {
        return this.tenantDistributionWalletService.queryWallets(param);
    }

    @Override
    public PaymentDto recharge(TenantDisWalletRechargeParam rechargeParam) {
        long supTenantId = rechargeParam.getSupTenantId();
        TenantOnlinePayDto config = tenantPaymentConfigService.getConfig(supTenantId);
        if (Boolean.FALSE.equals(config.getOpened())) {
            throw new BusinessException("租户未开启线上收款，请联系管理员");
        }

        Payment payment = this.tenantDistributionWalletService.createForRechange(rechargeParam);
        return BeanUtil.toBean(payment, PaymentDto.class);
    }

    @Override
    public List<TenantDistributorWalletDto> findAllBalance() {
        return this.tenantDistributionWalletService.findAllBalance();
    }

    @Override
    public TenantDisWalletCountDTO sumBySupTenant(Long tenantId) {
        return this.tenantDistributorWalletManager.sumBySupTenant(tenantId);
    }

    @Override
    public TenantDisWalletCountDTO sumByDisTenant(Long tenantId) {
        return this.tenantDistributorWalletManager.sumByDisTenant(tenantId);
    }
}
