package com.sdsdiy.paymentimpl.service.transaction.balance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.sdsdiy.paymentapi.constant.PaymentBillTypeEnum;
import com.sdsdiy.paymentapi.constant.PurposeType;
import com.sdsdiy.paymentapi.constant.TransactionEntryTypeEnum;
import com.sdsdiy.paymentimpl.bo.TransactionOperateRelatedEntityBo;
import com.sdsdiy.paymentimpl.entity.MerchantBill;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.entity.Refund;
import com.sdsdiy.paymentimpl.entity.TransactionEntry;
import com.sdsdiy.paymentimpl.service.MerchantBillGenerateService;
import com.sdsdiy.paymentimpl.service.transaction.IBalanceLog;
import com.sdsdiy.paymentimpl.service.transaction.IBalanceOperate;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
public abstract class AbstractBalanceOperate implements IBalanceOperate {


    public static MerchantBill generateBillByPayment(
        Payment payment,
        IBalanceLog balanceLog,
        String disposeRole
    ) {
        MerchantBill merchantBill = MerchantBillGenerateService.generateBaseBillByPayment(payment);
        merchantBill.setChangedBalance(balanceLog.getChangedBalance());
        merchantBill.setChangedGift(balanceLog.getChangedBonus());
        merchantBill.setDisposeBalanceAndBonus(disposeRole, balanceLog.getDisposeBalance(), balanceLog.getDisposeBonus());

        return merchantBill;
    }

    public static MerchantBill generateBillByRefund(
        Refund refund,
        IBalanceLog balanceLog,
        String disposeRole
    ) {
        MerchantBill merchantBill = MerchantBillGenerateService.generateBaseBillByRefund(refund);
        merchantBill.setChangedBalance(balanceLog.getChangedBalance());
        merchantBill.setChangedGift(balanceLog.getChangedBonus());
        merchantBill.setDisposeBalanceAndBonus(disposeRole, balanceLog.getDisposeBalance(), balanceLog.getDisposeBonus());

        return merchantBill;
    }


    public void generateBillForPayment(
        Payment payment,
        IBalanceLog balanceLog,
        TransactionOperateRelatedEntityBo relatedEntity
    ) {
        Integer billType = payment.getBillType();
        if (PaymentBillTypeEnum.NONE.getStatus().equals(billType)) {
            return;
        }
        if (PaymentBillTypeEnum.DIRECT.getStatus().equals(billType)) {
            MerchantBill bill = generateBillByPayment(payment, balanceLog, this.getRole());
            relatedEntity.getMerchantBills().add(bill);
        } else if (PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus().equals(billType)) {
            List<MerchantBill> bills = this.generateBillByTransactionEntry(payment, balanceLog, relatedEntity);
            relatedEntity.getMerchantBills().addAll(bills);
        }
    }

    public void generateBillForRefund(Refund payment,
                                      IBalanceLog balanceLog,
                                      TransactionOperateRelatedEntityBo relatedEntity) {
        Integer billType = payment.getBillType();
        if (PaymentBillTypeEnum.NONE.getStatus().equals(billType)) {
            return;
        }
        if (PaymentBillTypeEnum.DIRECT.getStatus().equals(billType)) {
            MerchantBill bill = generateBillByRefund(payment, balanceLog, this.getRole());
            relatedEntity.getMerchantBills().add(bill);
        } else if (PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus().equals(billType)) {
            List<MerchantBill> bills = this.generateBillByTransactionEntry(payment, balanceLog, relatedEntity);
            relatedEntity.getMerchantBills().addAll(bills);
        }
    }


    public List<MerchantBill> generateBillByTransactionEntry(
        Payment payment,
        IBalanceLog balanceLog,
        TransactionOperateRelatedEntityBo relatedEntity
    ) {
        if (CollUtil.isEmpty(relatedEntity.getTransactionEntryList())) {
            return Collections.emptyList();
        }

        List<MerchantBill> bills = new ArrayList<>();

        String currentTrade = payment.getTradeNo();
        BigDecimal originBalance = balanceLog.getOriginBalance();
        BigDecimal originBonus = balanceLog.getOriginBonus();

        for (TransactionEntry entry : relatedEntity.getTransactionEntryList()) {
            if (!currentTrade.equals(entry.getTradeNo())) {
                continue;
            }
            MerchantBill merchantBill = MerchantBillGenerateService.generateBaseBillByPayment(payment);

            BigDecimal changeBalance = entry.getBalance();
            BigDecimal changeBonus = entry.getBonus();

            BigDecimal disposeBalance = BigDecimal.ZERO;
            BigDecimal disposeBonus = BigDecimal.ZERO;

            if (!balanceLog.amountChanged()) {
                disposeBalance = originBalance;
                disposeBonus = originBonus;
            } else {
                if (TransactionEntryTypeEnum.PAY.getValue().equals(entry.getType())) {
                    disposeBalance = NumberUtil.sub(originBalance, changeBalance);
                    disposeBonus = NumberUtil.sub(originBonus, changeBonus);
                } else if (TransactionEntryTypeEnum.REFUND.getValue().equals(entry.getType())) {
                    disposeBalance = NumberUtil.add(originBalance, changeBalance);
                    disposeBonus = NumberUtil.add(originBonus, changeBonus);
                }
            }

            // 当前合并交易后是payment，但是单笔交易entry又是退款，需要变更账单类型
            if (TransactionEntryTypeEnum.REFUND.getValue().equals(entry.getType())) {
                merchantBill.setPurposeType(PurposeType.REFUND.getCode());
            }

            merchantBill.setChangedBalance(changeBalance);
            merchantBill.setChangedGift(changeBonus);
            merchantBill.setDisposeBalanceAndBonus(this.getRole(), disposeBalance, disposeBonus);

            bills.add(merchantBill);
        }
        return bills;
    }


    public List<MerchantBill> generateBillByTransactionEntry(
        Refund payment,
        IBalanceLog balanceLog,
        TransactionOperateRelatedEntityBo relatedEntity
    ) {
        if (CollUtil.isEmpty(relatedEntity.getTransactionEntryList())) {
            return Collections.emptyList();
        }

        List<MerchantBill> bills = new ArrayList<>();

        String currentTrade = payment.getTradeNo();
        BigDecimal originBalance = balanceLog.getOriginBalance();
        BigDecimal originBonus = balanceLog.getOriginBonus();

        for (TransactionEntry entry : relatedEntity.getTransactionEntryList()) {
            if (!currentTrade.equals(entry.getTradeNo())) {
                continue;
            }
            MerchantBill merchantBill = MerchantBillGenerateService.generateBaseBillByRefund(payment);

            BigDecimal changeBalance = entry.getBalance();
            BigDecimal changeBonus = entry.getBonus();

            BigDecimal disposeBalance = BigDecimal.ZERO;
            BigDecimal disposeBonus = BigDecimal.ZERO;

            if (!balanceLog.amountChanged()) {
                disposeBalance = originBalance;
                disposeBonus = originBonus;
            } else {
                if (TransactionEntryTypeEnum.PAY.getValue().equals(entry.getType())) {
                    disposeBalance = NumberUtil.sub(originBalance, changeBalance);
                    disposeBonus = NumberUtil.sub(originBonus, changeBonus);
                } else if (TransactionEntryTypeEnum.REFUND.getValue().equals(entry.getType())) {
                    disposeBalance = NumberUtil.add(originBalance, changeBalance);
                    disposeBonus = NumberUtil.add(originBonus, changeBonus);
                }
            }

            // 当前合并交易后是payment，但是单笔交易entry又是退款，需要变更账单类型
            if (TransactionEntryTypeEnum.REFUND.getValue().equals(entry.getType())) {
                merchantBill.setPurposeType(PurposeType.REFUND.getCode());
            }

            merchantBill.setChangedBalance(changeBalance);
            merchantBill.setChangedGift(changeBonus);
            merchantBill.setBizNo(entry.getBizNoForBill());
            merchantBill.setOrderName(entry.getTitle());
            merchantBill.setRemarks(entry.getRemark());
            merchantBill.setDisposeBalanceAndBonus(this.getRole(), disposeBalance, disposeBonus);

            bills.add(merchantBill);
        }
        return bills;
    }
}
