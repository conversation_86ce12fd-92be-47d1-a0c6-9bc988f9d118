package com.sdsdiy.paymentimpl.service.pay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sds.platform.sdk.lakala.LakalaHttpClient;
import com.sds.platform.sdk.lakala.order.LakalaOrderStatusEnum;
import com.sds.platform.sdk.lakala.order.LakalaOrderTradePayModeEnum;
import com.sds.platform.sdk.lakala.order.OrderBaseRespBody;
import com.sds.platform.sdk.lakala.order.create.OrderCreateRespData;
import com.sds.platform.sdk.lakala.order.query.OrderMasterTradeInfoDTO;
import com.sds.platform.sdk.lakala.order.query.OrderQueryRespData;
import com.sds.platform.sdk.lakala.order.query.OrderTradeInfo;
import com.sds.platform.sdk.lakala.util.LakalaAmountUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.ResponseCodeStatusEnum;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.payment.PaymentTagConst;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentapi.dto.PaymentCreateDto;
import com.sdsdiy.paymentapi.dto.lakala.split.SupplierRadioConfigBO;
import com.sdsdiy.paymentapi.dto.msg.PaymentAlipayCallbackMqMsg;
import com.sdsdiy.paymentapi.param.LakalaClientConfigParam;
import com.sdsdiy.paymentapi.param.LklLedgerDivideParam;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.lakala.LakalaPaidNotifyBaseParam;
import com.sdsdiy.paymentapi.param.lakala.PaidNotifyBaseParam;
import com.sdsdiy.paymentimpl.bo.*;
import com.sdsdiy.paymentimpl.config.LakalaConfig;
import com.sdsdiy.paymentimpl.entity.*;
import com.sdsdiy.paymentimpl.entity.po.IPlatformPaidNotify;
import com.sdsdiy.paymentimpl.entity.po.LakalaOrder;
import com.sdsdiy.paymentimpl.entity.po.LakalaOrderNotify;
import com.sdsdiy.paymentimpl.entity.po.LakalaOrderSplitInfo;
import com.sdsdiy.paymentimpl.manager.*;
import com.sdsdiy.paymentimpl.service.*;
import com.sdsdiy.paymentimpl.service.lakala.LakalaClientConfService;
import com.sdsdiy.paymentimpl.service.lakala.LakalaService;
import com.sdsdiy.paymentimpl.service.lakala.LklOrderService;
import com.sdsdiy.paymentimpl.service.transaction.IBalanceOperate;
import com.sdsdiy.paymentimpl.service.transaction.TransactionCheckService;
import com.sdsdiy.paymentimpl.service.transaction.TransactionDbService;
import com.sdsdiy.paymentimpl.service.transaction.balance.BalanceOperateFactory;
import com.sdsdiy.paymentimpl.util.TradeNoUtil;
import javafx.util.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class LakalaPaymentImpl extends AbstractPayment {

    private final BalanceOperateFactory balanceOperateFactory;
    private final LakalaConfig lakalaConfig;
    private final LakalaService lakalaService;
    private final LklOrderService lklOrderService;
    private final LakalaOrderNotifyManage lakalaOrderNotifyManage;
    private final PaymentMsgService paymentMsgService;
    private final RocketMQTemplate rocketMQTemplate;
    private final PaymentMapperManager paymentMapperManager;
    private final LakalaOrderManage lakalaOrderManage;
    private final LakalaClientConfService lakalaClientConfService;

    private static final String NOTIFY_SUCCESS_RETURN_CONTENT = "{\"code\":\"SUCCESS\",\"message\":\"SUCCESS\"}";
    private final PaymentOnlineConfigMapperManager paymentOnlineConfigMapperManager;
    private final LakalaOrderSplitInfoManage lakalaOrderSplitInfoManage;

    @Override
    @Deprecated
    public void operatePaymentAfterPaid(Payment payment, PayRelatedEntityBo relatedEntity) {
        Assert.validateFalse(payment.getSourceRole().equals(PaymentRoleEnum.MERCHANT.getCode())
            || payment.getSourceRole().equals(PaymentRoleEnum.TENANT.getCode()), "付款人信息错误"
        );

        Merchant sourceMerchant = relatedEntity.getMerchantMap().get(payment.getSourceMerchantId());
        TenantWallet sourceTenant = relatedEntity.getTenantWalletMap().get(payment.getSourceTenantId());

        boolean onlinePayValid = PaymentCheckService.checkPaymentOnlinePayValid(payment, relatedEntity);
        // 如果打开了线上支付，支付宝发起退款
        if (!onlinePayValid) {
            log.info("alipay payment open_online_pay invalid refund payment={}", payment.getId());
            throw new BusinessException("已开启线上收款，请稍后刷新重试。");
        }

        MerchantBillDisposeMoney disposeMoney = MerchantBillGenerateService.generateDisposeMoney(sourceTenant, sourceMerchant);

        // 租户充值
        if (PurposeType.RECHARGE.getCode().equals(payment.getPurposeType())
            && PaymentRoleEnum.TENANT.getCode().equals(payment.getSourceRole())
        ) {
            Assert.validateNull(sourceTenant, "付款租户不能为空");
            BalanceOperatorBo tenantBalanceOperatorBo = BalanceOperatorBo.builder()
                .bizNo(payment.getTradeNo())
                .operatorEnum(BalanceOperator.ADD)
                .tradeNo(payment.getTradeNo())
                .tenantId(payment.getSourceTenantId())
                .changeBalance(payment.getBalance())
                .changeGiftMoney(payment.getBonus())
                .build();
            TenantBalanceLog balanceLog = TenantBalanceService.calculateBalanceAndGenLog(sourceTenant, tenantBalanceOperatorBo);

            relatedEntity.getTenantBalanceLogs().add(balanceLog);

            disposeMoney.setDisposeTenantGift(balanceLog.getDisposeBonus())
                .setDisposeTenantBalance(balanceLog.getDisposeBalance());
        } else if (PurposeType.RECHARGE.getCode().equals(payment.getPurposeType())
            && PaymentRoleEnum.MERCHANT.getCode().equals(payment.getSourceRole())
        ) {
            Assert.validateNull(sourceMerchant, "付款商户不能为空");
            BalanceOperatorBo tenantBalanceOperatorBo = BalanceOperatorBo.builder()
                .bizNo(payment.getTradeNo())
                .operatorEnum(BalanceOperator.ADD)
                .tradeNo(payment.getTradeNo())
                .merchantId(payment.getSourceMerchantId())
                .changeBalance(payment.getBalance())
                .changeGiftMoney(payment.getBonus())
                .build();

            MerchantBalanceLog balanceLog = MerchantBalanceService.calculateBalanceAndGenLog(sourceMerchant, tenantBalanceOperatorBo);
            relatedEntity.getMerchantBalanceLogs().add(balanceLog);

            disposeMoney.setDisposeMerchantGift(balanceLog.getDisposeBonus())
                .setDisposeMerchantBalance(balanceLog.getDisposeBalance());
        }

        payment.setBalanceOperateFinish(BasePoConstant.YES);

        if (Boolean.TRUE.equals(relatedEntity.getRecordBills())) {
            MerchantBill merchantBill = MerchantBillGenerateService.genByPayment(payment, disposeMoney);
            relatedEntity.getMerchantBills().add(merchantBill);
        }
    }

    @Override
    public void operate(Payment payment, TransactionOperateRelatedEntityBo operateRelatedEntityBo) {
        WalletType walletType = new WalletType()
            .setWalletType(payment.getSourceRole())
            .setTenantId(payment.getSourceTenantId())
            .setMerchantId(payment.getSourceMerchantId())
            .setBalanceUsedType(payment.getBalanceType())
            .setMerchantUserId(payment.getSourceUserId());
        if (PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getSourceRole())) {
            walletType.setTenantId(0L);
            walletType.setMerchantId(0L);
            walletType.setMerchantUserId(0L);
            walletType.setDisTenantId(payment.getSourceTenantId());
            walletType.setSupTenantId(payment.getTargetTenantId());
        }

        BalanceOperatorBo balanceOperatorBo = new BalanceOperatorBo()
            .setTradeNo(payment.getTradeNo())
            .setOperatorEnum(BalanceOperator.NO_CHANGE)
            .setBillType(payment.getBillType())
            .setChangeBalance(payment.getBalance())
            .setChangeGiftMoney(payment.getBonus());
        if (PurposeType.RECHARGE.getCode().equals(payment.getPurposeType())) {
            balanceOperatorBo.setOperatorEnum(BalanceOperator.ADD);
        }

        IBalanceOperate strategy = balanceOperateFactory.getStrategy(payment.getSourceRole());
        strategy.doForPayment(walletType, balanceOperatorBo, operateRelatedEntityBo);

        payment.setBalanceOperateFinish(BasePoConstant.YES);
    }

    @Override
    public PaymentMethodEnum getMethod() {
        return PaymentMethodEnum.LAKALA;
    }

    @Override
    public Payment generate(String parentTradeNo, PaymentParam param, TransactionPayRelatedEntityBo relatedEntityBo) {
        Pair<String, String> tradeNos = TransactionDbService.genTradeNo(parentTradeNo, param.getPayType());
        String tradeNo = tradeNos.getValue();

        OnlinePayAccountChooseParam chooseParam = OnlinePayAccountChooseParam.builder()
            .targetTenantId(param.getTargetTenantId())
            .targetRole(param.getTargetRole())
            .amount(param.getBalance())
            .build();
        LakalaClientConfigParam lakalaClientConfigParam = lakalaClientConfService.chooseLakalaAccount(chooseParam);
        LakalaHttpClient lakalaHttpClient = lakalaService.getLakalaClient(lakalaClientConfigParam);

        String title = param.getTitle();
        Integer expiredSecond = param.getExpiredSecond();

        long lakalaSupportAmount = LakalaAmountUtil.realAmountToLakalaSupportAmount(param.getBalance());
        this.formatSplitRadioConfigBos(lakalaClientConfigParam.getSplitRadioConfigBos(), tradeNo, lakalaSupportAmount);
        OrderCreateRespData respData = null;
        try {
            log.info("lakala param {}", lakalaClientConfigParam);
            OrderBaseRespBody<OrderCreateRespData> resp = lklOrderService.create(lakalaHttpClient,
                lakalaClientConfigParam.getMerchantNo(),
                lakalaClientConfigParam.getTermNo(),
                tradeNo,
                lakalaSupportAmount,
                title,
                expiredSecond,
                lakalaClientConfigParam.getSplitRadioConfigBos());
            respData = resp.getRespData();
        } catch (Exception e) {
            log.error("lakala order create error {}", e.getMessage());
            throw new BusinessException(ResponseCodeStatusEnum.PAYMENT_ALIPAY_CONFIG_ERROR);
        }

        PaymentOnlineConfig onlineConfig = new PaymentOnlineConfig()
            .setPaymentConfig(getConfigJson(lakalaClientConfigParam))
            .setPaymentType(getMethod().getCode())
            .setTenantId(chooseParam.getTargetTenantId())
            .setTradeNo(tradeNo);
        Payment payment = BeanUtil.toBean(param, Payment.class);
        payment.setPaymentOnlineConfig(onlineConfig);
        payment.setTradeNos(tradeNos);
        payment.setAppId(lakalaClientConfigParam.getAppId());
        payment.setAlipayTradeNo(respData.getPayOrderNo());
        payment.setImgUrl(respData.getCounterUrl());
        payment.setTotalAmount(NumberUtil.add(payment.getBalance(), payment.getBonus()));

        TransactionCheckService.refreshPaymentSourceOpenOnlinePay(payment, relatedEntityBo);

        return payment;
    }


    @Override
    public CustomerPaidResultBo queryPaidResultFromPaidPlatform(Payment payment) {
        PaymentOnlineConfig paymentOnlineConfig = paymentOnlineConfigMapperManager.getById(payment.getId());
        LakalaHttpClient httpClient;
        Long tenantId = TenantCommonConstant.SDSDIY_TENANT_ID;
        if (paymentOnlineConfig != null) {
            httpClient = lakalaService.getConfigFromConfigJson(paymentOnlineConfig.getPaymentConfig());
            tenantId = paymentOnlineConfig.getTenantId();
        } else {
            httpClient = lakalaConfig.getHttpClient();
        }
        String lklOrderNo = payment.getAlipayTradeNo();

        OrderBaseRespBody<OrderQueryRespData> result = LklOrderService.query(httpClient, lklOrderNo);
        OrderQueryRespData respData = result.getRespData();
        saveLakalaOrder(tenantId, respData);

        boolean paid = isLakalaOrderPaid(respData);

        log.info("query paid result lklOrderNo={} paid={}", lklOrderNo, paid);

        CustomerPaidResultBo customerPaidResultBo = new CustomerPaidResultBo();
        customerPaidResultBo.setPaid(paid);

        if (paid) {
            String payMode = getLakalaOrderPayMode(respData);
            String payChannel = lakalaOrderTradePayModeToPayChannel(payMode);
            log.info("query paid result payMode={} payChannel={}", payMode, payChannel);
            customerPaidResultBo.setPayChannel(payChannel);
        }

        return customerPaidResultBo;
    }


    private void saveLakalaOrder(Long tenantId, OrderQueryRespData respData) {
        LakalaOrder lakalaOrder = new LakalaOrder();
        lakalaOrder.setLakalaOrderNo(respData.getPayOrderNo());
        lakalaOrder.setLakalaOrderStatus(respData.getOrderStatus());
        lakalaOrder.setTradeNo(respData.getOutOrderNo());
        lakalaOrder.setContent(JSON.toJSONString(respData));
        List<LakalaOrderSplitInfo> splitInfoList = lakalaOrderSplitInfoManage.getByTradeNo(respData.getOutOrderNo());
        //这样是因为新的lakala订单不分帐了，新的lakala订单，在分账定时任务中不要被获取到
        if (CollUtil.isNotEmpty(splitInfoList)) {
            lakalaOrder.setProcessLedgerAt(BasePoConstant.LONG_ONE);
            if (splitInfoList.size() > 1) {
                lakalaOrder.setIsSplitOrder(1);
            }
        }
        String lakalaTradeNo = "";
        String lakalaTradeLogNo = "";
        String lakalaAccTradeNo = "";
        String payMode = "";
        BigDecimal amount = BigDecimal.ZERO;
        List<OrderTradeInfo> orderTradeInfoList = respData.getOrderTradeInfoList();
        List<OrderMasterTradeInfoDTO> masterOrderTradeInfoList = respData.getMasterOrderTradeInfoList();

        if (CollUtil.isNotEmpty(masterOrderTradeInfoList)) {
            for (OrderMasterTradeInfoDTO tradeInfo : masterOrderTradeInfoList) {

                lakalaTradeNo = tradeInfo.getTradeNo();
                lakalaTradeLogNo = tradeInfo.getLogNo();
                lakalaAccTradeNo = tradeInfo.getAccTradeNo();
                payMode = tradeInfo.getPayMode();
                Integer tradeAmount = respData.getTotalAmount();
                amount = NumberUtil.div(tradeAmount.toString(), "100", 2);
            }

        } else if (CollUtil.isNotEmpty(orderTradeInfoList)) {
            for (OrderTradeInfo tradeInfo : orderTradeInfoList) {
                if ("S".equals(tradeInfo.getTradeStatus())) {
                    lakalaTradeNo = tradeInfo.getTradeNo();
                    lakalaTradeLogNo = tradeInfo.getLogNo();
                    lakalaAccTradeNo = tradeInfo.getAccTradeNo();
                    payMode = tradeInfo.getPayMode();

                    Integer tradeAmount = tradeInfo.getTradeAmount();
                    amount = NumberUtil.div(tradeAmount.toString(), "100", 2);
                }
            }
        }

        lakalaOrder.setLakalaTradeNo(lakalaTradeNo);
        lakalaOrder.setLakalaTradeLogNo(lakalaTradeLogNo);
        lakalaOrder.setLakalaAccTradeNo(lakalaAccTradeNo);
        lakalaOrder.setLakalaPayMode(payMode);
        lakalaOrder.setAmount(amount);
        lakalaOrder.setTenantId(tenantId);

        lakalaOrderManage.saveOrUpdateLakalaOrder(lakalaOrder);
    }


    @Override
    public IPlatformPaidNotify savePlatformNotifyParam(PaidNotifyBaseParam param) {
        LakalaPaidNotifyBaseParam lakalaPaidNotifyParam = (LakalaPaidNotifyBaseParam) param;

        Map<String, String> headers = lakalaPaidNotifyParam.getHeaders();
        String body = lakalaPaidNotifyParam.getBody();

        JSONObject bodyJson = JSON.parseObject(body);
        String payOrderNo = bodyJson.getString("pay_order_no");
        String outOrderNo = bodyJson.getString("out_order_no");

        LakalaOrderNotify lakalaOrderNotify = new LakalaOrderNotify();
        lakalaOrderNotify.setLakalaOrderNo(payOrderNo);
        lakalaOrderNotify.setTradeNo(outOrderNo);
        lakalaOrderNotify.setOperateStatus(CustomerPaidResultNotifyOperateStatus.WAITING.getStatus());
        lakalaOrderNotify.setHeaders(JSON.toJSONString(headers));
        lakalaOrderNotify.setBody(body);
        lakalaOrderNotify.setCreatedAt(System.currentTimeMillis());
        lakalaOrderNotifyManage.save(lakalaOrderNotify);

        return null;
    }

    @Override
    public boolean saveNotifyResult(ProcessPaidNotifyResultBo resultBo) {
        log.info("saveNotifyResult resultBo={}", JSON.toJSONString(resultBo));
        LakalaOrderNotify lakalaNotify = (LakalaOrderNotify) resultBo.getPaidNotify();
        lakalaNotify.setOperateStatus(resultBo.getOperateStatus());
        lakalaNotify.setRemark(resultBo.getRemark());
        lakalaOrderNotifyManage.updateById(lakalaNotify);

        if (CustomerPaidResultNotifyOperateStatus.FAIL.getStatus().equals(resultBo.getOperateStatus())) {
            return false;
        }

        Payment payment = resultBo.getPayment();
        if (CustomerPaidResultNotifyOperateStatus.PAYMENT_OPERATE.getStatus().equals(resultBo.getOperateStatus())) {
            sendLedgerMsg(payment.getTradeNo());
            return true;
        }

        PaymentAlipayCallbackMqMsg msg = PaymentMsgService.paymentToMsg(payment);
        if (CustomerPaidResultNotifyOperateStatus.PAYMENT_PAID.getStatus().equals(resultBo.getOperateStatus())) {
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PAYMENT, PaymentTagConst.ALIPAY_CALLBACK, msg);
            sendLedgerMsg(payment.getTradeNo());
            return true;
        }

        CustomerPaidResultBo customerPaidResultBo = resultBo.getCustomerPaidResultBo();
        payment.setStatus(PaymentStatusEnum.PAID.getStatus());
        payment.setPayTime(System.currentTimeMillis());
        payment.setPayChannel(customerPaidResultBo.getPayChannel());
        paymentMapperManager.updateById(payment);
        rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PAYMENT, PaymentTagConst.ALIPAY_CALLBACK, msg);
        sendLedgerMsg(payment.getTradeNo());

        return true;
    }


    private void sendLedgerMsg(String tradeNo) {
        // 存在分账信息则不发送
        List<LakalaOrderSplitInfo> splitInfoList = lakalaOrderSplitInfoManage.getByTradeNo(tradeNo);
        if (CollUtil.isNotEmpty(splitInfoList)) {
            return;
        }

        LakalaOrder oneByTradeNo = lakalaOrderManage.findOneByTradeNo(tradeNo);
        sendLedgerMsg(oneByTradeNo);
    }

    public void sendLedgerMsg(LakalaOrder oneByTradeNo) {
        if (!LakalaOrderStatusEnum.SUCCESS.getCode().equals(oneByTradeNo.getLakalaOrderStatus())) {
            log.info("lakala order not paid no send ledger msg tradeNo={}", oneByTradeNo.getTradeNo());
            return;
        }

        LklLedgerDivideParam param = new LklLedgerDivideParam();
        param.setTradeNo(oneByTradeNo.getTradeNo());
        param.setLakalaOrderNo(oneByTradeNo.getLakalaOrderNo());

        String logDate = "";

        OrderQueryRespData respData = JSON.parseObject(oneByTradeNo.getContent(), OrderQueryRespData.class);

        String logNo = "";
        List<OrderTradeInfo> orderTradeInfoList = respData.getOrderTradeInfoList();
        if (CollUtil.isNotEmpty(orderTradeInfoList)) {
            for (OrderTradeInfo tradeInfo : orderTradeInfoList) {
                if ("S".equals(tradeInfo.getTradeStatus())) {
                    logNo = tradeInfo.getLogNo();
                    logDate = tradeInfo.getTradeTime().substring(0, 8);
                    break;
                }
            }
        }
        param.setLogNo(logNo);
        param.setLogDate(logDate);

        paymentMsgService.sendLakalaLedgerMsg(param);
    }


    private static boolean isLakalaOrderPaid(OrderQueryRespData respData) {
        boolean lklTradeSuccess = false;
        List<OrderTradeInfo> orderTradeInfoList = respData.getOrderTradeInfoList();
        if (CollUtil.isNotEmpty(orderTradeInfoList)) {
            for (OrderTradeInfo tradeInfo : orderTradeInfoList) {
                if ("S".equals(tradeInfo.getTradeStatus())) {
                    lklTradeSuccess = true;
                    break;
                }
            }
        }
        return lklTradeSuccess && LakalaOrderStatusEnum.SUCCESS.getCode().equals(respData.getOrderStatus());
    }

    private static String getLakalaOrderPayMode(OrderQueryRespData respData) {
        List<OrderTradeInfo> orderTradeInfoList = respData.getOrderTradeInfoList();
        if (CollUtil.isEmpty(orderTradeInfoList)) {
            return "";
        }

        for (OrderTradeInfo tradeInfo : orderTradeInfoList) {
            if ("S".equals(tradeInfo.getTradeStatus())) {
                return tradeInfo.getPayMode();
            }
        }
        return "";
    }


    private static String lakalaOrderTradePayModeToPayChannel(String payMode) {
        log.info("detect lakala pay mode={}", payMode);
        if (LakalaOrderTradePayModeEnum.ALIPAY.getCode().equals(payMode)) {
            return PayChannelEnum.ALI_PAY.getCode();
        } else if (LakalaOrderTradePayModeEnum.WECHAT.getCode().equals(payMode)) {
            return PayChannelEnum.WECHAT.getCode();
        } else if (LakalaOrderTradePayModeEnum.UQRCODEPAY.getCode().equals(payMode)) {
            return PayChannelEnum.UNION_PAY.getCode();
        }
        return "";
    }

    @Override
    @Deprecated
    public PaymentCreateDto create(PaymentParam param) {
        if (true) {
            throw new BusinessException("代码废弃10");
        }
        OnlinePayAccountChooseParam chooseParam = OnlinePayAccountChooseParam.builder()
            .targetTenantId(param.getTargetTenantId())
            .targetRole(param.getTargetRole())
            .amount(param.getBalance())
            .build();
        LakalaClientConfigParam lakalaClientConfigParam = lakalaClientConfService.chooseLakalaAccount(chooseParam);
        LakalaHttpClient lakalaHttpClient = lakalaService.getLakalaClient(lakalaClientConfigParam);
        BigDecimal amount = param.getBalance();

        String tradeNo = "";
        String title = param.getTitle();
        Integer expiredSecond = param.getExpiredSecond();

        long lakalaSupportAmount = LakalaAmountUtil.realAmountToLakalaSupportAmount(amount);
        this.formatSplitRadioConfigBos(lakalaClientConfigParam.getSplitRadioConfigBos(), tradeNo, lakalaSupportAmount);
        try {
            log.info("lakala param {}", lakalaClientConfigParam);
            OrderBaseRespBody<OrderCreateRespData> resp = lklOrderService.create(lakalaHttpClient,
                lakalaClientConfigParam.getMerchantNo(),
                lakalaClientConfigParam.getTermNo(),
                tradeNo,
                lakalaSupportAmount,
                title,
                expiredSecond,
                lakalaClientConfigParam.getSplitRadioConfigBos());
            OrderCreateRespData respData = resp.getRespData();
            String lakalaOrderNo = respData.getPayOrderNo();

            PaymentCreateDto payment = PaymentCreateService.paramToEntity(param, getConfigJson(lakalaClientConfigParam));
            payment.setOnlinePaymentAccountTenantId(lakalaClientConfigParam.getTenantId());
            payment.setAppId(lakalaClientConfigParam.getAppId());
            payment.setAlipayTradeNo(lakalaOrderNo);
            payment.setImgUrl(respData.getCounterUrl());
            lakalaOrderSplitInfoManage.saveBatchByBos(lakalaClientConfigParam.getSplitRadioConfigBos());

            return payment;
        } catch (Exception e) {
            log.error("alipay trade response error {}", e.getMessage());
            throw new BusinessException(ResponseCodeStatusEnum.PAYMENT_ALIPAY_CONFIG_ERROR);
        }

    }

    public void formatSplitRadioConfigBos(List<SupplierRadioConfigBO> splitRadioConfigBos, String tradeNo, Long lakalaSupportAmount) {
        if (CollUtil.isEmpty(splitRadioConfigBos)) {
            return;
        }
        BigDecimal amountBigDecimal = new BigDecimal(lakalaSupportAmount);
        BigDecimal alreadySplitAmount = BigDecimal.ZERO;
        for (int i = 0; i < splitRadioConfigBos.size(); i++) {
            SupplierRadioConfigBO bo = splitRadioConfigBos.get(i);
            bo.setTradeNo(tradeNo);
            String subOrderNo = TradeNoUtil.getUniqTradeNo(32);

            bo.setOutSubOrderNo(subOrderNo);

            BigDecimal thisSubOrderAmount;

            if (i < splitRadioConfigBos.size() - 1) {
                // 前 N-1 次计算：amount * ratio / 100
                thisSubOrderAmount = amountBigDecimal.multiply(bo.getRatio())
                    .divide(new BigDecimal(100), 0, RoundingMode.DOWN);
                alreadySplitAmount = alreadySplitAmount.add(thisSubOrderAmount);

            } else {
                // 最后一次直接取剩余金额，避免因四舍五入导致总和不等于原金额
                thisSubOrderAmount = amountBigDecimal.subtract(alreadySplitAmount);
            }
            if (thisSubOrderAmount.compareTo(BigDecimal.ZERO) < 1) {
                throw new BusinessException("拉卡拉创建合单支付订单，子单的金额不能低于一分钱，请检查商户号" + bo.getUnionpayMerchantNo() + "的拉卡拉比例配置。");
            }
            bo.setSubAmount(thisSubOrderAmount.longValue());
        }
    }

    public static String getConfigJson(LakalaClientConfigParam lakalaClientConfigParam) {
        Map<String, String> map = Maps.newHashMap();
        map.put("appId", lakalaClientConfigParam.getAppId());
        map.put("serialNo", lakalaClientConfigParam.getSerialNo());
        map.put("apiPrivateKey", lakalaClientConfigParam.getApiPrivateKey());
        map.put("merchantNo", lakalaClientConfigParam.getMerchantNo());
        return JSONUtil.toJsonStr(map);
    }


    @Override
    public String adaptNotifyReturnContent(boolean success) {
        if (success) {
            return NOTIFY_SUCCESS_RETURN_CONTENT;
        }
        return "";
    }
}
