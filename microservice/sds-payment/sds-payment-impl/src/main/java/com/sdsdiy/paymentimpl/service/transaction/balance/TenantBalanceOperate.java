package com.sdsdiy.paymentimpl.service.transaction.balance;

import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import com.sdsdiy.paymentimpl.bo.BalanceOperatorBo;
import com.sdsdiy.paymentimpl.bo.TransactionOperateRelatedEntityBo;
import com.sdsdiy.paymentimpl.bo.WalletType;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.entity.Refund;
import com.sdsdiy.paymentimpl.entity.TenantBalanceLog;
import com.sdsdiy.paymentimpl.entity.TenantWallet;
import com.sdsdiy.paymentimpl.service.TenantBalanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class TenantBalanceOperate extends AbstractBalanceOperate {

    @Override
    public String getRole() {
        return PaymentRoleEnum.TENANT.getCode();
    }

    @Override
    public void doForRefund(WalletType walletType, BalanceOperatorBo balanceOperatorBo, TransactionOperateRelatedEntityBo relatedEntity) {
        String tradeNo = balanceOperatorBo.getTradeNo();
        Long tenantId = walletType.getTenantId();
        TenantWallet tenantWallet = relatedEntity.getTenantWalletMap().get(tenantId);
        if (tenantWallet == null) {
            throw new BusinessException("未找到租户钱包信息，tenantId: " + tenantId);
        }

        TenantBalanceLog balanceLog = TenantBalanceService.calculateBalanceAndGenLog(tenantWallet, balanceOperatorBo);
        relatedEntity.getTenantBalanceLogs().add(balanceLog);

        Refund current = relatedEntity.getWantOperateRefunds().stream()
            .filter(refund -> tradeNo.equals(refund.getTradeNo()))
            .findFirst()
            .orElse(null);
        if (current == null) {
            throw new BusinessException("Refund数据异常");
        }

        this.generateBillForRefund(current, balanceLog, relatedEntity);
    }

    @Override
    public void doForPayment(WalletType walletType, BalanceOperatorBo balanceOperatorBo, TransactionOperateRelatedEntityBo relatedEntity) {
        String tradeNo = balanceOperatorBo.getTradeNo();
        Long tenantId = walletType.getTenantId();
        TenantWallet tenantWallet = relatedEntity.getTenantWalletMap().get(tenantId);
        if (tenantWallet == null) {
            throw new BusinessException("未找到租户钱包信息，tenantId: " + tenantId);
        }

        TenantBalanceService.checkBalanceEnough(
            tenantWallet,
            balanceOperatorBo.getChangeBalance(),
            balanceOperatorBo.getChangeGiftMoney()
        );
        TenantBalanceLog balanceLog = TenantBalanceService.calculateBalanceAndGenLog(tenantWallet, balanceOperatorBo);
        relatedEntity.getTenantBalanceLogs().add(balanceLog);

        Payment currentPayment = relatedEntity.getWantOperatePayments().stream()
            .filter(payment -> tradeNo.equals(payment.getTradeNo()))
            .findFirst()
            .orElse(null);
        if (currentPayment == null) {
            throw new BusinessException("Payment数据异常");
        }
        this.generateBillForPayment(currentPayment, balanceLog, relatedEntity);
    }

    private TenantBalanceLog handleTenantBalance(BalanceOperatorBo balanceOperatorBo,
                                                 TransactionOperateRelatedEntityBo relatedEntity,
                                                 TenantWallet tenantWallet) {
        TenantBalanceService.checkBalanceEnough(
            tenantWallet,
            balanceOperatorBo.getChangeBalance(),
            balanceOperatorBo.getChangeGiftMoney()
        );

        TenantBalanceLog balanceLog = TenantBalanceService.calculateBalanceAndGenLog(tenantWallet, balanceOperatorBo);
        relatedEntity.getTenantBalanceLogs().add(balanceLog);
        return balanceLog;
    }

}
