package com.sdsdiy.paymentimpl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.*;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.orderapi.constant.OrderConstant;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayRecordPageResp;
import com.sdsdiy.orderapi.dto.order.OrderEsDto;
import com.sdsdiy.orderapi.dto.order.OrderEsQueryDto;
import com.sdsdiy.orderdata.enums.OrderStatus;
import com.sdsdiy.paymentapi.param.TenantDistributionQueryParam;
import com.sdsdiy.paymentapi.vo.distribution.PodDistributorVO;
import com.sdsdiy.paymentapi.vo.distribution.PodSupplierVO;
import com.sdsdiy.paymentimpl.entity.TenantDisWallet;
import com.sdsdiy.paymentimpl.feign.*;
import com.sdsdiy.paymentimpl.feign.stat.TenantDistributionOrderStatFeign;
import com.sdsdiy.paymentimpl.manager.TenantDistributorWalletManager;
import com.sdsdiy.productdata.dto.distribution.TenantAuthProductCountDTO;
import com.sdsdiy.productdata.dto.distribution.TenantDistributionProductCountDTO;
import com.sdsdiy.productdata.param.ListDistributionProductCountParam;
import com.sdsdiy.statapi.dto.OrderStatSummaryDTO;
import com.sdsdiy.statapi.dto.OrderStatSummaryParam;
import com.sdsdiy.userapi.dto.MerchantSimpleDto;
import com.sdsdiy.userapi.dto.tenant.TenantListReq;
import com.sdsdiy.userapi.dto.tenant.TenantRespDto;
import com.sdsdiy.userapi.dto.tenant.TenantSysUserDto;
import com.sdsdiy.userapi.dto.tenant.resp.TenantDTO;
import com.sdsdiy.paymentapi.param.distribution.AddDistributionReq;
import com.sdsdiy.paymentapi.vo.distribution.SaasDistributorVO;
import com.sdsdiy.paymentapi.vo.distribution.SaasSupplierAndDistributorPageVO;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class FrontTenantDistributionWalletService {
    private final TenantDistributorWalletManager tenantDistributorWalletManager;
    private final TenantFeign tenantFeign;
    private final MerchantFeign merchantFeign;
    private final ProductDistributionAuthFeign productDistributionAuthFeign;
    private final TenantDistributionWalletService tenantDistributionWalletService;
    private final TenantSysUserFeign tenantSysUserFeign;
    private final ProductFeign productFeign;
    private final OrderEsFeign orderEsFeign;
    private final OfflinePayRecordFeign offlinePayRecordFeign;
    private final TenantDistributionOrderStatFeign tenantDistributionOrderStatFeign;
    public List<BaseIdAndNameDTO> supplierList(Long distributorTenantId) {
        List<Long> supplierTenantIds = tenantDistributorWalletManager.listSupplierIdsByDistributorTenantId(distributorTenantId);
        if (CollUtil.isEmpty(supplierTenantIds)) {
            return new ArrayList<>();
        }
        return this.genRes(supplierTenantIds);
    }

    public List<BaseIdAndNameDTO> distributorList(Long supplierId) {
        List<Long> distributorTenantIds = tenantDistributorWalletManager.listDistributorIdsBySupplierTenantId(supplierId);
        if (CollUtil.isEmpty(distributorTenantIds)) {
            return new ArrayList<>();
        }
        return this.genRes(distributorTenantIds);
    }

    private List<BaseIdAndNameDTO> genRes(List<Long> tenantIds) {
        List<BaseIdAndNameDTO> res = new ArrayList<>();
        TenantListReq req = new TenantListReq();
        req.setInIds(tenantIds);
        List<TenantRespDto> tenants = tenantFeign.listDto(req);
        Map<Long, String> tenantNameMap = tenants.stream().collect(Collectors.toMap(TenantRespDto::getId, TenantRespDto::getName));
        for (Long tenantId : tenantIds) {
            BaseIdAndNameDTO dto = new BaseIdAndNameDTO();
            dto.setId(tenantId);
            dto.setName(tenantNameMap.get(tenantId));
            res.add(dto);
        }
        return res;
    }

    public PageResult<SaasSupplierAndDistributorPageVO> saasPage(String supplierName, String distributorName, Integer page, Integer size) {

        PageResult<SaasSupplierAndDistributorPageVO> res = new PageResult<>();
        res.setPage(page);
        res.setSize(size);
        res.setTotal(0L);
        List<Long> supplierTenantIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(supplierName)) {
            supplierTenantIds = getTenantIdsByNameOrTenantCodeLike(supplierName);
            if (CollUtil.isEmpty(supplierTenantIds)) {
                return res;
            }
        }
        if (StrUtil.isNotEmpty(distributorName)) {
            List<Long> distributorTenantIds = this.getTenantIdsByNameOrTenantCodeLike(distributorName);
            List<Long> tenantIds = tenantDistributorWalletManager.listSupplierIdsByDistributorTenantIds(distributorTenantIds);
            if (CollUtil.isEmpty(tenantIds)) {
                return res;
            }
            if (StrUtil.isNotEmpty(supplierName)) {
                supplierTenantIds.retainAll(tenantIds);
                if (CollUtil.isEmpty(supplierTenantIds)) {
                    return res;
                }
            } else {
                supplierTenantIds = tenantIds;
            }
        }
        TenantListReq pageSelect = new TenantListReq();
        if (CollUtil.isNotEmpty(supplierTenantIds)) {
            pageSelect.setInIds(supplierTenantIds);
        }
        //分销关系 需要过滤sdspod租户这个供应商，只可作为分销商
        pageSelect.setNotInIds(Collections.singletonList(TenantCommonConstant.SDSDIY_TENANT_ID));
        pageSelect.setPage(page);
        pageSelect.setSize(size);
        PageResultDto<TenantRespDto> dataPage = tenantFeign.pageDto(pageSelect);
        res.setTotal(dataPage.getTotalCount());
        res.setPage(page);
        res.setSize(size);
        List<TenantRespDto> records = dataPage.getList();
        List<SaasSupplierAndDistributorPageVO> pageVOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            List<Long> tenantIds = records.stream().map(TenantRespDto::getId).collect(Collectors.toList());
            List<TenantDisWallet> relList = tenantDistributorWalletManager.listBySupplierIds(tenantIds);
            tenantIds.addAll(relList.stream().map(TenantDisWallet::getDisTenantId).collect(Collectors.toList()));
            TenantListReq listReq = new TenantListReq();
            listReq.setInIds(tenantIds);
            List<TenantRespDto> tenants = tenantFeign.listDto(listReq);
            List<Long> mainMerchantIds = tenants.stream().map(TenantRespDto::getMerchantId).collect(Collectors.toList());
            List<MerchantSimpleDto> mainMerchants = merchantFeign.findSimpleByIds(new BaseListDto<>(mainMerchantIds));
            Map<Long, String> mainMerchantNameMap = mainMerchants.stream().collect(Collectors.toMap(MerchantSimpleDto::getTenantId, MerchantSimpleDto::getName, (a, b) -> b));
            List<Long> relSupplierTenantIds = relList.stream().map(TenantDisWallet::getSupTenantId).distinct().collect(Collectors.toList());
            List<Long> relDistributorTenantIds = relList.stream().map(TenantDisWallet::getDisTenantId).distinct().collect(Collectors.toList());
            Map<String, Integer> supplierAndDistributorTenantAndCountMap = new HashMap<>();
            Map<String, BigDecimal> supplierAndDistributorTenantAndBalanceMap = new HashMap<>();
            for (TenantDisWallet wallet : relList) {
                supplierAndDistributorTenantAndBalanceMap.put(
                        genSupplierAndDistributorKey(wallet.getSupTenantId(),
                                wallet.getDisTenantId()), wallet.getBalance());
            }
            if (CollUtil.isNotEmpty(relSupplierTenantIds) && CollUtil.isNotEmpty(relDistributorTenantIds)) {
                ListDistributionProductCountParam param = new ListDistributionProductCountParam();
                param.setSupplierTenantIds(relSupplierTenantIds);
                param.setDistributorTenantIds(relDistributorTenantIds);

                List<TenantAuthProductCountDTO> tenantAuthProductCountDtoList = productDistributionAuthFeign.listAuthProductCount(param);
                for (TenantAuthProductCountDTO dto : tenantAuthProductCountDtoList) {
                    supplierAndDistributorTenantAndCountMap.put(
                            genSupplierAndDistributorKey(dto.getSupplierTenantId(),
                                    dto.getDistributorTenantId()), dto.getAuthCount());
                }
                TenantDistributionQueryParam distributionQueryParam = new TenantDistributionQueryParam();
                distributionQueryParam.setSupTenantId(relSupplierTenantIds);
                distributionQueryParam.setDisTenantIds(relDistributorTenantIds);
            }
            Map<Long, List<TenantDisWallet>> supplierAndDistributorListMap = relList.stream().collect(Collectors.groupingBy(TenantDisWallet::getSupTenantId));

            List<TenantSysUserDto> tenantBossSysUsers = tenantSysUserFeign.findBossByTenantIds(tenantIds);
            Map<Long, TenantSysUserDto> tenantBossUserMap = tenantBossSysUsers.stream().collect(Collectors.toMap(TenantSysUserDto::getTenantId, Function.identity(), (a, b) -> a));
            Map<Long, TenantRespDto> nameMap = tenants.stream().collect(Collectors.toMap(TenantRespDto::getId, Function.identity()));
            List<TenantDistributionProductCountDTO> distributionProductCountList = productFeign.distributionProductByTenantIds(tenantIds);
            Map<Long, TenantDistributionProductCountDTO> distributionProductCountMap = distributionProductCountList
                    .stream()
                    .collect(Collectors.toMap(TenantDistributionProductCountDTO::getTenantId,
                            Function.identity(),
                            (a, b) -> b));
            for (TenantRespDto tenant : records) {
                SaasSupplierAndDistributorPageVO vo = new SaasSupplierAndDistributorPageVO();
                vo.setSupplierTenantId(tenant.getId());
                vo.setSupplierTenantName(tenant.getName());
                vo.setTenantNo(tenant.getTenantNo());
                TenantSysUserDto tenantSysUser = tenantBossUserMap.get(tenant.getId());
                if (Objects.nonNull(tenantSysUser)) {
                    vo.setPhone(DesensitizedUtil.mobilePhone(tenantSysUser.getPhone()));
                }

                TenantDistributionProductCountDTO tenantDistributionProductCountDTO = distributionProductCountMap.get(tenant.getId());
                if (Objects.nonNull(tenantDistributionProductCountDTO)) {
                    vo.setDistributionProductNum(tenantDistributionProductCountDTO.getDistributionProductCount());
                    vo.setTotalProductNum(tenantDistributionProductCountDTO.getProductCount());
                } else {
                    vo.setDistributionProductNum(0);
                    vo.setTotalProductNum(0);
                }
                List<SaasDistributorVO> distributorVOS = new ArrayList<>();
                List<TenantDisWallet> distributors = supplierAndDistributorListMap.get(tenant.getId());
                if (CollUtil.isNotEmpty(distributors)) {
                    for (TenantDisWallet rel : distributors) {
                        SaasDistributorVO distributorVO = new SaasDistributorVO();
                        distributorVO.setDistributorTenantId(rel.getDisTenantId());
                        TenantRespDto distributorTenant = nameMap.get(rel.getDisTenantId());
                        if (Objects.nonNull(distributorTenant)) {
                            distributorVO.setDistributorName(distributorTenant.getName());
                            distributorVO.setTenantNo(distributorTenant.getTenantNo());
                        }
                        distributorVO.setMainMerchantName(mainMerchantNameMap.get(rel.getDisTenantId()));
                        String supplierAndDistributorKey = genSupplierAndDistributorKey(rel.getSupTenantId(), rel.getDisTenantId());
                        distributorVO.setDistributionBalance(supplierAndDistributorTenantAndBalanceMap.get(supplierAndDistributorKey));
                        distributorVO.setDistributionProductNum(supplierAndDistributorTenantAndCountMap.getOrDefault(supplierAndDistributorKey, 0));
                        distributorVO.setDistributorAgreementName(rel.getDistributorAgreementName());
                        distributorVO.setDistributorAgreementUrl(rel.getDistributorAgreementUrl());
                        distributorVOS.add(distributorVO);
                    }
                }
                vo.setDistributors(distributorVOS);
                pageVOList.add(vo);
            }
        }
        res.setRecords(pageVOList);
        return res;
    }

    private List<Long> getTenantIdsByNameOrTenantCodeLike(String name) {
        if (StrUtil.isNotEmpty(name)) {
            List<TenantDTO> tenantList = tenantFeign.getByLikeNameOrTenantNo(name);

            return tenantList.stream().map(TenantDTO::getId).collect(Collectors.toList());
        }
        return null;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    public void removeDistributor(Long supplierTenantId, Long distributorTenantId, Long updateUid) {
        OrderEsQueryDto orderEsQueryDto = new OrderEsQueryDto();
        orderEsQueryDto.setDistributorList(Collections.singletonList(distributorTenantId));
        orderEsQueryDto.setTenantId(supplierTenantId);
        orderEsQueryDto.setOrderStatusList(OrderStatus.PAY_AND_NOT_END_STATUS_LIST);
        orderEsQueryDto.setIsDistribution(BasePoConstant.YES);
        orderEsQueryDto.setProductionType(OrderConstant.PRODUCTION_TYPE_PRIVATE);
        EsSearchResHelper<OrderEsDto> list = orderEsFeign.getList(orderEsQueryDto, 1, 1);
        if (list.getTotalCount() > BasePoConstant.INTEGER_ZERO) {
            String no = list.getItems().get(0).getNo();
            throw new BusinessException("当前分销商还有未完成的订单 单号 " + no);
        }
        List<OfflinePayRecordPageResp> offlinePayNotFinishRecordList = offlinePayRecordFeign.listNotFinishRecordsBySupAndDisTenantId(supplierTenantId, distributorTenantId);
        if (CollUtil.isNotEmpty(offlinePayNotFinishRecordList)) {
            throw new BusinessException("当前分销商还有未完成的补款账单");
        }

        tenantDistributionWalletService.unlinkWallet(supplierTenantId, distributorTenantId, updateUid);
        productDistributionAuthFeign.removeTargetTenantAllDistributionAuth(supplierTenantId, distributorTenantId);
    }

    public void addDistributor(AddDistributionReq req) {
        tenantDistributionWalletService.createWallet(req);
    }

    public List<BaseIdAndNameDTO> canAddDistributeTenantIds(Long supplierTenantId) {
        List<Long> existDistributorTenantIds = tenantDistributorWalletManager.listDistributorIdsBySupplierTenantId(supplierTenantId);
        existDistributorTenantIds.add(supplierTenantId);
        TenantListReq req = new TenantListReq();
        req.setNotInIds(existDistributorTenantIds);
        List<TenantRespDto> tenants = tenantFeign.listDto(req);
        List<BaseIdAndNameDTO> res = new ArrayList<>();
        for (TenantRespDto tenant : tenants) {
            BaseIdAndNameDTO dto = new BaseIdAndNameDTO();
            dto.setId(tenant.getId());
            dto.setName(tenant.getName());
            res.add(dto);
        }
        return res;
    }

    private String genSupplierAndDistributorKey(Long supplierTenantId, Long distributorTenantId) {
        return supplierTenantId + "_" + distributorTenantId;
    }

    public Integer insufficientBalanceNum(Long distributorTenantId) {

        return tenantDistributorWalletManager.insufficientBalanceNum(distributorTenantId);
    }

    public PageResult<PodSupplierVO> mySupplierPage(Long distributorTenantId,
                                                    String supplierTenantName,
                                                    String productNameOrSku,
                                                    Boolean insufficientBalance,
                                                    Long page,
                                                    Long size) {
        PageResult<PodSupplierVO> res = new PageResult<>();
        res.setPage(page);
        res.setSize(size);
        res.setTotal(0);

        List<Long> searchSupplierTenantIds = null;
        List<Long> supplierTenantIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(supplierTenantName)) {
            List<TenantDTO> tenants = tenantFeign.getByLikeNameOrTenantNo(supplierTenantName);
            if (CollUtil.isEmpty(tenants)) {
                return res;
            }
            supplierTenantIds.addAll(tenants.stream().map(TenantDTO::getId).collect(Collectors.toList()));
            searchSupplierTenantIds = supplierTenantIds;
        }
        if (StrUtil.isNotEmpty(productNameOrSku)) {
            List<Long> productParentIds = productFeign.preciseSearchParentIdsByNameOrKeyword(productNameOrSku);
            if (CollUtil.isEmpty(productParentIds)) {
                return res;
            }
            List<Long> authProductSupplierTenantIds = productDistributionAuthFeign.listSupplierTenantIdsByProduct(distributorTenantId, productParentIds);
            if (CollUtil.isEmpty(authProductSupplierTenantIds)) {
                return res;
            }
            if (StrUtil.isNotEmpty(supplierTenantName)) {
                authProductSupplierTenantIds.retainAll(supplierTenantIds);
                if (CollUtil.isEmpty(authProductSupplierTenantIds)) {
                    return res;
                }
            }
            searchSupplierTenantIds = authProductSupplierTenantIds;
        }
        Page<TenantDisWallet> walletPage = tenantDistributorWalletManager
                .lambdaQuery()
                .eq(TenantDisWallet::getDisTenantId, distributorTenantId)
                .in(CollUtil.isNotEmpty(searchSupplierTenantIds), TenantDisWallet::getSupTenantId, searchSupplierTenantIds)
                .eq(TenantDisWallet::getIsDelete, BasePoConstant.INTEGER_ZERO)
                .lt(Boolean.TRUE.equals(insufficientBalance), TenantDisWallet::getBalance, 1000L)
                .ge(Boolean.FALSE.equals(insufficientBalance), TenantDisWallet::getBalance, 1000L)
                .page(new Page<>(page, size));
        res.setTotal(walletPage.getTotal());
        res.setPage(walletPage.getPages());
        res.setSize(walletPage.getSize());
        List<TenantDisWallet> records = walletPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<PodSupplierVO> voList = new ArrayList<>();
            List<Long> supTenantIds = records.stream().map(TenantDisWallet::getSupTenantId).distinct().collect(Collectors.toList());
            TenantListReq req = new TenantListReq();
            req.setInIds(supTenantIds);;
            List<TenantRespDto> tenantRespDtos = tenantFeign.listDto(req);
            Map<Long, TenantRespDto> tenantMap = tenantRespDtos.stream().collect(Collectors.toMap(TenantRespDto::getId, Function.identity()));
            Map<Long, Integer> supplierAndCountMap = new HashMap<>();
            ListDistributionProductCountParam param = new ListDistributionProductCountParam();
            param.setSupplierTenantIds(supTenantIds);
            param.setDistributorTenantIds(Collections.singletonList(distributorTenantId));
            List<TenantAuthProductCountDTO> tenantAuthProductCountDtoList = productDistributionAuthFeign.listAuthProductCount(param);
            for (TenantAuthProductCountDTO dto : tenantAuthProductCountDtoList) {
                supplierAndCountMap.put(dto.getSupplierTenantId(), dto.getAuthCount());
            }
            OrderStatSummaryParam statQueryParam = new OrderStatSummaryParam();
            statQueryParam.setTenantId(distributorTenantId);
            statQueryParam.setQueryTenantIds(supTenantIds);
            Calendar calendar = Calendar.getInstance();
            boolean isFirstDayOfMonth = calendar.get(Calendar.DAY_OF_MONTH) == 1;

            Map<Long, Integer> finishOrderNumMap = new HashMap<>();
            Map<Long, BigDecimal> finishOrderAmountMap = new HashMap<>();
            Map<Long, Integer> periodOrderChangeMap = new HashMap<>();
            Map<Long, Integer> periodProductChangeMap = new HashMap<>();
            if (Boolean.FALSE.equals(isFirstDayOfMonth)) {
                //今天
                Date currentDate = calendar.getTime();
                //这个月第一天
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                Date firstDayOfMonth = calendar.getTime();

                // 上个月同一天
                calendar = Calendar.getInstance();
                calendar.add(Calendar.MONTH, -1);
                Date sameDayLastMonth = calendar.getTime();
                //上个月第一天
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                Date lastMonthFirstDay = calendar.getTime();
                statQueryParam.setStartTime(firstDayOfMonth);
                statQueryParam.setEndTime(currentDate);
                Map<Long, OrderStatSummaryDTO> currentMonthStat = tenantDistributionOrderStatFeign.suppStatByQueryTenantIdKeyMap(statQueryParam);
                statQueryParam.setStartTime(lastMonthFirstDay);
                statQueryParam.setEndTime(sameDayLastMonth);
                Map<Long, OrderStatSummaryDTO> lastMonthStat = tenantDistributionOrderStatFeign.suppStatByQueryTenantIdKeyMap(statQueryParam);
                for (Map.Entry<Long, OrderStatSummaryDTO> dto : currentMonthStat.entrySet()) {
                    Long supTenantId = dto.getKey();
                    OrderStatSummaryDTO summaryDTO = dto.getValue();
                    finishOrderNumMap.put(supTenantId, summaryDTO.getOrderNumTotal());
                    BigDecimal productSaleAmountTotal = summaryDTO.getProductSaleAmountTotal();
                    BigDecimal logisticsAmountTotal = summaryDTO.getLogisticsAmountTotal();
                    BigDecimal amount = BigDecimal.ZERO;
                    if (Objects.nonNull(productSaleAmountTotal)) {
                        amount = amount.add(productSaleAmountTotal);
                    }
                    if (Objects.nonNull(logisticsAmountTotal)) {
                        amount = amount.add(logisticsAmountTotal);
                    }
                    finishOrderAmountMap.put(supTenantId, amount);

                    OrderStatSummaryDTO lastMonthSummary = lastMonthStat.get(supTenantId);
                    if (Objects.isNull(lastMonthSummary)) {
                        continue;
                    }
                    if (Objects.nonNull(lastMonthSummary.getOrderNumTotal()) && Objects.nonNull(summaryDTO.getOrderNumTotal())) {
                        periodOrderChangeMap.put(supTenantId, summaryDTO.getOrderNumTotal() - lastMonthSummary.getOrderNumTotal());
                    }
                    if (Objects.nonNull(lastMonthSummary.getProductNumTotal()) && Objects.nonNull(summaryDTO.getProductNumTotal())) {
                        periodProductChangeMap.put(supTenantId, summaryDTO.getProductNumTotal() - lastMonthSummary.getProductNumTotal());
                    }
                }
            }

            BigDecimal zero = BigDecimal.ZERO;
            for (TenantDisWallet wallet : records) {
                PodSupplierVO vo = new PodSupplierVO();
                Long supTenantId = wallet.getSupTenantId();
                vo.setSupplierTenantId(supTenantId);
                TenantRespDto tenantRespDto = tenantMap.get(supTenantId);

                if (Objects.nonNull(tenantRespDto)) {
                    vo.setTenantNo(tenantRespDto.getTenantNo());
                    vo.setSupplierTenantName(tenantRespDto.getName());
                }

                vo.setBalance(wallet.getBalance());
                vo.setGiftMoney(wallet.getGiftMoney());
                vo.setDistributionProductNum(supplierAndCountMap.getOrDefault(supTenantId, 0));
                vo.setMonthFinishSupplyOrderNum(finishOrderNumMap.getOrDefault(supTenantId, 0));
                vo.setMonthFinishSupplyOrderAmount(finishOrderAmountMap.getOrDefault(supTenantId, zero));
                vo.setPeriodOrderChange(periodOrderChangeMap.get(supTenantId));
                vo.setPeriodProductChange(periodProductChangeMap.get(supTenantId));
                voList.add(vo);
            }

            res.setRecords(voList);
        }
        return res;
    }

    public PageResult<PodDistributorVO> myDistributorPage(Long supplierTenantId,
                                                          String distributorTenantName,
                                                          String productNameOrSku,
                                                          Long page,
                                                          Long size) {

        PageResult<PodDistributorVO> res = new PageResult<>();
        res.setPage(page);
        res.setSize(size);
        res.setTotal(0);

        List<Long> searchDistributorTenantIds = null;
        List<Long> distributorTenantIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(distributorTenantName)) {
            List<TenantDTO> tenants = tenantFeign.getByLikeNameOrTenantNo(distributorTenantName);
            if (CollUtil.isEmpty(tenants)) {
                return res;
            }
            distributorTenantIds.addAll(tenants.stream().map(TenantDTO::getId).collect(Collectors.toList()));
            searchDistributorTenantIds = distributorTenantIds;
        }

        if (StrUtil.isNotEmpty(productNameOrSku)) {
            List<Long> productParentIds = productFeign.preciseSearchParentIdsByNameOrKeyword(productNameOrSku);
            if (CollUtil.isEmpty(productParentIds)) {
                return res;
            }
            List<Long> authProductDistributorTenantIds = productDistributionAuthFeign.listDistributorTenantIdsByProduct(supplierTenantId, productParentIds);
            if (CollUtil.isEmpty(authProductDistributorTenantIds)) {
                return res;
            }
            if (StrUtil.isNotEmpty(distributorTenantName)) {
                authProductDistributorTenantIds.retainAll(distributorTenantIds);
                if (CollUtil.isEmpty(authProductDistributorTenantIds)) {
                    return res;
                }
            }
            searchDistributorTenantIds = authProductDistributorTenantIds;
        }

        Page<TenantDisWallet> walletPage = tenantDistributorWalletManager
                .lambdaQuery()
                .eq(TenantDisWallet::getSupTenantId, supplierTenantId)
                .eq(TenantDisWallet::getIsDelete, BasePoConstant.INTEGER_ZERO)
                .in(CollUtil.isNotEmpty(searchDistributorTenantIds), TenantDisWallet::getDisTenantId, searchDistributorTenantIds)
                .page(new Page<>(page, size));

        res.setTotal(walletPage.getTotal());
        res.setPage(walletPage.getPages());
        res.setSize(walletPage.getSize());
        List<TenantDisWallet> records = walletPage.getRecords();

        if (CollUtil.isNotEmpty(records)) {
            List<PodDistributorVO> voList = new ArrayList<>();
            TenantListReq req = new TenantListReq();
            List<Long> disTenantIds = records.stream().map(TenantDisWallet::getDisTenantId).distinct().collect(Collectors.toList());
            req.setInIds(disTenantIds);;
            List<TenantRespDto> tenantRespDtos = tenantFeign.listDto(req);
            Map<Long, TenantRespDto> tenantMap = tenantRespDtos.stream().collect(Collectors.toMap(TenantRespDto::getId, Function.identity()));
            Map<Long, Integer> distributorIdAndCountMap = new HashMap<>();
            ListDistributionProductCountParam param = new ListDistributionProductCountParam();
            param.setDistributorTenantIds(disTenantIds);
            param.setSupplierTenantIds(Collections.singletonList(supplierTenantId));
            List<TenantAuthProductCountDTO> tenantAuthProductCountDtoList = productDistributionAuthFeign.listAuthProductCount(param);
            for (TenantAuthProductCountDTO dto : tenantAuthProductCountDtoList) {
                distributorIdAndCountMap.put(dto.getDistributorTenantId(), dto.getAuthCount());
            }

            Calendar calendar = Calendar.getInstance();
            boolean isFirstDayOfMonth = calendar.get(Calendar.DAY_OF_MONTH) == 1;

            Map<Long, Integer> finishOrderNumMap = new HashMap<>();
            Map<Long, BigDecimal> finishOrderAmountMap = new HashMap<>();
            Map<Long, Integer> periodOrderChangeMap = new HashMap<>();
            Map<Long, Integer> periodProductChangeMap = new HashMap<>();
            if (Boolean.FALSE.equals(isFirstDayOfMonth)) {
                OrderStatSummaryParam statQueryParam = new OrderStatSummaryParam();
                statQueryParam.setTenantId(supplierTenantId);
                statQueryParam.setQueryTenantIds(disTenantIds);
                //今天
                Date currentDate = calendar.getTime();
                //这个月第一天
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                Date firstDayOfMonth = calendar.getTime();

                // 上个月同一天
                calendar = Calendar.getInstance();
                calendar.add(Calendar.MONTH, -1);
                Date sameDayLastMonth = calendar.getTime();
                //上个月第一天
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                Date lastMonthFirstDay = calendar.getTime();
                statQueryParam.setStartTime(firstDayOfMonth);
                statQueryParam.setEndTime(currentDate);
                Map<Long, OrderStatSummaryDTO> currentMonthStat = tenantDistributionOrderStatFeign.distStatQueryTenantIdKeyMap(statQueryParam);
                statQueryParam.setStartTime(lastMonthFirstDay);
                statQueryParam.setEndTime(sameDayLastMonth);
                Map<Long, OrderStatSummaryDTO> lastMonthStat = tenantDistributionOrderStatFeign.distStatQueryTenantIdKeyMap(statQueryParam);
                for (Map.Entry<Long, OrderStatSummaryDTO> dto : currentMonthStat.entrySet()) {
                    Long supTenantId = dto.getKey();
                    OrderStatSummaryDTO summaryDTO = dto.getValue();
                    BigDecimal productSaleAmountTotal = summaryDTO.getProductSaleAmountTotal();
                    finishOrderNumMap.put(supTenantId, summaryDTO.getOrderNumTotal());
                    BigDecimal logisticsAmountTotal = summaryDTO.getLogisticsAmountTotal();
                    BigDecimal amount = BigDecimal.ZERO;
                    if (Objects.nonNull(productSaleAmountTotal)) {
                        amount = amount.add(productSaleAmountTotal);
                    }
                    if (Objects.nonNull(logisticsAmountTotal)) {
                        amount = amount.add(logisticsAmountTotal);
                    }
                    finishOrderAmountMap.put(supTenantId, amount);
                    OrderStatSummaryDTO lastMonthSummary = lastMonthStat.get(supTenantId);
                    if (Objects.nonNull(lastMonthSummary)) {
                        if (Objects.nonNull(lastMonthSummary.getProductNumTotal()) && Objects.nonNull(summaryDTO.getProductNumTotal())) {
                            periodProductChangeMap.put(supTenantId, summaryDTO.getProductNumTotal() - lastMonthSummary.getProductNumTotal());
                        }
                        if (Objects.nonNull(lastMonthSummary.getOrderNumTotal()) && Objects.nonNull(summaryDTO.getOrderNumTotal())) {
                            periodOrderChangeMap.put(supTenantId, summaryDTO.getOrderNumTotal() - lastMonthSummary.getOrderNumTotal());
                        }
                    }
                }
            }

            BigDecimal zero = BigDecimal.ZERO;
            for (TenantDisWallet wallet : records) {
                PodDistributorVO vo = new PodDistributorVO();
                Long disTenantId = wallet.getDisTenantId();
                vo.setDistributorTenantId(disTenantId);
                TenantRespDto tenantRespDto = tenantMap.get(disTenantId);

                if (Objects.nonNull(tenantRespDto)) {
                    vo.setTenantNo(tenantRespDto.getTenantNo());
                    vo.setDistributorTenantName(tenantRespDto.getName());
                }

                vo.setBalance(wallet.getBalance());
                vo.setGiftMoney(wallet.getGiftMoney());
                vo.setDistributionProductNum(distributorIdAndCountMap.getOrDefault(disTenantId, 0));
                vo.setMonthFinishDistributionOrderNum(finishOrderNumMap.getOrDefault(disTenantId, 0));
                vo.setMonthFinishDistributionOrderAmount(finishOrderAmountMap.getOrDefault(disTenantId, zero));
                vo.setPeriodOrderChange(periodOrderChangeMap.get(disTenantId));
                vo.setPeriodProductChange(periodProductChangeMap.get(disTenantId));
                voList.add(vo);
            }

            res.setRecords(voList);
        }
        return res;
    }
}
