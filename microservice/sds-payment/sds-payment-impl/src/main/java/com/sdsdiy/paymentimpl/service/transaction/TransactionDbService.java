package com.sdsdiy.paymentimpl.service.transaction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.paymentapi.constant.TradeNoEnum;
import com.sdsdiy.paymentapi.constant.TransactionPayTypeEnum;
import com.sdsdiy.paymentimpl.bo.TransactionCreateEntityBo;
import com.sdsdiy.paymentimpl.bo.TransactionOperateRelatedEntityBo;
import com.sdsdiy.paymentimpl.entity.*;
import com.sdsdiy.paymentimpl.manager.*;
import com.sdsdiy.paymentimpl.service.MerchantBillV2Service;
import com.sdsdiy.paymentimpl.util.TradeNoUtil;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionDbService {

    private final TransactionEntryManage transactionEntryMapperManager;
    private final PaymentMapperManager paymentMapperManager;
    private final PaymentOnlineConfigMapperManager paymentOnlineConfigMapperManager;
    private final RefundMapperManager refundMapperManager;
    private final MerchantBillMapperManager merchantBillMapperManager;
    private final TenantDistributorWalletManager tenantDistributorWalletManager;
    private final MerchantMapperManager merchantMapperManager;
    private final TenantWalletMapperManager tenantWalletMapperManager;
    private final MerchantUserAccountMapperManager merchantUserAccountMapperManager;
    private final MerchantBalanceLogMapperManager merchantBalanceLogMapperManager;
    private final TenantBalanceLogMapperManager tenantBalanceLogMapperManager;
    private final TenantDistributionBalanceLogManager tenantDistributionBalanceLogManager;
    private final MerchantUserAccountLogMapperManager merchantUserAccountLogMapperManager;
    private final SubTransactionTaskManage subTransactionTaskManage;


    public static Pair<String, String> genTradeNo(String parentTradeNo, Integer payType) {
        boolean mainTransaction = TransactionPayTypeEnum.MAIN.getValue().equals(payType);
        if (mainTransaction) {
            return new Pair<>("", parentTradeNo);
        }

        String subTradeNo = TradeNoUtil.generateTradeNo(TradeNoEnum.SUB);
        return new Pair<>(parentTradeNo, subTradeNo);
    }


    private List<SubTransactionTask> saveSubTransactionsForMainPayment(String mainTradeNo) {
        List<SubTransactionTask> subTransactionTasks = new ArrayList<>();

        // 查询所有子交易payment
        List<Payment> subPayments = paymentMapperManager.findAllByParentTradeNo(mainTradeNo);
        for (Payment subPayment : subPayments) {
            SubTransactionTask subTransactionTask = new SubTransactionTask()
                .setParentTradeNo(subPayment.getParentTradeNo())
                .setTradeNo(subPayment.getTradeNo())
                .setPaymentId(subPayment.getId())
                .setCreatedAt(System.currentTimeMillis())
                .setProcessCount(0)
                .setSuccessAt(System.currentTimeMillis())
                .setRemark("主交易处理成功后自动生成");
            subTransactionTasks.add(subTransactionTask);
        }

        // 查询所有子交易refund
        List<Refund> subRefunds = refundMapperManager.findAllByParentTradeNo(mainTradeNo);
        for (Refund subRefund : subRefunds) {
            SubTransactionTask subTransactionTask = new SubTransactionTask()
                .setParentTradeNo(subRefund.getParentTradeNo())
                .setTradeNo(subRefund.getTradeNo())
                .setRefundId(subRefund.getId())
                .setCreatedAt(System.currentTimeMillis())
                .setProcessCount(0)
                .setSuccessAt(System.currentTimeMillis())
                .setRemark("主交易处理成功后自动生成");
            subTransactionTasks.add(subTransactionTask);
        }

        // 保存子交易记录
        if (CollUtil.isNotEmpty(subTransactionTasks)) {
            subTransactionTaskManage.saveBatch(subTransactionTasks);
        }
        return subTransactionTasks;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    public void saveAfterOperateFinish(TransactionOperateRelatedEntityBo relatedEntityBo, String currentTradeNo, boolean mainTransaction) {
        boolean dbResult = false;
        if (CollUtil.isNotEmpty(relatedEntityBo.getMerchantMap())) {
            for (Merchant merchant : relatedEntityBo.getMerchantMap().values()) {
                dbResult = merchantMapperManager.updateById(merchant);
                Assert.validateFalse(dbResult, "更新商户余额失败");
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getMerchantUserAccountMap())) {
            for (MerchantUserAccount userAccount : relatedEntityBo.getMerchantUserAccountMap().values()) {
                dbResult = merchantUserAccountMapperManager.updateById(userAccount);
                Assert.validateFalse(dbResult, "更新商户用户余额失败");
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getTenantWalletMap())) {
            for (TenantWallet tenantWallet : relatedEntityBo.getTenantWalletMap().values()) {
                dbResult = tenantWalletMapperManager.updateById(tenantWallet);
                Assert.validateFalse(dbResult, "更新租户余额失败");
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getTenantDisWalletMap())) {
            for (TenantDisWallet tenantDisWallet : relatedEntityBo.getTenantDisWalletMap().values()) {
                dbResult = tenantDistributorWalletManager.updateById(tenantDisWallet);
                Assert.validateFalse(dbResult, "更新租户分销余额失败");
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getWantOperatePayments())) {
            for (Payment payment : relatedEntityBo.getWantOperatePayments()) {
                dbResult = paymentMapperManager.updateById(payment);
                Assert.validateFalse(dbResult, "更新支付单失败");
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getWantOperateRefunds())) {
            for (Refund refund : relatedEntityBo.getWantOperateRefunds()) {
                dbResult = refundMapperManager.updateById(refund);
                Assert.validateFalse(dbResult, "更新付款单失败");
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getMerchantBills())) {
            relatedEntityBo.getMerchantBills();

            for (MerchantBill bill : relatedEntityBo.getMerchantBills()) {
                MerchantBillV2Service.refreshAmountChangeRoleOfBill(bill);
                MerchantBillV2Service.calculateAmountChangeTypeOfBill(bill);
                MerchantBillV2Service.calculateAndSetRelatedMerchantTenantSaas(bill);
            }
            merchantBillMapperManager.saveBatch(relatedEntityBo.getMerchantBills());
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getMerchantBalanceLogs())) {
            merchantBalanceLogMapperManager.saveBatch(relatedEntityBo.getMerchantBalanceLogs());
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getUserAccountLogs())) {
            merchantUserAccountLogMapperManager.saveBatch(relatedEntityBo.getUserAccountLogs());
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getTenantBalanceLogs())) {
            tenantBalanceLogMapperManager.saveBatch(relatedEntityBo.getTenantBalanceLogs());
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getTenantDisBalanceLogs())) {
            tenantDistributionBalanceLogManager.saveBatch(relatedEntityBo.getTenantDisBalanceLogs());
        }
        if (mainTransaction) {
            List<SubTransactionTask> subTransactionTasks = saveSubTransactionsForMainPayment(currentTradeNo);
            relatedEntityBo.setSubTransactionTasks(subTransactionTasks);
        }
    }


    @GlobalTransactional(rollbackFor = Exception.class)
    public TransactionCreateEntityBo saveForCreateTransaction(TransactionCreateEntityBo entityBo) {
        if (CollUtil.isNotEmpty(entityBo.getPaymentList())) {
            paymentMapperManager.saveBatch(entityBo.getPaymentList());
        }

        if (CollUtil.isNotEmpty(entityBo.getRefundList())) {
            refundMapperManager.saveBatch(entityBo.getRefundList());
        }

        savePaymentOnlineConfigs(entityBo.getPaymentOnlineConfigs(), entityBo.getPaymentList());
        saveTransactionEntries(entityBo.getTransactionEntries(), entityBo.getPaymentList(), entityBo.getRefundList());
        return entityBo;
    }

    private void savePaymentOnlineConfigs(List<PaymentOnlineConfig> paymentOnlineConfigs, List<Payment> paymentList) {
        if (CollUtil.isEmpty(paymentOnlineConfigs) || CollUtil.isEmpty(paymentList)) {
            return;
        }

        Map<String, Long> tradeNoToPaymentIdMap = paymentList.stream()
            .collect(Collectors.toMap(Payment::getTradeNo, Payment::getId, (existing, replacement) -> existing));

        for (PaymentOnlineConfig config : paymentOnlineConfigs) {
            Long paymentId = tradeNoToPaymentIdMap.get(config.getTradeNo());
            if (paymentId != null) {
                config.setId(paymentId);
                paymentOnlineConfigMapperManager.save(config);
            }
        }
    }

    private void saveTransactionEntries(List<TransactionEntry> transactionEntries,
                                        List<Payment> paymentList, List<Refund> refundList) {
        if (CollUtil.isEmpty(transactionEntries)) {
            return;
        }

        log.info("保存交易条目，数量: {}", transactionEntries.size());

        Map<String, String> paymentBizNoToTradeNoMap = new HashMap<>();
        Map<String, String> paymentBizNoToParentTradeNoMap = new HashMap<>();
        Map<String, String> refundBizNoToTradeNoMap = new HashMap<>();
        Map<String, String> refundBizNoToParentTradeNoMap = new HashMap<>();

        if (CollUtil.isNotEmpty(paymentList)) {
            for (Payment payment : paymentList) {
                paymentBizNoToTradeNoMap.put(payment.getBizNo(), payment.getTradeNo());
                paymentBizNoToParentTradeNoMap.put(payment.getBizNo(), payment.getParentTradeNo());
            }
        }

        if (CollUtil.isNotEmpty(refundList)) {
            for (Refund refund : refundList) {
                refundBizNoToTradeNoMap.put(refund.getBizNo(), refund.getTradeNo());
                refundBizNoToParentTradeNoMap.put(refund.getBizNo(), refund.getParentTradeNo());
            }
        }

        for (TransactionEntry entry : transactionEntries) {
            if (StrUtil.isNotBlank(entry.getPaymentBizNo())) {
                String tradeNo = paymentBizNoToTradeNoMap.get(entry.getPaymentBizNo());
                String parentTradeNo = paymentBizNoToParentTradeNoMap.get(entry.getPaymentBizNo());
                entry.setTradeNo(tradeNo);
                entry.setParentTradeNo(parentTradeNo);
            } else if (StrUtil.isNotBlank(entry.getRefundBizNo())) {
                String tradeNo = refundBizNoToTradeNoMap.get(entry.getRefundBizNo());
                String parentTradeNo = refundBizNoToParentTradeNoMap.get(entry.getRefundBizNo());
                entry.setTradeNo(tradeNo);
                entry.setParentTradeNo(parentTradeNo);
            }
        }

        transactionEntryMapperManager.saveBatch(transactionEntries);
    }

}
