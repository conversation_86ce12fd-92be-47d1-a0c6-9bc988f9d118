package com.sdsdiy.paymentimpl.service.pay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentapi.dto.PaymentCreateDto;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentimpl.bo.*;
import com.sdsdiy.paymentimpl.entity.*;
import com.sdsdiy.paymentimpl.service.*;
import com.sdsdiy.paymentimpl.service.transaction.IBalanceOperate;
import com.sdsdiy.paymentimpl.service.transaction.TransactionCheckService;
import com.sdsdiy.paymentimpl.service.transaction.TransactionDbService;
import com.sdsdiy.paymentimpl.service.transaction.balance.BalanceOperateFactory;
import com.sdsdiy.paymentimpl.service.user.MerchantUserAccountService;
import javafx.util.Pair;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class BalancePaymentImpl extends AbstractPayment {


    private final BalanceOperateFactory balanceOperateFactory;

    @Override
    public PaymentMethodEnum getMethod() {
        return PaymentMethodEnum.BALANCE;
    }

    @Override
    public Payment generate(String parentTradeNo, PaymentParam param, TransactionPayRelatedEntityBo relatedEntityBo) {
        Pair<String, String> tradeNos = TransactionDbService.genTradeNo(parentTradeNo, param.getPayType());

        Payment payment = BeanUtil.toBean(param, Payment.class);
        payment.setStatus(PaymentStatusEnum.WAIT_PAY.getStatus())
            .setBalanceOperateFinish(BasePoConstant.NO)
            .setPayChannel(PayChannelEnum.BALANCE.getCode());
        payment.setTotalAmount(NumberUtil.add(payment.getBalance(), payment.getBonus()));
        payment.setTradeNos(tradeNos);

        TransactionCheckService.refreshPaymentSourceOpenOnlinePay(payment, relatedEntityBo);

        return payment;
    }


    private static void checkMerchantUserBalance(MerchantUserAccount userAccount, BigDecimal costBalance, BigDecimal costGiftMoney) {
        if (userAccount.getBalance().compareTo(costBalance) < 0) {
            throw new BusinessException("用户余额不足");
        }
        if (userAccount.getFreeGold().compareTo(costGiftMoney) < 0) {
            throw new BusinessException("用户赠送金不足");
        }
    }


    @Override
    public void operatePaymentAfterPaid(Payment payment, PayRelatedEntityBo relatedEntity) {
        Assert.validateFalse(payment.getSourceRole().equals(PaymentRoleEnum.MERCHANT.getCode())
            || payment.getSourceRole().equals(PaymentRoleEnum.TENANT.getCode()), "付款人信息错误"
        );

        boolean onlinePayValid = PaymentCheckService.checkPaymentOnlinePayValid(payment, relatedEntity);
        Assert.validateFalse(onlinePayValid, "已开启线上收款，请稍后刷新重试");

        TenantWallet sourceTenant = relatedEntity.getTenantWalletMap().get(payment.getSourceTenantId());
        Merchant sourceMerchant = relatedEntity.getMerchantMap().get(payment.getSourceMerchantId());
        MerchantUserAccount sourceMerchantUserAccount = relatedEntity.getMerchantUserAccountMap().get(payment.getSourceUserId());

        MerchantBillDisposeMoney disposeMoney = MerchantBillGenerateService.generateDisposeMoney(sourceTenant, sourceMerchant);

        BalanceOperatorBo balanceOperatorBo = BalanceOperatorBo.builder()
            .bizNo(payment.getTradeNo())
            .changeGiftMoney(payment.getBonus())
            .changeBalance(payment.getBalance())
            .operatorEnum(BalanceOperator.SUBTRACT)
            .tradeNo(payment.getTradeNo())
            .build();
        if (PaymentRoleEnum.TENANT.getCode().equals(payment.getSourceRole())) {
            Assert.validateNull(sourceTenant, "付款租户不能为空");
            TenantBalanceService.checkBalanceEnough(sourceTenant, payment.getBalance(), payment.getBonus());
            balanceOperatorBo.setTenantId(payment.getSourceTenantId());

            TenantBalanceLog balanceLog = TenantBalanceService.calculateBalanceAndGenLog(sourceTenant, balanceOperatorBo);
            relatedEntity.getTenantBalanceLogs().add(balanceLog);

            disposeMoney.setDisposeTenantGift(balanceLog.getDisposeBonus())
                .setDisposeTenantBalance(balanceLog.getDisposeBalance());
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(payment.getSourceRole())) {
            Assert.validateNull(sourceMerchant, "付款商户不能为空");

            if (MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType().equals(payment.getBalanceType())) {
                Assert.validateNull(sourceMerchantUserAccount, "付款用户不能为空");
                MerchantBalanceService.checkTotalBalanceEnough(sourceMerchant, payment.getBalance(), payment.getBonus());
                checkMerchantUserBalance(sourceMerchantUserAccount, payment.getBalance(), payment.getBonus());

                balanceOperatorBo.setMerchantId(payment.getSourceMerchantId());
                MerchantBalanceLog balanceLog = MerchantBalanceService.calculateAssignedBalanceAndGenLog(sourceMerchant, balanceOperatorBo);
                relatedEntity.getMerchantBalanceLogs().add(balanceLog);

                // 保存变动金额，传给账单记录
                disposeMoney.setDisposeMerchantGift(balanceLog.getDisposeBonus())
                    .setDisposeMerchantBalance(balanceLog.getDisposeBalance());

                balanceOperatorBo.setMerchantUserId(payment.getSourceUserId());
                MerchantUserAccountLog userAccountLog = MerchantUserAccountService.calculateBalanceAndGenLog(sourceMerchantUserAccount, balanceOperatorBo);
                relatedEntity.getUserAccountLogs().add(userAccountLog);
                relatedEntity.setUserBalanceChanged(true);
            } else if (MerchantPayBalanceUsedType.COMMON_BALANCE.getUsedType().equals(payment.getBalanceType())) {
                MerchantBalanceService.checkCommonBalanceEnough(sourceMerchant, payment.getBalance(), payment.getBonus());
                balanceOperatorBo.setMerchantId(payment.getSourceMerchantId());
                MerchantBalanceLog balanceLog = MerchantBalanceService.calculateBalanceAndGenLog(sourceMerchant, balanceOperatorBo);
                relatedEntity.getMerchantBalanceLogs().add(balanceLog);

                disposeMoney.setDisposeMerchantGift(balanceLog.getDisposeBonus())
                    .setDisposeMerchantBalance(balanceLog.getDisposeBalance());
            }
        }

        payment.setBalanceOperateFinish(BasePoConstant.YES);
        payment.setStatus(PaymentStatusEnum.PAID.getStatus());
        payment.setPayChannel(PayChannelEnum.BALANCE.getCode());

        if (Boolean.TRUE.equals(relatedEntity.getRecordBills())) {
            MerchantBill merchantBill = MerchantBillGenerateService.genByPayment(payment, disposeMoney);
            relatedEntity.getMerchantBills().add(merchantBill);
        }
    }

    @Override
    public void operate(Payment payment, TransactionOperateRelatedEntityBo operateRelatedEntityBo) {
        WalletType walletType = new WalletType()
            .setWalletType(payment.getSourceRole())
            .setTenantId(payment.getSourceTenantId())
            .setMerchantId(payment.getSourceMerchantId())
            .setBalanceUsedType(payment.getBalanceType())
            .setMerchantUserId(payment.getSourceUserId());
        if (PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getSourceRole())) {
            walletType.setTenantId(0L);
            walletType.setDisTenantId(payment.getSourceTenantId());
            walletType.setSupTenantId(payment.getTargetTenantId());
        }

        BalanceOperatorBo balanceOperatorBo = new BalanceOperatorBo()
            .setChangeBalance(payment.getBalance())
            .setTradeNo(payment.getTradeNo())
            .setOperatorEnum(BalanceOperator.SUBTRACT)
            .setBillType(payment.getBillType())
            .setChangeGiftMoney(payment.getBonus());

        if (PurposeType.RECHARGE.getCode().equals(payment.getPurposeType())) {
            balanceOperatorBo.setOperatorEnum(BalanceOperator.ADD);
        }

        IBalanceOperate strategy = balanceOperateFactory.getStrategy(payment.getSourceRole());
        strategy.doForPayment(walletType, balanceOperatorBo, operateRelatedEntityBo);
        payment.setBalanceOperateFinish(BasePoConstant.YES);
        payment.setPayTime(System.currentTimeMillis());
    }


    @Override
    public PaymentCreateDto create(PaymentParam param) {
        long sourceUserId = 0L;
        if (param.getSourceRole().equals(PaymentRoleEnum.MERCHANT.getCode())
            && MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType().equals(param.getMerchantBalanceUsedType())
        ) {
            sourceUserId = param.getSourceUserId();
        }

        PaymentCreateDto payment = PaymentCreateService.paramToEntity(param);
        payment.setSourceUserId(sourceUserId);

        payment.setPayChannel(PayChannelEnum.BALANCE.getCode());
        return payment;
    }
}
