package com.sdsdiy.paymentimpl.bo;

import com.sdsdiy.paymentimpl.entity.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TransactionOperateRelatedEntityBo {

    private List<Refund> wantOperateRefunds = new ArrayList<>();
    private List<Payment> wantOperatePayments = new ArrayList<>();

    private List<TransactionEntry> transactionEntryList = new ArrayList<>();

    private Map<Long, TenantWallet> tenantWalletMap = new HashMap<>();
    private Map<Long, Merchant> merchantMap = new HashMap<>();
    private Map<Long, MerchantUserAccount> merchantUserAccountMap = new HashMap<>();
    private Map<String, TenantDisWallet> tenantDisWalletMap = new HashMap<>();

    private List<TenantBalanceLog> tenantBalanceLogs = new ArrayList<>();
    private List<MerchantBalanceLog> merchantBalanceLogs = new ArrayList<>();
    private List<MerchantUserAccountLog> userAccountLogs = new ArrayList<>();
    private List<TenantDisBalanceLog> tenantDisBalanceLogs = new ArrayList<>();

    private List<MerchantBill> merchantBills = new ArrayList<>();

    private List<SubTransactionTask> subTransactionTasks = new ArrayList<>();
}
