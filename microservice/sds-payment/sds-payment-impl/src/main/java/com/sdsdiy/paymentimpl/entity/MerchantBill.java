package com.sdsdiy.paymentimpl.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sdsdiy.paymentapi.constant.AmountChangeType;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "merchant_bill")
@Accessors(chain = true)
public class MerchantBill {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * {@link com.sdsdiy.paymentapi.constant.PurposeType}
     */
    private String purposeType;

    private String detailPurpose;

    private BigDecimal changedBalance;
    private BigDecimal changedGift;

    private String orderName;
    private String remarks;

    /**
     * 交易流水号
     */
    private String tradeNo;

    /**
     * 业务流水号
     */
    private String bizNo;

    /**
     * 角色
     */
    private String sourceRole;

    /**
     * 角色
     */
    private String targetRole;

    /**
     * 租户ID
     */
    private Long sourceTenantId;

    /**
     * 商户ID
     */
    private Long sourceMerchantId;

    /**
     * 用户ID
     */
    private Long sourceUserId;

    /**
     * 租户ID
     */
    private Long targetTenantId;

    /**
     * 商户ID
     */
    private Long targetMerchantId;

    /**
     * 用户ID
     */
    private Long targetUserId;

    private String paymentMethod;
    private String payChannel;

    /**
     * 创建时间戳-微秒
     */
    private Long createTimestamp;

    private LocalDateTime createdTime;

    private String amountChangeTypeOfMerchant;
    private String amountChangeTypeOfTenant;
    private String amountChangeTypeOfSupTenant;
    private String amountChangeTypeOfDisTenant;
    private String amountChangeTypeOfSaas;

    private BigDecimal disposeMerchantBalance;
    private BigDecimal disposeMerchantGift;
    private BigDecimal disposeTenantBalance;
    private BigDecimal disposeTenantGift;

    private String operateRole;
    private Long operateUserId;

    /*** 金额变动的角色 */
    @TableField("role")
    private String amountChangeRole;

    @TableField("role_id")
    private Long amountChangeRoleId;

    /**
     * 操作对象角色
     * {@link com.sdsdiy.paymentapi.constant.PaymentRoleEnum}
     */
    private String operateTargetRole;
    private Long operateTargetRoleId;

    private Long relatedMerchantId;
    private Long relatedTenantId;
    private Long relatedSupTenantId;
    private Long relatedDisTenantId;
    private Integer relatedSaas;


    @TableField(exist = false)
    private Integer billIndexOfTrade;

    public void setDisposeBalanceAndBonus(String disposeRole, BigDecimal disposeBalance, BigDecimal disposeBonus) {
        if (PaymentRoleEnum.MERCHANT.getCode().equals(disposeRole)) {
            this.setDisposeMerchantBalance(disposeBalance);
            this.setDisposeMerchantGift(disposeBonus);
        } else if (PaymentRoleEnum.TENANT.getCode().equals(disposeRole)) {
            this.setDisposeTenantBalance(disposeBalance);
            this.setDisposeTenantGift(disposeBonus);
        } else if (PaymentRoleEnum.TENANT_DIS.getCode().equals(disposeRole)) {
            this.setDisposeTenantBalance(disposeBalance);
            this.setDisposeTenantGift(disposeBonus);
        }
    }

    public void setAmountChangeType(String amountChangeRole, AmountChangeType amountChangeType) {
        if (PaymentRoleEnum.MERCHANT.getCode().equals(amountChangeRole)) {
            this.amountChangeTypeOfMerchant = amountChangeType.getCode();
        } else if (PaymentRoleEnum.TENANT.getCode().equals(amountChangeRole)) {
            this.amountChangeTypeOfTenant = amountChangeType.getCode();
        } else if (PaymentRoleEnum.TENANT_DIS.getCode().equals(amountChangeRole)) {
            this.amountChangeTypeOfDisTenant = amountChangeType.getCode();
        } else if (PaymentRoleEnum.TENANT_SUP.getCode().equals(amountChangeRole)) {
            this.amountChangeTypeOfSupTenant = amountChangeType.getCode();
        } else if (PaymentRoleEnum.SAAS.getCode().equals(amountChangeRole)) {
            this.amountChangeTypeOfSaas = amountChangeType.getCode();
        }
    }


    public void setAmountChangeRoleAndId(String role, Long id) {
        this.setAmountChangeRole(role);
        if (PaymentRoleEnum.TENANT_DIS.getCode().equals(role)
            || PaymentRoleEnum.TENANT_SUP.getCode().equals(role)) {
            this.setAmountChangeRole(PaymentRoleEnum.TENANT.getCode());
        }
        this.setAmountChangeRoleId(id);
    }
}
