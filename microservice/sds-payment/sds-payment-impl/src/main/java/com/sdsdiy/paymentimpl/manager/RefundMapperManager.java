package com.sdsdiy.paymentimpl.manager;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sdsdiy.paymentimpl.entity.Refund;
import com.sdsdiy.paymentimpl.mapper.RefundMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 退款记录表(Refund)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-16 15:22:05
 */
@Service
@Slf4j
@DS("common")
public class RefundMapperManager extends ServiceImpl<RefundMapper, Refund> {
    
    public List<Refund> findAllByIds(List<Long> ids) {
        return lambdaQuery()
            .in(Refund::getId, ids)
            .list();
    }

    public List<Refund> findAllByParentTradeNo(String parentTradeNo) {
        return lambdaQuery()
            .eq(Refund::getParentTradeNo, parentTradeNo)
            .ne(Refund::getTradeNo, parentTradeNo) // 排除主交易本身
            .list();
    }

    public Refund findOneTradeNo(String tradeNo) {
        return lambdaQuery()
            .eq(Refund::getTradeNo, tradeNo)
            .one();
    }
}
