package com.sdsdiy.paymentimpl.entity;


import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import javafx.util.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 退款记录表
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class Refund {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer status;

    private String bizNo;
    private Integer billType;

    private String tradeNo;
    private String parentTradeNo;

    private Long createTimestamp;

    private BigDecimal totalAmount;
    private BigDecimal balance;
    private BigDecimal bonus;

    private Long sourceTenantId;
    private Long sourceMerchantId;
    private Long sourceUserId;
    private Long targetTenantId;
    private Long targetMerchantId;
    private Long targetUserId;
    private String sourceRole;
    private String targetRole;

    private String subject;
    private String remark;

    private Integer balanceOperateFinish;

    private String purposeType;
    private String detailPurpose;
    
    private String payMethod;
    private String payChannel;

    private Long paymentId;
    
    private String operateRole;
    private Long operateUserId;

    private String operateTargetRole;
    private Long operateTargetRoleId;

    @TableField("merchant_balance_used_type")
    private Integer balanceType;

    @Deprecated
    private Boolean recordBill;


    public boolean isMainTransaction() {
        return CharSequenceUtil.isBlank(this.getParentTradeNo());
    }


    public boolean isTenantDisTrade() {
        return PaymentRoleEnum.TENANT_DIS.getCode().equals(this.getTargetRole())
            || PaymentRoleEnum.TENANT_SUP.getCode().equals(this.getTargetRole());
    }

    public void setTradeNos(Pair<String, String> tradeNos) {
        this.parentTradeNo = tradeNos.getKey();
        this.tradeNo = tradeNos.getValue();
    }
}
