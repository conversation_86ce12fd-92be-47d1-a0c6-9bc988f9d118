package com.sdsdiy.paymentimpl.manager;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sdsdiy.paymentimpl.entity.SubTransaction;
import com.sdsdiy.paymentimpl.mapper.SubTransactionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
@DS("common")
public class SubTransactionManage extends ServiceImpl<SubTransactionMapper, SubTransaction> {

    public List<SubTransaction> findAllByParentTradeNo(String parentTradeNo) {
        return lambdaQuery()
            .eq(SubTransaction::getParentTradeNo, parentTradeNo)
            .list();
    }
}
