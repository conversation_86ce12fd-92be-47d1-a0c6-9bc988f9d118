package com.sdsdiy.paymentimpl.manager;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sdsdiy.paymentimpl.entity.SubTransactionTask;
import com.sdsdiy.paymentimpl.mapper.SubTransactionTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
@DS("common")
public class SubTransactionTaskManage extends ServiceImpl<SubTransactionTaskMapper, SubTransactionTask> {

    public List<SubTransactionTask> findAllByParentTradeNo(String parentTradeNo) {
        return lambdaQuery()
            .eq(SubTransactionTask::getParentTradeNo, parentTradeNo)
            .list();
    }
}
