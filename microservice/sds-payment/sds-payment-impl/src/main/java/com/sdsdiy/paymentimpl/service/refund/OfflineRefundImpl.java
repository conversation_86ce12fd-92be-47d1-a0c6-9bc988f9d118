package com.sdsdiy.paymentimpl.service.refund;

import cn.hutool.core.bean.BeanUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentimpl.bo.*;
import com.sdsdiy.paymentimpl.entity.MerchantBalanceLog;
import com.sdsdiy.paymentimpl.entity.MerchantUserAccountLog;
import com.sdsdiy.paymentimpl.entity.Refund;
import com.sdsdiy.paymentimpl.entity.TenantBalanceLog;
import com.sdsdiy.paymentimpl.service.MerchantBalanceService;
import com.sdsdiy.paymentimpl.service.TenantBalanceService;
import com.sdsdiy.paymentimpl.service.transaction.IBalanceOperate;
import com.sdsdiy.paymentimpl.service.transaction.TransactionDbService;
import com.sdsdiy.paymentimpl.service.transaction.balance.BalanceOperateFactory;
import com.sdsdiy.paymentimpl.service.user.MerchantUserAccountService;
import javafx.util.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class OfflineRefundImpl implements IRefund {

    private final BalanceOperateFactory balanceOperateFactory;

    @Override
    public PaymentMethodEnum paymentChannel() {
        return PaymentMethodEnum.OFFLINE;
    }

    @Override
    public void refund(Refund refund, RefundRelatedEntityBo relatedEntityBo) {
        BalanceOperatorBo balanceOperatorBo = BalanceOperatorBo.builder()
            .bizNo(refund.getBizNo())
            .changeGiftMoney(refund.getBonus())
            .changeBalance(refund.getBalance())
            .operatorEnum(BalanceOperator.NO_CHANGE)
            .tradeNo(refund.getTradeNo())
            .build();

        MerchantBillDisposeMoney merchantBillDisposeMoney = new MerchantBillDisposeMoney();

        if (PaymentRoleEnum.TENANT.getCode().equals(refund.getTargetRole())) {
            balanceOperatorBo.setTenantId(refund.getTargetTenantId());
            TenantBalanceLog balanceLog = TenantBalanceService.calculateBalanceAndGenLog(relatedEntityBo.getTenantWallet(), balanceOperatorBo);
            relatedEntityBo.setTenantBalanceChanged(true);
            relatedEntityBo.setTenantBalanceLog(balanceLog);

            merchantBillDisposeMoney.setDisposeTenantBalance(balanceLog.getDisposeBalance());
            merchantBillDisposeMoney.setDisposeTenantGift(balanceLog.getDisposeBonus());

            RefundHandleService.recordBill(relatedEntityBo, refund, merchantBillDisposeMoney);
            refund.setBalanceOperateFinish(BasePoConstant.YES);
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(refund.getTargetRole())) {
            if (MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType().equals(refund.getBalanceType())) {
                MerchantBalanceLog balanceLog = MerchantBalanceService.calculateAssignedBalanceAndGenLog(relatedEntityBo.getMerchant(), balanceOperatorBo);
                relatedEntityBo.setMerchantBalanceChanged(true);

                relatedEntityBo.setMerchantBalanceLog(balanceLog);
                MerchantUserAccountLog userAccountLog = MerchantUserAccountService.calculateBalanceAndGenLog(relatedEntityBo.getMerchantUserAccount(), balanceOperatorBo);
                relatedEntityBo.setUserAccountLog(userAccountLog);
                relatedEntityBo.setMerchantUserAccountBalanceChanged(true);

                merchantBillDisposeMoney.setDisposeMerchantBalance(balanceLog.getDisposeBalance());
                merchantBillDisposeMoney.setDisposeMerchantGift(balanceLog.getDisposeBonus());
            } else {
                MerchantBalanceLog balanceLog = MerchantBalanceService.calculateBalanceAndGenLog(relatedEntityBo.getMerchant(), balanceOperatorBo);
                relatedEntityBo.setMerchantBalanceChanged(true);
                relatedEntityBo.setMerchantBalanceLog(balanceLog);
                merchantBillDisposeMoney.setDisposeMerchantBalance(balanceLog.getDisposeBalance());
                merchantBillDisposeMoney.setDisposeMerchantGift(balanceLog.getDisposeBonus());
            }

            RefundHandleService.recordBill(relatedEntityBo, refund, merchantBillDisposeMoney);
            refund.setBalanceOperateFinish(BasePoConstant.YES);
        }
    }

    @Override
    public void doRefund(Refund refund, TransactionOperateRelatedEntityBo relatedEntityBo) {
        WalletType walletType = new WalletType()
            .setWalletType(refund.getTargetRole())
            .setTenantId(refund.getTargetTenantId())
            .setBalanceUsedType(refund.getBalanceType())
            .setMerchantId(refund.getTargetMerchantId())
            .setMerchantUserId(refund.getTargetUserId());
        if (PaymentRoleEnum.TENANT_DIS.getCode().equals(refund.getTargetRole())) {
            walletType.setTenantId(0L);
            walletType.setSupTenantId(refund.getSourceTenantId());
            walletType.setDisTenantId(refund.getTargetTenantId());
        }

        BalanceOperatorBo balanceOperatorBo = new BalanceOperatorBo()
            .setChangeBalance(refund.getBalance())
            .setTradeNo(refund.getTradeNo())
            .setOperatorEnum(BalanceOperator.NO_CHANGE)
            .setBillType(refund.getBillType())
            .setChangeGiftMoney(refund.getBonus());

        if (refund.isTenantDisTrade()) {
            IBalanceOperate strategy = balanceOperateFactory.getStrategy(PaymentRoleEnum.TENANT_DIS.getCode());
            strategy.doForRefund(walletType, balanceOperatorBo, relatedEntityBo);
        } else {
            IBalanceOperate strategy = balanceOperateFactory.getStrategy(refund.getTargetRole());
            strategy.doForRefund(walletType, balanceOperatorBo, relatedEntityBo);
        }
        refund.setBalanceOperateFinish(BasePoConstant.YES);
    }

    @Override
    public Refund generate(String parentTradeNo, RefundParam param, TransactionPayRelatedEntityBo relatedEntityBo) {
        Pair<String, String> tradeNos = TransactionDbService.genTradeNo(parentTradeNo, param.getPayType());

        Refund entity = BeanUtil.toBean(param, Refund.class);
        entity.setStatus(PaymentStatusEnum.WAIT_PAY.getStatus())
            .setBalanceOperateFinish(BasePoConstant.NO)
            .setPayMethod(PaymentMethodEnum.OFFLINE.getCode())
            .setPayChannel(PayChannelEnum.OFFLINE.getCode());
        entity.setTradeNos(tradeNos);

        return entity;
    }
}
