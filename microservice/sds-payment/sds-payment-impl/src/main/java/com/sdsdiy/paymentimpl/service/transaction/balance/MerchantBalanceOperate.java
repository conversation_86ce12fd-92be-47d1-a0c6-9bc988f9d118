package com.sdsdiy.paymentimpl.service.transaction.balance;

import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.paymentapi.constant.BalanceUsedType;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import com.sdsdiy.paymentimpl.bo.BalanceOperatorBo;
import com.sdsdiy.paymentimpl.bo.TransactionOperateRelatedEntityBo;
import com.sdsdiy.paymentimpl.bo.WalletType;
import com.sdsdiy.paymentimpl.entity.*;
import com.sdsdiy.paymentimpl.service.MerchantBalanceService;
import com.sdsdiy.paymentimpl.service.user.MerchantUserAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantBalanceOperate extends AbstractBalanceOperate {

    @Override
    public String getRole() {
        return PaymentRoleEnum.MERCHANT.getCode();
    }

    @Override
    public void doForRefund(WalletType walletType, BalanceOperatorBo balanceOperatorBo, TransactionOperateRelatedEntityBo relatedEntity) {
        String tradeNo = balanceOperatorBo.getTradeNo();
        Long merchantId = walletType.getMerchantId();
        Merchant merchant = relatedEntity.getMerchantMap().get(merchantId);
        if (merchant == null) {
            throw new BusinessException("未找到商户信息，merchantId: " + merchantId);
        }

        MerchantBalanceLog balanceLog = null;
        if (BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType().equals(walletType.getBalanceUsedType())) {
            balanceLog = MerchantBalanceService.calculateBalanceAndGenLog(merchant, balanceOperatorBo);
            relatedEntity.getMerchantBalanceLogs().add(balanceLog);
        } else if (BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType().equals(walletType.getBalanceUsedType())) {
            balanceLog = MerchantBalanceService.calculateBalanceAndGenLog(merchant, balanceOperatorBo);
            relatedEntity.getMerchantBalanceLogs().add(balanceLog);

            MerchantUserAccountLog userAccountLog = MerchantUserAccountService.calculateBalanceAndGenLog(relatedEntity.getMerchantUserAccountMap().get(merchant.getId()), balanceOperatorBo);
            relatedEntity.getUserAccountLogs().add(userAccountLog);
        }

        Refund current = relatedEntity.getWantOperateRefunds().stream()
            .filter(refund -> tradeNo.equals(refund.getTradeNo()))
            .findFirst()
            .orElse(null);
        if (current == null) {
            throw new BusinessException("Payment数据异常");
        }

        this.generateBillForRefund(current, balanceLog, relatedEntity);
    }

    @Override
    public void doForPayment(WalletType walletType, BalanceOperatorBo balanceOperatorBo, TransactionOperateRelatedEntityBo relatedEntity) {
        String tradeNo = balanceOperatorBo.getTradeNo();
        Long merchantId = walletType.getMerchantId();
        Merchant merchant = relatedEntity.getMerchantMap().get(merchantId);
        if (merchant == null) {
            throw new BusinessException("未找到商户信息，merchantId: " + merchantId);
        }

        MerchantBalanceLog balanceLog = null;
        if (BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType().equals(walletType.getBalanceUsedType())) {
            balanceLog = handleMerchantCommonBalance(balanceOperatorBo, relatedEntity, merchant);
        } else if (BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType().equals(walletType.getBalanceUsedType())) {
            handleUserAccountBalance(balanceOperatorBo, relatedEntity, merchant);
            balanceLog = handleMerchantCommonBalance(balanceOperatorBo, relatedEntity, merchant);
        }

        Payment currentPayment = relatedEntity.getWantOperatePayments().stream()
            .filter(payment -> tradeNo.equals(payment.getTradeNo()))
            .findFirst()
            .orElse(null);
        if (currentPayment == null) {
            throw new BusinessException("Payment数据异常");
        }
        this.generateBillForPayment(currentPayment, balanceLog, relatedEntity);
    }


    private void checkUserAccountBalance(BalanceOperatorBo balanceOperatorBo,
                                         TransactionOperateRelatedEntityBo relatedEntity,
                                         Merchant merchant) {
        MerchantUserAccount userAccount = relatedEntity.getMerchantUserAccountMap().get(merchant.getId());
        if (userAccount == null) {
            throw new BusinessException("未找到商户用户账户信息，merchantId: " + merchant.getId());
        }
        if (userAccount.getBalance().compareTo(balanceOperatorBo.getChangeBalance()) < 0) {
            throw new BusinessException("商户用户账户余额不足");
        }
        if (userAccount.getFreeGold().compareTo(balanceOperatorBo.getChangeGiftMoney()) < 0) {
            throw new BusinessException("商户用户账户赠送金不足");
        }
    }

    private void handleUserAccountBalance(BalanceOperatorBo balanceOperatorBo,
                                          TransactionOperateRelatedEntityBo relatedEntity,
                                          Merchant merchant) {
        MerchantUserAccount userAccount = relatedEntity.getMerchantUserAccountMap().get(merchant.getId());
        if (userAccount == null) {
            throw new BusinessException("未找到商户用户账户信息，merchantId: " + merchant.getId());
        }
        if (userAccount.getBalance().compareTo(balanceOperatorBo.getChangeBalance()) < 0) {
            throw new BusinessException("商户用户账户余额不足");
        }
        if (userAccount.getFreeGold().compareTo(balanceOperatorBo.getChangeGiftMoney()) < 0) {
            throw new BusinessException("商户用户账户赠送金不足");
        }

        MerchantUserAccountLog userAccountLog = MerchantUserAccountService.calculateBalanceAndGenLog(userAccount, balanceOperatorBo);
        relatedEntity.getUserAccountLogs().add(userAccountLog);
    }

    private MerchantBalanceLog handleMerchantCommonBalance(BalanceOperatorBo balanceOperatorBo,
                                                           TransactionOperateRelatedEntityBo relatedEntity,
                                                           Merchant merchant) {
        MerchantBalanceService.checkCommonBalanceEnough(
            merchant,
            balanceOperatorBo.getChangeBalance(),
            balanceOperatorBo.getChangeGiftMoney()
        );

        MerchantBalanceLog balanceLog = MerchantBalanceService.calculateBalanceAndGenLog(merchant, balanceOperatorBo);
        relatedEntity.getMerchantBalanceLogs().add(balanceLog);
        return balanceLog;
    }

}
