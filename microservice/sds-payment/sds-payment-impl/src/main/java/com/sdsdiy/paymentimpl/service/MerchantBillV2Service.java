package com.sdsdiy.paymentimpl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.BaseDownloadDTO;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.common.base.enums.S3ModuleEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.core.aws.s3.S3Util;
import com.sdsdiy.core.base.util.ConvertUtil;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.*;
import com.sdsdiy.paymentapi.param.bill.BillQueryParam;
import com.sdsdiy.paymentimpl.bo.BillNeedNameMap;
import com.sdsdiy.paymentimpl.entity.MerchantBill;
import com.sdsdiy.paymentimpl.entity.bill.SaasTotalBillMonthlyAmount;
import com.sdsdiy.paymentimpl.feign.*;
import com.sdsdiy.paymentimpl.manager.MerchantBillMapperReadManager;
import com.sdsdiy.paymentimpl.manager.MerchantMapperManager;
import com.sdsdiy.paymentimpl.service.bill.SaasTotalBillAmountService;
import com.sdsdiy.paymentimpl.service.user.TenantService;
import com.sdsdiy.statdata.constant.DataExportRecordTypeEnum;
import com.sdsdiy.statdata.dto.bill.CrossTenantSupplyMonthBillDTO;
import com.sdsdiy.statdata.dto.bill.TenantProductDistributionMonthBillDTO;
import com.sdsdiy.userapi.dto.base.MerchantSysUserRespDto;
import com.sdsdiy.userapi.dto.saas.SaasSysUserDto;
import com.sdsdiy.userapi.dto.tenant.TenantSysUserDto;
import com.sdsdiy.userapi.param.SysUserIdsParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.NO;
import static com.sdsdiy.common.base.constant.BasePoConstant.YES;

@Service
@Slf4j
@RequiredArgsConstructor
public class MerchantBillV2Service {
    private final S3Util s3Util;
    private final MerchantBillMapperReadManager merchantBillMapperReadManager;
    private final MerchantMapperManager merchantMapperManager;
    private final TenantService tenantService;
    private final MerchantSysUserFeign merchantSysUserFeign;
    private final TenantSysUserFeign tenantSysUserFeign;
    private final SaasSysUserFeign saasSysUserFeign;
    private final TenantDisBalanceStatFeign tenantDisBalanceStatFeign;
    private final MerchantFeign merchantFeign;


    private static final String SOURCE_IS_ROLE = "source";
    private static final String TARGET_IS_ROLE = "target";


    public BillNeedNameMap getBillNeedNameMap(List<MerchantBillVo> billVos) {
        BillNeedNameMap nameMap = new BillNeedNameMap();
        nameMap.setMerchantNameMp(this.getBillTotalMerchantName(billVos));
        nameMap.setTenantNameMp(this.getBillTotalTenantName(billVos));
        Map<String, MerchantBillOperateUserVo> operatorUserMap = getOperatorUserNameMap(billVos);
        Set<Long> tenantId = new HashSet<>();


        nameMap.setOperateUserMap(operatorUserMap);
        return nameMap;
    }

    public void refreshRelatedSupAndDisTenantName(List<MerchantBillVo> billList, Map<Long, String> tenantNameMap) {
        billList.forEach(item -> {
            if (item.getRelatedDisTenantId() > 0) {
                item.setRelatedDisTenantName(tenantNameMap.get(item.getRelatedDisTenantId()));
            }
            if (item.getRelatedSupTenantId() > 0) {
                item.setRelatedSupTenantName(tenantNameMap.get(item.getRelatedSupTenantId()));
            }
        });
    }

    private Map<Long, String> getBillTotalMerchantName(List<MerchantBillVo> billVos) {
        Set<Long> merchantIds = new HashSet<>();
        for (MerchantBillVo vo : billVos) {
            merchantIds.addAll(vo.getAllRelatedMerchant());
        }
        merchantIds.remove(0L);
        return this.merchantMapperManager.getNameMap(merchantIds);
    }


    public void refreshOperateUserName(List<MerchantBillVo> dtoList, BillNeedNameMap billNeedNameMap, BillQueryParam billQueryParam) {
        for (MerchantBillVo dto : dtoList) {
            if (CharSequenceUtil.isEmpty(dto.getOperateRole())) {
                continue;
            }
            dto.setCurrentViewRoleCode(billQueryParam.getCurrentViewRole());
            dto.setCurrentViewRoleId(billQueryParam.getCurrentViewRoleId());
            String operateUserKey = genOperateUserMapKey(dto.getOperateRole(), dto.getOperateUserId());
            MerchantBillOperateUserVo operateUserVo = billNeedNameMap.getOperateUserMap().get(operateUserKey);
            dto.setMerchantBillOPerateUser(operateUserVo);
        }
    }

    private Map<Long, String> getBillTotalTenantName(List<MerchantBillVo> billVos) {
        Set<Long> ids = new HashSet<>();
        for (MerchantBillVo vo : billVos) {
            ids.addAll(vo.getAllRelatedTenantId());
        }
        return this.tenantService.getTenantNameMap(ids);
    }

    public void refreshOperateTargetRoleDetail(List<MerchantBillVo> billList, Map<Long, String> merchantNameMap) {
        billList.forEach(item -> {
            if (PaymentRoleEnum.MERCHANT.getCode().equals(item.getOperateTargetRole())) {
                item.setOperateTargetRoleDetail("商户[" + merchantNameMap.getOrDefault(item.getOperateTargetRoleId(), StrUtil.EMPTY) + "]");
            } else if (PaymentRoleEnum.TENANT.getCode().equals(item.getOperateTargetRole())) {
                item.setOperateTargetRoleDetail(PaymentRoleEnum.TENANT.getDesc());
            }
        });
    }

    public void refreshAmountChangeRoleDetail(List<MerchantBillVo> billList, Map<Long, String> merchantNameMap, Map<Long, String> tenantNameMap) {
        billList.forEach(item -> {
            if (PaymentRoleEnum.MERCHANT.getCode().equals(item.getAmountChangeRole())) {
                item.setAmountChangeRoleDetail("商户[" + merchantNameMap.getOrDefault(item.getAmountChangeRoleId(), StrUtil.EMPTY) + "]");
            } else if (PaymentRoleEnum.TENANT.getCode().equals(item.getAmountChangeRole())) {
                item.setAmountChangeRoleDetail("租户[" + tenantNameMap.getOrDefault(item.getAmountChangeRoleId(), StrUtil.EMPTY) + "]");
            }
        });
    }

    private static String calculateAmountRoleIsFromSourceOrTarget(String purposeTypeCode, String paymentMethod) {
        Optional<PurposeType> optional = PurposeType.getByCode(purposeTypeCode);
        if (!optional.isPresent()) {
            return "";
        }
        PurposeType purposeType = optional.get();
        switch (purposeType) {
            case RECHARGE:
                if (PaymentMethodEnum.ALI_PAY.getCode().equals(paymentMethod)
                        || PaymentMethodEnum.LAKALA.getCode().equals(paymentMethod)) {
                    return SOURCE_IS_ROLE;
                } else {
                    return TARGET_IS_ROLE;
                }
            case REFUND:
            case PAYOUT:
                return TARGET_IS_ROLE;
            case WITHDRAW:
            case BUY_PRODUCT:
            case BUY_SERVICE:
            case BUY_MATERIAL:
            case CAL_VOLUME:
            case ADMIN_PREPAY_SHIPPING:
            case CHANGE_WAYBILL:
            case CUSTOMS_DUTY:
            case OTHER:
            case JIT_SHIPPING_COST:
                return SOURCE_IS_ROLE;
            default:
                return "";
        }
    }


    public static void calculateAndSetRelatedMerchantTenantSaas(MerchantBill bill) {
        if (PaymentRoleEnum.TENANT_SUP.getCode().equals(bill.getSourceRole())) {
            bill.setRelatedSupTenantId(bill.getSourceTenantId());
        } else if (PaymentRoleEnum.TENANT_DIS.getCode().equals(bill.getSourceRole())) {
            bill.setRelatedDisTenantId(bill.getSourceTenantId());
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(bill.getSourceRole())) {
            bill.setRelatedMerchantId(bill.getSourceMerchantId());
        } else if (PaymentRoleEnum.TENANT.getCode().equals(bill.getSourceRole())) {
            bill.setRelatedTenantId(bill.getSourceTenantId());
        } else if (PaymentRoleEnum.SAAS.getCode().equals(bill.getSourceRole())) {
            bill.setRelatedSaas(BasePoConstant.YES);
        }

        if (PaymentRoleEnum.TENANT_SUP.getCode().equals(bill.getTargetRole())) {
            bill.setRelatedSupTenantId(bill.getTargetTenantId());
        } else if (PaymentRoleEnum.TENANT_DIS.getCode().equals(bill.getTargetRole())) {
            bill.setRelatedDisTenantId(bill.getTargetTenantId());
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(bill.getTargetRole())) {
            bill.setRelatedMerchantId(bill.getTargetMerchantId());
        } else if (PaymentRoleEnum.TENANT.getCode().equals(bill.getTargetRole())) {
            bill.setRelatedTenantId(bill.getTargetTenantId());
        } else if (PaymentRoleEnum.SAAS.getCode().equals(bill.getTargetRole())) {
            bill.setRelatedSaas(BasePoConstant.YES);
        }
    }


    public static void calculateAmountChangeTypeOfBill(MerchantBill bill) {
        Optional<PurposeType> purposeTypeOptional = PurposeType.getByCode(bill.getPurposeType());
        if (!purposeTypeOptional.isPresent()) {
            return;
        }
        switch (purposeTypeOptional.get()) {
            case RECHARGE:
                bill.setAmountChangeType(bill.getSourceRole(), AmountChangeType.RECHARGE);
                bill.setAmountChangeType(bill.getTargetRole(), AmountChangeType.RECHARGE);
                return;
            case WITHDRAW:
                bill.setAmountChangeType(bill.getSourceRole(), AmountChangeType.WITHDRAW);
                bill.setAmountChangeType(bill.getTargetRole(), AmountChangeType.WITHDRAW);
                return;
            case REFUND:
            case PAYOUT:
                bill.setAmountChangeType(bill.getSourceRole(), AmountChangeType.EXPEND);
                bill.setAmountChangeType(bill.getTargetRole(), AmountChangeType.INCOME);
                return;
            case BUY_PRODUCT:
            case BUY_SERVICE:
            case BUY_MATERIAL:
            case CHANGE_WAYBILL:
            case OTHER:
            case CUSTOMS_DUTY:
            case ADMIN_PREPAY_SHIPPING:
            case CAL_VOLUME:
            case JIT_SHIPPING_COST:
                bill.setAmountChangeType(bill.getSourceRole(), AmountChangeType.EXPEND);
                bill.setAmountChangeType(bill.getTargetRole(), AmountChangeType.INCOME);
            default:
                break;
        }
    }

    public static void refreshAmountChangeRoleOfBill(MerchantBill bill) {
        String roleFromWhere = calculateAmountRoleIsFromSourceOrTarget(bill.getPurposeType(), bill.getPaymentMethod());
        if (roleFromWhere.equals(SOURCE_IS_ROLE)) {
            if (PaymentRoleEnum.MERCHANT.getCode().equals(bill.getSourceRole())) {
                bill.setAmountChangeRoleAndId(bill.getSourceRole(), bill.getSourceMerchantId());
            } else if (PaymentRoleEnum.TENANT.getCode().equals(bill.getSourceRole())
                    || PaymentRoleEnum.TENANT_DIS.getCode().equals(bill.getSourceRole())) {
                bill.setAmountChangeRoleAndId(bill.getSourceRole(), bill.getSourceTenantId());
            }
        } else if (roleFromWhere.equals(TARGET_IS_ROLE)) {
            if (PaymentRoleEnum.MERCHANT.getCode().equals(bill.getTargetRole())) {
                bill.setAmountChangeRoleAndId(bill.getTargetRole(), bill.getTargetMerchantId());
            } else if (PaymentRoleEnum.TENANT.getCode().equals(bill.getTargetRole())
                    || PaymentRoleEnum.TENANT_DIS.getCode().equals(bill.getTargetRole())) {
                bill.setAmountChangeRoleAndId(bill.getTargetRole(), bill.getTargetTenantId());
            }
        }
    }


    public List<MerchantBillVo> billQueryList(BillQueryParam queryParam) {
        List<MerchantBill> bills = this.merchantBillMapperReadManager.billQueryList(queryParam);
        List<MerchantBillVo> dtoList = BeanUtil.copyToList(bills, MerchantBillVo.class);

        BillNeedNameMap billNeedNameMap = this.getBillNeedNameMap(dtoList);
        this.refreshOperateTargetRoleDetail(dtoList, billNeedNameMap.getMerchantNameMp());
        this.refreshRelatedSupAndDisTenantName(dtoList, billNeedNameMap.getTenantNameMp());
        this.refreshAmountChangeRoleDetail(dtoList, billNeedNameMap.getMerchantNameMp(), billNeedNameMap.getTenantNameMp());
        this.refreshOperateUserName(dtoList, billNeedNameMap, queryParam);
        return dtoList;
    }


    public PageResultDto<MerchantBillVo> billQueryPage(BillQueryParam queryParam) {
        formatMerchantTestOfBillQueryParam(queryParam);
        attachBillLimitMerchantSysUserId(queryParam);
        Page<MerchantBill> page = new Page<>(queryParam.getCurrentPage(), queryParam.getPageSize());
        Page<MerchantBill> result = this.merchantBillMapperReadManager.billQueryPage(queryParam, page);
        List<MerchantBillVo> dtoList = BeanUtil.copyToList(result.getRecords(), MerchantBillVo.class);

        BillNeedNameMap billNeedNameMap = this.getBillNeedNameMap(dtoList);
        this.refreshOperateTargetRoleDetail(dtoList, billNeedNameMap.getMerchantNameMp());
        this.refreshRelatedSupAndDisTenantName(dtoList, billNeedNameMap.getTenantNameMp());
        this.refreshAmountChangeRoleDetail(dtoList, billNeedNameMap.getMerchantNameMp(), billNeedNameMap.getTenantNameMp());
        this.refreshOperateUserName(dtoList, billNeedNameMap, queryParam);

        return new PageResultDto<>(page, dtoList);
    }


    public void attachBillLimitMerchantSysUserId(BillQueryParam queryParam) {
        List<Long> limitMerchantUserIds = new ArrayList<>();
        if (ObjectUtil.isNotNull(queryParam.getMerchantDepartmentId())) {
            limitMerchantUserIds.add(-1L);
            List<MerchantSysUserRespDto> userRespDtos = merchantSysUserFeign.getByOfficeId(queryParam.getMerchantDepartmentId());
            userRespDtos.forEach(item -> {
                limitMerchantUserIds.add(item.getId());
            });
        }
        queryParam.setLimitMerchantUserIds(limitMerchantUserIds);

        if (PaymentRoleEnum.MERCHANT.getCode().equals(queryParam.getOperateRole())
            && ObjectUtil.isNotNull(queryParam.getOperateUserId())) {
            if (CollUtil.isNotEmpty(limitMerchantUserIds)) {
                limitMerchantUserIds.retainAll(Collections.singletonList(queryParam.getOperateUserId()));
                limitMerchantUserIds.add(-1L);
            } else {
                limitMerchantUserIds.add(queryParam.getOperateUserId());
            }
        }
    }


    public BillMonthlyAmountOfMerchantVo calculateAndGenerateMonthlyAmount(BillQueryParam billQueryParam) {
        BillMonthlyAmountOfMerchantVo amount = new BillMonthlyAmountOfMerchantVo();
        amount.setMonth(billQueryParam.getBeginCreateTime());
        List<MerchantBillVo> merchantBills = billQueryList(billQueryParam);
        for (MerchantBillVo merchantBill : merchantBills) {
            calculateBillAmount(amount, merchantBill);
        }
        return amount;
    }


    public static void calculateBillAmount(
        BillMonthlyAmountOfMerchantVo amount,
        MerchantBillVo merchantBill
    ) {
        if (merchantBill.getRelatedMerchantId() == 0L) {
            return;
        }

        calculateIncome(amount, merchantBill);
        calculateExpend(amount, merchantBill);
        calculateRecharge(amount, merchantBill);
        calculateWithdraw(amount, merchantBill);
        amount.setConsume(amount.getExpend().subtract(amount.getIncome()));
    }

    private static void calculateRecharge(BillMonthlyAmountOfMerchantVo amount, MerchantBillVo bill) {
        if (!bill.getAmountChangeTypeOfMerchant().equals(AmountChangeType.RECHARGE.getCode())) {
            return;
        }
        if (bill.getDetailPurpose().equals(DetailPurpose.TENANT_OPEN_ONLINE_PAY.getCode())) {
            return;
        }
        amount.setRecharge(amount.getRecharge().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        amount.setBalanceOfRecharge(amount.getBalanceOfRecharge().add(bill.getChangedBalance()));
        amount.setGiftOfRecharge(amount.getGiftOfRecharge().add(bill.getChangedGift()));
    }

    private static void calculateWithdraw(BillMonthlyAmountOfMerchantVo amount, MerchantBillVo bill) {
        if (!bill.getAmountChangeTypeOfMerchant().equals(AmountChangeType.WITHDRAW.getCode())) {
            return;
        }
        if (bill.getDetailPurpose().equals(DetailPurpose.TENANT_OPEN_ONLINE_PAY.getCode())) {
            return;
        }

        amount.setWithdraw(amount.getWithdraw().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        amount.setBalanceOfWithdraw(amount.getBalanceOfWithdraw().add(bill.getChangedBalance()));
        amount.setGiftOfWithdraw(amount.getGiftOfWithdraw().add(bill.getChangedGift()));
    }

    private static void calculateExpend(BillMonthlyAmountOfMerchantVo amount, MerchantBillVo bill) {
        if (!bill.getAmountChangeTypeOfMerchant().equals(AmountChangeType.EXPEND.getCode())) {
            return;
        }
        amount.setExpend(amount.getExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));

        if (PaymentMethodEnum.BALANCE.getCode().equals(bill.getPaymentMethod())) {
            amount.setBalanceOfExpend(amount.getBalanceOfExpend().add(bill.getChangedBalance()));
            amount.setGiftOfExpend(amount.getGiftOfExpend().add(bill.getChangedGift()));
        } else if (PaymentMethodEnum.ALI_PAY.getCode().equals(bill.getPaymentMethod())) {
            amount.setAlipayOfExpend(amount.getAlipayOfExpend().add(bill.getChangedBalance()));
        } else if (PaymentMethodEnum.OFFLINE.getCode().equals(bill.getPaymentMethod())) {
            amount.setOfflineOfExpend(amount.getOfflineOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PaymentMethodEnum.LAKALA.getCode().equals(bill.getPaymentMethod())) {
            amount.setLakalaOfExpend(amount.getLakalaOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        }

        if (PayChannelEnum.ALI_PAY.getCode().equals(bill.getPayChannel())) {
            amount.setAlipayChannelOfExpend(amount.getAlipayChannelOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PayChannelEnum.WECHAT.getCode().equals(bill.getPayChannel())) {
            amount.setWechatChannelOfExpend(amount.getWechatChannelOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PayChannelEnum.UNION_PAY.getCode().equals(bill.getPayChannel())) {
            amount.setUnionPayChannelOfExpend(amount.getUnionPayChannelOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        }

        if (PurposeType.BUY_PRODUCT.getCode().equals(bill.getPurposeType())) {
            amount.setBuyProductOfExpend(amount.getBuyProductOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PurposeType.BUY_SERVICE.getCode().equals(bill.getPurposeType())) {
            amount.setBuyServiceOfExpend(amount.getBuyServiceOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PurposeType.BUY_MATERIAL.getCode().equals(bill.getPurposeType())) {
            amount.setBuyMaterialOfExpend(amount.getBuyMaterialOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PurposeType.ADMIN_PREPAY_SHIPPING.getCode().equals(bill.getPurposeType())) {
            amount.setAdminPrepayShippingOfExpend(amount.getAdminPrepayShippingOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PurposeType.CAL_VOLUME.getCode().equals(bill.getPurposeType())) {
            amount.setCalVolumeOfExpend(amount.getCalVolumeOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PurposeType.CHANGE_WAYBILL.getCode().equals(bill.getPurposeType())) {
            amount.setChangeWaybillOfExpend(amount.getChangeWaybillOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PurposeType.CUSTOMS_DUTY.getCode().equals(bill.getPurposeType())) {
            amount.setCustomsDutyOfExpend(amount.getCustomsDutyOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PurposeType.OTHER.getCode().equals(bill.getPurposeType())) {
            amount.setOtherOfExpend(amount.getOtherOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PurposeType.JIT_SHIPPING_COST.getCode().equals(bill.getPurposeType())) {
            amount.setJitShippingCostOfExpend(amount.getJitShippingCostOfExpend().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        }
    }

    private static void calculateIncome(BillMonthlyAmountOfMerchantVo amount, MerchantBillVo bill) {
        if (!bill.getAmountChangeTypeOfMerchant().equals(AmountChangeType.INCOME.getCode())) {
            return;
        }
        amount.setIncome(amount.getIncome().add(bill.getChangedBalance()).add(bill.getChangedGift()));

        if (PaymentMethodEnum.BALANCE.getCode().equals(bill.getPaymentMethod())) {
            amount.setBalanceOfIncome(amount.getBalanceOfIncome().add(bill.getChangedBalance()));
            amount.setGiftOfIncome(amount.getGiftOfIncome().add(bill.getChangedGift()));
        } else if (PaymentMethodEnum.ALI_PAY.getCode().equals(bill.getPaymentMethod())) {
            amount.setAlipayOfIncome(amount.getAlipayOfIncome().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PaymentMethodEnum.OFFLINE.getCode().equals(bill.getPaymentMethod())) {
            amount.setOfflineOfIncome(amount.getOfflineOfIncome().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PaymentMethodEnum.LAKALA.getCode().equals(bill.getPaymentMethod())) {
            amount.setLakalaOfIncome(amount.getLakalaOfIncome().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        }

        if (PayChannelEnum.ALI_PAY.getCode().equals(bill.getPayChannel())) {
            amount.setAlipayChannelOfIncome(amount.getAlipayChannelOfIncome().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PayChannelEnum.WECHAT.getCode().equals(bill.getPayChannel())) {
            amount.setWechatChannelOfIncome(amount.getWechatChannelOfIncome().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PayChannelEnum.UNION_PAY.getCode().equals(bill.getPayChannel())) {
            amount.setUnionPayChannelOfIncome(amount.getUnionPayChannelOfIncome().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        }

        if (PurposeType.PAYOUT.getCode().equals(bill.getPurposeType())) {
            amount.setPayoutOfIncome(amount.getPayoutOfIncome().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        } else if (PurposeType.REFUND.getCode().equals(bill.getPurposeType())) {
            amount.setRefundOfIncome(amount.getRefundOfIncome().add(bill.getChangedBalance()).add(bill.getChangedGift()));
        }
    }

    public void formatMerchantTestOfBillQueryParam(BillQueryParam param) {
        Integer isMerchantTest = param.getIsMerchantTest();
        if (isMerchantTest != null) {
            List<Long> testMerchantIds = merchantFeign.getTestMerchantIds(null);
            if (YES.equals(isMerchantTest) && CollectionUtil.isNotEmpty(testMerchantIds)) {
                param.setTestMerchantIds(testMerchantIds);
            }
            if (NO.equals(isMerchantTest) && CollectionUtil.isNotEmpty(testMerchantIds)) {
                param.setNoTestMerchantIds(testMerchantIds);
            }
        }
    }

    public TenantTotalBillMonthlyAmountDto monthlyAmountStatForPodTenantBill(BillQueryParam param) {
        formatMerchantTestOfBillQueryParam(param);
        attachBillLimitMerchantSysUserId(param);
        List<MerchantBill> merchantBills = this.merchantBillMapperReadManager.billQueryList(param);
        SaasTotalBillMonthlyAmount amount = new SaasTotalBillMonthlyAmount();
        amount.setMonthly(DateUtil.format(new Date(param.getBeginCreateTime()), DatePattern.NORM_MONTH_FORMATTER));
        SaasTotalBillAmountService.calculateBills(amount, merchantBills);
        return null;
    }

    public SaasTotalBillMonthlyAmountDto monthlyAmountStatForSaasPlatformBill(BillQueryParam param) {
        formatMerchantTestOfBillQueryParam(param);
        attachBillLimitMerchantSysUserId(param);
        List<MerchantBill> merchantBills = this.merchantBillMapperReadManager.billQueryList(param);
        SaasTotalBillMonthlyAmount amount = new SaasTotalBillMonthlyAmount();
        amount.setMonthly(DateUtil.format(new Date(param.getBeginCreateTime()), DatePattern.NORM_MONTH_FORMATTER));
        SaasTotalBillAmountService.calculateBills(amount, merchantBills);
        return ConvertUtil.dtoConvert(amount, SaasTotalBillMonthlyAmountDto.class);
    }

    public BaseDownloadDTO billQueryExportV2(BillQueryParam queryParam, DataExportRecordTypeEnum exportType) {
        OutputStream os = new ByteArrayOutputStream();
        ClassPathResource resource = new ClassPathResource(exportType.getTemplatePath());
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcel.write(os)
                    .withTemplate(resource.getInputStream())
                    .autoCloseStream(true)
                    .build();
        } catch (IOException e) {
            throw new BusinessException(e);
        }

        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        // 1. 先填充非列表数据
        if (DataExportRecordTypeEnum.POD_TENANT_DIS_BILL_CURRENT_DIS.equals(exportType)) {
            CrossTenantSupplyMonthBillDTO crossTenantSupplyMonthBillDTO = tenantDisBalanceStatFeign.disTenantDisBillMothStat(queryParam.getRelatedDisTenantId());
            log.info("crossTenantSupplyMonthBillDTO={}, param={}", JSON.toJSONString(crossTenantSupplyMonthBillDTO), JSON.toJSONString(queryParam));
            excelWriter.fill(crossTenantSupplyMonthBillDTO, writeSheet);
        } else if (DataExportRecordTypeEnum.POD_TENANT_DIS_BILL_CURRENT_SUP.equals(exportType)) {
            TenantProductDistributionMonthBillDTO tenantProductDistributionMonthBillDTO = tenantDisBalanceStatFeign.supTenantDisBillMothStat(queryParam.getRelatedSupTenantId());
            log.info("tenantProductDistributionMonthBillDTO={}, param={}", JSON.toJSONString(tenantProductDistributionMonthBillDTO), JSON.toJSONString(queryParam));
            excelWriter.fill(tenantProductDistributionMonthBillDTO, writeSheet);
        } else if (DataExportRecordTypeEnum.SAAS_PLATFORM_BILL == exportType) {
            SaasTotalBillMonthlyAmountDto amountDto = monthlyAmountStatForSaasPlatformBill(queryParam);
            excelWriter.fill(amountDto, writeSheet);
        }

        long page = 1L;
        long size = 5000L;
        queryParam.setPageSize(size);
        int totalRowNum = 0;
        while (true) {
            queryParam.setCurrentPage(page);
            PageResultDto<MerchantBillVo> dataPageList = this.billQueryPage(queryParam);
            List<MerchantBillVo> list = dataPageList.getList();
            if (CollUtil.isEmpty(list)) {
                break;
            }
            // 每批次追加到 Excel
            FillConfig fillConfig = FillConfig.builder().forceNewRow(true).build();
            excelWriter.fill(list, fillConfig, writeSheet);
            totalRowNum += list.size();
            if (list.size() < size) {
                break;
            }
            page++;
        }
        if (totalRowNum == 0) {
            // 填充空数据，清掉占位符
            excelWriter.fill(Collections.emptyList(), writeSheet);
        }
        // 3. 结束写入
        excelWriter.finish();

        String downloadUrl = this.s3Util.uploadToS3(S3ModuleEnum.EXPORT, exportType.getFileName()
                , "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                , ConvertUtil.parse(os));
        return new BaseDownloadDTO(downloadUrl);
    }


    private static String genOperateUserMapKey(String currentRole, long userId) {
        return currentRole + "-" + userId;
    }

    public Map<String, MerchantBillOperateUserVo> getOperatorUserNameMap(List<MerchantBillVo> dtoList) {
        Set<Long> merchantUserId = new HashSet<>();
        Set<Long> tenantUserId = new HashSet<>();
        Set<Long> saasUserIds = new HashSet<>();

        dtoList.forEach(item -> {
            merchantUserId.addAll(item.getMerchantSysUserIds());
            tenantUserId.addAll(item.getTenantSysUserIds());
            saasUserIds.addAll(item.getSaasSysUserIds());
        });

        Map<String, MerchantBillOperateUserVo> operateUserVoHashMap = new HashMap<>(10);
        if (CollUtil.isNotEmpty(merchantUserId)) {
            SysUserIdsParam param = new SysUserIdsParam();
            param.setSysUserIds(new ArrayList<>(merchantUserId));
            List<MerchantSysUserRespDto> merchantSysUserList = merchantSysUserFeign.getMerchantSysUserList(param);
            merchantSysUserList.forEach(item -> operateUserVoHashMap.put(
                genOperateUserMapKey(PaymentRoleEnum.MERCHANT.getCode(), item.getId()),
                new MerchantBillOperateUserVo()
                    .setOperateUserId(item.getId())
                    .setOperateUserName(item.getUsername())
                    .setOperateRoleCode(PaymentRoleEnum.MERCHANT.getCode())
                    .setOperateRoleId(item.getMerchantId())
            ));
        }
        if (CollUtil.isNotEmpty(tenantUserId)) {
            List<TenantSysUserDto> tenantSysUserDtos = tenantSysUserFeign.findByIds("tenantName", new ArrayList<>(tenantUserId));

            Set<Long> tenantId = tenantSysUserDtos.stream().map(TenantSysUserDto::getTenantId).collect(Collectors.toSet());
            Map<Long, String> tenantNameMap = tenantService.getTenantNameMap(tenantId);
            for (TenantSysUserDto tenantSysUserDto : tenantSysUserDtos) {
                tenantSysUserDto.setTenantName(tenantNameMap.get(tenantSysUserDto.getTenantId()));
            }

            tenantSysUserDtos.forEach(item -> operateUserVoHashMap.put(
                genOperateUserMapKey(PaymentRoleEnum.TENANT.getCode(), item.getId()),
                new MerchantBillOperateUserVo()
                    .setOperateUserId(item.getId())
                    .setOperateUserName(item.getUserName())
                    .setOperateRoleName(item.getTenantName())
                    .setOperateRoleCode(PaymentRoleEnum.TENANT.getCode())
                    .setOperateRoleId(item.getTenantId())
            ));

        }
        if (CollUtil.isNotEmpty(saasUserIds)) {
            List<SaasSysUserDto> saasSysUserDtos = saasSysUserFeign.simpleDetailByUids("", new ArrayList<>(saasUserIds));
            saasSysUserDtos.forEach(item -> operateUserVoHashMap.put(
                genOperateUserMapKey(PaymentRoleEnum.SAAS.getCode(), item.getId()),
                new MerchantBillOperateUserVo()
                    .setOperateUserId(item.getId())
                    .setOperateUserName(item.getUserName())
                    .setOperateRoleCode(PaymentRoleEnum.SAAS.getCode())
                    .setOperateRoleId(0L)
            ));
        }

        return operateUserVoHashMap;
    }
}
