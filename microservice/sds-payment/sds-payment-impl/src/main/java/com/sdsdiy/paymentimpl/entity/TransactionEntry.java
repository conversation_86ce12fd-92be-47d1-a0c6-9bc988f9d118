package com.sdsdiy.paymentimpl.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TransactionEntry {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String tradeNo;
    private String bizNoForBill;
    private String parentTradeNo;
    private String paymentBizNo;
    private String refundBizNo;

    private BigDecimal balance;
    private BigDecimal bonus;
    private Integer type;

    private String title;
    private String remark;

    private Long createdAt;

    public boolean currentIsPay() {
        return BigDecimal.ZERO.compareTo(balance) < 0;
    }

    public boolean currentIsRefund() {
        return BigDecimal.ZERO.compareTo(balance) > 0;
    }
}
