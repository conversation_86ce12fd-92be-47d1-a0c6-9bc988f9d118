package com.sdsdiy.paymentimpl.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.paymentapi.constant.DetailPurpose;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import com.sdsdiy.paymentapi.param.bill.*;
import com.sdsdiy.paymentimpl.entity.MerchantBill;
import com.sdsdiy.paymentimpl.mapper.MerchantBillMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * (MerchantBill)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-04 21:47:31
 */
@Service
@Slf4j
@DS("masterslave")
public class MerchantBillMapperReadManager extends ServiceImpl<MerchantBillMapper, MerchantBill> {

    /**
     * 商户-租户 账单查询
     *
     * @param param
     *
     * @return
     */
    private LambdaQueryChainWrapper merchantToTenantBillQueryWrapper(MerchantToTenantBillQueryParam param) {
        return lambdaQuery()
            .ge(MerchantBill::getCreateTimestamp, param.getBeginCreateTime())
            .le(MerchantBill::getCreateTimestamp, param.getEndCreateTime())

            // 直接判断非saas关联账单，方便利用索引
            .eq(MerchantBill::getRelatedSaas, BasePoConstant.NO)
            .eq(ObjectUtil.isNotNull(param.getMerchantId()), MerchantBill::getRelatedMerchantId, param.getMerchantId())
            .eq(ObjectUtil.isNotNull(param.getTenantId()), MerchantBill::getRelatedTenantId, param.getTenantId())
            .eq(CharSequenceUtil.isNotEmpty(param.getPayChannel()), MerchantBill::getPayChannel, param.getPayChannel())

            .eq(StrUtil.isNotEmpty(param.getPayMethod()), MerchantBill::getPaymentMethod, param.getPayMethod())
            .eq(ObjectUtil.isNotNull(param.getPurposeType()), MerchantBill::getPurposeType, param.getPurposeType())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfMerchant()), MerchantBill::getAmountChangeTypeOfMerchant, param.getAmountChangeTypeOfMerchant())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfTenant()), MerchantBill::getAmountChangeTypeOfTenant, param.getAmountChangeTypeOfTenant())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfSaas()), MerchantBill::getAmountChangeTypeOfSaas, param.getAmountChangeTypeOfSaas())
            .and(StrUtil.isNotEmpty(param.getKeyword()),
                a -> a.like(StrUtil.isNotEmpty(param.getKeyword()), MerchantBill::getBizNo, param.getKeyword())
                    .or()
                    .like(StrUtil.isNotEmpty(param.getKeyword()), MerchantBill::getOrderName, param.getKeyword())
            )
            .orderByDesc(MerchantBill::getCreateTimestamp);
    }

    private LambdaQueryChainWrapper merchantToSaasBillQueryWrapper(MerchantToSaasBillQueryParam param) {
        return lambdaQuery()
            .ge(MerchantBill::getCreateTimestamp, param.getBeginCreateTime())
            .le(MerchantBill::getCreateTimestamp, param.getEndCreateTime())

            .eq(MerchantBill::getRelatedSaas, BasePoConstant.YES)
            .eq(ObjectUtil.isNotNull(param.getMerchantId()), MerchantBill::getRelatedMerchantId, param.getMerchantId())

            .eq(StrUtil.isNotEmpty(param.getPayMethod()), MerchantBill::getPaymentMethod, param.getPayMethod())
            .eq(CharSequenceUtil.isNotEmpty(param.getPayChannel()), MerchantBill::getPayChannel, param.getPayChannel())
            .eq(ObjectUtil.isNotNull(param.getPurposeType()), MerchantBill::getPurposeType, param.getPurposeType())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfMerchant()), MerchantBill::getAmountChangeTypeOfMerchant, param.getAmountChangeTypeOfMerchant())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfTenant()), MerchantBill::getAmountChangeTypeOfTenant, param.getAmountChangeTypeOfTenant())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfSaas()), MerchantBill::getAmountChangeTypeOfSaas, param.getAmountChangeTypeOfSaas())
            .and(StrUtil.isNotEmpty(param.getKeyword()),
                a -> a.like(StrUtil.isNotEmpty(param.getKeyword()), MerchantBill::getBizNo, param.getKeyword())
                    .or()
                    .like(StrUtil.isNotEmpty(param.getKeyword()), MerchantBill::getOrderName, param.getKeyword())
            )
            .orderByDesc(MerchantBill::getCreateTimestamp);
    }

    private LambdaQueryChainWrapper tenantToSaasBillQueryWrapper(TenantToSaasBillQueryParam param) {
        return lambdaQuery()
            .ge(MerchantBill::getCreateTimestamp, param.getBeginCreateTime())
            .le(MerchantBill::getCreateTimestamp, param.getEndCreateTime())

            .eq(MerchantBill::getRelatedSaas, BasePoConstant.YES)
            .eq(ObjectUtil.isNotNull(param.getTenantId()), MerchantBill::getRelatedTenantId, param.getTenantId())

            .eq(StrUtil.isNotEmpty(param.getPayMethod()), MerchantBill::getPaymentMethod, param.getPayMethod())
            .eq(StrUtil.isNotEmpty(param.getPayChannel()), MerchantBill::getPayChannel, param.getPayChannel())
            .eq(ObjectUtil.isNotNull(param.getPurposeType()), MerchantBill::getPurposeType, param.getPurposeType())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfMerchant()), MerchantBill::getAmountChangeTypeOfMerchant, param.getAmountChangeTypeOfMerchant())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfTenant()), MerchantBill::getAmountChangeTypeOfTenant, param.getAmountChangeTypeOfTenant())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfSaas()), MerchantBill::getAmountChangeTypeOfSaas, param.getAmountChangeTypeOfSaas())
            .eq(StrUtil.isNotEmpty(param.getOperateTargetRole()), MerchantBill::getOperateTargetRole, param.getOperateTargetRole())
            .eq(ObjectUtil.isNotNull(param.getOperateTargetRoleId()), MerchantBill::getOperateTargetRoleId, param.getOperateTargetRoleId())
            .and(StrUtil.isNotEmpty(param.getKeyword()),
                a -> a.like(StrUtil.isNotEmpty(param.getKeyword()), MerchantBill::getBizNo, param.getKeyword())
                    .or()
                    .like(StrUtil.isNotEmpty(param.getKeyword()), MerchantBill::getOrderName, param.getKeyword())
            )
            .orderByDesc(MerchantBill::getCreateTimestamp);
    }


    private LambdaQueryChainWrapper tenantTotalBillQueryWrapper(TenantTotalBillQueryParam param) {
        return lambdaQuery()
            .ge(MerchantBill::getCreateTimestamp, param.getBeginCreateTime())
            .le(MerchantBill::getCreateTimestamp, param.getEndCreateTime())

            // 直接取relatedSaas的全部值，方便走索引
            .eq(MerchantBill::getRelatedSaas, BasePoConstant.NO)
            .eq(ObjectUtil.isNotNull(param.getTenantId()), MerchantBill::getRelatedTenantId, param.getTenantId())

            .eq(StrUtil.isNotEmpty(param.getPayMethod()), MerchantBill::getPaymentMethod, param.getPayMethod())
            .eq(StrUtil.isNotEmpty(param.getPayChannel()), MerchantBill::getPayChannel, param.getPayChannel())
            .eq(ObjectUtil.isNotNull(param.getPurposeType()), MerchantBill::getPurposeType, param.getPurposeType())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfMerchant()), MerchantBill::getAmountChangeTypeOfMerchant, param.getAmountChangeTypeOfMerchant())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfTenant()), MerchantBill::getAmountChangeTypeOfTenant, param.getAmountChangeTypeOfTenant())
            .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfSaas()), MerchantBill::getAmountChangeTypeOfSaas, param.getAmountChangeTypeOfSaas())
            .and(StrUtil.isNotEmpty(param.getKeyword()),
                a -> a.like(MerchantBill::getBizNo, param.getKeyword())
                    .or()
                    .like(MerchantBill::getOrderName, param.getKeyword())
            )
            .orderByDesc(MerchantBill::getCreateTimestamp);
    }


    private LambdaQueryChainWrapper saasTotalBillQueryWrapper(SaasTotalBillQueryParam param) {
        return lambdaQuery()
                .ge(MerchantBill::getCreateTimestamp, param.getBeginCreateTime())
                .le(MerchantBill::getCreateTimestamp, param.getEndCreateTime())
                .eq(MerchantBill::getRelatedSaas, BasePoConstant.YES)
                .eq(StrUtil.isNotEmpty(param.getPayMethod()), MerchantBill::getPaymentMethod, param.getPayMethod())
            .eq(StrUtil.isNotEmpty(param.getPayChannel()), MerchantBill::getPayChannel, param.getPayChannel())
                .eq(ObjectUtil.isNotNull(param.getPurposeType()), MerchantBill::getPurposeType, param.getPurposeType())
                .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfMerchant()), MerchantBill::getAmountChangeTypeOfMerchant, param.getAmountChangeTypeOfMerchant())
                .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfTenant()), MerchantBill::getAmountChangeTypeOfTenant, param.getAmountChangeTypeOfTenant())
                .eq(StrUtil.isNotEmpty(param.getAmountChangeTypeOfSaas()), MerchantBill::getAmountChangeTypeOfSaas, param.getAmountChangeTypeOfSaas())
            .eq(StrUtil.isNotEmpty(param.getRole()), MerchantBill::getAmountChangerRole, param.getRole())
                .in(CollUtil.isNotEmpty(param.getTestMerchantIds()), MerchantBill::getRelatedMerchantId, param.getTestMerchantIds())
                .notIn(CollUtil.isNotEmpty(param.getNoTestMerchantIds()), MerchantBill::getRelatedMerchantId, param.getNoTestMerchantIds())
            .and(StrUtil.isNotEmpty(param.getKeyword()),
                a -> a.like(MerchantBill::getBizNo, param.getKeyword())
                    .or()
                    .like(MerchantBill::getOrderName, param.getKeyword())
            )
            .orderByDesc(MerchantBill::getCreateTimestamp);
    }


    private LambdaQueryChainWrapper merchantTotalBillQueryWrapper(MerchantTotalBillQueryParam param) {
        List<Integer> relatedSaasCondition = new ArrayList<>();
        if (ObjectUtil.isNotNull(param.getRelatedSaas())) {
            relatedSaasCondition.add(param.getRelatedSaas());
        } else {
            relatedSaasCondition.add(BasePoConstant.YES);
            relatedSaasCondition.add(BasePoConstant.NO);
        }

        return lambdaQuery()
            .ge(MerchantBill::getCreateTimestamp, param.getBeginCreateTime())
            .le(MerchantBill::getCreateTimestamp, param.getEndCreateTime())

            // 直接取relatedSaas的全部值，方便走索引
            .in(MerchantBill::getRelatedSaas, relatedSaasCondition)
            .eq(ObjectUtil.isNotNull(param.getMerchantId()), MerchantBill::getRelatedMerchantId, param.getMerchantId())
            .ne(PaymentRoleEnum.MERCHANT.getCode().equals(param.getCurrentRoleCode()), MerchantBill::getDetailPurpose, DetailPurpose.TENANT_OPEN_ONLINE_PAY.getCode())

            .eq(CharSequenceUtil.isNotEmpty(param.getPayMethod()), MerchantBill::getPaymentMethod, param.getPayMethod())
            .eq(CharSequenceUtil.isNotEmpty(param.getPayChannel()), MerchantBill::getPayChannel, param.getPayChannel())
            .eq(ObjectUtil.isNotNull(param.getPurposeType()), MerchantBill::getPurposeType, param.getPurposeType())
            .eq(CharSequenceUtil.isNotEmpty(param.getAmountChangeTypeOfMerchant()), MerchantBill::getAmountChangeTypeOfMerchant, param.getAmountChangeTypeOfMerchant())
            .eq(CharSequenceUtil.isNotEmpty(param.getAmountChangeTypeOfTenant()), MerchantBill::getAmountChangeTypeOfTenant, param.getAmountChangeTypeOfTenant())
            .eq(CharSequenceUtil.isNotEmpty(param.getAmountChangeTypeOfSaas()), MerchantBill::getAmountChangeTypeOfSaas, param.getAmountChangeTypeOfSaas())

            .and(ObjectUtil.isNotNull(param.getMerchantOperateUserId()),
                a -> a.eq(MerchantBill::getOperateRole, PaymentRoleEnum.MERCHANT.getCode())
                    .eq(MerchantBill::getOperateUserId, param.getMerchantOperateUserId())
            )
            .and(CollUtil.isNotEmpty(param.getLimitMerchantUserIds()),
                a -> a.eq(MerchantBill::getOperateRole, PaymentRoleEnum.MERCHANT.getCode())
                    .in(MerchantBill::getOperateUserId, param.getLimitMerchantUserIds())
            ).and(CharSequenceUtil.isNotEmpty(param.getKeyword()),
                a -> a.like(MerchantBill::getBizNo, param.getKeyword())
                    .or()
                    .like(MerchantBill::getOrderName, param.getKeyword())
            )
            .orderByDesc(MerchantBill::getCreateTimestamp);
    }


    /**
     * 租户去查看下属某个商户的账单
     * 只查询当前商户跟当前租户的账单
     *
     * @param
     *
     * @return
     */
    public List<MerchantBill> findAllMerchantToTenantBill(MerchantToTenantBillQueryParam param) {
        return merchantToTenantBillQueryWrapper(param).list();
    }

    public Page<MerchantBill> findPageMerchantToTenantBill(MerchantToTenantBillQueryParam param, Page page) {
        return (Page<MerchantBill>) merchantToTenantBillQueryWrapper(param).page(page);
    }

    public List<MerchantBill> findAllMerchantToSaasBill(MerchantToSaasBillQueryParam param) {
        return merchantToSaasBillQueryWrapper(param).list();
    }

    public Page<MerchantBill> findPageMerchantToSaasBill(MerchantToSaasBillQueryParam param, Page page) {
        return (Page<MerchantBill>) merchantToSaasBillQueryWrapper(param).page(page);
    }

    public List<MerchantBill> findAllTenantToSaasBill(TenantToSaasBillQueryParam param) {
        return tenantToSaasBillQueryWrapper(param).list();
    }

    public Page<MerchantBill> findPageTenantToSaasBill(TenantToSaasBillQueryParam param, Page page) {
        return (Page<MerchantBill>) tenantToSaasBillQueryWrapper(param).page(page);
    }


    public List<MerchantBill> findAllTenantTotalBill(TenantTotalBillQueryParam queryParam) {
        return tenantTotalBillQueryWrapper(queryParam).list();
    }

    public Page<MerchantBill> findPageTenantTotalBill(TenantTotalBillQueryParam param, Page page) {
        return (Page<MerchantBill>) tenantTotalBillQueryWrapper(param).page(page);
    }


    public List<MerchantBill> findAllSaasTotalBill(SaasTotalBillQueryParam queryParam) {
        return saasTotalBillQueryWrapper(queryParam).list();
    }

    public Page<MerchantBill> findPageSaasTotalBill(SaasTotalBillQueryParam param, Page page) {
        return (Page<MerchantBill>) saasTotalBillQueryWrapper(param).page(page);
    }

    public List<MerchantBill> findAllMerchantTotalBill(MerchantTotalBillQueryParam queryParam) {
        return merchantTotalBillQueryWrapper(queryParam).list();
    }

    public Page<MerchantBill> findPageMerchantTotalBill(MerchantTotalBillQueryParam param, Page page) {
        return (Page<MerchantBill>) merchantTotalBillQueryWrapper(param).page(page);
    }

    public Page<MerchantBill> findAllMerchantBill(Page page) {
        return lambdaQuery().page(page);
    }

    public int findMerchantBillCount() {
        return lambdaQuery().count();
    }

    public Page<MerchantBill> billQueryPage(BillQueryParam param, Page<MerchantBill> page) {
        return genQueryChain(param).page(page);
    }

    public List<MerchantBill> billQueryList(BillQueryParam param) {
        return genQueryChain(param).list();
    }

    private LambdaQueryChainWrapper<MerchantBill> genQueryChain(BillQueryParam param) {
        return lambdaQuery()
            .ge(MerchantBill::getCreateTimestamp, param.getBeginCreateTime())
            .le(MerchantBill::getCreateTimestamp, param.getEndCreateTime())
            .eq(CharSequenceUtil.isNotEmpty(param.getPaymentMethod()), MerchantBill::getPaymentMethod, param.getPaymentMethod())
            .eq(CharSequenceUtil.isNotEmpty(param.getPayChannel()), MerchantBill::getPayChannel, param.getPayChannel())
            .eq(ObjectUtil.isNotNull(param.getPurposeType()), MerchantBill::getPurposeType, param.getPurposeType())
            .eq(CharSequenceUtil.isNotEmpty(param.getAmountChangeTypeOfMerchant()), MerchantBill::getAmountChangeTypeOfMerchant, param.getAmountChangeTypeOfMerchant())
            .eq(CharSequenceUtil.isNotEmpty(param.getAmountChangeTypeOfTenant()), MerchantBill::getAmountChangeTypeOfTenant, param.getAmountChangeTypeOfTenant())
            .eq(CharSequenceUtil.isNotEmpty(param.getAmountChangeTypeOfSaas()), MerchantBill::getAmountChangeTypeOfSaas, param.getAmountChangeTypeOfSaas())
            .eq(CharSequenceUtil.isNotEmpty(param.getAmountChangeTypeOfSupTenant()), MerchantBill::getAmountChangeTypeOfSupTenant, param.getAmountChangeTypeOfSupTenant())
            .eq(CharSequenceUtil.isNotEmpty(param.getAmountChangeTypeOfDisTenant()), MerchantBill::getAmountChangeTypeOfDisTenant, param.getAmountChangeTypeOfDisTenant())
            .eq(param.getRelatedSupTenantId() != null, MerchantBill::getRelatedSupTenantId, param.getRelatedSupTenantId())
            .eq(param.getRelatedDisTenantId() != null, MerchantBill::getRelatedDisTenantId, param.getRelatedDisTenantId())
            .eq(param.getRelatedTenantId() != null, MerchantBill::getRelatedTenantId, param.getRelatedTenantId())
            .eq(param.getRelatedMerchantId() != null, MerchantBill::getRelatedMerchantId, param.getRelatedMerchantId())
            .eq(param.getRelatedSaas() != null, MerchantBill::getRelatedSaas, param.getRelatedSaas())
            .eq(CharSequenceUtil.isNotEmpty(param.getOperateRole()), MerchantBill::getOperateRole, param.getOperateRole())
            .eq(ObjectUtil.isNotNull(param.getOperateUserId()), MerchantBill::getOperateUserId, param.getOperateUserId())
            .in(CollUtil.isNotEmpty(param.getTestMerchantIds()), MerchantBill::getRelatedMerchantId, param.getTestMerchantIds())
            .notIn(CollUtil.isNotEmpty(param.getNoTestMerchantIds()), MerchantBill::getRelatedMerchantId, param.getNoTestMerchantIds())
            .and(CollUtil.isNotEmpty(param.getLimitMerchantUserIds()),
                a -> a.eq(MerchantBill::getOperateRole, PaymentRoleEnum.MERCHANT.getCode())
                    .in(MerchantBill::getOperateUserId, param.getLimitMerchantUserIds())
            ).and(StrUtil.isNotEmpty(param.getKeyword()),
                a -> a.like(MerchantBill::getBizNo, param.getKeyword())
                    .or()
                    .like(MerchantBill::getOrderName, param.getKeyword())
            ).orderByDesc(MerchantBill::getId);
    }
}
