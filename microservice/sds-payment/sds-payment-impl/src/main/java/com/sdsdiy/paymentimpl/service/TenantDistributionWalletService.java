package com.sdsdiy.paymentimpl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.TenantDistributionQueryParam;
import com.sdsdiy.paymentapi.param.distribution.AddDistributionReq;
import com.sdsdiy.paymentapi.param.distribution.TenantDisWalletRechargeParam;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.entity.TenantDisWallet;
import com.sdsdiy.paymentimpl.manager.TenantDistributorWalletManager;
import com.sdsdiy.paymentimpl.service.transaction.TransactionPayService;
import com.sdsdiy.paymentimpl.util.TradeNoUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class TenantDistributionWalletService {

    private final TenantDistributorWalletManager tenantDistributorWalletManager;
    private final TransactionPayService transactionPayService;

    public List<TenantDistributorWalletDto> queryWallets(TenantDistributionQueryParam param) {
        if (param == null) {
            return Collections.emptyList();
        }

        List<TenantDisWallet> wallets;

        if (CollUtil.isNotEmpty(param.getSupTenantId()) && CollUtil.isNotEmpty(param.getDisTenantIds())) {
            wallets = tenantDistributorWalletManager.listBySupAndDisTenantIds(param.getSupTenantId(), param.getDisTenantIds());
        } else if (CollUtil.isNotEmpty(param.getSupTenantId())) {
            wallets = tenantDistributorWalletManager.listBySupTenantIds(param.getSupTenantId());
        } else if (CollUtil.isNotEmpty(param.getDisTenantIds())) {
            wallets = tenantDistributorWalletManager.listByDisTenantIds(param.getDisTenantIds());
        } else {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(wallets, TenantDistributorWalletDto.class);
    }

    public TenantDistributorWalletDto unlinkWallet(Long supTenantId, Long disTenantId, Long updateUid) {
        if (supTenantId == null || disTenantId == null) {
            throw new BusinessException("供应商租户ID和分销商租户ID不能为空");
        }

        TenantDisWallet wallet = tenantDistributorWalletManager.getWalletNoMatterIfDelete(disTenantId, supTenantId);
        if (wallet == null) {
            throw new BusinessException("钱包不存在");
        }
        if (BigDecimal.ZERO.compareTo(wallet.getBalance()) != 0
            || BigDecimal.ZERO.compareTo(wallet.getGiftMoney()) != 0) {
            throw new BusinessException("授信余额不等于0，不可删除");
        }

        wallet.setIsDelete(1);
        wallet.setUpdateUid(updateUid);
        boolean success = tenantDistributorWalletManager.updateById(wallet);
        if (!success) {
            throw new BusinessException("删除钱包失败");
        }
        return BeanUtil.toBean(wallet, TenantDistributorWalletDto.class);
    }

    public TenantDistributorWalletDto createWallet(AddDistributionReq addDistributionReq) {
        log.info("createWallet addDistributionReq = {}", JSONObject.toJSON(addDistributionReq));
        if (addDistributionReq == null) {
            throw new BusinessException("供应商租户ID和分销商租户ID不能为空");
        }
        Long supTenantId = addDistributionReq.getSupplierTenantId();
        Long disTenantId = addDistributionReq.getDistributorTenantId();

        if (supTenantId.equals(disTenantId)) {
            throw new BusinessException("供应商租户ID和分销商租户ID不能相同");
        }

        TenantDisWallet existWallet = tenantDistributorWalletManager.getWalletNoMatterIfDelete(disTenantId, supTenantId);
        if (Objects.nonNull(existWallet)) {
            if (BasePoConstant.yes(existWallet.getIsDelete())) {
                existWallet.setIsDelete(BasePoConstant.NO);
                existWallet.setUpdateUid(addDistributionReq.getUserId());
                existWallet.setDistributorAgreementUrl(StrUtil.emptyToDefault(addDistributionReq.getDistributorAgreementUrl(), ""));
                existWallet.setDistributorAgreementName(StrUtil.emptyToDefault(addDistributionReq.getDistributorAgreementName(), ""));
                tenantDistributorWalletManager.updateById(existWallet);
                return BeanUtil.toBean(existWallet, TenantDistributorWalletDto.class);
            }
            throw new BusinessException("该分销商已添加，请刷新后重试。");
        }

        TenantDisWallet wallet = tenantDistributorWalletManager.createWallet(supTenantId,
                disTenantId,
                addDistributionReq.getDistributorAgreementName(),
                addDistributionReq.getDistributorAgreementUrl(),
                addDistributionReq.getUserId());
        return BeanUtil.toBean(wallet, TenantDistributorWalletDto.class);
    }

    public Payment createForRechange(TenantDisWalletRechargeParam operateParam) {
        String bizNo = TradeNoUtil.generateBizNo();
        PaymentParam paymentParam = new PaymentParam()
            .setMethod(operateParam.getPaymentMethod())
            .setPayType(TransactionPayTypeEnum.MAIN.getValue())
            .setTitle("充值")
            .setBalance(operateParam.getAmount())
            .setBonus(BigDecimal.ZERO)
            .setBizNo(bizNo)
            .setPurposeType(PurposeType.RECHARGE.getCode())
            .setDetailPurpose(DetailPurpose.DIS_TENANT_ALIPAY_RECHARGE_TO_SUP_TENANT.getCode())
            .setBillType(PaymentBillTypeEnum.DIRECT.getStatus());

        paymentParam.setSourceRole(PaymentRoleEnum.TENANT_DIS.getCode())
            .setSourceTenantId(operateParam.getDisTenantId());
        paymentParam.setTargetRole(PaymentRoleEnum.TENANT_SUP.getCode())
            .setTargetTenantId(operateParam.getSupTenantId());

        paymentParam.setOperateRole(PaymentRoleEnum.TENANT.getCode())
                .setOperateUserId(operateParam.getOperateUserId())
                .setOperateTargetRole(PaymentRoleEnum.TENANT.getCode())
                .setOperateTargetRoleId(operateParam.getDisTenantId());

        MultiTransactionCreateParam createParam = new MultiTransactionCreateParam();
        createParam.getPaymentList().add(paymentParam);
        return transactionPayService.createForPay(createParam);
    }

    public List<TenantDistributorWalletDto> findAllBalance() {
        List<TenantDisWallet> list = tenantDistributorWalletManager.findAllBalance();
        return ListUtil.copyProperties(list, TenantDistributorWalletDto.class);
    }
}
