package com.sdsdiy.paymentapi.api;

import com.sdsdiy.common.base.entity.dto.BaseDownloadDTO;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.paymentapi.dto.*;
import com.sdsdiy.paymentapi.param.bill.BillQueryParam;
import com.sdsdiy.paymentapi.param.bill.MerchantBillsCreateParam;
import com.sdsdiy.statdata.constant.DataExportRecordTypeEnum;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Api(tags = "")
@RequestMapping("/microservice/merchantBill")
@Validated
public interface MerchantBillApi {

    @PostMapping("/batchCreate")
    @Deprecated
    List<MerchantBillDto> batchCreate(
            @RequestBody @Validated MerchantBillsCreateParam param
    );

    @PostMapping("/billQueryPage")
    PageResultDto<MerchantBillVo> billQueryPage(@RequestBody BillQueryParam param);

    @PostMapping("/billQueryList")
    List<MerchantBillVo> billQueryList(@RequestBody BillQueryParam param);

    @PostMapping("/billQueryExport")
    BaseDownloadDTO billQueryExport(@RequestBody BillQueryParam param,
                                    @RequestParam DataExportRecordTypeEnum exportType);

    @PostMapping("/monthlyAmountStatForMerchant")
    BillMonthlyAmountOfMerchantVo monthlyAmountStatForMerchant(@RequestBody BillQueryParam param);

    @PostMapping("/monthlyAmountStatForSaasPlatformBill")
    SaasTotalBillMonthlyAmountDto monthlyAmountStatForSaasPlatformBill(@RequestBody BillQueryParam param);

    @PostMapping("/monthlyAmountStatForPodTenantBill")
    TenantTotalBillMonthlyAmountDto monthlyAmountStatForPodTenantBill(@RequestBody BillQueryParam param);
}
