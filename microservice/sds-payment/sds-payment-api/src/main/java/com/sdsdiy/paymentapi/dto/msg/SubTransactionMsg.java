package com.sdsdiy.paymentapi.dto.msg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SubTransactionMsg {

    private String parentTradeNo;
    private Long tradeNo;
    private Long paymentId;
    private Long refundId;
    private String createTime;
}
