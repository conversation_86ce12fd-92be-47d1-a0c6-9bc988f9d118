package com.sdsdiy.paymentapi.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MerchantBillOperateUserVo {


    @ApiModelProperty(value = "具体用户名称")
    private String operateUserName;
    @ApiModelProperty(value = "具体用户ID")
    private Long operateUserId;

    @ApiModelProperty("当前查看的角色，MERCHANT/TENANT/SAAS")
    private String operateRoleCode;
    @ApiModelProperty(value = "商户ID/租户ID")
    private Long operateRoleId/**/;
    @ApiModelProperty(value = "商户名称或者租户名称")
    private String operateRoleName;

}
