package com.sdsdiy.paymentapi.param;


import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.paymentapi.constant.BalanceUsedType;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@Data
@Builder
@NoArgsConstructor
@Accessors(chain = true)
@AllArgsConstructor
public class RefundParam {

    /*** {@link com.sdsdiy.paymentapi.constant.TransactionPayTypeEnum}*/
    @ApiModelProperty(value = "区分是否是主交易", notes = "主交易业务方需要手动调用，子交易由支付系统自动处理")
    @NotBlank(message = "payType不能为空")
    private Integer payType;

    /*** 操作者角色*/
    @NotNull(message = "operateRole不能为空")
    private String operateRole;
    @NotNull(message = "operateUserId不能为空")
    private Long operateUserId;
    /*** {@link com.sdsdiy.paymentapi.constant.BalanceUsedType} */
    @ApiModelProperty("使用的余额类型")
    private Integer balanceType;

    /**
     * 记账类型
     * {@link com.sdsdiy.paymentapi.constant.PaymentBillTypeEnum}
     */
    @ApiModelProperty("记账类型")
    @NotNull(message = "billType不能为空")
    private Integer billType;

    /**
     * 操作对象角色，
     * - 比如购买订单，那这边应该是商户
     * - 租户给商户购买服务，这边也应该是商户
     * - 租户找SAAS购买服务，这边是租户
     * - 即，当前交易对应的业务处理对象
     * {@link com.sdsdiy.paymentapi.constant.PaymentRoleEnum}
     */
    @NotEmpty(message = "operateTargetRole不能为空")
    private String operateTargetRole;
    @NotNull(message = "operateTargetRoleId不能为空")
    private Long operateTargetRoleId;

    private String remark;

    @Deprecated
    Long id;

    private String bizNo;
    private String tradeNo;
    private String parentTradeNo;

    @Deprecated
    private BigDecimal totalAmount;
    private BigDecimal balance = BigDecimal.ZERO;
    private BigDecimal bonus = BigDecimal.ZERO;

    private Long sourceTenantId = 0L;
    private Long sourceMerchantId = 0L;
    private Long sourceUserId = 0L;
    private Long targetTenantId = 0L;
    private Long targetMerchantId = 0L;
    private Long targetUserId = 0L;
    private String sourceRole;
    private String targetRole;
    
    private String subject;
    @Deprecated
    private Integer balanceOperateFinish;

    private String purposeType;
    private String detailPurpose;
    private String payMethod;
    private Long paymentId;

    @Deprecated
    private Integer merchantBalanceUsedType;
    
    /*** 是否记录账单 */
    @Deprecated
    private Boolean recordBill = false;

    public void checkParam() {
        this.checkSource();
        this.checkTarget();
    }

    public void checkSource() {
        if (PaymentRoleEnum.SAAS.getCode().equals(this.getSourceRole())) {
            Assert.validateFalse(this.getSourceTenantId() == 0 && this.getSourceMerchantId() == 0 && this.getSourceUserId() == 0, "sourceRole异常");
        } else if (PaymentRoleEnum.TENANT.getCode().equals(this.getSourceRole())) {
            Assert.validateFalse(this.getSourceTenantId() > 0 && this.getSourceMerchantId() == 0 && this.getSourceUserId() == 0, "sourceRole异常");
        } else if (PaymentRoleEnum.TENANT_SUP.getCode().equals(this.getSourceRole())) {
            Assert.validateFalse(this.getSourceTenantId() > 0 && this.getSourceMerchantId() == 0 && this.getSourceUserId() == 0, "sourceRole异常");
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(this.getSourceRole())) {
            if (BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType().equals(this.getBalanceType())) {
                Assert.validateFalse(this.getSourceTenantId() > 0 && this.getSourceMerchantId() > 0 && this.getSourceUserId() == 0, "sourceRole异常");
            } else if (BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType().equals(this.getBalanceType())) {
                Assert.validateFalse(this.getSourceTenantId() > 0 && this.getSourceMerchantId() > 0 && this.getSourceUserId() > 0, "sourceRole异常");
            }
        } else {
            throw new BusinessException("sourceRole异常");
        }
    }

    public void checkTarget() {
        if (PaymentRoleEnum.SAAS.getCode().equals(this.getTargetRole())) {
            Assert.validateFalse(this.getTargetTenantId() == 0 && this.getTargetMerchantId() == 0, "targetRole异常");
        } else if (PaymentRoleEnum.TENANT.getCode().equals(this.getTargetRole())) {
            Assert.validateFalse(this.getTargetTenantId() > 0 && this.getTargetMerchantId() == 0, "targetRole异常");
        } else if (PaymentRoleEnum.TENANT_DIS.getCode().equals(this.getTargetRole())) {
            Assert.validateFalse(this.getTargetTenantId() > 0 && this.getTargetMerchantId() == 0, "targetRole异常");
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(this.getTargetRole())) {
            if (BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType().equals(this.getBalanceType())) {
                Assert.validateFalse(this.getTargetTenantId() > 0 && this.getTargetMerchantId() > 0, "targetRole异常");
            } else if (BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType().equals(this.getBalanceType())) {
                Assert.validateFalse(this.getTargetTenantId() > 0 && this.getTargetMerchantId() > 0 && this.getTargetUserId() > 0, "targetRole异常");
            }
        } else {
            throw new BusinessException("targetRole异常");
        }
    }
}
