package com.sdsdiy.paymentapi.api;

import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import com.sdsdiy.paymentapi.dto.wallet.TenantDisWalletCountDTO;
import com.sdsdiy.paymentapi.param.TenantDistributionQueryParam;
import com.sdsdiy.paymentapi.param.distribution.AddDistributionReq;
import com.sdsdiy.paymentapi.param.distribution.TenantDisWalletRechargeParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "")
@RequestMapping("/microservice/payment/tenantDistributionWallet")
public interface TenantDistributionWalletApi {

    @ApiOperation("获取钱包数据")
    @PostMapping("/queryWallets")
    List<TenantDistributorWalletDto> queryWallets(@RequestBody TenantDistributionQueryParam param);

    @ApiOperation("钱包提现")
    @PostMapping("/recharge")
    PaymentDto recharge(@RequestBody TenantDisWalletRechargeParam rechargeParam);

    @GetMapping("/findAllBalance")
    List<TenantDistributorWalletDto> findAllBalance();

    @GetMapping("/sumBySupTenant")
    TenantDisWalletCountDTO sumBySupTenant(@RequestParam Long tenantId);

    @GetMapping("/sumByDisTenant")
    TenantDisWalletCountDTO sumByDisTenant(@RequestParam Long tenantId);
}
