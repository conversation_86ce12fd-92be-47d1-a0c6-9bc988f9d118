package com.sdsdiy.paymentapi.dto;


import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.helper.TimeUtil;
import com.sdsdiy.paymentapi.annotation.TenantNameMapping;
import com.sdsdiy.paymentapi.constant.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MerchantBillVo {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "订单编号")
    private String bizNo;

    @ApiModelProperty(value = "金额")
    private BigDecimal changedBalance;
    @ApiModelProperty(value = "赠送金")
    private BigDecimal changedGift;

    @ApiModelProperty(value = "订单名称")
    private String orderName;

    @ApiModelProperty(value = "备注")
    private String remarks;

    private String sourceRole;
    private String targetRole;
    private Long sourceTenantId;
    private Long sourceMerchantId;
    private Long sourceUserId;
    private Long targetTenantId;
    private Long targetMerchantId;
    private Long targetUserId;

    @ApiModelProperty(value = "支付方式")
    private String paymentMethod;
    @ApiModelProperty(value = "支付渠道")
    private String payChannel;

    @ApiModelProperty(value = "支付方式")
    public String getPaymentMethodDesc() {
        PaymentMethodEnum byCode = PaymentMethodEnum.findByCode(this.paymentMethod);
        return byCode == null ? "" : byCode.getDesc();
    }

    @ApiModelProperty(value = "支付方式")
    public String getPayChannelDesc() {
        PayChannelEnum byCode = PayChannelEnum.getByCode(this.payChannel);
        return byCode == null ? "" : byCode.getDesc();
    }

    @ApiModelProperty(value = "用途")
    private String purposeType;

    @ApiModelProperty(value = "用途名称")
    public String getPurposeTypeDesc() {
        PurposeType byCode = PurposeType.findByCode(this.purposeType);
        return byCode == null ? "" : byCode.getDesc();
    }

    @ApiModelProperty(value = "详细用途")
    private String detailPurpose;

    @ApiModelProperty(value = "用途名称")
    public String getDetailPurposeDesc() {
        DetailPurpose byCode = DetailPurpose.getByCode(this.detailPurpose);
        return byCode == null ? "" : byCode.getDesc();
    }


    @ApiModelProperty(value = "创建时间，毫秒")
    private Long createTimestamp;

    public String getCreateTime() {
        return TimeUtil.format(createTimestamp, null);
    }

    /*** 操作人名字 */
    private String operatorName;
    private Boolean operatorDelFlag;
    private Boolean operatorActivate;

    private String amountChangeTypeOfMerchant;
    private String amountChangeTypeOfTenant;
    private String amountChangeTypeOfSupTenant;
    private String amountChangeTypeOfDisTenant;
    private String amountChangeTypeOfSaas;

    public String getAmountChangeTypeOfMerchantDesc() {
        Optional<AmountChangeType> byCode1 = AmountChangeType.getByCode(this.amountChangeTypeOfMerchant);
        return byCode1.orElse(AmountChangeType.NONE).getDesc();
    }

    public String getAmountChangeTypeOfTenantDesc() {
        Optional<AmountChangeType> byCode = AmountChangeType.getByCode(this.amountChangeTypeOfTenant);
        return byCode.orElse(AmountChangeType.NONE).getDesc();
    }

    public String getAmountChangeTypeOfSupTenantDesc() {
        Optional<AmountChangeType> byCode = AmountChangeType.getByCode(this.amountChangeTypeOfSupTenant);
        return byCode.orElse(AmountChangeType.NONE).getDesc();
    }

    public String getAmountChangeTypeOfDisTenantDesc() {
        Optional<AmountChangeType> byCode = AmountChangeType.getByCode(this.amountChangeTypeOfDisTenant);
        return byCode.orElse(AmountChangeType.NONE).getDesc();
    }

    public String getAmountChangeTypeOfSaasDesc() {
        Optional<AmountChangeType> byCode = AmountChangeType.getByCode(this.amountChangeTypeOfSaas);
        return byCode.orElse(AmountChangeType.NONE).getDesc();
    }

    @ApiModelProperty("金额变动对象角色")
    private String amountChangeRole;
    @ApiModelProperty("金额变动对象角色ID")
    private Long amountChangeRoleId;
    @ApiModelProperty("金额变动对象")
    private String amountChangeRoleDetail;

    @ApiModelProperty(value = "金额变动角色名称")
    public String getAmountChangeRoleDesc() {
        PaymentRoleEnum byCode = PaymentRoleEnum.getByCode(this.getAmountChangeRole());
        return byCode == null ? "" : byCode.getDesc();
    }

    @ApiModelProperty("处理后的商户余额")
    private BigDecimal disposeMerchantBalance;
    @ApiModelProperty("处理后的商户赠送金")
    private BigDecimal disposeMerchantGift;

    @ApiModelProperty("处理后的租户余额")
    private BigDecimal disposeTenantBalance;
    @ApiModelProperty("处理后的租户赠送金")
    private BigDecimal disposeTenantGift;

    @ApiModelProperty("操作人角色")
    private String operateRole;
    @ApiModelProperty("操作人ID")
    private Long operateUserId;


    @ApiModelProperty("业务变更对象角色")
    private String operateTargetRole;
    @ApiModelProperty("业务变更对象角色ID")
    private Long operateTargetRoleId;
    @ApiModelProperty("业务变更对象人-名字")
    private String operateTargetRoleDetail;

    private Long relatedMerchantId;
    private Long relatedTenantId;
    private Long relatedSupTenantId;
    private Long relatedDisTenantId;
    @TenantNameMapping(idField = "relatedSupTenantId")
    private String relatedSupTenantName;
    @TenantNameMapping(idField = "relatedDisTenantId")
    private String relatedDisTenantName;

    private Integer relatedSaas;

    @Deprecated
    private String tradingObjectOfMerchantBill;
    @Deprecated
    private Long turnIntoTestAt;

    private MerchantBillOperateUserVo merchantBillOPerateUser;


    public String getTradingObjWithMerchantDesc() {
        if (BasePoConstant.YES.equals(this.getRelatedSaas())) {
            return PaymentRoleEnum.SAAS.getDesc();
        }
        if (this.getRelatedTenantId() > 0) {
            return PaymentRoleEnum.TENANT.getDesc();
        }
        return "";
    }


    @ApiModelProperty("当前查看的角色，MERCHANT/TENANT/SAAS")
    private String currentViewRoleCode;
    @ApiModelProperty("当前查看的角色ID，MERCHANT ID/TENANT ID/SAAS 0")
    private Long currentViewRoleId;


    public String getOperateUserDetail() {
        if (PaymentRoleEnum.SYSTEM.getCode().equals(this.operateRole)) {
            return "系统";
        }

        if (merchantBillOPerateUser == null) {
            return "";
        }

        String operateRoleCode = merchantBillOPerateUser.getOperateRoleCode();
        String operateUserName = merchantBillOPerateUser.getOperateUserName();

        // 1. 如果operateUserRoleName是SYSTEM，都展示系统

        // 获取角色级别
        int currentViewRoleLevel = PaymentRoleEnum.getLevelByCode(currentViewRoleCode);
        int operateRoleLevel = PaymentRoleEnum.getLevelByCode(operateRoleCode);

        if (currentViewRoleLevel < operateRoleLevel) {
            return "平台";
        }

        if (currentViewRoleLevel > operateRoleLevel) {
            String roleDesc = PaymentRoleEnum.getDescByCode(operateRoleCode);
            return String.format("%s[%s]", roleDesc, operateUserName);
        }
        if (Objects.equals(currentViewRoleId, merchantBillOPerateUser.getOperateRoleId())) {
            String roleDesc = PaymentRoleEnum.getDescByCode(operateRoleCode);
            return String.format("%s[%s]", roleDesc, operateUserName);
        } else {
            String roleDesc = PaymentRoleEnum.getDescByCode(operateRoleCode);
            return String.format("%s[%s]", roleDesc, merchantBillOPerateUser.getOperateRoleName());
        }
    }

    public Set<Long> getAllRelatedMerchant() {
        Set<Long> merchantIds = new LinkedHashSet<>();
        merchantIds.add(relatedMerchantId);
        if (PaymentRoleEnum.MERCHANT.getCode().equals(this.amountChangeRole)) {
            merchantIds.add(this.amountChangeRoleId);
        }
        if (PaymentRoleEnum.MERCHANT.getCode().equals(this.operateTargetRole)) {
            merchantIds.add(this.operateTargetRoleId);
        }
        return merchantIds;
    }

    public Set<Long> getAllRelatedTenantId() {
        Set<Long> ids = new LinkedHashSet<>();
        ids.add(relatedMerchantId);
        if (PaymentRoleEnum.TENANT.getCode().equals(this.amountChangeRole)) {
            ids.add(this.amountChangeRoleId);
        }
        if (PaymentRoleEnum.TENANT.getCode().equals(this.operateTargetRole)) {
            ids.add(this.operateTargetRoleId);
        }
        ids.add(this.relatedDisTenantId);
        ids.add(this.relatedSupTenantId);
        return ids;
    }

    public Set<Long> getMerchantSysUserIds() {
        Set<Long> ids = new HashSet<>();
        if (PaymentRoleEnum.MERCHANT.getCode().equals(this.operateRole)) {
            ids.add(this.operateUserId);
        }
        return ids;
    }

    public Set<Long> getTenantSysUserIds() {
        Set<Long> ids = new HashSet<>();
        if (PaymentRoleEnum.TENANT.getCode().equals(this.operateRole)) {
            ids.add(this.operateUserId);
        }
        return ids;
    }

    public Set<Long> getSaasSysUserIds() {
        Set<Long> ids = new HashSet<>();
        if (PaymentRoleEnum.SAAS.getCode().equals(this.operateRole)) {
            ids.add(this.operateUserId);
        }
        return ids;
    }
}
