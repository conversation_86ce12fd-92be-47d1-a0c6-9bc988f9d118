package com.sdsdiy.paymentapi.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
@NoArgsConstructor
@Accessors(chain = true)
@AllArgsConstructor
public class BillMonthlyAmountOfMerchantVo {

    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 消费
     */
    private BigDecimal consume = BigDecimal.ZERO;

    /**
     * 收入
     */
    private BigDecimal income = BigDecimal.ZERO;

    /**
     * 收入-余额
     */
    private BigDecimal balanceOfIncome = BigDecimal.ZERO;

    /**
     * 收入-线下
     */
    private BigDecimal offlineOfIncome = BigDecimal.ZERO;

    /**
     * 收入-赠送金
     */
    private BigDecimal giftOfIncome = BigDecimal.ZERO;

    private BigDecimal alipayOfIncome = BigDecimal.ZERO;
    private BigDecimal lakalaOfIncome = BigDecimal.ZERO;
    private BigDecimal wechatChannelOfIncome = BigDecimal.ZERO;
    private BigDecimal unionPayChannelOfIncome = BigDecimal.ZERO;
    private BigDecimal alipayChannelOfIncome = BigDecimal.ZERO;

    private BigDecimal alipayOfExpend = BigDecimal.ZERO;
    private BigDecimal lakalaOfExpend = BigDecimal.ZERO;
    private BigDecimal wechatChannelOfExpend = BigDecimal.ZERO;
    private BigDecimal unionPayChannelOfExpend = BigDecimal.ZERO;
    private BigDecimal alipayChannelOfExpend = BigDecimal.ZERO;


    /**
     * 收入-退款
     */
    private BigDecimal refundOfIncome = BigDecimal.ZERO;

    /**
     * 收入-赔付
     */
    private BigDecimal payoutOfIncome = BigDecimal.ZERO;

    /**
     * 支出
     */
    private BigDecimal expend = BigDecimal.ZERO;

    /**
     * 支出-余额
     */
    private BigDecimal balanceOfExpend = BigDecimal.ZERO;

    /**
     * 支出-线下
     */
    private BigDecimal offlineOfExpend = BigDecimal.ZERO;

    /**
     * 支出-赠送金
     */
    private BigDecimal giftOfExpend = BigDecimal.ZERO;

    /**
     * 支出-购买产品
     */
    private BigDecimal buyProductOfExpend = BigDecimal.ZERO;

    /**
     * 支出-购买服务
     */
    private BigDecimal buyServiceOfExpend = BigDecimal.ZERO;

    /**
     * 充值
     */
    private BigDecimal recharge = BigDecimal.ZERO;

    /**
     * 充值-账户
     */
    private BigDecimal balanceOfRecharge = BigDecimal.ZERO;

    /**
     * 充值-赠送金
     */
    private BigDecimal giftOfRecharge = BigDecimal.ZERO;

    /**
     * 提现
     */
    private BigDecimal withdraw = BigDecimal.ZERO;

    /**
     * 提现-账户
     */
    private BigDecimal balanceOfWithdraw = BigDecimal.ZERO;

    /**
     * 提现-赠送金
     */
    private BigDecimal giftOfWithdraw = BigDecimal.ZERO;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 月份字符串，yyyy-MM
     */
    private String monthly;

    /**
     * 支出-购买素材
     */
    private BigDecimal buyMaterialOfExpend = BigDecimal.ZERO;
    ;

    /*** 支出-寄付运费 */
    @ApiModelProperty("支出-寄付运费")
    private BigDecimal adminPrepayShippingOfExpend = BigDecimal.ZERO;

    /*** 支出-换单差价 */
    @ApiModelProperty("支出-换单差价")
    private BigDecimal changeWaybillOfExpend = BigDecimal.ZERO;

    /*** 支出-计抛差价*/
    @ApiModelProperty("支出-计抛差价")
    private BigDecimal calVolumeOfExpend = BigDecimal.ZERO;

    /*** 支出-海关税费 */
    @ApiModelProperty("支出-海关税费")
    private BigDecimal customsDutyOfExpend = BigDecimal.ZERO;

    @ApiModelProperty("支出-JIT订单运费")
    private BigDecimal jitShippingCostOfExpend = BigDecimal.ZERO;

    /*** 支出-其他用户*/
    @ApiModelProperty("支出-其他用途")
    private BigDecimal otherOfExpend = BigDecimal.ZERO;

    /**
     * 统计的最大商户账单ID
     */
    private Long maxMerchantBillId;


    public void setMonthly(Long beginCreatedTime) {

    }

}
