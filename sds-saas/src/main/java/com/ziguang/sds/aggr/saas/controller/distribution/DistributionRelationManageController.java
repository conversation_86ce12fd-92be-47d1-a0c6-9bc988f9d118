package com.ziguang.sds.aggr.saas.controller.distribution;

import com.ps.ps.feign.payment.FrontTenantDistributionWalletFeign;
import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import com.sdsdiy.common.base.entity.dto.PageResult;
import com.sdsdiy.paymentapi.param.distribution.AddDistributionReq;
import com.sdsdiy.paymentapi.vo.distribution.SaasSupplierAndDistributorPageVO;
import com.ziguang.sds.aggr.saas.shiro.ISecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api("分销关系管理")
@RestController
@RequestMapping("/distributionRelationManage")
@RequiredArgsConstructor
public class DistributionRelationManageController {
    private final FrontTenantDistributionWalletFeign supplierDistributorRelFeign;
    @GetMapping("/page")
    @ApiOperation("分页查询")
    @RequiresPermissions("distribution:manage")
    public PageResult<SaasSupplierAndDistributorPageVO> page(@RequestParam(value = "supplierName", required = false) String supplierName,
                                                             @RequestParam(value = "distributorName", required = false) String distributorName,
                                                             @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                             @RequestParam(value = "size", defaultValue = "10") Integer size) {
        return supplierDistributorRelFeign.saasPage(supplierName, distributorName, page, size);
    }


    @DeleteMapping("/removeDistributor")
    @ApiOperation("删除分销商")
    @RequiresPermissions("distribution:manage")
    public void removeDistributor(@RequestParam("supplierTenantId") Long supplierTenantId,
                                  @RequestParam("distributorTenantId") Long distributorTenantId) {
        supplierDistributorRelFeign.removeDistributor(supplierTenantId, distributorTenantId, ISecurityUtils.getCurrUserId());
    }

    @PostMapping("/addDistributor")
    @ApiOperation("新增分销商")
    @RequiresPermissions("distribution:manage")
    public void addDistributor(@RequestBody AddDistributionReq req) {
        req.setUserId(ISecurityUtils.getCurrUserId());
        supplierDistributorRelFeign.addDistributor(req);
    }

    @GetMapping("/canAddDistributorTenants")
    @ApiOperation("可以新增为分销商的租户列表")
    @RequiresPermissions("distribution:manage")
    public List<BaseIdAndNameDTO> canAddDistributorTenants(@RequestParam("supplierTenantId")Long supplierTenantId) {
        return supplierDistributorRelFeign.canAddDistributorTenants(supplierTenantId);
    }
}