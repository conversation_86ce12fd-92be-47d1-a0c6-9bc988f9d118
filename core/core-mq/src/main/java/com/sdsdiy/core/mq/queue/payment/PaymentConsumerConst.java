package com.sdsdiy.core.mq.queue.payment;

public interface PaymentConsumerConst {
    String GID_CONSUMER_INCOMPLETE_ORDER_FINANCE_CREATE = "GID_CONSUMER_INCOMPLETE_ORDER_FINANCE_CREATE";
    String GID_CONSUMER_LEDGER = "GID_CONSUMER_LEDGER";
    String GID_CONSUMER_LEDGER_ORDER = "GID_CONSUMER_LEDGER_ORDER";
    String GID_OLD_CONSUMER_INCOMPLETE_ORDER_FINANCE_CREATE = "GID_OLD_CONSUMER_INCOMPLETE_ORDER_FINANCE_CREATE";
    String GID_COSUMER_ORDER_FINANCE_ES_REFRESH = "GID_COSUMER_ORDER_FINANCE_ES_REFRESH";
    String GID_COSUMER_ORDER_FINANCE_UPDATE = "GID_COSUMER_ORDER_FINANCE_UPDATE";
    String GID_COSUMER_PAYMENT_ALIPAY_REFUND = "GID_COSUMER_PAYMENT_ALIPAY_REFUND";
    String GID_ALI_PAYMENT = "GID_ALI_PAYMENT";
    String GID_TENANT_ADD_DIS_WALLET_BY_DISTRIBUTION_APP = "GID_TENANT_ADD_DIS_WALLET_BY_DISTRIBUTION_APP";
    String GID_SUB_TRANSACTION_OPERATE = "GID_SUB_TRANSACTION_OPERATE";
}
