package com.sdsdiy.core.mq.queue.payment;

/**
 * @author: bin_lin
 * @date: 2024/7/16 9:46
 * @desc: EVENT_PAYMENT topic的tag
 */
public interface PaymentTagConst {
    String TOPIC = "event_payment_topic";
    String CREATE_INCOMPLETE_ORDER_FINANCE_TOPIC = "event_incomplete_order_finance_create";
    String CREATE_OLD_INCOMPLETE_ORDER_FINANCE_TOPIC = "event_old_incomplete_order_finance_create";

    String ORDER_FINANCE_UPDATE_TOPIC = "event_order_finance_update";
    String LAKALA_CONF_UPDATE = "TENANT_LAKALA_CONF_UPDATE";
    String OLD_ORDER_FINANCE_UPDATE_TOPIC = "event_old_order_finance_update";
    String ORDER_FINANCE_ES_REFRESH_TOPIC = "event_order_finance_es_refresh";
    String ALIPAY_CALLBACK = "ALIPAY_CALLBACK";
    String ALIPAY_REFUND = "ALIPAY_REFUND";

    String TENANT_DIS_BALANCE_CHANGE_TAG = "tenant_dis_balance_change_tag";
    String SUB_TRANSACTION_OPERATE = "sub_transaction_operate";

    @Deprecated
    String TOPIC_LEDGER_ORDER = "topic_ledger_order";
    @Deprecated
    String TOPIC_LEDGER = "topic_ledger";

}
